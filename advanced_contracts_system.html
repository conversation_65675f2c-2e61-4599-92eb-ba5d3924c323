<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>نظام إدارة عقود الموردين والإيجارات المتقدم - جمعية المنقف</title>
  
  <!-- CSS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- JavaScript Libraries -->
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
  <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
  
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
    
    body { 
      font-family: 'Cairo', sans-serif; 
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    /* تخصيص الشريط الجانبي */
    .sidebar {
      background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
      transition: all 0.3s ease;
      box-shadow: 4px 0 15px rgba(0,0,0,0.1);
    }
    
    .sidebar.collapsed {
      width: 80px;
    }
    
    .sidebar-item {
      transition: all 0.3s ease;
      border-radius: 12px;
      margin: 8px 12px;
      position: relative;
      overflow: hidden;
    }
    
    .sidebar-item:hover {
      background: rgba(255,255,255,0.1);
      transform: translateX(5px);
    }
    
    .sidebar-item.active {
      background: rgba(255,255,255,0.2);
      box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
    
    .sidebar-item::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 4px;
      background: #fbbf24;
      transform: scaleY(0);
      transition: transform 0.3s ease;
    }
    
    .sidebar-item.active::before {
      transform: scaleY(1);
    }
    
    /* تخصيص البطاقات */
    .card {
      background: rgba(255,255,255,0.95);
      backdrop-filter: blur(10px);
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
      border: 1px solid rgba(255,255,255,0.2);
      transition: all 0.3s ease;
    }
    
    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    }
    
    /* تخصيص الأزرار */
    .btn {
      transition: all 0.3s ease;
      border-radius: 12px;
      font-weight: 600;
      position: relative;
      overflow: hidden;
    }
    
    .btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s;
    }
    
    .btn:hover::before {
      left: 100%;
    }
    
    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }
    
    /* تخصيص النماذج */
    .form-input {
      transition: all 0.3s ease;
      border-radius: 12px;
      border: 2px solid #e5e7eb;
    }
    
    .form-input:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
      transform: scale(1.02);
    }
    
    .form-input.error {
      border-color: #ef4444;
      box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
    }
    
    .form-input.success {
      border-color: #10b981;
      box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
    }
    
    /* تخصيص الجداول */
    .table-container {
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    
    .table-row {
      transition: all 0.3s ease;
    }
    
    .table-row:hover {
      background: linear-gradient(90deg, #f8fafc, #f1f5f9);
      transform: scale(1.01);
    }
    
    /* تخصيص الإشعارات */
    .toast {
      border-radius: 12px;
      backdrop-filter: blur(10px);
      animation: slideInRight 0.5s ease;
    }
    
    @keyframes slideInRight {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
    
    /* تخصيص النوافذ المنبثقة */
    .modal {
      backdrop-filter: blur(8px);
    }
    
    .modal-content {
      border-radius: 20px;
      background: rgba(255,255,255,0.95);
      backdrop-filter: blur(15px);
      animation: modalSlideIn 0.4s ease;
    }
    
    @keyframes modalSlideIn {
      from {
        transform: scale(0.8) translateY(-50px);
        opacity: 0;
      }
      to {
        transform: scale(1) translateY(0);
        opacity: 1;
      }
    }
    
    /* تخصيص الرسوم البيانية */
    .chart-container {
      background: rgba(255,255,255,0.9);
      border-radius: 16px;
      padding: 20px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    
    /* تخصيص مؤشرات التحميل */
    .loading-spinner {
      border: 3px solid #f3f4f6;
      border-top: 3px solid #3b82f6;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* تخصيص الطباعة */
    @media print {
      .no-print { display: none !important; }
      .sidebar { display: none !important; }
      .main-content { margin-right: 0 !important; }
      body { background: white !important; }
      .card { box-shadow: none !important; background: white !important; }
    }
    
    /* تخصيص الشاشات الصغيرة */
    @media (max-width: 768px) {
      .sidebar {
        position: fixed;
        z-index: 1000;
        height: 100vh;
        transform: translateX(-100%);
      }
      
      .sidebar.open {
        transform: translateX(0);
      }
      
      .main-content {
        margin-right: 0 !important;
      }
    }
    
    /* تأثيرات إضافية */
    .glow {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    }
    
    .pulse {
      animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }
    
    /* تخصيص أيقونات النجمة الحمراء */
    .required::after {
      content: " *";
      color: #ef4444;
      font-weight: bold;
      font-size: 1.2em;
    }
    
    /* تحسين التمرير */
    ::-webkit-scrollbar {
      width: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg, #3b82f6, #1d4ed8);
      border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(180deg, #1d4ed8, #1e40af);
    }
    
    /* تأثيرات الخلفية */
    .bg-pattern {
      background-image: 
        radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.1) 0%, transparent 50%);
    }
    
    /* تحسين الفلاتر */
    .filter-active {
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      color: white;
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    }
    
    /* تحسين الترقيم */
    .pagination-btn {
      transition: all 0.3s ease;
      border-radius: 8px;
    }
    
    .pagination-btn:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    .pagination-btn.active {
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      color: white;
    }
  </style>
</head>
<body class="bg-pattern min-h-screen">

  <!-- نافذة تسجيل الدخول -->
  <div id="loginModal" class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="modal-content bg-white p-8 rounded-2xl shadow-2xl w-full max-w-md mx-4">
      <div class="text-center mb-6">
        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-user-shield text-blue-600 text-2xl"></i>
        </div>
        <h2 class="text-2xl font-bold text-gray-800">تسجيل الدخول</h2>
        <p class="text-gray-600 mt-2">ادخل بياناتك للوصول إلى النظام</p>
      </div>
      
      <form id="loginForm" class="space-y-6">
        <div class="relative">
          <i class="fas fa-user absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          <input type="text" id="username" placeholder="اسم المستخدم" required 
                 class="form-input w-full pr-10 pl-4 py-3 border rounded-lg focus:outline-none">
        </div>
        
        <div class="relative">
          <i class="fas fa-lock absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          <input type="password" id="password" placeholder="كلمة المرور" required 
                 class="form-input w-full pr-10 pl-4 py-3 border rounded-lg focus:outline-none">
          <button type="button" id="togglePassword" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            <i class="fas fa-eye"></i>
          </button>
        </div>
        
        <div class="flex items-center justify-between">
          <label class="flex items-center">
            <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
            <span class="mr-2 text-sm text-gray-600">تذكرني</span>
          </label>
          <a href="#" class="text-sm text-blue-600 hover:text-blue-800">نسيت كلمة المرور؟</a>
        </div>
        
        <div class="flex gap-3">
          <button type="button" id="closeLogin" class="btn flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 px-4 rounded-lg">
            إلغاء
          </button>
          <button type="submit" class="btn flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg">
            <i class="fas fa-sign-in-alt ml-2"></i>
            دخول
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- الشريط الجانبي -->
  <aside id="sidebar" class="sidebar fixed right-0 top-0 h-full w-64 text-white z-40 transform transition-transform lg:translate-x-0">
    <div class="p-6">
      <!-- شعار النظام -->
      <div class="flex items-center mb-8">
        <div class="w-12 h-12 bg-yellow-400 rounded-lg flex items-center justify-center ml-3">
          <i class="fas fa-file-contract text-blue-800 text-xl"></i>
        </div>
        <div>
          <h1 class="text-xl font-bold">نظام العقود</h1>
          <p class="text-blue-200 text-sm">جمعية المنقف</p>
        </div>
      </div>
      
      <!-- معلومات المستخدم -->
      <div id="userInfo" class="bg-white bg-opacity-10 rounded-lg p-4 mb-6 hidden">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center ml-3">
            <i class="fas fa-user text-blue-800"></i>
          </div>
          <div>
            <p class="font-semibold" id="currentUser">مستخدم</p>
            <p class="text-blue-200 text-sm">المدير العام</p>
          </div>
        </div>
      </div>
      
      <!-- قائمة التنقل -->
      <nav class="space-y-2">
        <a href="#dashboard" class="sidebar-item active flex items-center p-3 text-white hover:text-yellow-300" data-section="dashboard">
          <i class="fas fa-tachometer-alt w-6 text-center ml-3"></i>
          <span>لوحة التحكم</span>
        </a>
        
        <a href="#contracts" class="sidebar-item flex items-center p-3 text-white hover:text-yellow-300" data-section="contracts">
          <i class="fas fa-file-contract w-6 text-center ml-3"></i>
          <span>إدارة العقود</span>
        </a>
        
        <a href="#suppliers" class="sidebar-item flex items-center p-3 text-white hover:text-yellow-300" data-section="suppliers">
          <i class="fas fa-truck w-6 text-center ml-3"></i>
          <span>إدارة الموردين</span>
        </a>
        
        <a href="#reports" class="sidebar-item flex items-center p-3 text-white hover:text-yellow-300" data-section="reports">
          <i class="fas fa-chart-bar w-6 text-center ml-3"></i>
          <span>التقارير والإحصائيات</span>
        </a>
        
        <a href="#settings" class="sidebar-item flex items-center p-3 text-white hover:text-yellow-300" data-section="settings">
          <i class="fas fa-cog w-6 text-center ml-3"></i>
          <span>الإعدادات</span>
        </a>
      </nav>
      
      <!-- إحصائيات سريعة -->
      <div class="mt-8 bg-white bg-opacity-10 rounded-lg p-4">
        <h3 class="text-sm font-semibold mb-3 text-yellow-300">إحصائيات سريعة</h3>
        <div class="space-y-2 text-sm">
          <div class="flex justify-between">
            <span>العقود النشطة:</span>
            <span id="activeContractsCount" class="font-bold text-yellow-300">0</span>
          </div>
          <div class="flex justify-between">
            <span>إجمالي الموردين:</span>
            <span id="totalSuppliersCount" class="font-bold text-yellow-300">0</span>
          </div>
          <div class="flex justify-between">
            <span>المسودات:</span>
            <span id="draftsCount" class="font-bold text-yellow-300">0</span>
          </div>
        </div>
      </div>
      
      <!-- زر تسجيل الخروج -->
      <div class="mt-8">
        <button id="logoutBtn" class="w-full flex items-center justify-center p-3 bg-red-600 hover:bg-red-700 rounded-lg transition-colors">
          <i class="fas fa-sign-out-alt ml-2"></i>
          تسجيل الخروج
        </button>
      </div>
    </div>
  </aside>

  <!-- طبقة تغطية للشريط الجانبي في الشاشات الصغيرة -->
  <div id="sidebarOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-30 hidden lg:hidden"></div>

  <!-- المحتوى الرئيسي -->
  <div class="main-content flex-1 mr-0 lg:mr-64 min-h-screen">

    <!-- الشريط العلوي -->
    <header class="bg-white shadow-lg p-4 flex items-center justify-between no-print sticky top-0 z-20">
      <div class="flex items-center">
        <button id="toggleSidebar" class="lg:hidden text-gray-700 hover:text-blue-600 mr-4">
          <i class="fas fa-bars text-xl"></i>
        </button>
        <div>
          <h1 class="text-xl font-bold text-gray-800">نظام إدارة عقود الموردين والإيجارات</h1>
          <p class="text-sm text-gray-600">جمعية المنقف التعاونية</p>
        </div>
      </div>

      <div class="flex items-center space-x-4">
        <!-- مؤشر حالة الاتصال -->
        <div id="connectionStatus" class="flex items-center">
          <div class="w-3 h-3 bg-green-500 rounded-full ml-2 pulse"></div>
          <span class="text-sm text-green-600 font-medium">متصل</span>
        </div>

        <!-- الوقت الحالي -->
        <div class="text-sm text-gray-600">
          <i class="fas fa-clock ml-1"></i>
          <span id="currentTime"></span>
        </div>

        <!-- زر تسجيل الدخول -->
        <button id="loginBtn" class="btn bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
          <i class="fas fa-user ml-2"></i>
          تسجيل دخول
        </button>
      </div>
    </header>

    <!-- المحتوى -->
    <div class="p-6 space-y-8">

      <!-- شريط الأدوات الرئيسي -->
      <div class="card p-6 no-print">
        <div class="flex flex-wrap gap-4 items-center justify-between">
          <div class="flex flex-wrap gap-3">
            <!-- تحميل التطبيق -->
            <a href="/downloads/SupplierContractManager.exe" download
               class="btn bg-purple-600 hover:bg-purple-700 text-white py-3 px-6 rounded-lg flex items-center">
              <i class="fas fa-download ml-2"></i>
              تحميل التطبيق
            </a>

            <!-- حفظ نهائي -->
            <button id="btnSaveFinal" class="btn bg-indigo-600 hover:bg-indigo-700 text-white py-3 px-6 rounded-lg flex items-center">
              <i class="fas fa-save ml-2"></i>
              حفظ نهائي
            </button>

            <!-- تصدير Excel -->
            <button id="exportExcel" class="btn bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg flex items-center">
              <i class="fas fa-file-excel ml-2"></i>
              تصدير Excel
            </button>

            <!-- تصدير PDF -->
            <button id="exportPDF" class="btn bg-red-600 hover:bg-red-700 text-white py-3 px-6 rounded-lg flex items-center">
              <i class="fas fa-file-pdf ml-2"></i>
              تصدير PDF
            </button>

            <!-- طباعة -->
            <button id="printBtn" class="btn bg-gray-700 hover:bg-gray-800 text-white py-3 px-6 rounded-lg flex items-center">
              <i class="fas fa-print ml-2"></i>
              طباعة
            </button>
          </div>

          <!-- معلومات الحالة -->
          <div class="flex items-center space-x-4 text-sm">
            <span class="text-gray-600">
              <i class="fas fa-database ml-1"></i>
              آخر حفظ: <span id="lastSaved">لم يتم الحفظ بعد</span>
            </span>
            <span class="text-gray-600">
              <i class="fas fa-file-alt ml-1"></i>
              إجمالي العقود: <span id="totalContracts">0</span>
            </span>
          </div>
        </div>
      </div>

      <!-- قسم لوحة التحكم -->
      <section id="dashboardSection" class="section">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <!-- بطاقة العقود النشطة -->
          <div class="card p-6 text-center">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-file-contract text-blue-600 text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-800" id="dashActiveContracts">0</h3>
            <p class="text-gray-600">العقود النشطة</p>
          </div>

          <!-- بطاقة إجمالي الموردين -->
          <div class="card p-6 text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-truck text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-800" id="dashTotalSuppliers">0</h3>
            <p class="text-gray-600">إجمالي الموردين</p>
          </div>

          <!-- بطاقة العقود المنتهية -->
          <div class="card p-6 text-center">
            <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-clock text-yellow-600 text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-800" id="dashExpiredContracts">0</h3>
            <p class="text-gray-600">العقود المنتهية</p>
          </div>

          <!-- بطاقة إجمالي القيمة -->
          <div class="card p-6 text-center">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-dollar-sign text-purple-600 text-2xl"></i>
            </div>
            <h3 class="text-2xl font-bold text-gray-800" id="dashTotalValue">0</h3>
            <p class="text-gray-600">إجمالي القيمة (د.ك)</p>
          </div>
        </div>

        <!-- الرسوم البيانية -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="chart-container">
            <h3 class="text-lg font-bold text-gray-800 mb-4">توزيع العقود حسب النوع</h3>
            <canvas id="contractsTypeChart" width="400" height="200"></canvas>
          </div>

          <div class="chart-container">
            <h3 class="text-lg font-bold text-gray-800 mb-4">العقود الشهرية</h3>
            <canvas id="monthlyContractsChart" width="400" height="200"></canvas>
          </div>
        </div>
      </section>

      <!-- قسم إدارة العقود -->
      <section id="contractsSection" class="section hidden">

        <!-- نموذج إضافة/تعديل عقد -->
        <div class="card p-6 mb-8">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-bold text-gray-800">
              <i class="fas fa-plus-circle ml-2 text-blue-600"></i>
              إضافة/تعديل عقد
            </h2>
            <button id="toggleFormBtn" class="btn bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
              <i class="fas fa-plus ml-2"></i>
              عقد جديد
            </button>
          </div>

          <form id="contract-form" class="space-y-6 hidden" novalidate>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

              <!-- اسم العقد -->
              <div>
                <label class="block text-gray-700 font-medium mb-2 required">اسم العقد</label>
                <input type="text" name="contractName" required
                       class="form-input w-full px-4 py-3 border rounded-lg focus:outline-none"
                       placeholder="أدخل اسم العقد">
                <div class="text-red-500 text-sm mt-1 hidden error-message">اسم العقد مطلوب</div>
              </div>

              <!-- رقم العقد -->
              <div>
                <label class="block text-gray-700 font-medium mb-2 required">رقم العقد</label>
                <div class="flex">
                  <input type="text" name="contractNumber" required
                         class="form-input flex-1 px-4 py-3 border rounded-r-lg focus:outline-none"
                         placeholder="2025-001" pattern="[0-9]{4}-[0-9]{3}">
                  <button type="button" id="generateNumberBtn"
                          class="btn bg-blue-600 hover:bg-blue-700 text-white px-4 rounded-l-lg">
                    <i class="fas fa-sync-alt"></i>
                  </button>
                </div>
                <div class="text-red-500 text-sm mt-1 hidden error-message">رقم العقد مطلوب بتنسيق YYYY-XXX</div>
              </div>

              <!-- اسم المورد -->
              <div>
                <label class="block text-gray-700 font-medium mb-2 required">اسم المورد</label>
                <input type="text" name="supplierName" required
                       class="form-input w-full px-4 py-3 border rounded-lg focus:outline-none"
                       placeholder="أدخل اسم المورد">
                <div class="text-red-500 text-sm mt-1 hidden error-message">اسم المورد مطلوب</div>
              </div>

              <!-- تاريخ البداية -->
              <div>
                <label class="block text-gray-700 font-medium mb-2 required">تاريخ البداية</label>
                <input type="text" id="startDate" name="startDate" required
                       class="form-input w-full px-4 py-3 border rounded-lg focus:outline-none"
                       placeholder="اختر تاريخ البداية">
                <div class="text-red-500 text-sm mt-1 hidden error-message">تاريخ البداية مطلوب</div>
              </div>

              <!-- تاريخ النهاية -->
              <div>
                <label class="block text-gray-700 font-medium mb-2 required">تاريخ النهاية</label>
                <input type="text" id="endDate" name="endDate" required
                       class="form-input w-full px-4 py-3 border rounded-lg focus:outline-none"
                       placeholder="اختر تاريخ النهاية">
                <div class="text-red-500 text-sm mt-1 hidden error-message">تاريخ النهاية مطلوب</div>
              </div>

              <!-- لون العقد -->
              <div>
                <label class="block text-gray-700 font-medium mb-2">لون العقد</label>
                <div class="flex items-center">
                  <input type="color" name="contractColor" value="#3b82f6"
                         class="w-12 h-12 border rounded-lg cursor-pointer">
                  <span class="mr-3 text-gray-600">اختر لون مميز للعقد</span>
                </div>
              </div>
            </div>

            <!-- الصف الثاني -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

              <!-- نوع العقد -->
              <div>
                <label class="block text-gray-700 font-medium mb-2 required">نوع العقد</label>
                <select name="contractType" required
                        class="form-input w-full px-4 py-3 border rounded-lg focus:outline-none">
                  <option value="">-- اختر النوع --</option>
                  <option value="توريد">📦 توريد</option>
                  <option value="إيجار">🏠 إيجار</option>
                  <option value="خدمات">🔧 خدمات</option>
                  <option value="صيانة">⚙️ صيانة</option>
                </select>
                <div class="text-red-500 text-sm mt-1 hidden error-message">نوع العقد مطلوب</div>
              </div>

              <!-- قسم المورد -->
              <div>
                <label class="block text-gray-700 font-medium mb-2">قسم المورد</label>
                <select name="supplierCategory"
                        class="form-input w-full px-4 py-3 border rounded-lg focus:outline-none">
                  <option value="">-- اختر القسم --</option>
                  <option value="بهارات">🌶️ بهارات</option>
                  <option value="استهلاكي">🧽 مواد استهلاكية</option>
                  <option value="أجبان">🧀 أجبان</option>
                </select>
              </div>

              <!-- المبلغ الإجمالي -->
              <div>
                <label class="block text-gray-700 font-medium mb-2">المبلغ الإجمالي (د.ك)</label>
                <input type="number" name="totalAmount" step="0.01" min="0"
                       class="form-input w-full px-4 py-3 border rounded-lg focus:outline-none"
                       placeholder="0.00">
                <div class="text-red-500 text-sm mt-1 hidden error-message">المبلغ يجب أن يكون رقماً موجباً</div>
              </div>

              <!-- حالة العقد -->
              <div>
                <label class="block text-gray-700 font-medium mb-2">حالة العقد</label>
                <select name="contractStatus"
                        class="form-input w-full px-4 py-3 border rounded-lg focus:outline-none">
                  <option value="نشط">✅ نشط</option>
                  <option value="منتهي">⏰ منتهي</option>
                  <option value="ملغي">❌ ملغي</option>
                  <option value="معلق">⏸️ معلق</option>
                </select>
              </div>

              <!-- شروط الدفع -->
              <div>
                <label class="block text-gray-700 font-medium mb-2">شروط الدفع</label>
                <input type="text" name="paymentTerms"
                       class="form-input w-full px-4 py-3 border rounded-lg focus:outline-none"
                       placeholder="مثال: دفع شهري مقدم">
              </div>
            </div>

            <!-- عناصر العقد المتعددة -->
            <div>
              <label class="block text-gray-700 font-medium mb-2 required">عناصر العقد</label>
              <select name="contractItems" multiple required
                      class="form-input w-full h-32 px-4 py-3 border rounded-lg focus:outline-none">
                <option value="بهارات_حارة">🌶️ بهارات حارة</option>
                <option value="بهارات_حلوة">🍯 بهارات حلوة</option>
                <option value="توابل_شرقية">🥘 توابل شرقية</option>
                <option value="مواد_تنظيف">🧽 مواد تنظيف</option>
                <option value="مستلزمات_مطبخ">🍴 مستلزمات مطبخ</option>
                <option value="أدوات_شخصية">🧴 أدوات شخصية</option>
                <option value="أجبان_طازجة">🧀 أجبان طازجة</option>
                <option value="منتجات_ألبان">🥛 منتجات ألبان</option>
                <option value="أجبان_مستوردة">🧀 أجبان مستوردة</option>
                <option value="خدمات_صيانة">🔧 خدمات صيانة</option>
                <option value="خدمات_تنظيف">🧹 خدمات تنظيف</option>
                <option value="خدمات_أمن">🛡️ خدمات أمن</option>
              </select>
              <div class="text-xs text-gray-500 mt-1">
                اضغط Ctrl (أو Cmd) + النقر لاختيار عدة عناصر
              </div>
              <div class="text-red-500 text-sm mt-1 hidden error-message">يجب اختيار عنصر واحد على الأقل</div>
            </div>

            <!-- الملاحظات -->
            <div>
              <label class="block text-gray-700 font-medium mb-2">الملاحظات</label>
              <textarea name="notes" rows="4"
                        class="form-input w-full px-4 py-3 border rounded-lg focus:outline-none"
                        placeholder="أي ملاحظات إضافية حول العقد..."></textarea>
            </div>

            <!-- أزرار النموذج -->
            <div class="flex flex-wrap gap-3 pt-6 border-t">
              <button type="button" id="btnSaveLocal"
                      class="btn bg-yellow-600 hover:bg-yellow-700 text-white py-3 px-6 rounded-lg flex items-center">
                <i class="fas fa-save ml-2"></i>
                حفظ مؤقت
              </button>

              <button type="submit"
                      class="btn bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg flex items-center">
                <i class="fas fa-check ml-2"></i>
                حفظ نهائي
              </button>

              <button type="button" id="btnClearForm"
                      class="btn bg-gray-500 hover:bg-gray-600 text-white py-3 px-6 rounded-lg flex items-center">
                <i class="fas fa-eraser ml-2"></i>
                مسح النموذج
              </button>

              <button type="button" id="btnCancelEdit"
                      class="btn bg-red-500 hover:bg-red-600 text-white py-3 px-6 rounded-lg flex items-center hidden">
                <i class="fas fa-times ml-2"></i>
                إلغاء التعديل
              </button>
            </div>
          </form>
        </div>

        <!-- بحث وفلاتر متقدمة -->
        <div class="card p-6 mb-8">
          <h3 class="text-lg font-bold text-gray-800 mb-4">
            <i class="fas fa-search ml-2 text-blue-600"></i>
            البحث والفلاتر المتقدمة
          </h3>

          <!-- الصف الأول - البحث الأساسي -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div class="relative">
              <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
              <input type="text" id="searchInput"
                     placeholder="ابحث باسم المورد أو رقم العقد..."
                     class="form-input w-full pr-10 pl-4 py-3 border rounded-lg focus:outline-none">
            </div>

            <select id="filterType" class="form-input border rounded-lg p-3 focus:outline-none">
              <option value="">كل الأنواع</option>
              <option value="توريد">📦 توريد</option>
              <option value="إيجار">🏠 إيجار</option>
              <option value="خدمات">🔧 خدمات</option>
              <option value="صيانة">⚙️ صيانة</option>
            </select>

            <select id="filterStatus" class="form-input border rounded-lg p-3 focus:outline-none">
              <option value="">كل الحالات</option>
              <option value="نشط">✅ نشط</option>
              <option value="منتهي">⏰ منتهي</option>
              <option value="ملغي">❌ ملغي</option>
              <option value="معلق">⏸️ معلق</option>
            </select>

            <select id="filterCategory" class="form-input border rounded-lg p-3 focus:outline-none">
              <option value="">كل الأقسام</option>
              <option value="بهارات">🌶️ بهارات</option>
              <option value="استهلاكي">🧽 مواد استهلاكية</option>
              <option value="أجبان">🧀 أجبان</option>
            </select>
          </div>

          <!-- الصف الثاني - فلاتر متقدمة -->
          <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
            <div>
              <label class="block text-sm text-gray-600 mb-1">من تاريخ</label>
              <input type="text" id="filterStartDate" placeholder="اختر التاريخ"
                     class="form-input w-full border rounded-lg p-3 focus:outline-none">
            </div>

            <div>
              <label class="block text-sm text-gray-600 mb-1">إلى تاريخ</label>
              <input type="text" id="filterEndDate" placeholder="اختر التاريخ"
                     class="form-input w-full border rounded-lg p-3 focus:outline-none">
            </div>

            <div>
              <label class="block text-sm text-gray-600 mb-1">المبلغ من</label>
              <input type="number" id="filterMinAmount" step="0.01" min="0" placeholder="0.00"
                     class="form-input w-full border rounded-lg p-3 focus:outline-none">
            </div>

            <div>
              <label class="block text-sm text-gray-600 mb-1">المبلغ إلى</label>
              <input type="number" id="filterMaxAmount" step="0.01" min="0" placeholder="∞"
                     class="form-input w-full border rounded-lg p-3 focus:outline-none">
            </div>

            <div class="flex items-end">
              <button id="btnClearFilters"
                      class="btn w-full bg-gray-500 hover:bg-gray-600 text-white py-3 px-4 rounded-lg flex items-center justify-center">
                <i class="fas fa-eraser ml-2"></i>
                مسح الفلاتر
              </button>
            </div>
          </div>

          <!-- معلومات النتائج -->
          <div class="flex items-center justify-between text-sm text-gray-600 pt-4 border-t">
            <span id="resultsInfo">عرض 0 من 0 عقد</span>
            <div class="flex items-center space-x-2">
              <span>عرض:</span>
              <select id="itemsPerPage" class="form-input border rounded p-2 text-sm">
                <option value="10">10 عقود</option>
                <option value="25">25 عقد</option>
                <option value="50">50 عقد</option>
                <option value="100">100 عقد</option>
              </select>
            </div>
          </div>
        </div>

        <!-- جدول العقود -->
        <div class="card overflow-hidden">
          <div class="p-4 bg-gray-50 border-b flex items-center justify-between">
            <h3 class="text-lg font-bold text-gray-800">
              <i class="fas fa-table ml-2 text-blue-600"></i>
              قائمة العقود
            </h3>
            <button id="btnRefresh"
                    class="btn bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
              <i class="fas fa-sync-alt ml-2"></i>
              تحديث
            </button>
          </div>

          <div class="table-container overflow-x-auto">
            <table class="min-w-full">
              <thead class="bg-gray-100">
                <tr>
                  <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200" data-sort="contractNumber">
                    رقم العقد
                    <i class="fas fa-sort ml-1"></i>
                  </th>
                  <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200" data-sort="contractName">
                    اسم العقد
                    <i class="fas fa-sort ml-1"></i>
                  </th>
                  <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200" data-sort="supplierName">
                    المورد
                    <i class="fas fa-sort ml-1"></i>
                  </th>
                  <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200" data-sort="contractType">
                    النوع
                    <i class="fas fa-sort ml-1"></i>
                  </th>
                  <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200" data-sort="startDate">
                    تاريخ البداية
                    <i class="fas fa-sort ml-1"></i>
                  </th>
                  <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200" data-sort="totalAmount">
                    المبلغ (د.ك)
                    <i class="fas fa-sort ml-1"></i>
                  </th>
                  <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200" data-sort="contractStatus">
                    الحالة
                    <i class="fas fa-sort ml-1"></i>
                  </th>
                  <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody id="contractsTableBody" class="bg-white divide-y divide-gray-200">
                <!-- سيتم ملء البيانات ديناميكياً -->
              </tbody>
            </table>
          </div>

          <!-- رسالة عدم وجود بيانات -->
          <div id="noDataMessage" class="hidden p-12 text-center text-gray-500">
            <div class="text-6xl mb-4">📄</div>
            <h3 class="text-lg font-medium mb-2">لا توجد عقود</h3>
            <p class="text-sm">ابدأ بإضافة عقد جديد أو قم بتعديل الفلاتر</p>
          </div>

          <!-- الترقيم -->
          <div id="pagination" class="p-4 bg-gray-50 border-t flex items-center justify-between">
            <div class="text-sm text-gray-600">
              عرض <span id="showingFrom">0</span> إلى <span id="showingTo">0</span> من <span id="totalItems">0</span> عقد
            </div>
            <div id="paginationButtons" class="flex space-x-2">
              <!-- أزرار الترقيم ستُضاف ديناميكياً -->
            </div>
          </div>
        </div>
      </section>

      <!-- قسم إدارة الموردين -->
      <section id="suppliersSection" class="section hidden">
        <div class="card p-8 text-center">
          <div class="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-truck text-blue-600 text-4xl"></i>
          </div>
          <h2 class="text-2xl font-bold text-gray-800 mb-4">إدارة الموردين</h2>
          <p class="text-gray-600 mb-6">قسم إدارة الموردين قيد التطوير</p>
          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <p class="text-yellow-800">
              <i class="fas fa-info-circle ml-2"></i>
              سيتم إضافة ميزات إدارة الموردين في التحديث القادم
            </p>
          </div>
        </div>
      </section>

      <!-- قسم التقارير -->
      <section id="reportsSection" class="section hidden">
        <div class="card p-8 text-center">
          <div class="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-chart-bar text-green-600 text-4xl"></i>
          </div>
          <h2 class="text-2xl font-bold text-gray-800 mb-4">التقارير والإحصائيات</h2>
          <p class="text-gray-600 mb-6">قسم التقارير والإحصائيات قيد التطوير</p>
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p class="text-blue-800">
              <i class="fas fa-chart-line ml-2"></i>
              سيتم إضافة تقارير مفصلة وإحصائيات متقدمة قريباً
            </p>
          </div>
        </div>
      </section>

      <!-- قسم الإعدادات -->
      <section id="settingsSection" class="section hidden">
        <div class="card p-8 text-center">
          <div class="w-24 h-24 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-cog text-purple-600 text-4xl"></i>
          </div>
          <h2 class="text-2xl font-bold text-gray-800 mb-4">إعدادات النظام</h2>
          <p class="text-gray-600 mb-6">قسم الإعدادات قيد التطوير</p>
          <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <p class="text-purple-800">
              <i class="fas fa-tools ml-2"></i>
              سيتم إضافة إعدادات النظام والتخصيص في التحديث القادم
            </p>
          </div>
        </div>
      </section>

    </div>
  </div>

  <!-- صندوق الإشعارات (Toast) -->
  <div id="toast" class="toast fixed top-5 left-5 hidden p-4 rounded-lg shadow-lg z-50 max-w-sm">
    <div class="flex items-center">
      <div id="toastIcon" class="flex-shrink-0 w-6 h-6 ml-3"></div>
      <div id="toastMessage" class="text-sm font-medium flex-1"></div>
      <button id="toastClose" class="mr-auto text-white hover:text-gray-200">
        <i class="fas fa-times"></i>
      </button>
    </div>
  </div>

  <!-- نافذة تأكيد الحذف -->
  <div id="deleteModal" class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="modal-content bg-white p-6 rounded-2xl shadow-2xl w-full max-w-md mx-4">
      <div class="text-center">
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
        </div>
        <h3 class="text-lg font-bold text-gray-800 mb-2">تأكيد الحذف</h3>
        <p class="text-gray-600 mb-6">هل أنت متأكد من حذف هذا العقد؟ لا يمكن التراجع عن هذا الإجراء.</p>
        <div class="flex gap-3">
          <button id="cancelDelete" class="btn flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
            إلغاء
          </button>
          <button id="confirmDelete" class="btn flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg">
            حذف
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- JavaScript الرئيسي -->
  <script>
    // ==================== المتغيرات العامة ====================
    let contracts = JSON.parse(localStorage.getItem('contracts') || '[]');
    let drafts = JSON.parse(localStorage.getItem('drafts') || '[]');
    let currentPage = 1;
    let itemsPerPage = 10;
    let sortField = 'contractNumber';
    let sortDirection = 'asc';
    let editingContractId = null;
    let filteredContracts = [];
    let isOnline = true;
    let currentUser = null;
    let deleteContractId = null;

    // منتقيات التاريخ
    let startDatePicker = null;
    let endDatePicker = null;
    let filterStartDatePicker = null;
    let filterEndDatePicker = null;

    // الرسوم البيانية
    let contractsTypeChart = null;
    let monthlyContractsChart = null;

    // إعدادات API
    const API_BASE_URL = 'http://localhost:3000/api';

    // ==================== تهيئة التطبيق ====================
    document.addEventListener('DOMContentLoaded', function() {
      initializeApp();
      setupEventListeners();
      initializeFlatpickr();
      initializeCharts();
      updateCurrentTime();
      setInterval(updateCurrentTime, 1000);
    });

    function initializeApp() {
      // إخفاء النموذج في البداية
      hideForm();

      // تحديث العرض
      applyFilters();
      updateStatistics();

      // إضافة بيانات تجريبية إذا لم تكن موجودة
      if (contracts.length === 0) {
        addSampleData();
      }

      // فحص الاتصال بالخادم
      checkServerConnection();

      // عرض لوحة التحكم افتراضياً
      switchSection('dashboard');
    }

    function addSampleData() {
      const sampleContracts = [
        {
          id: generateId(),
          contractNumber: '2025-001',
          contractName: 'عقد توريد البهارات الشرقية',
          supplierName: 'شركة البهارات الذهبية',
          contractType: 'توريد',
          supplierCategory: 'بهارات',
          contractItems: ['بهارات_حارة', 'توابل_شرقية'],
          startDate: '2025-01-01',
          endDate: '2025-12-31',
          totalAmount: 1200.00,
          contractStatus: 'نشط',
          contractColor: '#3b82f6',
          paymentTerms: 'دفع شهري مقدم',
          notes: 'عقد توريد البهارات والتوابل الشرقية عالية الجودة',
          createdAt: new Date().toISOString()
        },
        {
          id: generateId(),
          contractNumber: '2025-002',
          contractName: 'عقد توريد المواد الاستهلاكية',
          supplierName: 'مؤسسة المواد الاستهلاكية',
          contractType: 'توريد',
          supplierCategory: 'استهلاكي',
          contractItems: ['مواد_تنظيف', 'مستلزمات_مطبخ'],
          startDate: '2025-01-15',
          endDate: '2025-06-15',
          totalAmount: 800.00,
          contractStatus: 'نشط',
          contractColor: '#10b981',
          paymentTerms: 'دفع عند التسليم',
          notes: 'توريد مواد التنظيف والمستلزمات الأساسية',
          createdAt: new Date().toISOString()
        },
        {
          id: generateId(),
          contractNumber: '2025-003',
          contractName: 'عقد إيجار عين الأجبان',
          supplierName: 'شركة الأجبان الطازجة',
          contractType: 'إيجار',
          supplierCategory: 'أجبان',
          contractItems: ['أجبان_طازجة', 'منتجات_ألبان'],
          startDate: '2025-02-01',
          endDate: '2025-07-31',
          totalAmount: 360.00,
          contractStatus: 'نشط',
          contractColor: '#f59e0b',
          paymentTerms: 'دفع شهري 60 دينار',
          notes: 'إيجار عين واحدة في قسم الأجبان والألبان',
          createdAt: new Date().toISOString()
        },
        {
          id: generateId(),
          contractNumber: '2025-004',
          contractName: 'عقد صيانة المعدات',
          supplierName: 'شركة الصيانة المتخصصة',
          contractType: 'صيانة',
          supplierCategory: '',
          contractItems: ['خدمات_صيانة'],
          startDate: '2025-01-01',
          endDate: '2024-12-31',
          totalAmount: 500.00,
          contractStatus: 'منتهي',
          contractColor: '#ef4444',
          paymentTerms: 'دفع ربع سنوي',
          notes: 'عقد صيانة دورية لجميع المعدات',
          createdAt: new Date().toISOString()
        }
      ];

      contracts = sampleContracts;
      saveContracts();
      showToast('تم إضافة بيانات تجريبية للنظام', 'success');
    }

    function initializeFlatpickr() {
      // إعداد Flatpickr للتواريخ العربية
      const flatpickrConfig = {
        locale: 'ar',
        dateFormat: 'Y-m-d',
        allowInput: true,
        clickOpens: true,
        position: 'auto'
      };

      // تهيئة منتقي التواريخ في النموذج
      startDatePicker = flatpickr('#startDate', {
        ...flatpickrConfig,
        minDate: 'today',
        onChange: function(selectedDates, dateStr, instance) {
          if (endDatePicker && selectedDates[0]) {
            endDatePicker.set('minDate', selectedDates[0]);
          }
          validateField(document.getElementById('startDate'));
        }
      });

      endDatePicker = flatpickr('#endDate', {
        ...flatpickrConfig,
        onChange: function(selectedDates, dateStr, instance) {
          validateField(document.getElementById('endDate'));
        }
      });

      // تهيئة منتقي التواريخ في الفلاتر
      filterStartDatePicker = flatpickr('#filterStartDate', {
        ...flatpickrConfig,
        onChange: function(selectedDates, dateStr, instance) {
          if (filterEndDatePicker && selectedDates[0]) {
            filterEndDatePicker.set('minDate', selectedDates[0]);
          }
          applyFilters();
        }
      });

      filterEndDatePicker = flatpickr('#filterEndDate', {
        ...flatpickrConfig,
        onChange: function(selectedDates, dateStr, instance) {
          applyFilters();
        }
      });
    }

    function initializeCharts() {
      // رسم بياني لتوزيع العقود حسب النوع
      const typeCtx = document.getElementById('contractsTypeChart').getContext('2d');
      contractsTypeChart = new Chart(typeCtx, {
        type: 'doughnut',
        data: {
          labels: ['توريد', 'إيجار', 'خدمات', 'صيانة'],
          datasets: [{
            data: [0, 0, 0, 0],
            backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'],
            borderWidth: 2,
            borderColor: '#ffffff'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom'
            }
          }
        }
      });

      // رسم بياني للعقود الشهرية
      const monthlyCtx = document.getElementById('monthlyContractsChart').getContext('2d');
      monthlyContractsChart = new Chart(monthlyCtx, {
        type: 'line',
        data: {
          labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
          datasets: [{
            label: 'عدد العقود',
            data: [0, 0, 0, 0, 0, 0],
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });

      updateCharts();
    }

    // ==================== إدارة الأحداث ====================
    function setupEventListeners() {
      // الشريط الجانبي
      document.getElementById('toggleSidebar').addEventListener('click', toggleSidebar);
      document.getElementById('sidebarOverlay').addEventListener('click', closeSidebar);

      // التنقل بين الأقسام
      document.querySelectorAll('.sidebar-item').forEach(item => {
        item.addEventListener('click', (e) => {
          e.preventDefault();
          const section = item.dataset.section;
          if (section) {
            switchSection(section);
          }
        });
      });

      // تسجيل الدخول والخروج
      document.getElementById('loginBtn').addEventListener('click', showLoginModal);
      document.getElementById('closeLogin').addEventListener('click', hideLoginModal);
      document.getElementById('loginForm').addEventListener('submit', handleLogin);
      document.getElementById('logoutBtn').addEventListener('click', handleLogout);
      document.getElementById('togglePassword').addEventListener('click', togglePasswordVisibility);

      // أزرار التحكم الرئيسية
      document.getElementById('btnSaveFinal').addEventListener('click', () => {
        document.getElementById('contract-form').dispatchEvent(new Event('submit'));
      });
      document.getElementById('exportExcel').addEventListener('click', exportToExcel);
      document.getElementById('exportPDF').addEventListener('click', exportToPDF);
      document.getElementById('printBtn').addEventListener('click', () => window.print());

      // نموذج العقد
      document.getElementById('toggleFormBtn').addEventListener('click', toggleForm);
      document.getElementById('contract-form').addEventListener('submit', handleFormSubmit);
      document.getElementById('btnSaveLocal').addEventListener('click', saveDraft);
      document.getElementById('btnClearForm').addEventListener('click', clearForm);
      document.getElementById('btnCancelEdit').addEventListener('click', cancelEdit);
      document.getElementById('generateNumberBtn').addEventListener('click', generateContractNumber);

      // التحقق من صحة البيانات أثناء الكتابة
      document.querySelectorAll('.form-input').forEach(input => {
        input.addEventListener('blur', () => validateField(input));
        input.addEventListener('input', () => clearFieldError(input));
      });

      // البحث والفلاتر
      document.getElementById('searchInput').addEventListener('input', debounce(applyFilters, 300));
      document.getElementById('filterType').addEventListener('change', applyFilters);
      document.getElementById('filterStatus').addEventListener('change', applyFilters);
      document.getElementById('filterCategory').addEventListener('change', applyFilters);
      document.getElementById('filterMinAmount').addEventListener('input', debounce(applyFilters, 500));
      document.getElementById('filterMaxAmount').addEventListener('input', debounce(applyFilters, 500));
      document.getElementById('btnClearFilters').addEventListener('click', clearFilters);

      // الجدول والترقيم
      document.getElementById('itemsPerPage').addEventListener('change', changeItemsPerPage);
      document.getElementById('btnRefresh').addEventListener('click', refreshData);

      // ترتيب الجدول
      document.querySelectorAll('[data-sort]').forEach(header => {
        header.addEventListener('click', () => sortTable(header.dataset.sort));
      });

      // نافذة الحذف
      document.getElementById('cancelDelete').addEventListener('click', hideDeleteModal);
      document.getElementById('confirmDelete').addEventListener('click', confirmDeleteContract);

      // إغلاق Toast
      document.getElementById('toastClose').addEventListener('click', hideToast);

      // إغلاق النوافذ بالضغط على Escape
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
          hideToast();
          hideLoginModal();
          hideDeleteModal();
          closeSidebar();
        }
      });
    }

    // ==================== إدارة الشريط الجانبي ====================
    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      const overlay = document.getElementById('sidebarOverlay');

      sidebar.classList.toggle('open');
      overlay.classList.toggle('hidden');
    }

    function closeSidebar() {
      const sidebar = document.getElementById('sidebar');
      const overlay = document.getElementById('sidebarOverlay');

      sidebar.classList.remove('open');
      overlay.classList.add('hidden');
    }

    function switchSection(sectionName) {
      // إخفاء جميع الأقسام
      document.querySelectorAll('.section').forEach(section => {
        section.classList.add('hidden');
      });

      // إزالة الحالة النشطة من جميع عناصر التنقل
      document.querySelectorAll('.sidebar-item').forEach(item => {
        item.classList.remove('active');
      });

      // إظهار القسم المحدد
      const targetSection = document.getElementById(sectionName + 'Section');
      if (targetSection) {
        targetSection.classList.remove('hidden');
      }

      // تفعيل عنصر التنقل المحدد
      const activeNavItem = document.querySelector(`[data-section="${sectionName}"]`);
      if (activeNavItem) {
        activeNavItem.classList.add('active');
      }

      // إغلاق الشريط الجانبي في الشاشات الصغيرة
      closeSidebar();

      // تحديث الرسوم البيانية إذا كان القسم هو لوحة التحكم
      if (sectionName === 'dashboard') {
        setTimeout(updateCharts, 100);
      }
    }

    // ==================== إدارة تسجيل الدخول ====================
    function showLoginModal() {
      document.getElementById('loginModal').classList.remove('hidden');
    }

    function hideLoginModal() {
      document.getElementById('loginModal').classList.add('hidden');
      document.getElementById('loginForm').reset();
    }

    function handleLogin(e) {
      e.preventDefault();
      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;

      // تحقق بسيط (يمكن تطويره للاتصال بخادم حقيقي)
      if (username && password) {
        currentUser = username;
        updateUserInterface();
        hideLoginModal();
        showToast(`مرحباً ${username}! تم تسجيل الدخول بنجاح`, 'success');
      } else {
        showToast('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
      }
    }

    function handleLogout() {
      if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        currentUser = null;
        updateUserInterface();
        showToast('تم تسجيل الخروج بنجاح', 'success');
      }
    }

    function togglePasswordVisibility() {
      const passwordInput = document.getElementById('password');
      const toggleBtn = document.getElementById('togglePassword');

      if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i>';
      } else {
        passwordInput.type = 'password';
        toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
      }
    }

    function updateUserInterface() {
      const loginBtn = document.getElementById('loginBtn');
      const userInfo = document.getElementById('userInfo');
      const currentUserSpan = document.getElementById('currentUser');

      if (currentUser) {
        loginBtn.classList.add('hidden');
        userInfo.classList.remove('hidden');
        currentUserSpan.textContent = currentUser;
      } else {
        loginBtn.classList.remove('hidden');
        userInfo.classList.add('hidden');
      }
    }

    // ==================== إدارة النموذج ====================
    function toggleForm() {
      const form = document.getElementById('contract-form');
      const toggleBtn = document.getElementById('toggleFormBtn');

      if (form.classList.contains('hidden')) {
        showForm();
      } else {
        hideForm();
      }
    }

    function showForm() {
      const form = document.getElementById('contract-form');
      const toggleBtn = document.getElementById('toggleFormBtn');

      form.classList.remove('hidden');
      toggleBtn.innerHTML = '<i class="fas fa-times ml-2"></i>إخفاء النموذج';
      toggleBtn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
      toggleBtn.classList.add('bg-red-600', 'hover:bg-red-700');

      generateContractNumber();
    }

    function hideForm() {
      const form = document.getElementById('contract-form');
      const toggleBtn = document.getElementById('toggleFormBtn');

      form.classList.add('hidden');
      toggleBtn.innerHTML = '<i class="fas fa-plus ml-2"></i>عقد جديد';
      toggleBtn.classList.remove('bg-red-600', 'hover:bg-red-700');
      toggleBtn.classList.add('bg-blue-600', 'hover:bg-blue-700');

      clearForm();
    }

    function clearForm() {
      const form = document.getElementById('contract-form');
      form.reset();

      // مسح أخطاء التحقق
      form.querySelectorAll('.form-input').forEach(field => {
        clearFieldError(field);
      });

      // إعادة تعيين القيم الافتراضية
      document.querySelector('[name="contractStatus"]').value = 'نشط';
      document.querySelector('[name="contractColor"]').value = '#3b82f6';

      // مسح التواريخ
      if (startDatePicker) startDatePicker.clear();
      if (endDatePicker) endDatePicker.clear();

      // إخفاء زر إلغاء التعديل
      document.getElementById('btnCancelEdit').classList.add('hidden');

      editingContractId = null;
    }

    function cancelEdit() {
      clearForm();
      hideForm();
      showToast('تم إلغاء التعديل', 'info');
    }

    function generateContractNumber() {
      const year = new Date().getFullYear();
      const nextNumber = contracts.length + 1;
      const contractNumber = `${year}-${nextNumber.toString().padStart(3, '0')}`;
      document.querySelector('[name="contractNumber"]').value = contractNumber;
    }

    async function handleFormSubmit(e) {
      e.preventDefault();

      // التحقق من صحة البيانات باستخدام HTML5
      const form = e.target;
      if (!form.checkValidity()) {
        form.reportValidity();
        return;
      }

      // التحقق المخصص
      if (!validateForm()) {
        showToast('يرجى تصحيح الأخطاء في النموذج', 'error');
        return;
      }

      try {
        setFormLoading(true);

        const formData = new FormData(form);
        const contractData = {
          id: editingContractId || generateId(),
          contractNumber: formData.get('contractNumber'),
          contractName: formData.get('contractName'),
          supplierName: formData.get('supplierName'),
          contractType: formData.get('contractType'),
          supplierCategory: formData.get('supplierCategory'),
          contractItems: Array.from(formData.getAll('contractItems')),
          startDate: formData.get('startDate'),
          endDate: formData.get('endDate'),
          totalAmount: parseFloat(formData.get('totalAmount')) || 0,
          contractStatus: formData.get('contractStatus'),
          contractColor: formData.get('contractColor'),
          paymentTerms: formData.get('paymentTerms'),
          notes: formData.get('notes'),
          createdAt: editingContractId ?
            contracts.find(c => c.id === editingContractId)?.createdAt :
            new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        let success = false;

        if (editingContractId) {
          // تحديث عقد موجود
          success = await updateContract(contractData);
        } else {
          // إضافة عقد جديد
          success = await createContract(contractData);
        }

        if (success) {
          applyFilters();
          updateStatistics();
          updateCharts();
          hideForm();
          updateLastSaved();
        }

      } catch (error) {
        console.error('Error saving contract:', error);
        showToast('حدث خطأ أثناء حفظ العقد', 'error');
      } finally {
        setFormLoading(false);
      }
    }

    // ==================== التحقق من صحة البيانات ====================
    function validateField(field) {
      const value = field.value.trim();
      const fieldName = field.name;
      let isValid = true;
      let errorMessage = '';

      // مسح الأخطاء السابقة
      clearFieldError(field);

      switch (fieldName) {
        case 'contractNumber':
          if (!value) {
            isValid = false;
            errorMessage = 'رقم العقد مطلوب';
          } else if (!/^[0-9]{4}-[0-9]{3}$/.test(value)) {
            isValid = false;
            errorMessage = 'تنسيق رقم العقد يجب أن يكون YYYY-XXX';
          } else if (isDuplicateContractNumber(value)) {
            isValid = false;
            errorMessage = 'رقم العقد موجود مسبقاً';
          }
          break;

        case 'contractName':
        case 'supplierName':
          if (!value) {
            isValid = false;
            errorMessage = 'هذا الحقل مطلوب';
          } else if (value.length < 2) {
            isValid = false;
            errorMessage = 'يجب أن يكون حرفين على الأقل';
          } else if (value.length > 100) {
            isValid = false;
            errorMessage = 'لا يجب أن يتجاوز 100 حرف';
          }
          break;

        case 'contractType':
          if (!value) {
            isValid = false;
            errorMessage = 'نوع العقد مطلوب';
          }
          break;

        case 'contractItems':
          const selectedItems = Array.from(field.selectedOptions);
          if (selectedItems.length === 0) {
            isValid = false;
            errorMessage = 'يجب اختيار عنصر واحد على الأقل';
          }
          break;

        case 'startDate':
          if (!value) {
            isValid = false;
            errorMessage = 'تاريخ البداية مطلوب';
          } else {
            const startDate = new Date(value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            if (startDate < today && !editingContractId) {
              isValid = false;
              errorMessage = 'تاريخ البداية لا يمكن أن يكون في الماضي';
            }
          }
          break;

        case 'endDate':
          if (!value) {
            isValid = false;
            errorMessage = 'تاريخ النهاية مطلوب';
          } else {
            const startDate = document.querySelector('[name="startDate"]').value;
            if (startDate && new Date(value) <= new Date(startDate)) {
              isValid = false;
              errorMessage = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية';
            }
          }
          break;

        case 'totalAmount':
          if (value && (isNaN(value) || parseFloat(value) < 0)) {
            isValid = false;
            errorMessage = 'المبلغ يجب أن يكون رقماً موجباً';
          }
          break;
      }

      if (!isValid) {
        showFieldError(field, errorMessage);
      }

      return isValid;
    }

    function showFieldError(field, message) {
      field.classList.add('error');
      field.classList.remove('success');

      const errorDiv = field.parentNode.querySelector('.error-message');
      if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.classList.remove('hidden');
      }
    }

    function clearFieldError(field) {
      field.classList.remove('error');
      field.classList.add('success');

      const errorDiv = field.parentNode.querySelector('.error-message');
      if (errorDiv) {
        errorDiv.classList.add('hidden');
      }
    }

    function validateForm() {
      const form = document.getElementById('contract-form');
      const requiredFields = form.querySelectorAll('[required]');
      let isValid = true;

      requiredFields.forEach(field => {
        if (!validateField(field)) {
          isValid = false;
        }
      });

      return isValid;
    }

    function isDuplicateContractNumber(contractNumber) {
      return contracts.some(contract =>
        contract.contractNumber === contractNumber &&
        contract.id !== editingContractId
      );
    }

    // ==================== دوال API ====================
    async function checkServerConnection() {
      try {
        const response = await fetch(`${API_BASE_URL}/health`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });

        if (response.ok) {
          updateConnectionStatus(true);
        } else {
          throw new Error('Server not responding');
        }
      } catch (error) {
        updateConnectionStatus(false);
        showToast('العمل في الوضع المحلي - لا يوجد اتصال بالخادم', 'warning');
      }
    }

    async function createContract(contractData) {
      try {
        if (isOnline) {
          const response = await fetch(`${API_BASE_URL}/contracts`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(contractData)
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          showToast('تم حفظ العقد على الخادم بنجاح', 'success');
        }

        // حفظ محلي
        contracts.push(contractData);
        saveContracts();

        if (!isOnline) {
          showToast('تم حفظ العقد محلياً - سيتم مزامنته لاحقاً', 'warning');
        }

        return true;

      } catch (error) {
        console.error('Error creating contract:', error);

        // التراجع للحفظ المحلي
        contracts.push(contractData);
        saveContracts();
        showToast('تم حفظ العقد محلياً - سيتم مزامنته لاحقاً', 'warning');

        return true;
      }
    }

    async function updateContract(contractData) {
      try {
        if (isOnline) {
          const response = await fetch(`${API_BASE_URL}/contracts/${contractData.id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(contractData)
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          showToast('تم تحديث العقد على الخادم بنجاح', 'success');
        }

        // تحديث محلي
        const index = contracts.findIndex(c => c.id === contractData.id);
        if (index !== -1) {
          contracts[index] = contractData;
          saveContracts();
        }

        if (!isOnline) {
          showToast('تم تحديث العقد محلياً - سيتم مزامنته لاحقاً', 'warning');
        }

        return true;

      } catch (error) {
        console.error('Error updating contract:', error);

        // التراجع للتحديث المحلي
        const index = contracts.findIndex(c => c.id === contractData.id);
        if (index !== -1) {
          contracts[index] = contractData;
          saveContracts();
        }
        showToast('تم تحديث العقد محلياً - سيتم مزامنته لاحقاً', 'warning');

        return true;
      }
    }

    // ==================== دوال التصدير ====================
    function exportToExcel() {
      try {
        const dataToExport = filteredContracts.map(contract => ({
          'رقم العقد': contract.contractNumber,
          'اسم العقد': contract.contractName,
          'المورد': contract.supplierName,
          'النوع': contract.contractType,
          'القسم': contract.supplierCategory || 'غير محدد',
          'تاريخ البداية': formatDate(contract.startDate),
          'تاريخ النهاية': formatDate(contract.endDate),
          'المبلغ (د.ك)': contract.totalAmount,
          'الحالة': contract.contractStatus,
          'شروط الدفع': contract.paymentTerms || 'غير محدد',
          'الملاحظات': contract.notes || 'لا توجد'
        }));

        const ws = XLSX.utils.json_to_sheet(dataToExport);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'العقود');

        // تحسين عرض الأعمدة
        const colWidths = [
          { wch: 15 }, // رقم العقد
          { wch: 25 }, // اسم العقد
          { wch: 20 }, // المورد
          { wch: 10 }, // النوع
          { wch: 15 }, // القسم
          { wch: 15 }, // تاريخ البداية
          { wch: 15 }, // تاريخ النهاية
          { wch: 12 }, // المبلغ
          { wch: 10 }, // الحالة
          { wch: 20 }, // شروط الدفع
          { wch: 30 }  // الملاحظات
        ];
        ws['!cols'] = colWidths;

        const fileName = `عقود_الموردين_${new Date().toISOString().split('T')[0]}.xlsx`;
        XLSX.writeFile(wb, fileName);

        showToast('تم تصدير البيانات إلى Excel بنجاح', 'success');
      } catch (error) {
        console.error('Error exporting to Excel:', error);
        showToast('حدث خطأ أثناء تصدير البيانات', 'error');
      }
    }

    function exportToPDF() {
      try {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF('p', 'mm', 'a4');

        // إعداد الخط (يمكن تحسينه لدعم العربية بشكل أفضل)
        doc.setFontSize(16);
        doc.text('تقرير عقود الموردين والإيجارات', 105, 20, { align: 'center' });
        doc.text('جمعية المنقف التعاونية', 105, 30, { align: 'center' });

        doc.setFontSize(10);
        doc.text(`تاريخ التقرير: ${new Date().toLocaleDateString('en-GB')}`, 20, 45);
        doc.text(`إجمالي العقود: ${filteredContracts.length}`, 20, 52);

        let y = 65;
        doc.setFontSize(8);

        // رؤوس الجدول
        doc.text('رقم العقد', 20, y);
        doc.text('اسم العقد', 50, y);
        doc.text('المورد', 90, y);
        doc.text('النوع', 120, y);
        doc.text('المبلغ', 140, y);
        doc.text('الحالة', 160, y);

        y += 10;
        doc.line(20, y-5, 190, y-5); // خط تحت الرؤوس

        // بيانات الجدول
        filteredContracts.forEach(contract => {
          if (y > 270) { // صفحة جديدة
            doc.addPage();
            y = 20;
          }

          doc.text(contract.contractNumber || '', 20, y);
          doc.text((contract.contractName || '').substring(0, 20), 50, y);
          doc.text((contract.supplierName || '').substring(0, 15), 90, y);
          doc.text(contract.contractType || '', 120, y);
          doc.text(contract.totalAmount.toFixed(2), 140, y);
          doc.text(contract.contractStatus || '', 160, y);

          y += 7;
        });

        const fileName = `عقود_الموردين_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);

        showToast('تم تصدير التقرير إلى PDF بنجاح', 'success');
      } catch (error) {
        console.error('Error exporting to PDF:', error);
        showToast('حدث خطأ أثناء تصدير التقرير', 'error');
      }
    }

    // ==================== دوال مساعدة ====================
    function generateId() {
      return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    function formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-GB');
    }

    function updateCurrentTime() {
      const now = new Date();
      document.getElementById('currentTime').textContent = now.toLocaleString('en-GB');
    }

    function updateStatistics() {
      const total = contracts.length;
      const active = contracts.filter(c => c.contractStatus === 'نشط').length;
      const expired = contracts.filter(c => c.contractStatus === 'منتهي').length;
      const totalValue = contracts.reduce((sum, c) => sum + (c.totalAmount || 0), 0);
      const suppliers = [...new Set(contracts.map(c => c.supplierName))].length;
      const draftsCount = drafts.length;

      // تحديث العدادات في الشريط الجانبي
      document.getElementById('activeContractsCount').textContent = active;
      document.getElementById('totalSuppliersCount').textContent = suppliers;
      document.getElementById('draftsCount').textContent = draftsCount;

      // تحديث بطاقات لوحة التحكم
      document.getElementById('dashActiveContracts').textContent = active;
      document.getElementById('dashTotalSuppliers').textContent = suppliers;
      document.getElementById('dashExpiredContracts').textContent = expired;
      document.getElementById('dashTotalValue').textContent = totalValue.toFixed(2);

      // تحديث الشريط العلوي
      document.getElementById('totalContracts').textContent = total;
    }

    function updateLastSaved() {
      const now = new Date();
      document.getElementById('lastSaved').textContent = now.toLocaleTimeString('ar-SA');
    }

    function updateConnectionStatus(online) {
      isOnline = online;
      const statusElement = document.getElementById('connectionStatus');

      if (online) {
        statusElement.innerHTML = `
          <div class="w-3 h-3 bg-green-500 rounded-full ml-2 pulse"></div>
          <span class="text-sm text-green-600 font-medium">متصل</span>
        `;
      } else {
        statusElement.innerHTML = `
          <div class="w-3 h-3 bg-red-500 rounded-full ml-2"></div>
          <span class="text-sm text-red-600 font-medium">غير متصل</span>
        `;
      }
    }

    function updateCharts() {
      if (!contractsTypeChart || !monthlyContractsChart) return;

      // تحديث رسم توزيع العقود حسب النوع
      const typeCounts = {
        'توريد': contracts.filter(c => c.contractType === 'توريد').length,
        'إيجار': contracts.filter(c => c.contractType === 'إيجار').length,
        'خدمات': contracts.filter(c => c.contractType === 'خدمات').length,
        'صيانة': contracts.filter(c => c.contractType === 'صيانة').length
      };

      contractsTypeChart.data.datasets[0].data = Object.values(typeCounts);
      contractsTypeChart.update();

      // تحديث رسم العقود الشهرية (بيانات تجريبية)
      const monthlyData = [
        contracts.filter(c => c.startDate && c.startDate.includes('2025-01')).length,
        contracts.filter(c => c.startDate && c.startDate.includes('2025-02')).length,
        contracts.filter(c => c.startDate && c.startDate.includes('2025-03')).length,
        contracts.filter(c => c.startDate && c.startDate.includes('2025-04')).length,
        contracts.filter(c => c.startDate && c.startDate.includes('2025-05')).length,
        contracts.filter(c => c.startDate && c.startDate.includes('2025-06')).length
      ];

      monthlyContractsChart.data.datasets[0].data = monthlyData;
      monthlyContractsChart.update();
    }

    function setFormLoading(loading) {
      const submitBtn = document.querySelector('#contract-form button[type="submit"]');
      const saveLocalBtn = document.getElementById('btnSaveLocal');

      if (loading) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<div class="loading-spinner inline-block ml-2"></div> جاري الحفظ...';
        saveLocalBtn.disabled = true;
      } else {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-check ml-2"></i> حفظ نهائي';
        saveLocalBtn.disabled = false;
      }
    }

    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

    function saveContracts() {
      localStorage.setItem('contracts', JSON.stringify(contracts));
    }

    function saveDrafts() {
      localStorage.setItem('drafts', JSON.stringify(drafts));
    }

    function saveDraft() {
      const formData = new FormData(document.getElementById('contract-form'));
      const draftData = {
        id: generateId(),
        contractNumber: formData.get('contractNumber'),
        contractName: formData.get('contractName'),
        supplierName: formData.get('supplierName'),
        contractType: formData.get('contractType'),
        supplierCategory: formData.get('supplierCategory'),
        contractItems: Array.from(formData.getAll('contractItems')),
        startDate: formData.get('startDate'),
        endDate: formData.get('endDate'),
        totalAmount: formData.get('totalAmount'),
        contractStatus: formData.get('contractStatus'),
        contractColor: formData.get('contractColor'),
        paymentTerms: formData.get('paymentTerms'),
        notes: formData.get('notes'),
        savedAt: new Date().toISOString()
      };

      drafts.push(draftData);
      saveDrafts();
      updateStatistics();
      showToast('تم حفظ المسودة بنجاح', 'success');
    }

    function showToast(message, type = 'success') {
      const toast = document.getElementById('toast');
      const toastMessage = document.getElementById('toastMessage');
      const toastIcon = document.getElementById('toastIcon');

      toastMessage.textContent = message;

      const icons = {
        success: '<i class="fas fa-check-circle text-green-500"></i>',
        error: '<i class="fas fa-exclamation-circle text-red-500"></i>',
        warning: '<i class="fas fa-exclamation-triangle text-yellow-500"></i>',
        info: '<i class="fas fa-info-circle text-blue-500"></i>'
      };

      const colors = {
        success: 'bg-green-600 text-white',
        error: 'bg-red-600 text-white',
        warning: 'bg-yellow-600 text-white',
        info: 'bg-blue-600 text-white'
      };

      toastIcon.innerHTML = icons[type] || icons.info;
      toast.className = `toast fixed top-5 left-5 p-4 rounded-lg shadow-lg z-50 max-w-sm ${colors[type] || colors.info}`;
      toast.classList.remove('hidden');

      setTimeout(() => {
        hideToast();
      }, 4000);
    }

    function hideToast() {
      document.getElementById('toast').classList.add('hidden');
    }

    // ==================== دوال البحث والفلترة ====================
    function applyFilters() {
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();
      const typeFilter = document.getElementById('filterType').value;
      const statusFilter = document.getElementById('filterStatus').value;
      const categoryFilter = document.getElementById('filterCategory').value;
      const minAmount = parseFloat(document.getElementById('filterMinAmount').value) || 0;
      const maxAmount = parseFloat(document.getElementById('filterMaxAmount').value) || Infinity;
      const startDateFilter = document.getElementById('filterStartDate').value;
      const endDateFilter = document.getElementById('filterEndDate').value;

      filteredContracts = contracts.filter(contract => {
        const matchesSearch = !searchTerm ||
          contract.contractNumber.toLowerCase().includes(searchTerm) ||
          contract.contractName.toLowerCase().includes(searchTerm) ||
          contract.supplierName.toLowerCase().includes(searchTerm);

        const matchesType = !typeFilter || contract.contractType === typeFilter;
        const matchesStatus = !statusFilter || contract.contractStatus === statusFilter;
        const matchesCategory = !categoryFilter || contract.supplierCategory === categoryFilter;
        const matchesAmount = contract.totalAmount >= minAmount && contract.totalAmount <= maxAmount;

        const matchesStartDate = !startDateFilter ||
          (contract.startDate && contract.startDate >= startDateFilter);
        const matchesEndDate = !endDateFilter ||
          (contract.endDate && contract.endDate <= endDateFilter);

        return matchesSearch && matchesType && matchesStatus &&
               matchesCategory && matchesAmount && matchesStartDate && matchesEndDate;
      });

      currentPage = 1;
      renderTable();
      updateResultsInfo();
    }

    function clearFilters() {
      document.getElementById('searchInput').value = '';
      document.getElementById('filterType').value = '';
      document.getElementById('filterStatus').value = '';
      document.getElementById('filterCategory').value = '';
      document.getElementById('filterMinAmount').value = '';
      document.getElementById('filterMaxAmount').value = '';

      if (filterStartDatePicker) filterStartDatePicker.clear();
      if (filterEndDatePicker) filterEndDatePicker.clear();

      applyFilters();
      showToast('تم مسح جميع الفلاتر', 'success');
    }

    function renderTable() {
      const tbody = document.getElementById('contractsTableBody');
      const noDataMessage = document.getElementById('noDataMessage');

      if (filteredContracts.length === 0) {
        tbody.innerHTML = '';
        noDataMessage.classList.remove('hidden');
        return;
      }

      noDataMessage.classList.add('hidden');

      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const pageContracts = filteredContracts.slice(startIndex, endIndex);

      tbody.innerHTML = pageContracts.map(contract => `
        <tr class="table-row hover:bg-gray-50 transition-colors">
          <td class="px-6 py-4 text-sm font-medium text-gray-900">${contract.contractNumber}</td>
          <td class="px-6 py-4 text-sm text-gray-900">${contract.contractName}</td>
          <td class="px-6 py-4 text-sm text-gray-900">${contract.supplierName}</td>
          <td class="px-6 py-4 text-sm">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeClass(contract.contractType)}">
              ${getTypeIcon(contract.contractType)} ${contract.contractType}
            </span>
          </td>
          <td class="px-6 py-4 text-sm text-gray-900">${formatDate(contract.startDate)}</td>
          <td class="px-6 py-4 text-sm text-gray-900">${contract.totalAmount.toFixed(2)}</td>
          <td class="px-6 py-4 text-sm">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusClass(contract.contractStatus)}">
              ${getStatusIcon(contract.contractStatus)} ${contract.contractStatus}
            </span>
          </td>
          <td class="px-6 py-4 text-sm">
            <div class="flex space-x-2">
              <button onclick="editContract('${contract.id}')"
                      class="text-blue-600 hover:text-blue-800 transition-colors" title="تعديل">
                <i class="fas fa-edit"></i>
              </button>
              <button onclick="showDeleteModal('${contract.id}')"
                      class="text-red-600 hover:text-red-800 transition-colors" title="حذف">
                <i class="fas fa-trash"></i>
              </button>
              <button onclick="viewContract('${contract.id}')"
                      class="text-green-600 hover:text-green-800 transition-colors" title="عرض">
                <i class="fas fa-eye"></i>
              </button>
            </div>
          </td>
        </tr>
      `).join('');

      renderPagination();
    }

    function getTypeClass(type) {
      const classes = {
        'توريد': 'bg-blue-100 text-blue-800',
        'إيجار': 'bg-green-100 text-green-800',
        'خدمات': 'bg-yellow-100 text-yellow-800',
        'صيانة': 'bg-purple-100 text-purple-800'
      };
      return classes[type] || 'bg-gray-100 text-gray-800';
    }

    function getTypeIcon(type) {
      const icons = {
        'توريد': '📦',
        'إيجار': '🏠',
        'خدمات': '🔧',
        'صيانة': '⚙️'
      };
      return icons[type] || '📄';
    }

    function getStatusClass(status) {
      const classes = {
        'نشط': 'bg-green-100 text-green-800',
        'منتهي': 'bg-gray-100 text-gray-800',
        'ملغي': 'bg-red-100 text-red-800',
        'معلق': 'bg-yellow-100 text-yellow-800'
      };
      return classes[status] || 'bg-gray-100 text-gray-800';
    }

    function getStatusIcon(status) {
      const icons = {
        'نشط': '✅',
        'منتهي': '⏰',
        'ملغي': '❌',
        'معلق': '⏸️'
      };
      return icons[status] || '📄';
    }

    function renderPagination() {
      const totalPages = Math.ceil(filteredContracts.length / itemsPerPage);
      const paginationContainer = document.getElementById('paginationButtons');

      if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
      }

      let paginationHTML = '';

      // زر السابق
      if (currentPage > 1) {
        paginationHTML += `
          <button onclick="goToPage(${currentPage - 1})"
                  class="pagination-btn px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
            <i class="fas fa-chevron-right"></i>
          </button>
        `;
      }

      // أرقام الصفحات
      for (let i = 1; i <= totalPages; i++) {
        if (i === currentPage) {
          paginationHTML += `
            <button class="pagination-btn active px-3 py-2 bg-blue-600 text-white border border-blue-600 rounded-lg">
              ${i}
            </button>
          `;
        } else if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
          paginationHTML += `
            <button onclick="goToPage(${i})"
                    class="pagination-btn px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
              ${i}
            </button>
          `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
          paginationHTML += `<span class="px-2 py-2 text-gray-500">...</span>`;
        }
      }

      // زر التالي
      if (currentPage < totalPages) {
        paginationHTML += `
          <button onclick="goToPage(${currentPage + 1})"
                  class="pagination-btn px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
            <i class="fas fa-chevron-left"></i>
          </button>
        `;
      }

      paginationContainer.innerHTML = paginationHTML;

      // تحديث معلومات العرض
      const startItem = (currentPage - 1) * itemsPerPage + 1;
      const endItem = Math.min(currentPage * itemsPerPage, filteredContracts.length);

      document.getElementById('showingFrom').textContent = startItem;
      document.getElementById('showingTo').textContent = endItem;
      document.getElementById('totalItems').textContent = filteredContracts.length;
    }

    function updateResultsInfo() {
      const total = filteredContracts.length;
      document.getElementById('resultsInfo').textContent = `عرض ${total} من ${contracts.length} عقد`;
    }

    function goToPage(page) {
      currentPage = page;
      renderTable();
    }

    function changeItemsPerPage() {
      itemsPerPage = parseInt(document.getElementById('itemsPerPage').value);
      currentPage = 1;
      renderTable();
    }

    function sortTable(field) {
      if (sortField === field) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        sortField = field;
        sortDirection = 'asc';
      }

      filteredContracts.sort((a, b) => {
        let aValue = a[field];
        let bValue = b[field];

        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
      });

      renderTable();
    }

    function refreshData() {
      applyFilters();
      updateStatistics();
      updateCharts();
      showToast('تم تحديث البيانات بنجاح', 'success');
    }

    // ==================== دوال إدارة العقود ====================
    function editContract(id) {
      const contract = contracts.find(c => c.id === id);
      if (!contract) return;

      editingContractId = id;

      // ملء النموذج
      document.querySelector('[name="contractNumber"]').value = contract.contractNumber;
      document.querySelector('[name="contractName"]').value = contract.contractName;
      document.querySelector('[name="supplierName"]').value = contract.supplierName;
      document.querySelector('[name="contractType"]').value = contract.contractType;
      document.querySelector('[name="supplierCategory"]').value = contract.supplierCategory || '';
      document.querySelector('[name="totalAmount"]').value = contract.totalAmount || '';
      document.querySelector('[name="contractStatus"]').value = contract.contractStatus;
      document.querySelector('[name="contractColor"]').value = contract.contractColor || '#3b82f6';
      document.querySelector('[name="paymentTerms"]').value = contract.paymentTerms || '';
      document.querySelector('[name="notes"]').value = contract.notes || '';

      // تعيين التواريخ
      if (startDatePicker && contract.startDate) {
        startDatePicker.setDate(contract.startDate);
      }
      if (endDatePicker && contract.endDate) {
        endDatePicker.setDate(contract.endDate);
      }

      // تحديد العناصر
      if (contract.contractItems) {
        const itemsSelect = document.querySelector('[name="contractItems"]');
        Array.from(itemsSelect.options).forEach(option => {
          option.selected = contract.contractItems.includes(option.value);
        });
      }

      // إظهار زر إلغاء التعديل
      document.getElementById('btnCancelEdit').classList.remove('hidden');

      showForm();
      switchSection('contracts');
      showToast('تم تحميل بيانات العقد للتعديل', 'success');
    }

    function showDeleteModal(id) {
      deleteContractId = id;
      document.getElementById('deleteModal').classList.remove('hidden');
    }

    function hideDeleteModal() {
      deleteContractId = null;
      document.getElementById('deleteModal').classList.add('hidden');
    }

    function confirmDeleteContract() {
      if (!deleteContractId) return;

      const index = contracts.findIndex(c => c.id === deleteContractId);
      if (index !== -1) {
        contracts.splice(index, 1);
        saveContracts();
        applyFilters();
        updateStatistics();
        updateCharts();
        showToast('تم حذف العقد بنجاح', 'success');
        updateLastSaved();
      }

      hideDeleteModal();
    }

    function viewContract(id) {
      const contract = contracts.find(c => c.id === id);
      if (!contract) return;

      const contractItems = contract.contractItems && contract.contractItems.length > 0
        ? contract.contractItems.join('، ')
        : 'غير محدد';

      const details = `
📋 تفاصيل العقد
═══════════════════

🔢 رقم العقد: ${contract.contractNumber}
📝 اسم العقد: ${contract.contractName}
🏢 المورد: ${contract.supplierName}
📦 النوع: ${contract.contractType}
📂 القسم: ${contract.supplierCategory || 'غير محدد'}

📋 عناصر العقد:
${contractItems}

📅 تاريخ البداية: ${formatDate(contract.startDate)}
📅 تاريخ النهاية: ${formatDate(contract.endDate)}
💰 المبلغ الإجمالي: ${contract.totalAmount.toFixed(2)} د.ك

📊 الحالة: ${contract.contractStatus}
💳 شروط الدفع: ${contract.paymentTerms || 'غير محدد'}

📝 الملاحظات:
${contract.notes || 'لا توجد ملاحظات'}

⏰ تاريخ الإنشاء: ${formatDate(contract.createdAt)}
${contract.updatedAt ? `🔄 آخر تحديث: ${formatDate(contract.updatedAt)}` : ''}
      `;

      alert(details);
    }
  </script>

</body>
</html>
