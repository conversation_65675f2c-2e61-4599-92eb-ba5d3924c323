<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>إدارة العقود - نظام إدارة العقود | محمد مرزوق العقاب</title>

  <!-- CSS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

  <style>
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap');

    :root {
      --bg-primary: #0a0f1c;
      --bg-secondary: #1a2332;
      --bg-tertiary: #2a3441;
      --text-primary: #ffffff;
      --text-secondary: #f1f5f9;
      --accent-blue: #3b82f6;
      --accent-green: #10b981;
      --accent-red: #ef4444;
      --accent-orange: #f59e0b;
      --border-primary: #475569;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Cairo', sans-serif;
      background: var(--bg-primary);
      color: var(--text-primary);
      min-height: 100vh;
    }

    .glass-card {
      background: rgba(26, 35, 50, 0.8);
      backdrop-filter: blur(20px);
      border: 1px solid var(--border-primary);
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--accent-blue), #2563eb);
      color: var(--text-primary);
      padding: 12px 24px;
      border-radius: 12px;
      border: none;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    }

    .btn-success {
      background: linear-gradient(135deg, var(--accent-green), #059669);
      color: var(--text-primary);
      padding: 12px 24px;
      border-radius: 12px;
      border: none;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .btn-warning {
      background: linear-gradient(135deg, var(--accent-orange), #d97706);
      color: var(--text-primary);
      padding: 12px 24px;
      border-radius: 12px;
      border: none;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .btn-danger {
      background: linear-gradient(135deg, var(--accent-red), #dc2626);
      color: var(--text-primary);
      padding: 12px 24px;
      border-radius: 12px;
      border: none;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .form-input {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid var(--border-primary);
      border-radius: 12px;
      background: var(--bg-tertiary);
      color: var(--text-primary);
      font-family: 'Cairo', sans-serif;
      transition: all 0.3s ease;
    }

    .form-input:focus {
      outline: none;
      border-color: var(--accent-blue);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .table-container {
      overflow-x: auto;
      border-radius: 16px;
      background: var(--bg-secondary);
    }

    .data-table {
      width: 100%;
      border-collapse: collapse;
    }

    .data-table th {
      background: var(--bg-tertiary);
      padding: 16px;
      text-align: right;
      font-weight: 700;
      color: var(--text-primary);
      border-bottom: 2px solid var(--border-primary);
    }

    .data-table td {
      padding: 12px 16px;
      border-bottom: 1px solid var(--border-primary);
      color: var(--text-secondary);
    }

    .data-table tbody tr:hover {
      background: rgba(59, 130, 246, 0.1);
    }

    .badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
    }

    .badge-success {
      background: rgba(16, 185, 129, 0.2);
      color: var(--accent-green);
      border: 1px solid var(--accent-green);
    }

    .badge-warning {
      background: rgba(245, 158, 11, 0.2);
      color: var(--accent-orange);
      border: 1px solid var(--accent-orange);
    }

    .badge-danger {
      background: rgba(239, 68, 68, 0.2);
      color: var(--accent-red);
      border: 1px solid var(--accent-red);
    }

    .badge-info {
      background: rgba(59, 130, 246, 0.2);
      color: var(--accent-blue);
      border: 1px solid var(--accent-blue);
    }

    .loading {
      display: none;
      text-align: center;
      padding: 20px;
    }

    .spinner {
      border: 4px solid var(--border-primary);
      border-top: 4px solid var(--accent-blue);
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <!-- Header -->
  <header class="glass-card m-6 p-6">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
          <i class="fas fa-file-contract text-white text-xl"></i>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-white">إدارة العقود</h1>
          <p class="text-slate-300">جمعية المنقف التعاونية</p>
        </div>
      </div>
      <div class="text-right">
        <p class="text-white font-semibold">تطوير وتنفيذ</p>
        <p class="text-blue-400 font-bold">محمد مرزوق العقاب</p>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <div class="mx-6 mb-6">
    <!-- Controls -->
    <div class="glass-card p-6 mb-6">
      <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div class="flex gap-4">
          <button class="btn-primary" onclick="showAddModal()">
            <i class="fas fa-plus ml-2"></i>
            إضافة عقد جديد
          </button>
          <button class="btn-success" onclick="refreshData()">
            <i class="fas fa-sync ml-2"></i>
            تحديث البيانات
          </button>
          <button class="btn-warning" onclick="checkExpiringContracts()">
            <i class="fas fa-exclamation-triangle ml-2"></i>
            العقود المنتهية قريباً
          </button>
        </div>

        <div class="flex gap-4">
          <input type="text" id="searchInput" placeholder="البحث في العقود..."
                 class="form-input w-64" onkeyup="searchContracts()">
          <select id="typeFilter" class="form-input w-48" onchange="filterByType()">
            <option value="">جميع الأنواع</option>
            <option value="توريد">توريد</option>
            <option value="إيجار">إيجار</option>
            <option value="خدمات">خدمات</option>
          </select>
          <select id="statusFilter" class="form-input w-48" onchange="filterByStatus()">
            <option value="">جميع الحالات</option>
            <option value="active">نشط</option>
            <option value="expired">منتهي</option>
            <option value="pending">قيد المراجعة</option>
          </select>
        </div>
      </div>

      <!-- Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-blue-500/20 p-4 rounded-xl border border-blue-500/30">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-blue-400 text-sm font-medium">إجمالي العقود</p>
              <p class="text-white text-2xl font-bold" id="totalContracts">0</p>
            </div>
            <i class="fas fa-file-contract text-blue-400 text-2xl"></i>
          </div>
        </div>

        <div class="bg-green-500/20 p-4 rounded-xl border border-green-500/30">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-green-400 text-sm font-medium">العقود النشطة</p>
              <p class="text-white text-2xl font-bold" id="activeContracts">0</p>
            </div>
            <i class="fas fa-check-circle text-green-400 text-2xl"></i>
          </div>
        </div>

        <div class="bg-orange-500/20 p-4 rounded-xl border border-orange-500/30">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-orange-400 text-sm font-medium">عقود التوريد</p>
              <p class="text-white text-2xl font-bold" id="supplyContracts">0</p>
            </div>
            <i class="fas fa-truck text-orange-400 text-2xl"></i>
          </div>
        </div>

        <div class="bg-red-500/20 p-4 rounded-xl border border-red-500/30">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-red-400 text-sm font-medium">العقود المنتهية قريباً</p>
              <p class="text-white text-2xl font-bold" id="expiringContracts">0</p>
            </div>
            <i class="fas fa-exclamation-triangle text-red-400 text-2xl"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Contracts Table -->
    <div class="glass-card p-6">
      <h2 class="text-xl font-bold text-white mb-4">قائمة العقود</h2>

      <div class="loading" id="loading">
        <div class="spinner"></div>
        <p class="text-slate-300 mt-4">جاري تحميل البيانات...</p>
      </div>

      <div class="table-container" id="tableContainer">
        <table class="data-table">
          <thead>
            <tr>
              <th>رقم العقد</th>
              <th>عنوان العقد</th>
              <th>المورد</th>
              <th>نوع العقد</th>
              <th>تاريخ البداية</th>
              <th>تاريخ النهاية</th>
              <th>المبلغ (د.ك)</th>
              <th>الحالة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody id="contractsTableBody">
            <!-- سيتم ملء البيانات هنا -->
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Add/Edit Modal -->
  <div id="contractModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
      <div class="glass-card p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-2xl font-bold text-white" id="modalTitle">إضافة عقد جديد</h3>
          <button onclick="closeModal()" class="text-slate-400 hover:text-white">
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>

        <form id="contractForm" onsubmit="saveContract(event)">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-white font-medium mb-2">رقم العقد *</label>
              <input type="text" id="contractNumber" name="contract_number" required class="form-input" placeholder="CON-2024-001">
            </div>

            <div>
              <label class="block text-white font-medium mb-2">المورد *</label>
              <select id="contractSupplier" name="supplier_id" required class="form-input">
                <option value="">اختر المورد</option>
                <!-- سيتم ملء الموردين هنا -->
              </select>
            </div>

            <div>
              <label class="block text-white font-medium mb-2">نوع العقد *</label>
              <select id="contractType" name="contract_type" required class="form-input">
                <option value="">اختر نوع العقد</option>
                <option value="توريد">توريد</option>
                <option value="إيجار">إيجار</option>
                <option value="خدمات">خدمات</option>
              </select>
            </div>

            <div>
              <label class="block text-white font-medium mb-2">عنوان العقد *</label>
              <input type="text" id="contractTitle" name="title" required class="form-input" placeholder="عقد توريد بهارات">
            </div>

            <div>
              <label class="block text-white font-medium mb-2">تاريخ البداية *</label>
              <input type="date" id="contractStartDate" name="start_date" required class="form-input">
            </div>

            <div>
              <label class="block text-white font-medium mb-2">تاريخ النهاية *</label>
              <input type="date" id="contractEndDate" name="end_date" required class="form-input">
            </div>

            <div>
              <label class="block text-white font-medium mb-2">المبلغ (د.ك)</label>
              <input type="number" id="contractAmount" name="amount" step="0.01" class="form-input" placeholder="15000.00">
            </div>

            <div>
              <label class="block text-white font-medium mb-2">العملة</label>
              <select id="contractCurrency" name="currency" class="form-input">
                <option value="KWD">دينار كويتي (KWD)</option>
                <option value="USD">دولار أمريكي (USD)</option>
                <option value="EUR">يورو (EUR)</option>
              </select>
            </div>

            <div class="md:col-span-2">
              <label class="block text-white font-medium mb-2">وصف العقد</label>
              <textarea id="contractDescription" name="description" rows="3" class="form-input" placeholder="وصف تفصيلي للعقد..."></textarea>
            </div>

            <div class="md:col-span-2">
              <label class="block text-white font-medium mb-2">الشروط والأحكام</label>
              <textarea id="contractTerms" name="terms" rows="4" class="form-input" placeholder="الشروط والأحكام الخاصة بالعقد..."></textarea>
            </div>

            <div class="md:col-span-2">
              <label class="block text-white font-medium mb-2">ملاحظات</label>
              <textarea id="contractNotes" name="notes" rows="3" class="form-input" placeholder="ملاحظات إضافية..."></textarea>
            </div>
          </div>

          <div class="flex gap-4 mt-8">
            <button type="submit" class="btn-primary flex-1">
              <i class="fas fa-save ml-2"></i>
              حفظ العقد
            </button>
            <button type="button" onclick="closeModal()" class="btn-danger">
              <i class="fas fa-times ml-2"></i>
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
  <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>

  <script>
    // متغيرات عامة
    let contracts = [];
    let suppliers = [];
    let editingId = null;

    // تحميل البيانات عند بدء الصفحة
    document.addEventListener('DOMContentLoaded', function() {
      loadContracts();
      loadSuppliers();
      initializeDatePickers();
    });

    // تهيئة منتقي التواريخ
    function initializeDatePickers() {
      flatpickr("#contractStartDate", {
        locale: "ar",
        dateFormat: "Y-m-d",
        allowInput: true
      });

      flatpickr("#contractEndDate", {
        locale: "ar",
        dateFormat: "Y-m-d",
        allowInput: true
      });
    }

    // تحميل العقود
    async function loadContracts() {
      try {
        showLoading(true);
        const response = await fetch('/api/contracts');
        const result = await response.json();

        if (result.success) {
          contracts = result.data;
          displayContracts(contracts);
          updateStatistics();
        } else {
          throw new Error(result.error || 'فشل في تحميل البيانات');
        }
      } catch (error) {
        console.error('Error loading contracts:', error);
        Swal.fire({
          icon: 'error',
          title: 'خطأ',
          text: 'فشل في تحميل بيانات العقود',
          confirmButtonText: 'موافق'
        });
      } finally {
        showLoading(false);
      }
    }

    // تحميل الموردين
    async function loadSuppliers() {
      try {
        const response = await fetch('/api/suppliers');
        const result = await response.json();

        if (result.success) {
          suppliers = result.data;
          populateSupplierSelect();
        }
      } catch (error) {
        console.error('Error loading suppliers:', error);
      }
    }

    // ملء قائمة الموردين
    function populateSupplierSelect() {
      const select = document.getElementById('contractSupplier');
      select.innerHTML = '<option value="">اختر المورد</option>';

      suppliers.forEach(supplier => {
        const option = document.createElement('option');
        option.value = supplier.id;
        option.textContent = supplier.name;
        select.appendChild(option);
      });
    }

    // عرض العقود في الجدول
    function displayContracts(data) {
      const tbody = document.getElementById('contractsTableBody');
      tbody.innerHTML = '';

      data.forEach((contract) => {
        const row = document.createElement('tr');
        const statusClass = getStatusClass(contract.status);
        const statusText = getStatusText(contract.status);

        row.innerHTML = `
          <td class="font-semibold text-white">${contract.contract_number}</td>
          <td class="font-medium">${contract.title}</td>
          <td>${contract.supplier_name || 'غير محدد'}</td>
          <td>
            <span class="badge badge-info">${contract.contract_type}</span>
          </td>
          <td>${formatDate(contract.start_date)}</td>
          <td>${formatDate(contract.end_date)}</td>
          <td class="font-semibold">${contract.amount ? parseFloat(contract.amount).toLocaleString() : '-'}</td>
          <td>
            <span class="badge ${statusClass}">${statusText}</span>
          </td>
          <td>
            <div class="flex gap-2">
              <button onclick="viewContract(${contract.id})" class="text-blue-400 hover:text-blue-300" title="عرض">
                <i class="fas fa-eye"></i>
              </button>
              <button onclick="editContract(${contract.id})" class="text-green-400 hover:text-green-300" title="تعديل">
                <i class="fas fa-edit"></i>
              </button>
              <button onclick="deleteContract(${contract.id})" class="text-red-400 hover:text-red-300" title="حذف">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </td>
        `;
        tbody.appendChild(row);
      });
    }

    // تحديث الإحصائيات
    function updateStatistics() {
      const total = contracts.length;
      const active = contracts.filter(c => c.status === 'active').length;
      const supply = contracts.filter(c => c.contract_type === 'توريد').length;
      const expiring = contracts.filter(c => isExpiringSoon(c.end_date)).length;

      document.getElementById('totalContracts').textContent = total;
      document.getElementById('activeContracts').textContent = active;
      document.getElementById('supplyContracts').textContent = supply;
      document.getElementById('expiringContracts').textContent = expiring;
    }

    // فحص العقود المنتهية قريباً
    function isExpiringSoon(endDate) {
      const today = new Date();
      const contractEnd = new Date(endDate);
      const diffTime = contractEnd - today;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays <= 30 && diffDays >= 0;
    }

    // البحث في العقود
    function searchContracts() {
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();
      const filtered = contracts.filter(contract =>
        contract.contract_number.toLowerCase().includes(searchTerm) ||
        contract.title.toLowerCase().includes(searchTerm) ||
        (contract.supplier_name && contract.supplier_name.toLowerCase().includes(searchTerm))
      );
      displayContracts(filtered);
    }

    // تصفية حسب النوع
    function filterByType() {
      const type = document.getElementById('typeFilter').value;
      const filtered = type ? contracts.filter(c => c.contract_type === type) : contracts;
      displayContracts(filtered);
    }

    // تصفية حسب الحالة
    function filterByStatus() {
      const status = document.getElementById('statusFilter').value;
      const filtered = status ? contracts.filter(c => c.status === status) : contracts;
      displayContracts(filtered);
    }

    // إظهار نافذة الإضافة
    function showAddModal() {
      editingId = null;
      document.getElementById('modalTitle').textContent = 'إضافة عقد جديد';
      document.getElementById('contractForm').reset();
      document.getElementById('contractModal').classList.remove('hidden');
    }

    // تعديل عقد
    function editContract(id) {
      const contract = contracts.find(c => c.id === id);
      if (!contract) return;

      editingId = id;
      document.getElementById('modalTitle').textContent = 'تعديل بيانات العقد';

      // ملء النموذج بالبيانات
      document.getElementById('contractNumber').value = contract.contract_number || '';
      document.getElementById('contractSupplier').value = contract.supplier_id || '';
      document.getElementById('contractType').value = contract.contract_type || '';
      document.getElementById('contractTitle').value = contract.title || '';
      document.getElementById('contractStartDate').value = contract.start_date || '';
      document.getElementById('contractEndDate').value = contract.end_date || '';
      document.getElementById('contractAmount').value = contract.amount || '';
      document.getElementById('contractCurrency').value = contract.currency || 'KWD';
      document.getElementById('contractDescription').value = contract.description || '';
      document.getElementById('contractTerms').value = contract.terms || '';
      document.getElementById('contractNotes').value = contract.notes || '';

      document.getElementById('contractModal').classList.remove('hidden');
    }

    // حفظ العقد
    async function saveContract(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const contractData = Object.fromEntries(formData.entries());

      try {
        const url = editingId ? `/api/contracts/${editingId}` : '/api/contracts';
        const method = editingId ? 'PUT' : 'POST';

        const response = await fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(contractData)
        });

        const result = await response.json();

        if (result.success) {
          Swal.fire({
            icon: 'success',
            title: 'نجح',
            text: result.message,
            confirmButtonText: 'موافق'
          });

          closeModal();
          loadContracts();
        } else {
          throw new Error(result.error || 'فشل في حفظ البيانات');
        }
      } catch (error) {
        console.error('Error saving contract:', error);
        Swal.fire({
          icon: 'error',
          title: 'خطأ',
          text: 'فشل في حفظ بيانات العقد',
          confirmButtonText: 'موافق'
        });
      }
    }

    // حذف عقد
    async function deleteContract(id) {
      const result = await Swal.fire({
        icon: 'warning',
        title: 'تأكيد الحذف',
        text: 'هل أنت متأكد من حذف هذا العقد؟',
        showCancelButton: true,
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#ef4444'
      });

      if (result.isConfirmed) {
        try {
          const response = await fetch(`/api/contracts/${id}`, {
            method: 'DELETE'
          });

          const result = await response.json();

          if (result.success) {
            Swal.fire({
              icon: 'success',
              title: 'تم الحذف',
              text: result.message,
              confirmButtonText: 'موافق'
            });

            loadContracts();
          } else {
            throw new Error(result.error || 'فشل في حذف العقد');
          }
        } catch (error) {
          console.error('Error deleting contract:', error);
          Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'فشل في حذف العقد',
            confirmButtonText: 'موافق'
          });
        }
      }
    }

    // عرض تفاصيل العقد
    function viewContract(id) {
      const contract = contracts.find(c => c.id === id);
      if (!contract) return;

      Swal.fire({
        title: 'تفاصيل العقد',
        html: `
          <div class="text-right">
            <p><strong>رقم العقد:</strong> ${contract.contract_number}</p>
            <p><strong>العنوان:</strong> ${contract.title}</p>
            <p><strong>المورد:</strong> ${contract.supplier_name || 'غير محدد'}</p>
            <p><strong>النوع:</strong> ${contract.contract_type}</p>
            <p><strong>تاريخ البداية:</strong> ${formatDate(contract.start_date)}</p>
            <p><strong>تاريخ النهاية:</strong> ${formatDate(contract.end_date)}</p>
            <p><strong>المبلغ:</strong> ${contract.amount ? parseFloat(contract.amount).toLocaleString() + ' ' + contract.currency : 'غير محدد'}</p>
            <p><strong>الوصف:</strong> ${contract.description || 'لا يوجد'}</p>
          </div>
        `,
        confirmButtonText: 'إغلاق'
      });
    }

    // فحص العقود المنتهية قريباً
    function checkExpiringContracts() {
      const expiring = contracts.filter(c => isExpiringSoon(c.end_date));

      if (expiring.length === 0) {
        Swal.fire({
          icon: 'info',
          title: 'لا توجد عقود منتهية قريباً',
          text: 'جميع العقود صالحة لأكثر من 30 يوماً',
          confirmButtonText: 'موافق'
        });
        return;
      }

      let html = '<div class="text-right"><h4>العقود المنتهية خلال 30 يوماً:</h4><ul>';
      expiring.forEach(contract => {
        const daysLeft = Math.ceil((new Date(contract.end_date) - new Date()) / (1000 * 60 * 60 * 24));
        html += `<li>${contract.contract_number} - ${contract.title} (${daysLeft} يوم متبقي)</li>`;
      });
      html += '</ul></div>';

      Swal.fire({
        icon: 'warning',
        title: 'تنبيه: عقود منتهية قريباً',
        html: html,
        confirmButtonText: 'موافق'
      });
    }

    // دوال مساعدة
    function getStatusClass(status) {
      switch(status) {
        case 'active': return 'badge-success';
        case 'expired': return 'badge-danger';
        case 'pending': return 'badge-warning';
        default: return 'badge-info';
      }
    }

    function getStatusText(status) {
      switch(status) {
        case 'active': return 'نشط';
        case 'expired': return 'منتهي';
        case 'pending': return 'قيد المراجعة';
        default: return 'غير محدد';
      }
    }

    function formatDate(dateString) {
      if (!dateString) return '-';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-GB');
    }

    // إغلاق النافذة المنبثقة
    function closeModal() {
      document.getElementById('contractModal').classList.add('hidden');
      editingId = null;
    }

    // إظهار/إخفاء التحميل
    function showLoading(show) {
      document.getElementById('loading').style.display = show ? 'block' : 'none';
      document.getElementById('tableContainer').style.display = show ? 'none' : 'block';
    }

    // تحديث البيانات
    function refreshData() {
      loadContracts();
      loadSuppliers();
    }
  </script>
</body>
</html>