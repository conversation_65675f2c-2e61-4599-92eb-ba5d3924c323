#!/usr/bin/env node

/**
 * سكريبت اختبار النظام
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// إعدادات الاختبار
const TEST_CONFIG = {
  BASE_URL: `http://localhost:${process.env.PORT || 3000}`,
  API_BASE_URL: `http://localhost:${process.env.PORT || 3000}/api/v1`,
  TIMEOUT: 10000
};

// نتائج الاختبارات
const testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

/**
 * عرض رسالة ملونة
 */
function colorLog(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * تنفيذ اختبار واحد
 */
async function runTest(testName, testFunction) {
  testResults.total++;
  
  try {
    colorLog(`🧪 تشغيل اختبار: ${testName}`, 'blue');
    
    const startTime = Date.now();
    await testFunction();
    const duration = Date.now() - startTime;
    
    testResults.passed++;
    testResults.details.push({
      name: testName,
      status: 'PASSED',
      duration: `${duration}ms`
    });
    
    colorLog(`✅ نجح الاختبار: ${testName} (${duration}ms)`, 'green');
    
  } catch (error) {
    testResults.failed++;
    testResults.details.push({
      name: testName,
      status: 'FAILED',
      error: error.message
    });
    
    colorLog(`❌ فشل الاختبار: ${testName}`, 'red');
    colorLog(`   الخطأ: ${error.message}`, 'red');
  }
}

/**
 * اختبار اتصال قاعدة البيانات
 */
async function testDatabaseConnection() {
  const database = require('../backend/utils/database');
  
  // اختبار الاتصال
  await database.testConnection();
  
  // اختبار استعلام بسيط
  const result = await database.query('SELECT 1 as test');
  if (result[0].test !== 1) {
    throw new Error('فشل في تنفيذ استعلام الاختبار');
  }
}

/**
 * اختبار خادم API
 */
async function testAPIServer() {
  // بدء تشغيل الخادم في الخلفية
  const { spawn } = require('child_process');
  const serverProcess = spawn('node', ['backend/api/server.js'], {
    stdio: 'pipe',
    env: { ...process.env, NODE_ENV: 'test' }
  });
  
  // انتظار بدء تشغيل الخادم
  await new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error('انتهت مهلة انتظار بدء تشغيل الخادم'));
    }, 10000);
    
    serverProcess.stdout.on('data', (data) => {
      if (data.toString().includes('الخادم يعمل على المنفذ')) {
        clearTimeout(timeout);
        resolve();
      }
    });
    
    serverProcess.stderr.on('data', (data) => {
      clearTimeout(timeout);
      reject(new Error(`خطأ في بدء تشغيل الخادم: ${data.toString()}`));
    });
  });
  
  // إيقاف الخادم
  serverProcess.kill();
}

/**
 * اختبار نقاط النهاية الأساسية
 */
async function testBasicEndpoints() {
  const endpoints = [
    { path: '/', method: 'GET', expectedStatus: 200 },
    { path: '/api/health', method: 'GET', expectedStatus: 200 }
  ];
  
  for (const endpoint of endpoints) {
    try {
      const response = await axios({
        method: endpoint.method,
        url: `${TEST_CONFIG.BASE_URL}${endpoint.path}`,
        timeout: TEST_CONFIG.TIMEOUT,
        validateStatus: () => true // لا نرمي خطأ للحالات غير 2xx
      });
      
      if (response.status !== endpoint.expectedStatus) {
        throw new Error(`حالة غير متوقعة: ${response.status} (متوقع: ${endpoint.expectedStatus})`);
      }
      
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        throw new Error('لا يمكن الاتصال بالخادم - تأكد من تشغيله');
      }
      throw error;
    }
  }
}

/**
 * اختبار مسارات API
 */
async function testAPIRoutes() {
  // ملاحظة: هذا الاختبار يتطلب تشغيل الخادم
  const routes = [
    { path: '/suppliers', method: 'GET' },
    { path: '/contracts', method: 'GET' },
    { path: '/reports/dashboard', method: 'GET' }
  ];
  
  for (const route of routes) {
    try {
      const response = await axios({
        method: route.method,
        url: `${TEST_CONFIG.API_BASE_URL}${route.path}`,
        timeout: TEST_CONFIG.TIMEOUT,
        validateStatus: () => true,
        headers: {
          'Authorization': 'Bearer test-token' // رمز اختبار
        }
      });
      
      // نتوقع 401 (غير مصرح) أو 200 (نجح)
      if (![200, 401].includes(response.status)) {
        throw new Error(`حالة غير متوقعة: ${response.status}`);
      }
      
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        colorLog('⚠️  تخطي اختبار API - الخادم غير متاح', 'yellow');
        return;
      }
      throw error;
    }
  }
}

/**
 * اختبار الملفات الأساسية
 */
async function testEssentialFiles() {
  const requiredFiles = [
    'package.json',
    '.env',
    'backend/api/server.js',
    'backend/utils/database.js',
    'backend/database/schema.sql',
    'frontend/index.html',
    'frontend/assets/css/main.css',
    'frontend/assets/js/main.js'
  ];
  
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      throw new Error(`الملف المطلوب غير موجود: ${file}`);
    }
  }
}

/**
 * اختبار المجلدات المطلوبة
 */
async function testRequiredDirectories() {
  const requiredDirs = [
    'backend',
    'frontend',
    'backend/uploads',
    'backend/uploads/contracts',
    'backend/uploads/documents',
    'backend/uploads/images'
  ];
  
  for (const dir of requiredDirs) {
    if (!fs.existsSync(dir)) {
      throw new Error(`المجلد المطلوب غير موجود: ${dir}`);
    }
  }
}

/**
 * اختبار متغيرات البيئة
 */
async function testEnvironmentVariables() {
  const requiredEnvVars = [
    'DB_HOST',
    'DB_PORT',
    'DB_NAME',
    'DB_USER',
    'DB_PASSWORD',
    'JWT_SECRET'
  ];
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`متغير البيئة المطلوب غير محدد: ${envVar}`);
    }
  }
}

/**
 * اختبار تبعيات Node.js
 */
async function testNodeDependencies() {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  const criticalDeps = [
    'express',
    'mysql2',
    'cors',
    'helmet',
    'dotenv',
    'bcryptjs',
    'jsonwebtoken'
  ];
  
  for (const dep of criticalDeps) {
    if (!dependencies[dep]) {
      throw new Error(`التبعية المطلوبة غير موجودة: ${dep}`);
    }
    
    try {
      require(dep);
    } catch (error) {
      throw new Error(`فشل في تحميل التبعية: ${dep}`);
    }
  }
}

/**
 * اختبار إعدادات قاعدة البيانات
 */
async function testDatabaseSchema() {
  const database = require('../backend/utils/database');
  
  // اختبار وجود الجداول الأساسية
  const requiredTables = [
    'users',
    'suppliers',
    'supplier_categories',
    'contracts',
    'contract_types',
    'contract_items'
  ];
  
  for (const table of requiredTables) {
    const result = await database.query(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = ?
    `, [process.env.DB_NAME, table]);
    
    if (result[0].count === 0) {
      throw new Error(`الجدول المطلوب غير موجود: ${table}`);
    }
  }
}

/**
 * إنشاء تقرير الاختبار
 */
function generateTestReport() {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total: testResults.total,
      passed: testResults.passed,
      failed: testResults.failed,
      success_rate: `${((testResults.passed / testResults.total) * 100).toFixed(2)}%`
    },
    details: testResults.details
  };
  
  // حفظ التقرير
  const reportPath = path.join(__dirname, '../test-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  return report;
}

/**
 * الدالة الرئيسية
 */
async function main() {
  colorLog('🚀 بدء اختبار النظام الشامل', 'cyan');
  colorLog('نظام إدارة عقود الموردين والإيجارات - جمعية المنقف', 'cyan');
  colorLog('================================================', 'cyan');
  
  // تشغيل الاختبارات
  await runTest('اختبار الملفات الأساسية', testEssentialFiles);
  await runTest('اختبار المجلدات المطلوبة', testRequiredDirectories);
  await runTest('اختبار متغيرات البيئة', testEnvironmentVariables);
  await runTest('اختبار تبعيات Node.js', testNodeDependencies);
  await runTest('اختبار اتصال قاعدة البيانات', testDatabaseConnection);
  await runTest('اختبار هيكل قاعدة البيانات', testDatabaseSchema);
  
  // اختبارات الخادم (اختيارية)
  try {
    await runTest('اختبار نقاط النهاية الأساسية', testBasicEndpoints);
    await runTest('اختبار مسارات API', testAPIRoutes);
  } catch (error) {
    colorLog('⚠️  تخطي اختبارات الخادم - تأكد من تشغيل الخادم للاختبار الكامل', 'yellow');
  }
  
  // إنشاء التقرير
  const report = generateTestReport();
  
  // عرض النتائج
  colorLog('\n📊 نتائج الاختبار:', 'cyan');
  colorLog(`   إجمالي الاختبارات: ${report.summary.total}`, 'white');
  colorLog(`   نجح: ${report.summary.passed}`, 'green');
  colorLog(`   فشل: ${report.summary.failed}`, 'red');
  colorLog(`   معدل النجاح: ${report.summary.success_rate}`, 'cyan');
  
  if (testResults.failed > 0) {
    colorLog('\n❌ فشلت بعض الاختبارات:', 'red');
    testResults.details
      .filter(test => test.status === 'FAILED')
      .forEach(test => {
        colorLog(`   - ${test.name}: ${test.error}`, 'red');
      });
  }
  
  colorLog(`\n📄 تم حفظ تقرير مفصل في: test-report.json`, 'blue');
  
  if (testResults.failed === 0) {
    colorLog('\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام', 'green');
    process.exit(0);
  } else {
    colorLog('\n⚠️  بعض الاختبارات فشلت، يرجى مراجعة الأخطاء وإصلاحها', 'yellow');
    process.exit(1);
  }
}

// تشغيل الاختبارات
if (require.main === module) {
  main().catch(error => {
    colorLog(`❌ خطأ في تشغيل الاختبارات: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { main };
