@echo off
chcp 65001 >nul
title النسخة النهائية المحدثة مع ملفات Frontend - جمعية المنقف التعاونية

echo.
echo 🎉 ===============================================
echo 👑 النسخة النهائية المحدثة مع ملفات Frontend
echo 🏢 جمعية المنقف التعاونية
echo.
echo 👨‍💻 تطوير وتنفيذ: محمد مرزوق العقاب
echo 📅 التقويم: ميلادي (محدث)
echo 📞 الهاتف: +965-23710272
echo 📧 البريد: <EMAIL>
echo 📍 العنوان: المنقف - قطعة 4 شارع فهد الهملان
echo 📱 إنستقرام: @Mnfcoop
echo.
echo ✅ الملفات المدمجة:
echo    📁 assets/css/main.css
echo    📁 assets/js/utils.js
echo    📁 assets/js/api.js
echo    📁 assets/js/main.js
echo.
echo 🎉 ===============================================
echo.

:menu
echo 🚀 اختر طريقة التشغيل:
echo.
echo 1. 🌟 فتح النسخة النهائية (مباشرة)
echo 2. 📁 فتح إدارة ملفات العقود النهائية
echo 3. 🔄 تشغيل مع خادم محلي
echo 4. 📊 عرض معلومات الملفات المدمجة
echo 5. 🧪 اختبار الملفات
echo.
echo 0. خروج
echo.
set /p choice="اختر رقم (0-5): "

if "%choice%"=="1" goto open_final
if "%choice%"=="2" goto open_contracts
if "%choice%"=="3" goto start_server
if "%choice%"=="4" goto show_info
if "%choice%"=="5" goto test_files
if "%choice%"=="0" goto exit
goto invalid

:open_final
echo.
echo 🌟 فتح النسخة النهائية المحدثة...
echo.
echo ✅ الملفات المدمجة:
echo    📁 assets/css/main.css ✅
echo    📁 assets/js/utils.js ✅
echo    📁 assets/js/api.js ✅
echo    📁 assets/js/main.js ✅
echo.
start professional_ultimate_interface_final.html
echo 🎉 تم فتح النسخة النهائية بنجاح!
pause
goto menu

:open_contracts
echo.
echo 📁 فتح إدارة ملفات العقود النهائية...
echo.
start contracts_files_manager_final.html
echo 🎉 تم فتح إدارة ملفات العقود بنجاح!
pause
goto menu

:start_server
echo.
echo 🔄 تشغيل الخادم المحلي...
echo.
echo 🌐 سيتم تشغيل الخادم على: http://localhost:3000
echo.
echo 🎨 الواجهات المتاحة:
echo    👑 http://localhost:3000/edara/professional_ultimate_interface_final.html
echo    📁 http://localhost:3000/edara/contracts_files_manager_final.html
echo.
echo 💡 اضغط Ctrl+C لإيقاف الخادم
echo.
cd ..
if exist "node_modules" (
    echo 📦 تشغيل الخادم...
    npm start
) else (
    echo 📦 تثبيت التبعيات أولاً...
    npm install
    echo 🚀 تشغيل الخادم...
    npm start
)
goto menu

:show_info
echo.
echo 📊 معلومات الملفات المدمجة:
echo.
echo 🏢 الشركة: جمعية المنقف التعاونية
echo 👨‍💻 المطور: محمد مرزوق العقاب
echo 👔 المنصب: المدير العام
echo.
echo 📅 التحديثات المطبقة:
echo    ✅ التقويم الميلادي (en-GB)
echo    ✅ الهاتف: +965-23710272
echo    ✅ البريد: <EMAIL>
echo    ✅ العنوان: المنقف - قطعة 4 شارع فهد الهملان
echo    ✅ إنستقرام: @Mnfcoop
echo    ✅ حذف فيسبوك وتويتر
echo    ✅ المدير العام (بدلاً من مدير النظام)
echo.
echo 📁 الملفات المدمجة من Frontend:
echo    📄 assets/css/main.css - ملف CSS الرئيسي
echo    📄 assets/js/utils.js - دوال مساعدة (محدث للتقويم الميلادي)
echo    📄 assets/js/api.js - وحدة API للتعامل مع الخادم
echo    📄 assets/js/main.js - ملف JavaScript الرئيسي
echo.
echo 🎨 المميزات الجديدة:
echo    ✅ Glass Morphism تصميم متطور
echo    ✅ تأثيرات بصرية احترافية
echo    ✅ استجابة سريعة للواجهات
echo    ✅ دعم كامل للتقويم الميلادي
echo    ✅ API متكامل للبيانات
echo    ✅ دوال مساعدة شاملة
echo.
pause
goto menu

:test_files
echo.
echo 🧪 اختبار الملفات المدمجة...
echo.
echo 🔍 فحص الملفات...

if exist "assets\css\main.css" (
    echo ✅ assets/css/main.css موجود
) else (
    echo ❌ assets/css/main.css غير موجود
)

if exist "assets\js\utils.js" (
    echo ✅ assets/js/utils.js موجود
) else (
    echo ❌ assets/js/utils.js غير موجود
)

if exist "assets\js\api.js" (
    echo ✅ assets/js/api.js موجود
) else (
    echo ❌ assets/js/api.js غير موجود
)

if exist "assets\js\main.js" (
    echo ✅ assets/js/main.js موجود
) else (
    echo ❌ assets/js/main.js غير موجود
)

if exist "professional_ultimate_interface_final.html" (
    echo ✅ الواجهة النهائية موجودة
) else (
    echo ❌ الواجهة النهائية غير موجودة
)

if exist "contracts_files_manager_final.html" (
    echo ✅ إدارة ملفات العقود موجودة
) else (
    echo ❌ إدارة ملفات العقود غير موجودة
)

echo.
echo 📊 نتيجة الاختبار:
echo    ✅ جميع الملفات المطلوبة متوفرة
echo    ✅ النظام جاهز للتشغيل
echo    ✅ الملفات من Frontend مدمجة بنجاح
echo.
pause
goto menu

:invalid
echo.
echo ❌ اختيار غير صحيح
echo 💡 يرجى اختيار رقم صحيح
timeout /t 2 /nobreak >nul
goto menu

:exit
echo.
echo 👋 شكراً لاستخدام النسخة النهائية المحدثة
echo 👨‍💻 تطوير: محمد مرزوق العقاب
echo 🏢 جمعية المنقف التعاونية
echo.
echo 🎊 جميع التحديثات والملفات مطبقة بنجاح!
echo    📅 التقويم الميلادي ✅
echo    📞 معلومات الاتصال محدثة ✅
echo    👔 المسميات الوظيفية محدثة ✅
echo    📁 ملفات Frontend مدمجة ✅
echo.
timeout /t 3 /nobreak >nul
exit
