/**
 * Professional Interface JavaScript
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 */

// Application State Management
class AppState {
  constructor() {
    this.currentUser = null;
    this.currentSection = 'dashboard';
    this.suppliers = [];
    this.contracts = [];
    this.eyes = [];
    this.notifications = [];
    this.filters = {};
    this.searchQuery = '';
    this.isLoading = false;
  }

  // Update state and notify listeners
  setState(newState) {
    Object.assign(this, newState);
    this.notifyListeners();
  }

  // Add state change listeners
  addListener(callback) {
    if (!this.listeners) this.listeners = [];
    this.listeners.push(callback);
  }

  // Notify all listeners of state changes
  notifyListeners() {
    if (this.listeners) {
      this.listeners.forEach(callback => callback(this));
    }
  }
}

// Global app state
const appState = new AppState();

// API Service
class ApiService {
  constructor(baseUrl = '/api/v1') {
    this.baseUrl = baseUrl;
    this.token = localStorage.getItem('authToken');
  }

  // Generic API request method
  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };

    if (this.token) {
      defaultOptions.headers['Authorization'] = `Bearer ${this.token}`;
    }

    const finalOptions = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    };

    try {
      const response = await fetch(url, finalOptions);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP Error: ${response.status}`);
      }

      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      } else {
        return response;
      }
    } catch (error) {
      console.error('API Request Error:', error);
      throw error;
    }
  }

  // Suppliers API
  async getSuppliers(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return await this.request(`/suppliers${queryString ? `?${queryString}` : ''}`);
  }

  async createSupplier(data) {
    return await this.request('/suppliers', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  async updateSupplier(id, data) {
    return await this.request(`/suppliers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  async deleteSupplier(id) {
    return await this.request(`/suppliers/${id}`, {
      method: 'DELETE'
    });
  }

  // Contracts API
  async getContracts(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return await this.request(`/contracts${queryString ? `?${queryString}` : ''}`);
  }

  async createContract(data) {
    return await this.request('/contracts', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  // Reports API
  async getDashboardStats() {
    return await this.request('/reports/dashboard');
  }

  async generateReport(type, format, params = {}) {
    const queryString = new URLSearchParams({ format, ...params }).toString();
    return await this.request(`/reports/${type}?${queryString}`);
  }
}

// Global API service instance
const api = new ApiService();

// Notification System
class NotificationManager {
  constructor() {
    this.notifications = [];
    this.container = null;
    this.init();
  }

  init() {
    // Create notification container
    this.container = document.createElement('div');
    this.container.id = 'notification-container';
    this.container.className = 'fixed top-4 left-4 z-50 space-y-2';
    document.body.appendChild(this.container);
  }

  show(message, type = 'info', duration = 5000) {
    const notification = this.createNotification(message, type);
    this.container.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.classList.add('notification-slide');
    }, 10);

    // Auto remove
    if (duration > 0) {
      setTimeout(() => {
        this.remove(notification);
      }, duration);
    }

    return notification;
  }

  createNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `
      glass-card p-4 rounded-lg text-white max-w-sm transform translate-x-full 
      transition-transform duration-300 ${this.getTypeClass(type)}
    `;

    const icon = this.getTypeIcon(type);
    
    notification.innerHTML = `
      <div class="flex items-center gap-3">
        <i class="fas fa-${icon} text-lg"></i>
        <span class="flex-1">${message}</span>
        <button onclick="notificationManager.remove(this.closest('.glass-card'))" 
                class="text-white/70 hover:text-white transition-colors">
          <i class="fas fa-times"></i>
        </button>
      </div>
    `;

    return notification;
  }

  getTypeClass(type) {
    const classes = {
      success: 'border-green-500/30 bg-green-500/20',
      error: 'border-red-500/30 bg-red-500/20',
      warning: 'border-yellow-500/30 bg-yellow-500/20',
      info: 'border-blue-500/30 bg-blue-500/20'
    };
    return classes[type] || classes.info;
  }

  getTypeIcon(type) {
    const icons = {
      success: 'check-circle',
      error: 'exclamation-circle',
      warning: 'exclamation-triangle',
      info: 'info-circle'
    };
    return icons[type] || icons.info;
  }

  remove(notification) {
    notification.classList.add('removing');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }

  clear() {
    this.container.innerHTML = '';
  }
}

// Global notification manager
const notificationManager = new NotificationManager();

// Form Validation
class FormValidator {
  constructor(form) {
    this.form = form;
    this.rules = {};
    this.errors = {};
  }

  addRule(fieldName, rule) {
    if (!this.rules[fieldName]) {
      this.rules[fieldName] = [];
    }
    this.rules[fieldName].push(rule);
    return this;
  }

  required(fieldName, message = 'هذا الحقل مطلوب') {
    return this.addRule(fieldName, {
      type: 'required',
      message,
      validate: (value) => value && value.trim() !== ''
    });
  }

  email(fieldName, message = 'البريد الإلكتروني غير صحيح') {
    return this.addRule(fieldName, {
      type: 'email',
      message,
      validate: (value) => !value || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
    });
  }

  phone(fieldName, message = 'رقم الهاتف غير صحيح') {
    return this.addRule(fieldName, {
      type: 'phone',
      message,
      validate: (value) => !value || /^[0-9+\-\s()]+$/.test(value)
    });
  }

  minLength(fieldName, length, message = `يجب أن يكون على الأقل ${length} أحرف`) {
    return this.addRule(fieldName, {
      type: 'minLength',
      message,
      validate: (value) => !value || value.length >= length
    });
  }

  validate() {
    this.errors = {};
    const formData = new FormData(this.form);

    Object.keys(this.rules).forEach(fieldName => {
      const value = formData.get(fieldName) || '';
      const fieldRules = this.rules[fieldName];

      fieldRules.forEach(rule => {
        if (!rule.validate(value)) {
          if (!this.errors[fieldName]) {
            this.errors[fieldName] = [];
          }
          this.errors[fieldName].push(rule.message);
        }
      });
    });

    this.displayErrors();
    return Object.keys(this.errors).length === 0;
  }

  displayErrors() {
    // Clear previous errors
    this.form.querySelectorAll('.error-message').forEach(el => el.remove());
    this.form.querySelectorAll('.error').forEach(el => el.classList.remove('error'));

    // Display new errors
    Object.keys(this.errors).forEach(fieldName => {
      const field = this.form.querySelector(`[name="${fieldName}"]`);
      if (field) {
        field.classList.add('error');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message text-red-400 text-sm mt-1';
        errorDiv.textContent = this.errors[fieldName][0];
        
        field.parentNode.appendChild(errorDiv);
      }
    });
  }

  clearErrors() {
    this.form.querySelectorAll('.error-message').forEach(el => el.remove());
    this.form.querySelectorAll('.error').forEach(el => el.classList.remove('error'));
  }
}

// Data Table Manager
class DataTable {
  constructor(containerId, options = {}) {
    this.container = document.getElementById(containerId);
    this.options = {
      searchable: true,
      sortable: true,
      pagination: true,
      pageSize: 10,
      ...options
    };
    this.data = [];
    this.filteredData = [];
    this.currentPage = 1;
    this.sortColumn = null;
    this.sortDirection = 'asc';
  }

  setData(data) {
    this.data = data;
    this.filteredData = [...data];
    this.render();
  }

  search(query) {
    if (!query) {
      this.filteredData = [...this.data];
    } else {
      this.filteredData = this.data.filter(row => 
        Object.values(row).some(value => 
          String(value).toLowerCase().includes(query.toLowerCase())
        )
      );
    }
    this.currentPage = 1;
    this.render();
  }

  sort(column) {
    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortColumn = column;
      this.sortDirection = 'asc';
    }

    this.filteredData.sort((a, b) => {
      const aVal = a[column];
      const bVal = b[column];
      
      if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
      if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    this.render();
  }

  goToPage(page) {
    this.currentPage = page;
    this.render();
  }

  render() {
    if (!this.container) return;

    const startIndex = (this.currentPage - 1) * this.options.pageSize;
    const endIndex = startIndex + this.options.pageSize;
    const pageData = this.filteredData.slice(startIndex, endIndex);

    // Render table
    this.renderTable(pageData);
    
    // Render pagination
    if (this.options.pagination) {
      this.renderPagination();
    }
  }

  renderTable(data) {
    // Implementation depends on specific table structure
    // This is a basic example
    const tableHtml = `
      <div class="table-modern">
        <table class="w-full">
          <thead>
            <tr>
              ${this.options.columns.map(col => `
                <th class="text-right p-4 cursor-pointer" onclick="dataTable.sort('${col.key}')">
                  ${col.title}
                  ${this.sortColumn === col.key ? 
                    `<i class="fas fa-sort-${this.sortDirection === 'asc' ? 'up' : 'down'} mr-2"></i>` : 
                    '<i class="fas fa-sort mr-2"></i>'
                  }
                </th>
              `).join('')}
            </tr>
          </thead>
          <tbody>
            ${data.map(row => `
              <tr>
                ${this.options.columns.map(col => `
                  <td class="p-4">${this.formatCell(row[col.key], col)}</td>
                `).join('')}
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;

    this.container.innerHTML = tableHtml;
  }

  formatCell(value, column) {
    if (column.formatter) {
      return column.formatter(value);
    }
    return value || '-';
  }

  renderPagination() {
    const totalPages = Math.ceil(this.filteredData.length / this.options.pageSize);
    
    if (totalPages <= 1) return;

    const paginationHtml = `
      <div class="flex items-center justify-between mt-4 p-4">
        <div class="text-white/70">
          عرض ${((this.currentPage - 1) * this.options.pageSize) + 1} إلى 
          ${Math.min(this.currentPage * this.options.pageSize, this.filteredData.length)} 
          من ${this.filteredData.length} نتيجة
        </div>
        <div class="flex gap-2">
          ${this.currentPage > 1 ? 
            `<button onclick="dataTable.goToPage(${this.currentPage - 1})" 
                     class="btn btn-secondary btn-sm">السابق</button>` : ''
          }
          ${this.currentPage < totalPages ? 
            `<button onclick="dataTable.goToPage(${this.currentPage + 1})" 
                     class="btn btn-secondary btn-sm">التالي</button>` : ''
          }
        </div>
      </div>
    `;

    this.container.insertAdjacentHTML('beforeend', paginationHtml);
  }
}

// Utility Functions
const Utils = {
  // Format currency
  formatCurrency(amount, currency = 'د.ك') {
    if (!amount && amount !== 0) return '-';
    
    const formatted = parseFloat(amount).toLocaleString('ar-KW', {
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    });
    
    return `${formatted} ${currency}`;
  },

  // Format date
  formatDate(date, format = 'DD/MM/YYYY') {
    if (!date) return '-';
    
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    
    switch (format) {
      case 'DD/MM/YYYY':
        return `${day}/${month}/${year}`;
      case 'YYYY-MM-DD':
        return `${year}-${month}-${day}`;
      default:
        return `${day}/${month}/${year}`;
    }
  },

  // Debounce function
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  // Generate unique ID
  generateId() {
    return Math.random().toString(36).substr(2, 9);
  },

  // Copy to clipboard
  async copyToClipboard(text) {
    try {
      await navigator.clipboard.writeText(text);
      notificationManager.show('تم النسخ بنجاح', 'success');
    } catch (err) {
      console.error('Failed to copy: ', err);
      notificationManager.show('فشل في النسخ', 'error');
    }
  },

  // Download file
  downloadFile(data, filename, type = 'application/octet-stream') {
    const blob = new Blob([data], { type });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  }
};

// Export for global use
window.AppState = AppState;
window.ApiService = ApiService;
window.NotificationManager = NotificationManager;
window.FormValidator = FormValidator;
window.DataTable = DataTable;
window.Utils = Utils;
window.appState = appState;
window.api = api;
window.notificationManager = notificationManager;
