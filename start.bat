@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM =====================================================
REM سكريبت التشغيل السريع لنظام إدارة عقود الموردين والإيجارات
REM جمعية المنقف التعاونية - Windows
REM =====================================================

title نظام إدارة عقود الموردين والإيجارات - جمعية المنقف

REM الألوان
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "PURPLE=[95m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

REM رموز
set "SUCCESS=✓"
set "ERROR=✗"
set "WARNING=⚠"
set "INFO=ℹ"
set "ROCKET=🚀"
set "GEAR=⚙"

:print_header
echo.
echo %CYAN%==============================================
echo   نظام إدارة عقود الموردين والإيجارات
echo         جمعية المنقف التعاونية
echo ==============================================%NC%
echo.
goto :eof

:print_separator
echo %BLUE%----------------------------------------------%NC%
goto :eof

:check_requirements
echo %BLUE%%GEAR% التحقق من المتطلبات...%NC%
set "missing_req=0"

REM التحقق من Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo %RED%%ERROR% Node.js غير مثبت%NC%
    set "missing_req=1"
) else (
    for /f "tokens=*" %%i in ('node --version') do set "node_version=%%i"
    echo %GREEN%%SUCCESS% Node.js: !node_version!%NC%
)

REM التحقق من npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo %RED%%ERROR% npm غير مثبت%NC%
    set "missing_req=1"
) else (
    for /f "tokens=*" %%i in ('npm --version') do set "npm_version=%%i"
    echo %GREEN%%SUCCESS% npm: !npm_version!%NC%
)

REM التحقق من MySQL
mysql --version >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%%INFO% MySQL غير متوفر في PATH (قد يكون مثبت)%NC%
) else (
    echo %GREEN%%SUCCESS% MySQL متوفر%NC%
)

REM التحقق من Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%%INFO% Docker غير متوفر (اختياري)%NC%
) else (
    for /f "tokens=3" %%i in ('docker --version') do set "docker_version=%%i"
    echo %GREEN%%SUCCESS% Docker: !docker_version!%NC%
)

REM التحقق من Docker Compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%%INFO% Docker Compose غير متوفر (اختياري)%NC%
) else (
    echo %GREEN%%SUCCESS% Docker Compose متوفر%NC%
)

if !missing_req! equ 1 (
    echo.
    echo %RED%%ERROR% يرجى تثبيت المتطلبات المفقودة قبل المتابعة%NC%
    pause
    exit /b 1
)

echo %GREEN%%SUCCESS% جميع المتطلبات متوفرة!%NC%
goto :eof

:setup_env
if not exist .env (
    echo %YELLOW%%INFO% إنشاء ملف .env من القالب...%NC%
    copy .env.example .env >nul
    echo %GREEN%%SUCCESS% تم إنشاء ملف .env%NC%
    echo %YELLOW%%WARNING% يرجى تحديث إعدادات قاعدة البيانات في ملف .env%NC%
) else (
    echo %GREEN%%SUCCESS% ملف .env موجود%NC%
)
goto :eof

:install_dependencies
echo %BLUE%%GEAR% تثبيت التبعيات...%NC%

if exist package-lock.json (
    npm ci
) else (
    npm install
)

if errorlevel 1 (
    echo %RED%%ERROR% فشل في تثبيت التبعيات%NC%
    pause
    exit /b 1
)

echo %GREEN%%SUCCESS% تم تثبيت التبعيات بنجاح%NC%
goto :eof

:create_directories
echo %BLUE%%GEAR% إنشاء المجلدات المطلوبة...%NC%

set "directories=backend\logs backend\uploads backend\uploads\contracts backend\uploads\documents backend\uploads\images data data\mysql data\redis data\uploads data\logs data\backups data\nginx-logs"

for %%d in (%directories%) do (
    if not exist "%%d" (
        mkdir "%%d" 2>nul
        echo %GREEN%%SUCCESS% تم إنشاء المجلد: %%d%NC%
    )
)
goto :eof

:show_menu
call :print_separator
echo %CYAN%اختر طريقة التشغيل:%NC%
echo.
echo %GREEN%1^)%NC% تشغيل عادي ^(Node.js مباشرة^)
echo %GREEN%2^)%NC% تشغيل للتطوير ^(مع nodemon^)
echo %GREEN%3^)%NC% تشغيل باستخدام Docker
echo %GREEN%4^)%NC% تشغيل باستخدام Docker مع المراقبة
echo %GREEN%5^)%NC% إعداد أولي فقط
echo %GREEN%6^)%NC% عرض السجلات
echo %GREEN%7^)%NC% إيقاف الخدمات
echo %GREEN%8^)%NC% فتح المتصفح
echo %GREEN%9^)%NC% تنظيف الملفات المؤقتة
echo %GREEN%0^)%NC% خروج
echo.
call :print_separator
goto :eof

:start_normal
echo %BLUE%%ROCKET% تشغيل النظام...%NC%
npm start
goto :eof

:start_dev
echo %BLUE%%ROCKET% تشغيل النظام في وضع التطوير...%NC%
npm run dev
goto :eof

:start_docker
echo %BLUE%%ROCKET% تشغيل النظام باستخدام Docker...%NC%

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo %RED%%ERROR% Docker Compose غير متوفر%NC%
    pause
    goto :main_menu
)

docker-compose up -d

if errorlevel 1 (
    echo %RED%%ERROR% فشل في تشغيل Docker%NC%
    pause
    goto :main_menu
)

echo %GREEN%%SUCCESS% تم تشغيل النظام باستخدام Docker%NC%
echo %CYAN%%INFO% يمكنك الوصول للنظام على: http://localhost:3000%NC%
echo %CYAN%%INFO% لعرض السجلات: docker-compose logs -f app%NC%
goto :eof

:start_docker_monitoring
echo %BLUE%%ROCKET% تشغيل النظام باستخدام Docker مع المراقبة...%NC%

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo %RED%%ERROR% Docker Compose غير متوفر%NC%
    pause
    goto :main_menu
)

docker-compose --profile monitoring up -d

if errorlevel 1 (
    echo %RED%%ERROR% فشل في تشغيل Docker%NC%
    pause
    goto :main_menu
)

echo %GREEN%%SUCCESS% تم تشغيل النظام مع خدمات المراقبة%NC%
echo %CYAN%%INFO% النظام: http://localhost:3000%NC%
echo %CYAN%%INFO% Prometheus: http://localhost:9090%NC%
echo %CYAN%%INFO% Grafana: http://localhost:3001%NC%
goto :eof

:show_logs
call :print_separator
echo %CYAN%اختر نوع السجلات:%NC%
echo.
echo %GREEN%1^)%NC% سجلات التطبيق ^(ملفات^)
echo %GREEN%2^)%NC% سجلات Docker
echo %GREEN%3^)%NC% سجلات قاعدة البيانات
echo.
set /p "log_choice=اختر رقم الخيار: "

if "!log_choice!"=="1" (
    if exist backend\logs\combined.log (
        type backend\logs\combined.log
        echo.
        echo %CYAN%%INFO% اضغط Ctrl+C لإيقاف متابعة السجلات%NC%
        powershell -Command "Get-Content backend\logs\combined.log -Wait"
    ) else (
        echo %YELLOW%%WARNING% ملف السجلات غير موجود%NC%
    )
) else if "!log_choice!"=="2" (
    docker-compose logs -f app
) else if "!log_choice!"=="3" (
    docker-compose logs -f database
) else (
    echo %RED%%ERROR% خيار غير صحيح%NC%
)
goto :eof

:stop_services
echo %BLUE%%GEAR% إيقاف الخدمات...%NC%

docker-compose --version >nul 2>&1
if not errorlevel 1 (
    docker-compose down
    echo %GREEN%%SUCCESS% تم إيقاف خدمات Docker%NC%
)

REM إيقاف العمليات التي تستخدم المنفذ 3000
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000') do (
    taskkill /f /pid %%a >nul 2>&1
    if not errorlevel 1 (
        echo %GREEN%%SUCCESS% تم إيقاف العملية على المنفذ 3000%NC%
    )
)
goto :eof

:open_browser
echo %BLUE%%INFO% فتح المتصفح...%NC%
start http://localhost:3000
goto :eof

:cleanup
echo %BLUE%%GEAR% تنظيف الملفات المؤقتة...%NC%

if exist node_modules (
    echo %YELLOW%%INFO% حذف node_modules...%NC%
    rmdir /s /q node_modules
)

if exist package-lock.json (
    echo %YELLOW%%INFO% حذف package-lock.json...%NC%
    del package-lock.json
)

if exist backend\logs (
    echo %YELLOW%%INFO% تنظيف السجلات...%NC%
    del /q backend\logs\*.log 2>nul
)

echo %GREEN%%SUCCESS% تم تنظيف الملفات المؤقتة%NC%
goto :eof

:initial_setup
echo %BLUE%%GEAR% بدء الإعداد الأولي...%NC%

call :check_requirements
call :setup_env
call :create_directories
call :install_dependencies

echo %GREEN%%SUCCESS% تم الإعداد الأولي بنجاح!%NC%
echo %CYAN%%INFO% يرجى إعداد قاعدة البيانات يدوياً إذا لم تكن معدة مسبقاً%NC%
goto :eof

:main_menu
call :print_header

REM التحقق من وجود ملف package.json
if not exist package.json (
    echo %RED%%ERROR% ملف package.json غير موجود%NC%
    echo %CYAN%%INFO% تأكد من تشغيل السكريبت من المجلد الجذر للمشروع%NC%
    pause
    exit /b 1
)

:menu_loop
call :show_menu
set /p "choice=اختر رقم الخيار: "

if "!choice!"=="1" (
    call :initial_setup
    call :start_normal
) else if "!choice!"=="2" (
    call :initial_setup
    call :start_dev
) else if "!choice!"=="3" (
    call :create_directories
    call :start_docker
) else if "!choice!"=="4" (
    call :create_directories
    call :start_docker_monitoring
) else if "!choice!"=="5" (
    call :initial_setup
) else if "!choice!"=="6" (
    call :show_logs
) else if "!choice!"=="7" (
    call :stop_services
) else if "!choice!"=="8" (
    call :open_browser
) else if "!choice!"=="9" (
    call :cleanup
) else if "!choice!"=="0" (
    echo %CYAN%%INFO% شكراً لاستخدام النظام!%NC%
    pause
    exit /b 0
) else (
    echo %RED%%ERROR% خيار غير صحيح، يرجى المحاولة مرة أخرى%NC%
)

echo.
pause
cls
goto :menu_loop

REM نقطة الدخول الرئيسية
:main
goto :main_menu
