/**
 * مسارات API للتقارير
 * جمعية المنقف التعاونية
 */

const express = require('express');
const { query, validationResult } = require('express-validator');
const ExcelJS = require('exceljs');
const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs');
const database = require('../utils/database');
const logger = require('../utils/logger');

const router = express.Router();

// =====================================================
// مساعدات التقارير
// =====================================================

/**
 * إنشاء تقرير Excel للموردين
 */
async function generateSuppliersExcel(suppliers, filters) {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('تقرير الموردين');

  // إعداد العناوين
  worksheet.columns = [
    { header: 'الرقم', key: 'id', width: 10 },
    { header: 'اسم المورد', key: 'name', width: 25 },
    { header: 'رقم الهاتف', key: 'phone', width: 15 },
    { header: 'البريد الإلكتروني', key: 'email', width: 25 },
    { header: 'الفئة', key: 'category', width: 15 },
    { header: 'العنوان', key: 'address', width: 30 },
    { header: 'الحالة', key: 'status', width: 12 },
    { header: 'تاريخ الإضافة', key: 'created_at', width: 15 }
  ];

  // تنسيق العناوين
  worksheet.getRow(1).font = { bold: true };
  worksheet.getRow(1).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FF3B82F6' }
  };

  // إضافة البيانات
  suppliers.forEach(supplier => {
    worksheet.addRow({
      id: supplier.id,
      name: supplier.name,
      phone: supplier.phone,
      email: supplier.email || '-',
      category: supplier.category,
      address: supplier.address || '-',
      status: supplier.status,
      created_at: new Date(supplier.created_at).toLocaleDateString('en-GB')
    });
  });

  // إضافة معلومات التقرير
  worksheet.insertRow(1, ['تقرير الموردين - جمعية المنقف']);
  worksheet.insertRow(2, [`تاريخ التقرير: ${new Date().toLocaleDateString('en-GB')}`]);
  worksheet.insertRow(3, [`عدد الموردين: ${suppliers.length}`]);
  if (filters.category) {
    worksheet.insertRow(4, [`الفئة: ${filters.category}`]);
  }
  worksheet.insertRow(5, []); // سطر فارغ

  // دمج خلايا العنوان
  worksheet.mergeCells('A1:H1');
  worksheet.getCell('A1').font = { bold: true, size: 16 };
  worksheet.getCell('A1').alignment = { horizontal: 'center' };

  return workbook;
}

/**
 * إنشاء تقرير Excel للعقود
 */
async function generateContractsExcel(contracts, filters) {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('تقرير العقود');

  // إعداد العناوين
  worksheet.columns = [
    { header: 'رقم العقد', key: 'contract_number', width: 15 },
    { header: 'اسم المورد', key: 'supplier_name', width: 25 },
    { header: 'نوع العقد', key: 'contract_type', width: 15 },
    { header: 'تاريخ البداية', key: 'start_date', width: 15 },
    { header: 'تاريخ النهاية', key: 'end_date', width: 15 },
    { header: 'المبلغ الإجمالي', key: 'total_amount', width: 15 },
    { header: 'الحالة', key: 'status', width: 12 },
    { header: 'تاريخ الإنشاء', key: 'created_at', width: 15 }
  ];

  // تنسيق العناوين
  worksheet.getRow(1).font = { bold: true };
  worksheet.getRow(1).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FF10B981' }
  };

  // إضافة البيانات
  contracts.forEach(contract => {
    worksheet.addRow({
      contract_number: contract.contract_number,
      supplier_name: contract.supplier_name,
      contract_type: contract.contract_type,
      start_date: new Date(contract.start_date).toLocaleDateString('en-GB'),
      end_date: new Date(contract.end_date).toLocaleDateString('en-GB'),
      total_amount: `${contract.total_amount} د.ك`,
      status: contract.status,
      created_at: new Date(contract.created_at).toLocaleDateString('en-GB')
    });
  });

  // إضافة معلومات التقرير
  worksheet.insertRow(1, ['تقرير العقود - جمعية المنقف']);
  worksheet.insertRow(2, [`تاريخ التقرير: ${new Date().toLocaleDateString('en-GB')}`]);
  worksheet.insertRow(3, [`عدد العقود: ${contracts.length}`]);
  if (filters.status) {
    worksheet.insertRow(4, [`الحالة: ${filters.status}`]);
  }
  worksheet.insertRow(5, []); // سطر فارغ

  // دمج خلايا العنوان
  worksheet.mergeCells('A1:H1');
  worksheet.getCell('A1').font = { bold: true, size: 16 };
  worksheet.getCell('A1').alignment = { horizontal: 'center' };

  return workbook;
}

// =====================================================
// المسارات
// =====================================================

/**
 * GET /api/v1/reports/suppliers
 * تقرير الموردين
 */
router.get('/suppliers', [
  query('format').isIn(['excel', 'pdf']).withMessage('تنسيق التقرير غير صحيح'),
  query('category').optional().isIn(['بهارات', 'استهلاكي', 'أجبان']).withMessage('الفئة غير صحيحة'),
  query('status').optional().isIn(['نشط', 'غير نشط']).withMessage('الحالة غير صحيحة')
], async (req, res) => {
  try {
    // التحقق من صحة البيانات
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { format, category, status = 'نشط' } = req.query;

    // بناء استعلام البحث
    let whereClause = 'WHERE status = ?';
    let queryParams = [status];

    if (category) {
      whereClause += ' AND category = ?';
      queryParams.push(category);
    }

    // جلب بيانات الموردين
    const query = `
      SELECT 
        id,
        name,
        phone,
        email,
        category,
        address,
        status,
        created_at
      FROM suppliers 
      ${whereClause}
      ORDER BY category, name
    `;

    const suppliers = await database.query(query, queryParams);

    if (format === 'excel') {
      // إنشاء تقرير Excel
      const workbook = await generateSuppliersExcel(suppliers, { category, status });
      
      // إعداد الاستجابة
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="suppliers_report_${Date.now()}.xlsx"`);
      
      await workbook.xlsx.write(res);
      res.end();

    } else if (format === 'pdf') {
      // إنشاء تقرير PDF (سيتم تنفيذه لاحقاً)
      res.status(501).json({
        success: false,
        message: 'تقارير PDF قيد التطوير'
      });
    }

    logger.info(`تم إنشاء تقرير الموردين بتنسيق ${format}`);

  } catch (error) {
    logger.error('خطأ في إنشاء تقرير الموردين:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إنشاء التقرير'
    });
  }
});

/**
 * GET /api/v1/reports/contracts
 * تقرير العقود
 */
router.get('/contracts', [
  query('format').isIn(['excel', 'pdf']).withMessage('تنسيق التقرير غير صحيح'),
  query('status').optional().isIn(['نشط', 'منتهي', 'ملغي']).withMessage('الحالة غير صحيحة'),
  query('start_date').optional().isISO8601().withMessage('تاريخ البداية غير صحيح'),
  query('end_date').optional().isISO8601().withMessage('تاريخ النهاية غير صحيح')
], async (req, res) => {
  try {
    // التحقق من صحة البيانات
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { format, status, start_date, end_date } = req.query;

    // بناء استعلام البحث
    let whereClause = 'WHERE 1=1';
    let queryParams = [];

    if (status) {
      whereClause += ' AND c.status = ?';
      queryParams.push(status);
    }

    if (start_date) {
      whereClause += ' AND c.start_date >= ?';
      queryParams.push(start_date);
    }

    if (end_date) {
      whereClause += ' AND c.end_date <= ?';
      queryParams.push(end_date);
    }

    // جلب بيانات العقود
    const query = `
      SELECT 
        c.contract_number,
        s.name as supplier_name,
        c.contract_type,
        c.start_date,
        c.end_date,
        c.total_amount,
        c.status,
        c.created_at
      FROM contracts c
      JOIN suppliers s ON c.supplier_id = s.id
      ${whereClause}
      ORDER BY c.created_at DESC
    `;

    const contracts = await database.query(query, queryParams);

    if (format === 'excel') {
      // إنشاء تقرير Excel
      const workbook = await generateContractsExcel(contracts, { status, start_date, end_date });
      
      // إعداد الاستجابة
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="contracts_report_${Date.now()}.xlsx"`);
      
      await workbook.xlsx.write(res);
      res.end();

    } else if (format === 'pdf') {
      // إنشاء تقرير PDF (سيتم تنفيذه لاحقاً)
      res.status(501).json({
        success: false,
        message: 'تقارير PDF قيد التطوير'
      });
    }

    logger.info(`تم إنشاء تقرير العقود بتنسيق ${format}`);

  } catch (error) {
    logger.error('خطأ في إنشاء تقرير العقود:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إنشاء التقرير'
    });
  }
});

/**
 * GET /api/v1/reports/dashboard
 * إحصائيات لوحة التحكم
 */
router.get('/dashboard', async (req, res) => {
  try {
    // إحصائيات الموردين
    const suppliersStats = await database.query(`
      SELECT 
        category,
        COUNT(*) as count,
        SUM(CASE WHEN status = 'نشط' THEN 1 ELSE 0 END) as active_count
      FROM suppliers 
      GROUP BY category
    `);

    // إحصائيات العقود
    const contractsStats = await database.query(`
      SELECT 
        status,
        COUNT(*) as count,
        SUM(total_amount) as total_amount
      FROM contracts 
      GROUP BY status
    `);

    // العقود المنتهية قريباً (خلال 30 يوم)
    const expiringContracts = await database.query(`
      SELECT 
        c.contract_number,
        s.name as supplier_name,
        c.end_date,
        DATEDIFF(c.end_date, CURDATE()) as days_remaining
      FROM contracts c
      JOIN suppliers s ON c.supplier_id = s.id
      WHERE c.status = 'نشط' 
        AND c.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
      ORDER BY c.end_date ASC
    `);

    // الإيرادات الشهرية
    const monthlyRevenue = await database.query(`
      SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        SUM(total_amount) as revenue
      FROM contracts 
      WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
      GROUP BY DATE_FORMAT(created_at, '%Y-%m')
      ORDER BY month DESC
    `);

    res.json({
      success: true,
      data: {
        suppliers: suppliersStats.reduce((acc, stat) => {
          acc[stat.category] = {
            total: stat.count,
            active: stat.active_count
          };
          return acc;
        }, {}),
        contracts: contractsStats.reduce((acc, stat) => {
          acc[stat.status] = {
            count: stat.count,
            total_amount: stat.total_amount || 0
          };
          return acc;
        }, {}),
        expiring_contracts: expiringContracts,
        monthly_revenue: monthlyRevenue
      }
    });

  } catch (error) {
    logger.error('خطأ في جلب إحصائيات لوحة التحكم:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الإحصائيات'
    });
  }
});

module.exports = router;
