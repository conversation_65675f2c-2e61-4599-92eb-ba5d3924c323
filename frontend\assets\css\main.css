/**
 * التنسيق الرئيسي للنظام
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 */

/* ==================== الخطوط والمتغيرات ==================== */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');

:root {
  /* الألوان الأساسية */
  --primary-color: #3b82f6;
  --primary-dark: #1e40af;
  --primary-light: #93c5fd;
  
  --secondary-color: #6366f1;
  --secondary-dark: #4338ca;
  --secondary-light: #a5b4fc;
  
  /* ألوان الحالة */
  --success-color: #10b981;
  --success-dark: #047857;
  --success-light: #6ee7b7;
  
  --warning-color: #f59e0b;
  --warning-dark: #d97706;
  --warning-light: #fcd34d;
  
  --error-color: #ef4444;
  --error-dark: #dc2626;
  --error-light: #fca5a5;
  
  --info-color: #06b6d4;
  --info-dark: #0891b2;
  --info-light: #67e8f9;
  
  /* ألوان رمادية */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* المسافات والأحجام */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  --border-radius-sm: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  --border-radius-2xl: 1.5rem;
  
  /* الظلال */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* الانتقالات */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* خط الأساس */
  --font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ==================== الإعدادات الأساسية ==================== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--gray-800);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  direction: rtl;
  text-align: right;
}

/* ==================== التايبوغرافي ==================== */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: var(--spacing-md);
  color: var(--gray-900);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
  margin-bottom: var(--spacing-md);
  color: var(--gray-700);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
}

/* ==================== الأزرار ==================== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--border-radius-lg);
  font-family: inherit;
  font-size: 0.875rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  user-select: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left var(--transition-slow);
}

.btn:hover::before {
  left: 100%;
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn:active:not(:disabled) {
  transform: translateY(0);
}

/* أنواع الأزرار */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color), var(--success-dark));
  color: white;
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-color), var(--warning-dark));
  color: white;
}

.btn-error {
  background: linear-gradient(135deg, var(--error-color), var(--error-dark));
  color: white;
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
}

.btn-ghost {
  background: transparent;
  color: var(--gray-700);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--gray-100);
}

/* أحجام الأزرار */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: 0.75rem;
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 1rem;
}

.btn-xl {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: 1.125rem;
}

/* ==================== النماذج ==================== */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  color: var(--gray-700);
}

.form-label.required::after {
  content: " *";
  color: var(--error-color);
  font-weight: bold;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--gray-300);
  border-radius: var(--border-radius-lg);
  font-family: inherit;
  font-size: 0.875rem;
  background: white;
  transition: all var(--transition-normal);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: scale(1.02);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: var(--error-color);
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

.form-input.success,
.form-select.success,
.form-textarea.success {
  border-color: var(--success-color);
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
}

.form-error {
  display: block;
  margin-top: var(--spacing-xs);
  font-size: 0.75rem;
  color: var(--error-color);
}

.form-help {
  display: block;
  margin-top: var(--spacing-xs);
  font-size: 0.75rem;
  color: var(--gray-500);
}

/* ==================== البطاقات ==================== */
.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius-2xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.card-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
}

/* ==================== الجداول ==================== */
.table-container {
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  background: white;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: var(--spacing-md);
  text-align: right;
  border-bottom: 1px solid var(--gray-200);
}

.table th {
  background: var(--gray-100);
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table tbody tr {
  transition: background-color var(--transition-fast);
}

.table tbody tr:hover {
  background: linear-gradient(90deg, var(--gray-50), var(--gray-100));
}

.table tbody tr:nth-child(even) {
  background: rgba(249, 250, 251, 0.5);
}

/* ==================== النوافذ المنبثقة ==================== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: var(--border-radius-2xl);
  box-shadow: var(--shadow-xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  transform: scale(0.8) translateY(-50px);
  transition: transform var(--transition-normal);
}

.modal.show .modal-content {
  transform: scale(1) translateY(0);
}

/* ==================== الإشعارات ==================== */
.toast {
  position: fixed;
  top: var(--spacing-lg);
  left: var(--spacing-lg);
  max-width: 400px;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
  z-index: 1100;
  transform: translateX(100%);
  transition: transform var(--transition-normal);
}

.toast.show {
  transform: translateX(0);
}

.toast-success {
  background: linear-gradient(135deg, var(--success-color), var(--success-dark));
  color: white;
}

.toast-error {
  background: linear-gradient(135deg, var(--error-color), var(--error-dark));
  color: white;
}

.toast-warning {
  background: linear-gradient(135deg, var(--warning-color), var(--warning-dark));
  color: white;
}

.toast-info {
  background: linear-gradient(135deg, var(--info-color), var(--info-dark));
  color: white;
}

/* ==================== مؤشرات التحميل ==================== */
.loading-spinner {
  border: 3px solid var(--gray-200);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-dots {
  display: inline-flex;
  gap: var(--spacing-xs);
}

.loading-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary-color);
  animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* ==================== الشارات ==================== */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-md);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary { background: var(--primary-light); color: var(--primary-dark); }
.badge-success { background: var(--success-light); color: var(--success-dark); }
.badge-warning { background: var(--warning-light); color: var(--warning-dark); }
.badge-error { background: var(--error-light); color: var(--error-dark); }
.badge-info { background: var(--info-light); color: var(--info-dark); }

/* ==================== الأدوات المساعدة ==================== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.invisible { visibility: hidden; }

.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* ==================== التمرير المخصص ==================== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--border-radius-md);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--primary-color), var(--primary-dark));
  border-radius: var(--border-radius-md);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--primary-dark), var(--primary-color));
}

/* ==================== الطباعة ==================== */
@media print {
  .no-print { display: none !important; }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .card {
    background: white !important;
    box-shadow: none !important;
    border: 1px solid var(--gray-300) !important;
  }
  
  .btn {
    display: none !important;
  }
}
