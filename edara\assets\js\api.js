/**
 * وحدة API للتعامل مع الخادم - النسخة المحدثة
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 * تطوير: محمد مرزوق العقاب
 */

class APIClient {
  constructor(baseURL = '/api') {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
  }

  /**
   * إرسال طلب HTTP
   * @param {string} method - نوع الطلب (GET, POST, PUT, DELETE)
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} data - البيانات المرسلة
   * @param {Object} options - خيارات إضافية
   */
  async request(method, endpoint, data = null, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    
    const config = {
      method: method.toUpperCase(),
      headers: {
        ...this.defaultHeaders,
        ...options.headers
      },
      ...options
    };

    // إضافة البيانات للطلبات التي تدعمها
    if (data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
      if (data instanceof FormData) {
        delete config.headers['Content-Type'];
        config.body = data;
      } else {
        config.body = JSON.stringify(data);
      }
    }

    try {
      console.log(`📡 ${method.toUpperCase()} ${url}`, data);
      
      const response = await fetch(url, config);
      
      const contentType = response.headers.get('content-type');
      let responseData;
      
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      console.log(`📨 ${response.status} ${url}`, responseData);

      if (!response.ok) {
        const error = new Error(responseData.message || `HTTP Error: ${response.status}`);
        error.status = response.status;
        error.data = responseData;
        throw error;
      }

      return responseData;

    } catch (error) {
      console.error(`❌ خطأ في API ${method.toUpperCase()} ${url}:`, error);
      
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new Error('خطأ في الاتصال بالخادم. تحقق من اتصال الإنترنت.');
      }
      
      throw error;
    }
  }

  /**
   * طلب GET
   */
  async get(endpoint, params = {}, options = {}) {
    const url = new URL(endpoint, window.location.origin + this.baseURL);
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined) {
        url.searchParams.append(key, params[key]);
      }
    });
    
    return this.request('GET', url.pathname + url.search, null, options);
  }

  /**
   * طلب POST
   */
  async post(endpoint, data = {}, options = {}) {
    return this.request('POST', endpoint, data, options);
  }

  /**
   * طلب PUT
   */
  async put(endpoint, data = {}, options = {}) {
    return this.request('PUT', endpoint, data, options);
  }

  /**
   * طلب DELETE
   */
  async delete(endpoint, options = {}) {
    return this.request('DELETE', endpoint, null, options);
  }
}

// إنشاء مثيل عام من API Client
const api = new APIClient();

// ==================== دوال API محددة ====================

/**
 * دوال الموردين
 */
const SuppliersAPI = {
  // جلب جميع الموردين
  async getAll(filters = {}) {
    return api.get('/suppliers', filters);
  },

  // جلب مورد واحد
  async getById(id) {
    return api.get(`/suppliers/${id}`);
  },

  // إضافة مورد جديد
  async create(supplierData) {
    return api.post('/suppliers', supplierData);
  },

  // تحديث مورد
  async update(id, supplierData) {
    return api.put(`/suppliers/${id}`, supplierData);
  },

  // حذف مورد
  async delete(id) {
    return api.delete(`/suppliers/${id}`);
  },

  // استيراد الموردين
  async import() {
    return api.post('/import/suppliers');
  }
};

/**
 * دوال العقود
 */
const ContractsAPI = {
  // جلب جميع العقود
  async getAll(filters = {}) {
    return api.get('/contracts', filters);
  },

  // جلب ملفات العقود PDF
  async getFiles() {
    return api.get('/contracts/files');
  },

  // جلب عقد واحد
  async getById(id) {
    return api.get(`/contracts/${id}`);
  },

  // إضافة عقد جديد
  async create(contractData) {
    return api.post('/contracts', contractData);
  },

  // تحديث عقد
  async update(id, contractData) {
    return api.put(`/contracts/${id}`, contractData);
  },

  // حذف عقد
  async delete(id) {
    return api.delete(`/contracts/${id}`);
  }
};

/**
 * دوال العيون
 */
const EyesAPI = {
  // جلب جميع العيون
  async getAll(filters = {}) {
    return api.get('/eyes', filters);
  },

  // جلب عين واحدة
  async getById(id) {
    return api.get(`/eyes/${id}`);
  },

  // إضافة عين جديدة
  async create(eyeData) {
    return api.post('/eyes', eyeData);
  },

  // تحديث عين
  async update(id, eyeData) {
    return api.put(`/eyes/${id}`, eyeData);
  },

  // حذف عين
  async delete(id) {
    return api.delete(`/eyes/${id}`);
  }
};

/**
 * دوال التقارير
 */
const ReportsAPI = {
  // تقرير الموردين
  async suppliers(format = 'json') {
    return api.get('/reports/suppliers', { format });
  },

  // تقرير العقود
  async contracts(format = 'json') {
    return api.get('/reports/contracts', { format });
  },

  // تقرير العيون
  async eyes(format = 'json') {
    return api.get('/reports/eyes', { format });
  },

  // إحصائيات عامة
  async dashboard() {
    return api.get('/reports/dashboard');
  }
};

/**
 * دوال رفع الملفات
 */
const FilesAPI = {
  // رفع ملف
  async upload(file, type = 'suppliers') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);
    
    return api.post('/upload', formData);
  },

  // تحميل ملف
  async download(filename) {
    const response = await fetch(`/contracts-files/${encodeURIComponent(filename)}`);
    if (!response.ok) {
      throw new Error('خطأ في تحميل الملف');
    }
    return response.blob();
  }
};

/**
 * دوال المصادقة
 */
const AuthAPI = {
  // تسجيل الدخول
  async login(credentials) {
    return api.post('/auth/login', credentials);
  },

  // تسجيل الخروج
  async logout() {
    return api.post('/auth/logout');
  },

  // التحقق من الجلسة
  async checkSession() {
    return api.get('/auth/me');
  }
};

// تصدير APIs للاستخدام العام
window.API = {
  client: api,
  suppliers: SuppliersAPI,
  contracts: ContractsAPI,
  eyes: EyesAPI,
  reports: ReportsAPI,
  files: FilesAPI,
  auth: AuthAPI
};

// دوال مساعدة للتعامل مع الأخطاء
window.APIHelpers = {
  /**
   * معالجة أخطاء API وعرضها للمستخدم
   * @param {Error} error - الخطأ
   * @param {string} defaultMessage - رسالة افتراضية
   */
  handleError(error, defaultMessage = 'حدث خطأ غير متوقع') {
    let message = defaultMessage;
    
    if (error.status === 400) {
      message = 'بيانات غير صحيحة';
    } else if (error.status === 401) {
      message = 'غير مصرح لك بهذا الإجراء';
    } else if (error.status === 403) {
      message = 'ليس لديك صلاحية لهذا الإجراء';
    } else if (error.status === 404) {
      message = 'العنصر المطلوب غير موجود';
    } else if (error.status === 500) {
      message = 'خطأ في الخادم';
    } else if (error.message) {
      message = error.message;
    }
    
    console.error('API Error:', error);
    
    // يمكن إضافة عرض الرسالة في واجهة المستخدم هنا
    if (window.showNotification) {
      window.showNotification(message, 'error');
    } else {
      alert(message);
    }
    
    return message;
  },

  /**
   * معالجة استجابة API الناجحة
   * @param {Object} response - الاستجابة
   * @param {string} successMessage - رسالة النجاح
   */
  handleSuccess(response, successMessage = 'تم بنجاح') {
    console.log('API Success:', response);
    
    if (window.showNotification) {
      window.showNotification(successMessage, 'success');
    }
    
    return response;
  }
};
