/**
 * وحدة API للتعامل مع الخادم - النسخة المحدثة
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 * تطوير: محمد مرزوق العقاب
 */

class APIClient {
  constructor(baseURL = '/api') {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
  }

  /**
   * إرسال طلب HTTP (وضع عدم الاتصال)
   * @param {string} method - نوع الطلب (GET, POST, PUT, DELETE)
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} data - البيانات المرسلة
   * @param {Object} options - خيارات إضافية
   */
  async request(method, endpoint, data = null, options = {}) {
    console.log(`📡 ${method.toUpperCase()} ${endpoint} (وضع عدم الاتصال)`, data);

    // محاكاة تأخير الشبكة
    await new Promise(resolve => setTimeout(resolve, 500));

    // إرجاع بيانات تجريبية حسب نوع الطلب
    return this.getMockResponse(method, endpoint, data);
  }

  /**
   * الحصول على استجابة تجريبية
   * @param {string} method - نوع الطلب
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} data - البيانات
   */
  getMockResponse(method, endpoint, data) {
    // بيانات تجريبية للموردين
    if (endpoint.includes('/suppliers')) {
      if (method === 'GET') {
        return {
          success: true,
          data: [
            { id: 1, name: 'شركة البهارات الذهبية', category: 'بهارات', phone: '99887766' },
            { id: 2, name: 'مؤسسة المواد الاستهلاكية', category: 'استهلاكي', phone: '99776655' },
            { id: 3, name: 'شركة الأجبان الطازجة', category: 'أجبان', phone: '99665544' }
          ]
        };
      }
      return { success: true, message: 'تم بنجاح' };
    }

    // بيانات تجريبية للعقود
    if (endpoint.includes('/contracts')) {
      if (endpoint.includes('/files')) {
        return {
          success: true,
          data: [
            {
              id: "1",
              fileName: "1) شركة البهارات الذهبية.pdf",
              filePath: "#",
              companyName: "شركة البهارات الذهبية",
              contractNumber: "1",
              fileSize: 245,
              lastModified: new Date('2024-01-15'),
              createdAt: new Date('2024-01-15')
            }
          ],
          count: 67
        };
      }

      if (method === 'GET') {
        return {
          success: true,
          data: [
            { id: 1, supplier_id: 1, type: 'توريد', status: 'active', start_date: '2024-01-01', end_date: '2024-12-31' },
            { id: 2, supplier_id: 2, type: 'إيجار', status: 'active', start_date: '2024-01-01', end_date: '2024-12-31' }
          ]
        };
      }
      return { success: true, message: 'تم بنجاح' };
    }

    // بيانات تجريبية للعيون
    if (endpoint.includes('/eyes')) {
      if (method === 'GET') {
        return {
          success: true,
          data: [
            { id: 1, table_number: 1, section: 1, status: 'rented', monthly_rent: 60 },
            { id: 2, table_number: 1, section: 2, status: 'rented', monthly_rent: 60 },
            { id: 3, table_number: 1, section: 3, status: 'available', monthly_rent: 60 }
          ]
        };
      }
      return { success: true, message: 'تم بنجاح' };
    }

    // استيراد الموردين
    if (endpoint.includes('/import/suppliers')) {
      return {
        success: true,
        message: 'تم استيراد الموردين بنجاح',
        processed: 2201,
        errors: 0,
        total: 2201
      };
    }

    // استجابة افتراضية
    return { success: true, message: 'تم بنجاح (وضع عدم الاتصال)' };
  }

  /**
   * طلب GET
   */
  async get(endpoint, params = {}, options = {}) {
    const url = new URL(endpoint, window.location.origin + this.baseURL);
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined) {
        url.searchParams.append(key, params[key]);
      }
    });
    
    return this.request('GET', url.pathname + url.search, null, options);
  }

  /**
   * طلب POST
   */
  async post(endpoint, data = {}, options = {}) {
    return this.request('POST', endpoint, data, options);
  }

  /**
   * طلب PUT
   */
  async put(endpoint, data = {}, options = {}) {
    return this.request('PUT', endpoint, data, options);
  }

  /**
   * طلب DELETE
   */
  async delete(endpoint, options = {}) {
    return this.request('DELETE', endpoint, null, options);
  }
}

// إنشاء مثيل عام من API Client
const api = new APIClient();

// ==================== دوال API محددة ====================

/**
 * دوال الموردين
 */
const SuppliersAPI = {
  // جلب جميع الموردين
  async getAll(filters = {}) {
    return api.get('/suppliers', filters);
  },

  // جلب مورد واحد
  async getById(id) {
    return api.get(`/suppliers/${id}`);
  },

  // إضافة مورد جديد
  async create(supplierData) {
    return api.post('/suppliers', supplierData);
  },

  // تحديث مورد
  async update(id, supplierData) {
    return api.put(`/suppliers/${id}`, supplierData);
  },

  // حذف مورد
  async delete(id) {
    return api.delete(`/suppliers/${id}`);
  },

  // استيراد الموردين
  async import() {
    return api.post('/import/suppliers');
  }
};

/**
 * دوال العقود
 */
const ContractsAPI = {
  // جلب جميع العقود
  async getAll(filters = {}) {
    return api.get('/contracts', filters);
  },

  // جلب ملفات العقود PDF
  async getFiles() {
    return api.get('/contracts/files');
  },

  // جلب عقد واحد
  async getById(id) {
    return api.get(`/contracts/${id}`);
  },

  // إضافة عقد جديد
  async create(contractData) {
    return api.post('/contracts', contractData);
  },

  // تحديث عقد
  async update(id, contractData) {
    return api.put(`/contracts/${id}`, contractData);
  },

  // حذف عقد
  async delete(id) {
    return api.delete(`/contracts/${id}`);
  }
};

/**
 * دوال العيون
 */
const EyesAPI = {
  // جلب جميع العيون
  async getAll(filters = {}) {
    return api.get('/eyes', filters);
  },

  // جلب عين واحدة
  async getById(id) {
    return api.get(`/eyes/${id}`);
  },

  // إضافة عين جديدة
  async create(eyeData) {
    return api.post('/eyes', eyeData);
  },

  // تحديث عين
  async update(id, eyeData) {
    return api.put(`/eyes/${id}`, eyeData);
  },

  // حذف عين
  async delete(id) {
    return api.delete(`/eyes/${id}`);
  }
};

/**
 * دوال التقارير
 */
const ReportsAPI = {
  // تقرير الموردين
  async suppliers(format = 'json') {
    return api.get('/reports/suppliers', { format });
  },

  // تقرير العقود
  async contracts(format = 'json') {
    return api.get('/reports/contracts', { format });
  },

  // تقرير العيون
  async eyes(format = 'json') {
    return api.get('/reports/eyes', { format });
  },

  // إحصائيات عامة
  async dashboard() {
    return api.get('/reports/dashboard');
  }
};

/**
 * دوال رفع الملفات
 */
const FilesAPI = {
  // رفع ملف
  async upload(file, type = 'suppliers') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);
    
    return api.post('/upload', formData);
  },

  // تحميل ملف
  async download(filename) {
    const response = await fetch(`/contracts-files/${encodeURIComponent(filename)}`);
    if (!response.ok) {
      throw new Error('خطأ في تحميل الملف');
    }
    return response.blob();
  }
};

/**
 * دوال المصادقة
 */
const AuthAPI = {
  // تسجيل الدخول
  async login(credentials) {
    return api.post('/auth/login', credentials);
  },

  // تسجيل الخروج
  async logout() {
    return api.post('/auth/logout');
  },

  // التحقق من الجلسة
  async checkSession() {
    return api.get('/auth/me');
  }
};

// تصدير APIs للاستخدام العام
window.API = {
  client: api,
  suppliers: SuppliersAPI,
  contracts: ContractsAPI,
  eyes: EyesAPI,
  reports: ReportsAPI,
  files: FilesAPI,
  auth: AuthAPI
};

// دوال مساعدة للتعامل مع الأخطاء
window.APIHelpers = {
  /**
   * معالجة أخطاء API وعرضها للمستخدم
   * @param {Error} error - الخطأ
   * @param {string} defaultMessage - رسالة افتراضية
   */
  handleError(error, defaultMessage = 'حدث خطأ غير متوقع') {
    let message = defaultMessage;
    
    if (error.status === 400) {
      message = 'بيانات غير صحيحة';
    } else if (error.status === 401) {
      message = 'غير مصرح لك بهذا الإجراء';
    } else if (error.status === 403) {
      message = 'ليس لديك صلاحية لهذا الإجراء';
    } else if (error.status === 404) {
      message = 'العنصر المطلوب غير موجود';
    } else if (error.status === 500) {
      message = 'خطأ في الخادم';
    } else if (error.message) {
      message = error.message;
    }
    
    console.error('API Error:', error);
    
    // يمكن إضافة عرض الرسالة في واجهة المستخدم هنا
    if (window.showNotification) {
      window.showNotification(message, 'error');
    } else {
      alert(message);
    }
    
    return message;
  },

  /**
   * معالجة استجابة API الناجحة
   * @param {Object} response - الاستجابة
   * @param {string} successMessage - رسالة النجاح
   */
  handleSuccess(response, successMessage = 'تم بنجاح') {
    console.log('API Success:', response);
    
    if (window.showNotification) {
      window.showNotification(successMessage, 'success');
    }
    
    return response;
  }
};
