/**
 * وحدة إدارة قاعدة البيانات
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 */

const mysql = require('mysql2/promise');
const logger = require('./logger');

class Database {
  constructor() {
    this.pool = null;
    this.config = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'contracts_app',
      password: process.env.DB_PASSWORD || 'SecurePassword123!',
      database: process.env.DB_NAME || 'supplier_contracts',
      charset: 'utf8mb4',
      timezone: '+03:00', // توقيت الكويت
      acquireTimeout: 60000,
      timeout: 60000,
      reconnect: true,
      connectionLimit: 10,
      queueLimit: 0,
      ssl: process.env.DB_SSL === 'true' ? {
        rejectUnauthorized: false
      } : false
    };
    
    this.initializePool();
  }

  /**
   * تهيئة مجموعة الاتصالات
   */
  initializePool() {
    try {
      this.pool = mysql.createPool(this.config);
      
      // معالجة أحداث المجموعة
      this.pool.on('connection', (connection) => {
        logger.info(`اتصال جديد بقاعدة البيانات: ${connection.threadId}`);
      });

      this.pool.on('error', (error) => {
        logger.error('خطأ في مجموعة اتصالات قاعدة البيانات:', error);
        
        if (error.code === 'PROTOCOL_CONNECTION_LOST') {
          logger.info('إعادة تهيئة مجموعة الاتصالات...');
          this.initializePool();
        }
      });

      logger.info('تم تهيئة مجموعة اتصالات قاعدة البيانات');
    } catch (error) {
      logger.error('فشل في تهيئة قاعدة البيانات:', error);
      throw error;
    }
  }

  /**
   * اختبار الاتصال بقاعدة البيانات
   */
  async testConnection() {
    try {
      const connection = await this.pool.getConnection();
      await connection.ping();
      connection.release();
      logger.info('تم اختبار الاتصال بقاعدة البيانات بنجاح');
      return true;
    } catch (error) {
      logger.error('فشل في اختبار الاتصال بقاعدة البيانات:', error);
      throw error;
    }
  }

  /**
   * تنفيذ استعلام SQL
   * @param {string} sql - استعلام SQL
   * @param {Array} params - معاملات الاستعلام
   * @returns {Promise} نتيجة الاستعلام
   */
  async query(sql, params = []) {
    let connection;
    try {
      connection = await this.pool.getConnection();
      const [results] = await connection.execute(sql, params);
      
      logger.debug('تم تنفيذ الاستعلام:', {
        sql: sql.substring(0, 100) + (sql.length > 100 ? '...' : ''),
        paramsCount: params.length,
        resultsCount: Array.isArray(results) ? results.length : 1
      });
      
      return results;
    } catch (error) {
      logger.error('خطأ في تنفيذ الاستعلام:', {
        error: error.message,
        sql: sql.substring(0, 100) + (sql.length > 100 ? '...' : ''),
        params: params
      });
      throw error;
    } finally {
      if (connection) {
        connection.release();
      }
    }
  }

  /**
   * تنفيذ معاملة قاعدة بيانات
   * @param {Function} callback - دالة المعاملة
   * @returns {Promise} نتيجة المعاملة
   */
  async transaction(callback) {
    let connection;
    try {
      connection = await this.pool.getConnection();
      await connection.beginTransaction();
      
      const result = await callback(connection);
      
      await connection.commit();
      logger.debug('تم تنفيذ المعاملة بنجاح');
      
      return result;
    } catch (error) {
      if (connection) {
        await connection.rollback();
        logger.error('تم التراجع عن المعاملة:', error);
      }
      throw error;
    } finally {
      if (connection) {
        connection.release();
      }
    }
  }

  /**
   * إدراج سجل جديد
   * @param {string} table - اسم الجدول
   * @param {Object} data - البيانات المراد إدراجها
   * @returns {Promise} معرف السجل الجديد
   */
  async insert(table, data) {
    const fields = Object.keys(data);
    const values = Object.values(data);
    const placeholders = fields.map(() => '?').join(', ');
    
    const sql = `INSERT INTO ${table} (${fields.join(', ')}) VALUES (${placeholders})`;
    
    try {
      const result = await this.query(sql, values);
      return result.insertId;
    } catch (error) {
      logger.error(`خطأ في إدراج البيانات في جدول ${table}:`, error);
      throw error;
    }
  }

  /**
   * تحديث سجل موجود
   * @param {string} table - اسم الجدول
   * @param {Object} data - البيانات المراد تحديثها
   * @param {Object} where - شروط التحديث
   * @returns {Promise} عدد السجلات المحدثة
   */
  async update(table, data, where) {
    const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
    const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
    
    const sql = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`;
    const values = [...Object.values(data), ...Object.values(where)];
    
    try {
      const result = await this.query(sql, values);
      return result.affectedRows;
    } catch (error) {
      logger.error(`خطأ في تحديث البيانات في جدول ${table}:`, error);
      throw error;
    }
  }

  /**
   * حذف سجل
   * @param {string} table - اسم الجدول
   * @param {Object} where - شروط الحذف
   * @returns {Promise} عدد السجلات المحذوفة
   */
  async delete(table, where) {
    const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
    const sql = `DELETE FROM ${table} WHERE ${whereClause}`;
    const values = Object.values(where);
    
    try {
      const result = await this.query(sql, values);
      return result.affectedRows;
    } catch (error) {
      logger.error(`خطأ في حذف البيانات من جدول ${table}:`, error);
      throw error;
    }
  }

  /**
   * البحث عن سجلات
   * @param {string} table - اسم الجدول
   * @param {Object} options - خيارات البحث
   * @returns {Promise} نتائج البحث
   */
  async find(table, options = {}) {
    let sql = `SELECT `;
    
    // تحديد الحقول
    if (options.select) {
      sql += Array.isArray(options.select) ? options.select.join(', ') : options.select;
    } else {
      sql += '*';
    }
    
    sql += ` FROM ${table}`;
    
    const values = [];
    
    // شروط WHERE
    if (options.where) {
      const whereClause = Object.keys(options.where).map(key => `${key} = ?`).join(' AND ');
      sql += ` WHERE ${whereClause}`;
      values.push(...Object.values(options.where));
    }
    
    // ترتيب النتائج
    if (options.orderBy) {
      sql += ` ORDER BY ${options.orderBy}`;
      if (options.order) {
        sql += ` ${options.order}`;
      }
    }
    
    // تحديد عدد النتائج
    if (options.limit) {
      sql += ` LIMIT ${options.limit}`;
      if (options.offset) {
        sql += ` OFFSET ${options.offset}`;
      }
    }
    
    try {
      return await this.query(sql, values);
    } catch (error) {
      logger.error(`خطأ في البحث في جدول ${table}:`, error);
      throw error;
    }
  }

  /**
   * البحث عن سجل واحد
   * @param {string} table - اسم الجدول
   * @param {Object} where - شروط البحث
   * @param {Array} select - الحقول المطلوبة
   * @returns {Promise} السجل الأول أو null
   */
  async findOne(table, where, select = '*') {
    const results = await this.find(table, {
      where,
      select,
      limit: 1
    });
    
    return results.length > 0 ? results[0] : null;
  }

  /**
   * عد السجلات
   * @param {string} table - اسم الجدول
   * @param {Object} where - شروط العد
   * @returns {Promise} عدد السجلات
   */
  async count(table, where = {}) {
    let sql = `SELECT COUNT(*) as count FROM ${table}`;
    const values = [];
    
    if (Object.keys(where).length > 0) {
      const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
      sql += ` WHERE ${whereClause}`;
      values.push(...Object.values(where));
    }
    
    try {
      const result = await this.query(sql, values);
      return result[0].count;
    } catch (error) {
      logger.error(`خطأ في عد السجلات في جدول ${table}:`, error);
      throw error;
    }
  }

  /**
   * تنفيذ إجراء مخزن
   * @param {string} procedureName - اسم الإجراء
   * @param {Array} params - معاملات الإجراء
   * @returns {Promise} نتيجة الإجراء
   */
  async callProcedure(procedureName, params = []) {
    const placeholders = params.map(() => '?').join(', ');
    const sql = `CALL ${procedureName}(${placeholders})`;
    
    try {
      return await this.query(sql, params);
    } catch (error) {
      logger.error(`خطأ في تنفيذ الإجراء ${procedureName}:`, error);
      throw error;
    }
  }

  /**
   * إغلاق مجموعة الاتصالات
   */
  async close() {
    if (this.pool) {
      await this.pool.end();
      logger.info('تم إغلاق مجموعة اتصالات قاعدة البيانات');
    }
  }

  /**
   * الحصول على إحصائيات الاتصالات
   */
  getPoolStats() {
    if (!this.pool) return null;
    
    return {
      totalConnections: this.pool._allConnections.length,
      freeConnections: this.pool._freeConnections.length,
      acquiringConnections: this.pool._acquiringConnections.length,
      connectionLimit: this.config.connectionLimit
    };
  }
}

// إنشاء مثيل واحد من قاعدة البيانات
const database = new Database();

module.exports = database;
