# 📁 دليل رفع ملفات الموردين
## نظام إدارة عقود الموردين والإيجارات - جمعية المنقف

---

## 🎯 نظرة عامة

نظام رفع ملفات الموردين يتيح لك إضافة عدد كبير من الموردين إلى النظام بشكل سريع وفعال من خلال رفع ملفات Excel أو CSV.

---

## 🚀 كيفية الوصول

### الطريقة الأولى - من صفحة التشغيل:
1. افتح `launch-interface.html`
2. انقر على "رفع ملفات الموردين"

### الطريقة الثانية - مباشرة:
```
http://localhost:3000/upload_suppliers.html
```

---

## 📋 خطوات الرفع

### 1. تحضير الملف

#### تحميل النموذج:
- انقر على زر "تحميل النموذج" 
- سيتم تحميل ملف Excel يحتوي على أمثلة
- استخدم هذا الملف كقالب لبياناتك

#### تنسيق الملف:
- **أنواع الملفات المدعومة**: Excel (.xlsx, .xls) أو CSV
- **الترميز**: UTF-8 للنصوص العربية
- **الصف الأول**: يجب أن يحتوي على أسماء الأعمدة

### 2. الأعمدة المطلوبة

| العمود | الحالة | الوصف | مثال |
|--------|--------|-------|------|
| **اسم المورد** | 🔴 مطلوب | اسم الشركة أو المورد | شركة البهارات الذهبية |
| **الفئة** | 🔴 مطلوب | فئة المورد | بهارات / استهلاكي / أجبان |
| **رقم الهاتف** | 🔴 مطلوب | رقم الاتصال | +965-12345678 |
| **البريد الإلكتروني** | 🟡 اختياري | عنوان البريد | <EMAIL> |
| **العنوان** | 🟡 اختياري | العنوان الكامل | الكويت - حولي |
| **الشخص المسؤول** | 🟡 اختياري | اسم الشخص المسؤول | أحمد محمد |

### 3. الفئات المتاحة

- **بهارات**: للموردين المتخصصين في البهارات والتوابل
- **استهلاكي**: للمواد الاستهلاكية والمنتجات العامة
- **أجبان**: للموردين المتخصصين في الأجبان ومنتجات الألبان

### 4. رفع الملف

#### طريقة السحب والإفلات:
1. اسحب الملف من مجلد الكمبيوتر
2. أفلته في منطقة الرفع المخصصة
3. سيظهر الملف في قائمة الملفات المحددة

#### طريقة النقر:
1. انقر على منطقة الرفع
2. اختر الملف من نافذة التصفح
3. انقر "فتح"

### 5. مراجعة الملفات

- ستظهر قائمة بالملفات المحددة
- يمكنك حذف أي ملف بالنقر على زر الحذف
- تأكد من صحة أسماء الملفات وأحجامها

### 6. بدء الرفع

1. انقر على زر "رفع الملفات"
2. ستظهر شريط التقدم
3. انتظر حتى اكتمال العملية

---

## 📊 فهم النتائج

### إحصائيات الرفع:

#### 🟢 **تم بنجاح**
- عدد الموردين الذين تم إضافتهم بنجاح
- هؤلاء الموردين أصبحوا متاحين في النظام

#### 🔴 **فشل**
- عدد الصفوف التي تحتوي على أخطاء
- سيتم عرض تفاصيل الأخطاء لكل صف

#### 🟡 **مكرر**
- عدد الموردين الموجودين بالفعل في النظام
- يتم التحقق من التكرار بناءً على الاسم أو رقم الهاتف

#### 🔵 **إجمالي**
- العدد الكلي للصفوف في الملف

### تقرير الأخطاء:

إذا كانت هناك أخطاء، يمكنك:
1. النقر على "تحميل تقرير الأخطاء"
2. سيتم تحميل ملف Excel يحتوي على:
   - رقم الصف الذي يحتوي على خطأ
   - البيانات الأصلية
   - وصف الخطأ
3. قم بإصلاح الأخطاء وأعد رفع الملف

---

## ⚠️ الأخطاء الشائعة وحلولها

### 1. "الحقل مطلوب"
**السبب**: حقل مطلوب فارغ
**الحل**: تأكد من ملء جميع الحقول المطلوبة (اسم المورد، الفئة، رقم الهاتف)

### 2. "الفئة غير صحيحة"
**السبب**: فئة غير موجودة في القائمة المسموحة
**الحل**: استخدم إحدى الفئات: بهارات، استهلاكي، أجبان

### 3. "رقم الهاتف غير صحيح"
**السبب**: رقم الهاتف يحتوي على أحرف غير مسموحة
**الحل**: استخدم أرقام وعلامات (+، -، مسافات، أقواس) فقط

### 4. "البريد الإلكتروني غير صحيح"
**السبب**: تنسيق البريد الإلكتروني خاطئ
**الحل**: تأكد من التنسيق الصحيح: <EMAIL>

### 5. "مورد موجود بالفعل"
**السبب**: مورد بنفس الاسم أو رقم الهاتف موجود
**الحل**: تحقق من قاعدة البيانات أو غير الاسم/رقم الهاتف

---

## 🔧 نصائح للاستخدام الأمثل

### تحضير البيانات:
1. **نظف البيانات**: تأكد من عدم وجود مسافات زائدة
2. **وحد التنسيق**: استخدم تنسيق موحد لأرقام الهواتف
3. **تحقق من الفئات**: تأكد من استخدام الفئات الصحيحة
4. **اختبر عينة صغيرة**: ابدأ بملف صغير للتأكد من التنسيق

### أثناء الرفع:
1. **لا تغلق الصفحة**: انتظر حتى اكتمال العملية
2. **تحقق من الاتصال**: تأكد من استقرار الاتصال بالإنترنت
3. **راقب التقدم**: تابع شريط التقدم والرسائل

### بعد الرفع:
1. **راجع النتائج**: تحقق من الإحصائيات
2. **اصلح الأخطاء**: حمل تقرير الأخطاء وأصلحها
3. **تحقق من البيانات**: راجع الموردين المضافين في النظام

---

## 📱 اختصارات لوحة المفاتيح

- **Ctrl + U**: رفع الملفات
- **Ctrl + D**: تحميل النموذج
- **Escape**: مسح الملفات المحددة

---

## 🔒 الأمان والخصوصية

### حماية البيانات:
- جميع الملفات المرفوعة محمية بتشفير
- يتم حذف الملفات المؤقتة تلقائياً بعد المعالجة
- لا يتم حفظ البيانات الحساسة في السجلات

### صلاحيات الوصول:
- يتطلب تسجيل الدخول للوصول لنظام الرفع
- يتم تسجيل جميع عمليات الرفع في سجل النظام
- يمكن للمديرين فقط الوصول لسجلات الرفع

---

## 🆘 الحصول على المساعدة

### إذا واجهت مشاكل:

1. **تحقق من الاتصال**: تأكد من عمل الخادم
2. **راجع تنسيق الملف**: تأكد من صحة التنسيق
3. **جرب ملف أصغر**: قلل عدد الصفوف للاختبار
4. **تحقق من السجلات**: راجع رسائل الخطأ في المتصفح

### للدعم التقني:
- 📧 **البريد الإلكتروني**: <EMAIL>
- 📱 **الهاتف**: +965-1234567
- 🌐 **الموقع**: https://manqaf-coop.com

---

## 📈 إحصائيات الأداء

### حدود النظام:
- **حجم الملف**: حد أقصى 10 ميجابايت
- **عدد الملفات**: حد أقصى 5 ملفات في المرة الواحدة
- **عدد الصفوف**: يُنصح بأقل من 1000 صف لكل ملف
- **وقت المعالجة**: حوالي 1-2 ثانية لكل 100 صف

### نصائح الأداء:
- قسم الملفات الكبيرة إلى ملفات أصغر
- ارفع الملفات في أوقات أقل ازدحاماً
- تأكد من سرعة الاتصال المناسبة

---

## 🔄 التحديثات المستقبلية

### قيد التطوير:
- دعم المزيد من تنسيقات الملفات
- معاينة البيانات قبل الرفع
- جدولة الرفع التلقائي
- تكامل مع أنظمة خارجية

### مخطط لها:
- رفع الصور والمرفقات
- استيراد من قواعد بيانات أخرى
- تصدير قوالب مخصصة
- واجهة برمجة تطبيقات للرفع

---

**تم إعداد هذا الدليل بـ ❤️ لجمعية المنقف التعاونية**

**للمزيد من المساعدة، راجع الوثائق الأخرى أو اتصل بفريق الدعم**
