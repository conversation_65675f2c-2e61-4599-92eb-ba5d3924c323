{"name": "supplier-contracts-system", "version": "2.0.0", "description": "نظام إدارة عقود الموردين والإيجارات المتطور - جمعية المنقف التعاونية - تطوير محمد مرزوق العقاب", "main": "electron/main.js", "scripts": {"start": "node backend/api/local-server.js", "dev": "nodemon backend/api/server.js", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "webpack --mode production", "build:backend": "babel backend -d dist", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.html", "lint:fix": "eslint . --ext .js,.html --fix", "setup": "npm install && npm run setup:db", "setup:db": "node scripts/setup-database.js", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "backup": "node scripts/backup.js", "deploy": "npm run build && npm run deploy:production", "interfaces": "echo 'Opening interfaces launcher...'", "ultimate": "echo 'Ultimate interface available at http://localhost:3000/ultimate_interface.html'", "professional": "echo 'Professional interface available at http://localhost:3000/professional_interface.html'", "test:upload": "node test-upload-system.js", "test:interactive": "node run-test.js", "launch:professional": "node start-system.js", "interface:professional-ultimate": "echo 'Professional Ultimate Interface: http://localhost:3000/professional_ultimate_interface.html'", "interface:advanced-management": "echo 'Advanced Management Interface: http://localhost:3000/advanced_management_interface.html'", "interface:upload": "echo 'Upload System: http://localhost:3000/upload_suppliers.html'", "interface:test": "echo 'Quick Test: http://localhost:3000/quick-upload-test.html'", "interface:launch": "echo 'Launch Interface: http://localhost:3000/launch-interface.html'", "local": "node backend/api/local-server.js", "local:dev": "nodemon backend/api/local-server.js", "offline": "node backend/api/local-server.js", "standalone": "node backend/api/local-server.js", "create:data": "node backend/utils/create-excel-files.js", "quick:start": "npm run create:data && npm start", "electron": "electron desktop/main.js", "electron:dev": "electron . --dev", "electron:build": "electron-builder", "electron:dist": "npm run build && electron-builder", "app": "electron .", "desktop": "electron .", "windows": "electron .", "simple": "echo 'Simple interface available at http://localhost:3000/web_interface.html'", "launch": "node start-system.js", "deploy:production": "pm2 start ecosystem.config.js --env production", "docker:build": "docker build -t supplier-contracts .", "docker:run": "docker-compose up -d", "mobile:android": "cd mobile && react-native run-android", "mobile:ios": "cd mobile && react-native run-ios"}, "homepage": "https://contracts.manqaf-coop.com", "build": {"appId": "com.manqaf.contracts", "productName": "نظام إدارة العقود", "directories": {"output": "dist/electron"}, "files": ["desktop/**/*", "frontend/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.business"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}, "keywords": ["contracts", "suppliers", "management", "arabic", "professional", "glass-morphism", "high-contrast", "kuwait", "manqaf", "kuwait", "cooperative"], "author": {"name": "جمعية المنقف التعاونية", "email": "<EMAIL>", "url": "https://manqaf-coop.com"}, "developer": {"name": "محمد مرزوق العقاب", "title": "مطور نظم معلومات محترف ومتخصص", "email": "<EMAIL>", "linkedin": "https://linkedin.com/in/moham<PERSON>-al<PERSON><PERSON>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/manqaf-coop/supplier-contracts-system.git"}, "bugs": {"url": "https://github.com/manqaf-coop/supplier-contracts-system/issues"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.0", "sequelize": "^6.32.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.4", "nodemailer": "^6.9.4", "puppeteer": "^21.0.3", "exceljs": "^4.3.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "joi": "^17.9.2", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "winston": "^3.10.0", "redis": "^4.6.7", "socket.io": "^4.7.2", "cron": "^2.4.1", "archiver": "^5.3.1", "qrcode": "^1.5.3", "uuid": "^9.0.0", "xlsx": "^0.18.5", "csv-parser": "^3.0.0", "sqlite3": "^5.1.6", "electron": "^28.0.0", "electron-builder": "^24.6.4", "auto-updater": "^1.0.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.45.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.27.5", "prettier": "^3.0.0", "husky": "^8.0.3", "lint-staged": "^13.2.3", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "babel-loader": "^9.1.3", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "html-webpack-plugin": "^5.5.3", "css-loader": "^6.8.1", "style-loader": "^3.3.3", "file-loader": "^6.2.0", "electron": "^25.3.1", "electron-builder": "^24.6.3", "concurrently": "^8.2.0"}, "optionalDependencies": {"pm2": "^5.3.0", "docker": "^1.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "jest": {"testEnvironment": "node", "collectCoverageFrom": ["backend/**/*.js", "frontend/assets/js/**/*.js", "!**/node_modules/**", "!**/coverage/**"], "testMatch": ["**/tests/**/*.test.js"]}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"], "*.{html,css,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}, "config": {"port": 3000, "db_host": "localhost", "db_port": 3306, "db_name": "supplier_contracts", "redis_host": "localhost", "redis_port": 6379}}