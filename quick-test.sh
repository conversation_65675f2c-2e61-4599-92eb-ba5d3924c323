#!/bin/bash

# اختبار نظام رفع ملفات الموردين - جمعية المنقف

# ألوان للطباعة
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# دالة طباعة ملونة
print_color() {
    echo -e "${1}${2}${NC}"
}

# دالة عرض الرأس
show_header() {
    clear
    print_color $CYAN "🧪 اختبار نظام رفع ملفات الموردين"
    print_color $CYAN "جمعية المنقف التعاونية"
    print_color $CYAN "============================================"
    echo
}

# دالة عرض القائمة
show_menu() {
    print_color $YELLOW "اختر نوع الاختبار:"
    echo
    echo "1. اختبار شامل للنظام"
    echo "2. اختبار سريع للملفات"
    echo "3. تشغيل الخادم واختبار يدوي"
    echo "4. إنشاء ملفات اختبار فقط"
    echo "5. فتح صفحة الاختبار"
    echo "6. عرض حالة النظام"
    echo "7. تثبيت التبعيات"
    echo "0. خروج"
    echo
    read -p "اختر رقم (0-7): " choice
}

# اختبار شامل
full_test() {
    echo
    print_color $CYAN "🔄 تشغيل الاختبار الشامل..."
    echo
    
    if [ -f "test-upload-system.js" ]; then
        node test-upload-system.js
    else
        print_color $RED "❌ ملف الاختبار غير موجود"
    fi
    
    echo
    print_color $GREEN "✅ الاختبار الشامل اكتمل"
}

# اختبار سريع
quick_test() {
    echo
    print_color $CYAN "⚡ تشغيل الاختبار السريع..."
    echo
    
    print_color $YELLOW "📁 التحقق من الملفات الأساسية:"
    
    files=("upload_suppliers.html" "quick-upload-test.html" "backend/routes/upload.js" "package.json")
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            print_color $GREEN "   ✅ $file"
        else
            print_color $RED "   ❌ $file - غير موجود"
        fi
    done
    
    echo
    print_color $YELLOW "📦 التحقق من التبعيات:"
    
    if [ -d "node_modules" ]; then
        print_color $GREEN "   ✅ node_modules موجود"
        
        deps=("multer" "xlsx" "csv-parser")
        for dep in "${deps[@]}"; do
            if [ -d "node_modules/$dep" ]; then
                print_color $GREEN "   ✅ $dep"
            else
                print_color $RED "   ❌ $dep - غير مثبت"
            fi
        done
    else
        print_color $RED "   ❌ node_modules غير موجود"
        print_color $YELLOW "   💡 قم بتشغيل: npm install"
    fi
    
    echo
    print_color $GREEN "✅ الاختبار السريع اكتمل"
}

# تشغيل الخادم
server_test() {
    echo
    print_color $CYAN "🚀 تشغيل الخادم..."
    echo
    
    if [ ! -d "node_modules" ]; then
        print_color $RED "❌ node_modules غير موجود. قم بتشغيل: npm install"
        return
    fi
    
    print_color $YELLOW "💡 سيتم تشغيل الخادم وفتح صفحات الاختبار"
    print_color $YELLOW "💡 اضغط Ctrl+C لإيقاف الخادم"
    echo
    
    # فتح صفحات الاختبار
    if command -v xdg-open > /dev/null; then
        xdg-open "http://localhost:3000/quick-upload-test.html" 2>/dev/null &
        sleep 2
        xdg-open "http://localhost:3000/upload_suppliers.html" 2>/dev/null &
    elif command -v open > /dev/null; then
        open "http://localhost:3000/quick-upload-test.html" 2>/dev/null &
        sleep 2
        open "http://localhost:3000/upload_suppliers.html" 2>/dev/null &
    else
        print_color $YELLOW "💡 افتح يدوياً: http://localhost:3000/quick-upload-test.html"
    fi
    
    npm start
}

# إنشاء ملفات اختبار
create_files() {
    echo
    print_color $CYAN "📄 إنشاء ملفات الاختبار..."
    echo
    
    # إنشاء ملف CSV
    cat > test-suppliers.csv << EOF
اسم المورد,الفئة,رقم الهاتف,البريد الإلكتروني,العنوان,الشخص المسؤول
شركة الاختبار الأولى,بهارات,+965-11111111,<EMAIL>,الكويت - منطقة الاختبار,مدير الاختبار
مؤسسة الاختبار الثانية,استهلاكي,+965-22222222,<EMAIL>,الكويت - منطقة الاختبار 2,مدير الاختبار 2
شركة الاختبار الثالثة,أجبان,+965-33333333,<EMAIL>,الكويت - منطقة الاختبار 3,مدير الاختبار 3
مورد خاطئ للاختبار,فئة خاطئة,رقم خاطئ,بريد خاطئ,,
EOF
    
    print_color $GREEN "   ✅ تم إنشاء test-suppliers.csv"
    
    # إنشاء ملف Excel إذا كان XLSX متاح
    if [ -d "node_modules/xlsx" ]; then
        node -e "
        const XLSX = require('xlsx');
        const data = [
            {
                'اسم المورد': 'شركة الاختبار الأولى',
                'الفئة': 'بهارات',
                'رقم الهاتف': '+965-11111111',
                'البريد الإلكتروني': '<EMAIL>',
                'العنوان': 'الكويت - منطقة الاختبار',
                'الشخص المسؤول': 'مدير الاختبار'
            },
            {
                'اسم المورد': 'مؤسسة الاختبار الثانية',
                'الفئة': 'استهلاكي',
                'رقم الهاتف': '+965-22222222',
                'البريد الإلكتروني': '<EMAIL>',
                'العنوان': 'الكويت - منطقة الاختبار 2',
                'الشخص المسؤول': 'مدير الاختبار 2'
            },
            {
                'اسم المورد': 'شركة الاختبار الثالثة',
                'الفئة': 'أجبان',
                'رقم الهاتف': '+965-33333333',
                'البريد الإلكتروني': '<EMAIL>',
                'العنوان': 'الكويت - منطقة الاختبار 3',
                'الشخص المسؤول': 'مدير الاختبار 3'
            },
            {
                'اسم المورد': 'مورد خاطئ للاختبار',
                'الفئة': 'فئة خاطئة',
                'رقم الهاتف': 'رقم خاطئ',
                'البريد الإلكتروني': 'بريد خاطئ',
                'العنوان': '',
                'الشخص المسؤول': ''
            }
        ];
        const ws = XLSX.utils.json_to_sheet(data);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'الموردين');
        ws['!cols'] = [
            { width: 25 }, { width: 15 }, { width: 20 },
            { width: 30 }, { width: 25 }, { width: 20 }
        ];
        XLSX.writeFile(wb, 'test-suppliers.xlsx');
        " 2>/dev/null
        
        if [ $? -eq 0 ]; then
            print_color $GREEN "   ✅ تم إنشاء test-suppliers.xlsx"
        else
            print_color $YELLOW "   ⚠️ لم يتم إنشاء ملف Excel (خطأ في XLSX)"
        fi
    else
        print_color $YELLOW "   ⚠️ لم يتم إنشاء ملف Excel (XLSX غير متاح)"
    fi
    
    echo
    print_color $GREEN "📁 ملفات الاختبار جاهزة للاستخدام!"
}

# فتح صفحة الاختبار
open_page() {
    echo
    print_color $CYAN "🌐 فتح صفحة الاختبار..."
    echo
    
    url="http://localhost:3000/quick-upload-test.html"
    
    if command -v xdg-open > /dev/null; then
        xdg-open "$url" 2>/dev/null
        print_color $GREEN "✅ تم فتح صفحة الاختبار في المتصفح"
    elif command -v open > /dev/null; then
        open "$url" 2>/dev/null
        print_color $GREEN "✅ تم فتح صفحة الاختبار في المتصفح"
    else
        print_color $YELLOW "💡 افتح يدوياً: $url"
    fi
    
    print_color $YELLOW "💡 تأكد من تشغيل الخادم أولاً: npm start"
}

# عرض حالة النظام
system_status() {
    echo
    print_color $CYAN "📊 حالة النظام:"
    echo
    
    print_color $YELLOW "📁 الملفات:"
    files=("upload_suppliers.html" "quick-upload-test.html" "backend/routes/upload.js" "backend/api/server.js" "package.json")
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            print_color $GREEN "   ✅ $file"
        else
            print_color $RED "   ❌ $file"
        fi
    done
    
    echo
    print_color $YELLOW "📦 التبعيات:"
    if [ -d "node_modules" ]; then
        print_color $GREEN "   ✅ node_modules موجود"
        
        deps=("multer" "xlsx" "csv-parser")
        for dep in "${deps[@]}"; do
            if [ -d "node_modules/$dep" ]; then
                print_color $GREEN "   ✅ $dep"
            else
                print_color $RED "   ❌ $dep"
            fi
        done
    else
        print_color $RED "   ❌ node_modules غير موجود"
        print_color $YELLOW "   💡 قم بتشغيل: npm install"
    fi
    
    echo
    print_color $YELLOW "🧪 ملفات الاختبار:"
    test_files=("test-suppliers.csv" "test-suppliers.xlsx")
    
    for file in "${test_files[@]}"; do
        if [ -f "$file" ]; then
            print_color $GREEN "   ✅ $file"
        else
            print_color $RED "   ❌ $file"
        fi
    done
}

# تثبيت التبعيات
install_deps() {
    echo
    print_color $CYAN "📦 تثبيت التبعيات..."
    echo
    
    if [ ! -f "package.json" ]; then
        print_color $RED "❌ package.json غير موجود"
        return
    fi
    
    print_color $YELLOW "🔄 جاري تثبيت التبعيات..."
    npm install
    
    if [ $? -eq 0 ]; then
        print_color $GREEN "✅ تم تثبيت التبعيات بنجاح"
    else
        print_color $RED "❌ فشل في تثبيت التبعيات"
    fi
}

# الحلقة الرئيسية
main() {
    while true; do
        show_header
        show_menu
        
        case $choice in
            1)
                full_test
                ;;
            2)
                quick_test
                ;;
            3)
                server_test
                ;;
            4)
                create_files
                ;;
            5)
                open_page
                ;;
            6)
                system_status
                ;;
            7)
                install_deps
                ;;
            0)
                echo
                print_color $CYAN "👋 وداعاً!"
                echo
                exit 0
                ;;
            *)
                echo
                print_color $RED "❌ اختيار غير صحيح"
                ;;
        esac
        
        echo
        print_color $YELLOW "⏸️ اضغط Enter للمتابعة..."
        read
    done
}

# تشغيل البرنامج
main
