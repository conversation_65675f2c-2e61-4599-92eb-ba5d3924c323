/**
 * ملف JavaScript الرئيسي - النسخة المحدثة
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 * تطوير: محمد مرزوق العقاب
 * التقويم: ميلادي (محدث)
 */

// ==================== المتغيرات العامة ====================

let currentUser = null;
let currentPage = 'dashboard';
let searchTimeout = null;

// ==================== تهيئة التطبيق ====================

document.addEventListener('DOMContentLoaded', function() {
  console.log('🚀 تهيئة النظام المحدث...');
  console.log('👨‍💻 تطوير: محمد مرزوق العقاب');
  console.log('🏢 جمعية المنقف التعاونية');
  console.log('📅 التقويم: ميلادي');
  
  initializeApp();
});

/**
 * تهيئة التطبيق
 */
async function initializeApp() {
  try {
    // تحديث التاريخ والوقت
    updateDateTime();
    setInterval(updateDateTime, 1000);
    
    // تهيئة المكونات
    initializeComponents();
    
    // تحميل البيانات الأولية
    await loadInitialData();
    
    // تهيئة أحداث الصفحة
    setupEventListeners();
    
    console.log('✅ تم تهيئة النظام بنجاح');
    
  } catch (error) {
    console.error('❌ خطأ في تهيئة النظام:', error);
    showNotification('خطأ في تهيئة النظام', 'error');
  }
}

/**
 * تحديث التاريخ والوقت (ميلادي)
 */
function updateDateTime() {
  const now = new Date();
  
  // تنسيق التاريخ الميلادي
  const dateOptions = { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric',
    weekday: 'long'
  };
  
  const timeOptions = {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  };
  
  const currentDate = now.toLocaleDateString('en-GB', dateOptions);
  const currentTime = now.toLocaleTimeString('en-GB', timeOptions);
  
  // تحديث العناصر في الصفحة
  const dateElement = document.getElementById('currentDate');
  const timeElement = document.getElementById('currentTime');
  
  if (dateElement) dateElement.textContent = currentDate;
  if (timeElement) timeElement.textContent = currentTime;
}

/**
 * تهيئة المكونات
 */
function initializeComponents() {
  // تهيئة Flatpickr للتواريخ (ميلادي)
  if (typeof flatpickr !== 'undefined') {
    flatpickr('.date-picker', {
      locale: 'default', // استخدام التقويم الميلادي
      dateFormat: 'd/m/Y',
      allowInput: true,
      clickOpens: true
    });
  }
  
  // تهيئة الجداول
  initializeTables();
  
  // تهيئة النماذج
  initializeForms();
  
  // تهيئة البحث
  initializeSearch();
}

/**
 * تحميل البيانات الأولية
 */
async function loadInitialData() {
  try {
    // تحميل الإحصائيات
    await loadDashboardStats();
    
    // تحميل ملفات العقود
    await loadContractFiles();
    
  } catch (error) {
    console.error('خطأ في تحميل البيانات:', error);
  }
}

/**
 * تحميل إحصائيات لوحة التحكم
 */
async function loadDashboardStats() {
  try {
    // بيانات تجريبية للإحصائيات
    const stats = {
      suppliers: { total: 2201, active: 1987, inactive: 214 },
      contracts: { total: 67, active: 45, expired: 12, expiring: 10 },
      eyes: { total: 80, rented: 65, available: 15 },
      revenue: { monthly: 3900, yearly: 46800 }
    };
    
    updateDashboardStats(stats);
    
  } catch (error) {
    console.error('خطأ في تحميل الإحصائيات:', error);
  }
}

/**
 * تحديث إحصائيات لوحة التحكم
 */
function updateDashboardStats(stats) {
  // تحديث عدد الموردين
  const suppliersTotal = document.getElementById('suppliersTotal');
  if (suppliersTotal) suppliersTotal.textContent = stats.suppliers.total.toLocaleString();
  
  // تحديث عدد العقود
  const contractsTotal = document.getElementById('contractsTotal');
  if (contractsTotal) contractsTotal.textContent = stats.contracts.total;
  
  // تحديث عدد العيون
  const eyesTotal = document.getElementById('eyesTotal');
  if (eyesTotal) eyesTotal.textContent = stats.eyes.total;
  
  // تحديث الإيرادات
  const monthlyRevenue = document.getElementById('monthlyRevenue');
  if (monthlyRevenue) monthlyRevenue.textContent = stats.revenue.monthly.toLocaleString() + ' د.ك';
}

/**
 * تحميل ملفات العقود
 */
async function loadContractFiles() {
  try {
    const response = await API.contracts.getFiles();
    
    if (response.success) {
      displayContractFiles(response.data);
      updateContractFilesStats(response.count);
    }
    
  } catch (error) {
    console.error('خطأ في تحميل ملفات العقود:', error);
  }
}

/**
 * عرض ملفات العقود
 */
function displayContractFiles(files) {
  const container = document.getElementById('contractFilesList');
  if (!container) return;
  
  if (files.length === 0) {
    container.innerHTML = '<p class="text-center text-gray-400">لا توجد ملفات عقود</p>';
    return;
  }
  
  const html = files.map(file => `
    <div class="glass-card p-4 hover:bg-white/10 transition-all duration-300">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <i class="${Utils.getFileIcon(file.fileName)}"></i>
          <div>
            <h4 class="font-semibold text-white">${file.companyName}</h4>
            <p class="text-sm text-gray-300">عقد رقم: ${file.contractNumber}</p>
            <p class="text-xs text-gray-400">${Utils.formatFileSize(file.fileSize * 1024)}</p>
          </div>
        </div>
        <div class="flex gap-2">
          <button onclick="viewContractFile('${file.filePath}')" 
                  class="btn btn-primary btn-sm">
            <i class="fas fa-eye"></i> عرض
          </button>
          <button onclick="downloadContractFile('${file.fileName}')" 
                  class="btn btn-outline btn-sm">
            <i class="fas fa-download"></i> تحميل
          </button>
        </div>
      </div>
    </div>
  `).join('');
  
  container.innerHTML = html;
}

/**
 * تحديث إحصائيات ملفات العقود
 */
function updateContractFilesStats(count) {
  const statsElement = document.getElementById('contractFilesCount');
  if (statsElement) statsElement.textContent = count;
}

/**
 * عرض ملف عقد
 */
function viewContractFile(filePath) {
  window.open(filePath, '_blank');
}

/**
 * تحميل ملف عقد
 */
async function downloadContractFile(fileName) {
  try {
    const blob = await API.files.download(fileName);
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    
    showNotification('تم تحميل الملف بنجاح', 'success');
    
  } catch (error) {
    console.error('خطأ في تحميل الملف:', error);
    showNotification('خطأ في تحميل الملف', 'error');
  }
}

/**
 * تهيئة الجداول
 */
function initializeTables() {
  // إضافة وظائف الترتيب والتصفية للجداول
  const tables = document.querySelectorAll('.data-table');
  tables.forEach(table => {
    addTableSorting(table);
  });
}

/**
 * تهيئة النماذج
 */
function initializeForms() {
  // إضافة التحقق من صحة النماذج
  const forms = document.querySelectorAll('form');
  forms.forEach(form => {
    form.addEventListener('submit', handleFormSubmit);
  });
}

/**
 * تهيئة البحث
 */
function initializeSearch() {
  const searchInputs = document.querySelectorAll('.search-input');
  searchInputs.forEach(input => {
    input.addEventListener('input', Utils.debounce(handleSearch, 300));
  });
}

/**
 * معالجة البحث
 */
function handleSearch(event) {
  const searchTerm = event.target.value.trim();
  const searchType = event.target.dataset.searchType || 'general';
  
  console.log(`🔍 البحث: ${searchTerm} (نوع: ${searchType})`);
  
  // تنفيذ البحث حسب النوع
  switch (searchType) {
    case 'suppliers':
      searchSuppliers(searchTerm);
      break;
    case 'contracts':
      searchContracts(searchTerm);
      break;
    case 'files':
      searchContractFiles(searchTerm);
      break;
    default:
      performGeneralSearch(searchTerm);
  }
}

/**
 * البحث في ملفات العقود
 */
function searchContractFiles(searchTerm) {
  const filesList = document.getElementById('contractFilesList');
  if (!filesList) return;
  
  const fileCards = filesList.querySelectorAll('.glass-card');
  
  fileCards.forEach(card => {
    const companyName = card.querySelector('h4').textContent;
    const contractNumber = card.querySelector('p').textContent;
    
    const isMatch = companyName.includes(searchTerm) || 
                   contractNumber.includes(searchTerm);
    
    card.style.display = isMatch ? 'block' : 'none';
  });
}

/**
 * معالجة إرسال النماذج
 */
function handleFormSubmit(event) {
  event.preventDefault();
  
  const form = event.target;
  const formData = new FormData(form);
  const data = Object.fromEntries(formData.entries());
  
  console.log('📝 إرسال نموذج:', data);
  
  // معالجة النموذج حسب نوعه
  const formType = form.dataset.formType;
  
  switch (formType) {
    case 'supplier':
      handleSupplierForm(data);
      break;
    case 'contract':
      handleContractForm(data);
      break;
    case 'eye':
      handleEyeForm(data);
      break;
    default:
      console.log('نوع نموذج غير معروف');
  }
}

/**
 * تهيئة أحداث الصفحة
 */
function setupEventListeners() {
  // أحداث التنقل
  const navLinks = document.querySelectorAll('.nav-link');
  navLinks.forEach(link => {
    link.addEventListener('click', handleNavigation);
  });
  
  // أحداث الأزرار
  const buttons = document.querySelectorAll('[data-action]');
  buttons.forEach(button => {
    button.addEventListener('click', handleButtonClick);
  });
}

/**
 * معالجة التنقل
 */
function handleNavigation(event) {
  event.preventDefault();
  
  const target = event.target.closest('.nav-link');
  const page = target.dataset.page;
  
  if (page) {
    navigateToPage(page);
  }
}

/**
 * التنقل إلى صفحة
 */
function navigateToPage(page) {
  currentPage = page;
  
  // إخفاء جميع الأقسام
  const sections = document.querySelectorAll('.page-section');
  sections.forEach(section => {
    section.classList.add('hidden');
  });
  
  // إظهار القسم المطلوب
  const targetSection = document.getElementById(`${page}Section`);
  if (targetSection) {
    targetSection.classList.remove('hidden');
  }
  
  // تحديث التنقل النشط
  updateActiveNavigation(page);
  
  console.log(`📄 التنقل إلى: ${page}`);
}

/**
 * تحديث التنقل النشط
 */
function updateActiveNavigation(activePage) {
  const navLinks = document.querySelectorAll('.nav-link');
  navLinks.forEach(link => {
    if (link.dataset.page === activePage) {
      link.classList.add('active');
    } else {
      link.classList.remove('active');
    }
  });
}

/**
 * معالجة النقر على الأزرار
 */
function handleButtonClick(event) {
  const button = event.target.closest('[data-action]');
  const action = button.dataset.action;
  
  switch (action) {
    case 'import-suppliers':
      importSuppliers();
      break;
    case 'export-data':
      exportData(button.dataset.type);
      break;
    case 'refresh-data':
      refreshData();
      break;
    default:
      console.log(`إجراء غير معروف: ${action}`);
  }
}

/**
 * استيراد الموردين
 */
async function importSuppliers() {
  try {
    showNotification('جاري استيراد الموردين...', 'info');
    
    const response = await API.suppliers.import();
    
    if (response.success) {
      showNotification(`تم استيراد ${response.processed} مورد بنجاح`, 'success');
      await loadDashboardStats(); // تحديث الإحصائيات
    }
    
  } catch (error) {
    console.error('خطأ في استيراد الموردين:', error);
    showNotification('خطأ في استيراد الموردين', 'error');
  }
}

/**
 * تصدير البيانات
 */
function exportData(type) {
  console.log(`📤 تصدير البيانات: ${type}`);
  showNotification(`جاري تصدير ${type}...`, 'info');
  
  // تنفيذ التصدير حسب النوع
  // يمكن إضافة المزيد من المنطق هنا
}

/**
 * تحديث البيانات
 */
async function refreshData() {
  try {
    showNotification('جاري تحديث البيانات...', 'info');
    
    await loadInitialData();
    
    showNotification('تم تحديث البيانات بنجاح', 'success');
    
  } catch (error) {
    console.error('خطأ في تحديث البيانات:', error);
    showNotification('خطأ في تحديث البيانات', 'error');
  }
}

/**
 * عرض الإشعارات
 */
function showNotification(message, type = 'info') {
  console.log(`📢 ${type.toUpperCase()}: ${message}`);
  
  // يمكن إضافة نظام إشعارات مرئي هنا
  // مؤقتاً سنستخدم console.log
}

// تصدير الدوال للاستخدام العام
window.AppMain = {
  updateDateTime,
  loadContractFiles,
  viewContractFile,
  downloadContractFile,
  importSuppliers,
  exportData,
  refreshData,
  showNotification,
  navigateToPage
};
