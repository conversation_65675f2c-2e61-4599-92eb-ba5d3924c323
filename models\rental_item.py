#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج عناصر الإيجار
Rental Item Model for Contract Management System
"""

from datetime import datetime
from typing import Optional, List, Dict

class RentalItem:
    """نموذج عناصر الإيجار (الطبليات، القواطع، الجندولات، الاستنادات)"""
    
    def __init__(self, db_manager):
        """
        تهيئة نموذج عناصر الإيجار
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db_manager = db_manager
    
    def create_item(self, item_data: Dict) -> Optional[int]:
        """
        إنشاء عنصر إيجار جديد
        
        Args:
            item_data: بيانات العنصر
            
        Returns:
            معرف العنصر الجديد أو None في حالة الفشل
        """
        query = """
        INSERT INTO rental_items (
            item_type, item_name, location, area_sqm, status, description
        ) VALUES (?, ?, ?, ?, ?, ?)
        """
        
        params = (
            item_data.get('item_type'),
            item_data.get('item_name'),
            item_data.get('location'),
            item_data.get('area_sqm'),
            item_data.get('status', 'متاح'),
            item_data.get('description')
        )
        
        if self.db_manager.execute_query(query, params):
            result = self.db_manager.fetch_one("SELECT last_insert_rowid() as id")
            return result['id'] if result else None
        return None
    
    def get_item_by_id(self, item_id: int) -> Optional[Dict]:
        """
        الحصول على عنصر بالمعرف
        
        Args:
            item_id: معرف العنصر
            
        Returns:
            بيانات العنصر أو None
        """
        query = "SELECT * FROM rental_items WHERE item_id = ?"
        return self.db_manager.fetch_one(query, (item_id,))
    
    def get_all_items(self, item_type: Optional[str] = None, status: Optional[str] = None) -> List[Dict]:
        """
        الحصول على جميع العناصر
        
        Args:
            item_type: نوع العنصر (اختياري)
            status: حالة العنصر (اختياري)
            
        Returns:
            قائمة بالعناصر
        """
        query = "SELECT * FROM rental_items WHERE 1=1"
        params = []
        
        if item_type:
            query += " AND item_type = ?"
            params.append(item_type)
        
        if status:
            query += " AND status = ?"
            params.append(status)
        
        query += " ORDER BY item_type, item_name"
        return self.db_manager.fetch_query(query, tuple(params))
    
    def update_item(self, item_id: int, item_data: Dict) -> bool:
        """
        تحديث بيانات العنصر
        
        Args:
            item_id: معرف العنصر
            item_data: البيانات الجديدة
            
        Returns:
            True إذا نجح التحديث، False إذا فشل
        """
        query = """
        UPDATE rental_items SET
            item_type = ?, item_name = ?, location = ?, area_sqm = ?,
            status = ?, description = ?, updated_at = CURRENT_TIMESTAMP
        WHERE item_id = ?
        """
        
        params = (
            item_data.get('item_type'),
            item_data.get('item_name'),
            item_data.get('location'),
            item_data.get('area_sqm'),
            item_data.get('status'),
            item_data.get('description'),
            item_id
        )
        
        return self.db_manager.execute_query(query, params)
    
    def delete_item(self, item_id: int) -> bool:
        """
        حذف عنصر
        
        Args:
            item_id: معرف العنصر
            
        Returns:
            True إذا نجح الحذف، False إذا فشل
        """
        # التحقق من وجود عقود مرتبطة بالعنصر
        contracts = self.db_manager.fetch_query(
            "SELECT COUNT(*) as count FROM contract_items WHERE item_id = ?",
            (item_id,)
        )
        
        if contracts and contracts[0]['count'] > 0:
            # تغيير الحالة إلى غير متاح بدلاً من الحذف
            return self.update_item(item_id, {'status': 'غير متاح'})
        else:
            # حذف العنصر إذا لم توجد عقود مرتبطة
            query = "DELETE FROM rental_items WHERE item_id = ?"
            return self.db_manager.execute_query(query, (item_id,))
    
    def search_items(self, search_term: str) -> List[Dict]:
        """
        البحث في العناصر
        
        Args:
            search_term: مصطلح البحث
            
        Returns:
            قائمة بالعناصر المطابقة
        """
        query = """
        SELECT * FROM rental_items 
        WHERE item_name LIKE ? OR location LIKE ? OR description LIKE ?
        ORDER BY item_type, item_name
        """
        search_pattern = f"%{search_term}%"
        params = (search_pattern, search_pattern, search_pattern)
        return self.db_manager.fetch_query(query, params)
    
    def get_available_items(self, item_type: Optional[str] = None) -> List[Dict]:
        """
        الحصول على العناصر المتاحة
        
        Args:
            item_type: نوع العنصر (اختياري)
            
        Returns:
            قائمة بالعناصر المتاحة
        """
        query = "SELECT * FROM rental_items WHERE status = 'متاح'"
        params = []
        
        if item_type:
            query += " AND item_type = ?"
            params.append(item_type)
        
        query += " ORDER BY item_type, item_name"
        return self.db_manager.fetch_query(query, tuple(params))
    
    def get_rented_items(self) -> List[Dict]:
        """
        الحصول على العناصر المؤجرة حالياً
        
        Returns:
            قائمة بالعناصر المؤجرة مع تفاصيل العقود
        """
        query = """
        SELECT ri.*, ci.contract_id, ci.start_date, ci.end_date,
               c.contract_number, s.supplier_name
        FROM rental_items ri
        JOIN contract_items ci ON ri.item_id = ci.item_id
        JOIN contracts c ON ci.contract_id = c.contract_id
        JOIN suppliers s ON c.supplier_id = s.supplier_id
        WHERE ri.status = 'مؤجر' AND ci.status = 'نشط'
        ORDER BY ri.item_type, ri.item_name
        """
        return self.db_manager.fetch_query(query)
    
    def update_item_status(self, item_id: int, status: str) -> bool:
        """
        تحديث حالة العنصر
        
        Args:
            item_id: معرف العنصر
            status: الحالة الجديدة
            
        Returns:
            True إذا نجح التحديث، False إذا فشل
        """
        query = """
        UPDATE rental_items SET 
            status = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE item_id = ?
        """
        return self.db_manager.execute_query(query, (status, item_id))
    
    def get_item_history(self, item_id: int) -> List[Dict]:
        """
        الحصول على تاريخ العنصر (العقود السابقة)
        
        Args:
            item_id: معرف العنصر
            
        Returns:
            قائمة بتاريخ العقود للعنصر
        """
        query = """
        SELECT ci.*, c.contract_number, c.start_date as contract_start,
               c.end_date as contract_end, s.supplier_name
        FROM contract_items ci
        JOIN contracts c ON ci.contract_id = c.contract_id
        JOIN suppliers s ON c.supplier_id = s.supplier_id
        WHERE ci.item_id = ?
        ORDER BY ci.start_date DESC
        """
        return self.db_manager.fetch_query(query, (item_id,))
    
    def get_items_summary(self) -> Dict:
        """
        الحصول على ملخص العناصر
        
        Returns:
            ملخص بإحصائيات العناصر
        """
        # إجمالي العناصر
        total_query = "SELECT COUNT(*) as total FROM rental_items"
        total_result = self.db_manager.fetch_one(total_query)
        total = total_result['total'] if total_result else 0
        
        # العناصر حسب النوع
        type_query = """
        SELECT item_type, COUNT(*) as count 
        FROM rental_items 
        GROUP BY item_type
        """
        by_type = self.db_manager.fetch_query(type_query)
        
        # العناصر حسب الحالة
        status_query = """
        SELECT status, COUNT(*) as count 
        FROM rental_items 
        GROUP BY status
        """
        by_status = self.db_manager.fetch_query(status_query)
        
        return {
            'total': total,
            'by_type': {item['item_type']: item['count'] for item in by_type},
            'by_status': {item['status']: item['count'] for item in by_status}
        }
