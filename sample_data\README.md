# 📊 ملفات البيانات النموذجية
## نظام إدارة عقود الموردين والإيجارات

### 🏢 **جمعية المنقف التعاونية**
### 👨‍💻 **تطوير وتنفيذ: محمد مرزوق العقاب**

---

## 📁 محتويات المجلد

### 📄 **ملفات CSV:**
1. **`موردين_جمعية_المنقف.csv`** - بيانات 25 مورد
2. **`عقود_جمعية_المنقف.csv`** - بيانات 30 عقد
3. **`ايجارات_العيون_جمعية_المنقف.csv`** - بيانات 45 إيجار

### 📊 **ملفات Excel:**
1. **`بيانات_جمعية_المنقف_الشاملة.xlsx`** - ملف شامل (4 أوراق)
2. **`موردين_جمعية_المنقف.xlsx`** - الموردين فقط
3. **`عقود_جمعية_المنقف.xlsx`** - العقود فقط
4. **`ايجارات_العيون_جمعية_المنقف.xlsx`** - إيجارات العيون فقط

---

## 📊 إحصائيات البيانات

### 👥 **الموردين (25 مورد):**
- **🌶️ بهارات**: 10 موردين
- **🛒 استهلاكي**: 8 موردين
- **🧀 أجبان**: 7 موردين

### 📋 **العقود (30 عقد):**
- **📦 توريد**: 20 عقد
- **🏠 إيجار**: 8 عقود
- **🔧 خدمات**: 2 عقد

### 👁️ **إيجارات العيون (45 إيجار):**
- **✅ نشط**: 43 إيجار
- **❌ منتهي**: 2 إيجار
- **💰 الإيرادات الشهرية**: 2,580 د.ك

### 🏢 **الطبليات والأقسام:**
- **📊 إجمالي الطبليات**: 20 طبلية
- **📦 إجمالي الأقسام**: 80 قسم (20 × 4)
- **💰 سعر الإيجار**: 60 د.ك لكل قسم شهرياً
- **🏠 الأقسام المؤجرة**: 43 قسم
- **🆓 الأقسام المتاحة**: 37 قسم

---

## 🎯 طريقة الاستخدام

### 1. **إنشاء الملفات:**
```bash
# Windows
create-sample-files.bat

# Linux/Mac
./create-sample-files.sh
```

### 2. **تشغيل النظام:**
```bash
# Windows
start-offline-system.bat

# Linux/Mac
./start-offline-system.sh
```

### 3. **رفع الملفات:**
- افتح: `http://localhost:3000/upload_suppliers.html`
- اختر ملف من مجلد `sample_data`
- ارفع الملف واختبر النظام

---

## 🔗 الواجهات المتاحة للاختبار

### 📁 **رفع الملفات:**
```
http://localhost:3000/upload_suppliers.html
```

### 👥 **إدارة الموردين:**
```
http://localhost:3000/suppliers_management.html
```

### 📋 **إدارة العقود:**
```
http://localhost:3000/contracts_management.html
```

### 👁️ **إدارة العيون:**
```
http://localhost:3000/eyes_management.html
```

### 📊 **التقارير والإحصائيات:**
```
http://localhost:3000/reports_dashboard.html
```

---

## 💡 نصائح للاختبار

### 📝 **ترتيب الرفع الموصى به:**
1. **أولاً**: ارفع ملف الموردين
2. **ثانياً**: ارفع ملف العقود
3. **ثالثاً**: ارفع ملف إيجارات العيون

### 🔍 **اختبار الوظائف:**
- ✅ **البحث والتصفية** في جميع الواجهات
- ✅ **إضافة وتعديل وحذف** البيانات
- ✅ **التقارير والإحصائيات** التفاعلية
- ✅ **تصدير البيانات** بصيغ مختلفة
- ✅ **نظام العيون والطبليات** التفاعلي

---

## 📋 تفاصيل البيانات

### 👥 **بيانات الموردين:**
```csv
الرقم,اسم المورد,الفئة,رقم الهاتف,البريد الإلكتروني,العنوان,السجل التجاري,الرقم الضريبي,الشخص المسؤول,الحالة,ملاحظات
```

**أمثلة الموردين:**
- شركة البهارات الكويتية (بهارات)
- مؤسسة الخليج للمواد الاستهلاكية (استهلاكي)
- شركة الأجبان الطازجة (أجبان)
- مؤسسة التوابل العربية (بهارات)
- شركة المنظفات الحديثة (استهلاكي)

### 📋 **بيانات العقود:**
```csv
رقم العقد,عنوان العقد,اسم المورد,نوع العقد,تاريخ البداية,تاريخ النهاية,المبلغ,العملة,الحالة,وصف العقد,الشروط والأحكام,ملاحظات
```

**أنواع العقود:**
- **توريد**: عقود توريد البهارات والأجبان والمواد الاستهلاكية
- **إيجار**: عقود إيجار العيون في الطبليات المختلفة
- **خدمات**: عقود خدمات التنظيف والصيانة والأمن

### 👁️ **بيانات إيجارات العيون:**
```csv
رقم العين,اسم المورد,رقم الطبلية,رقم القسم,الفئة,تاريخ البداية,تاريخ النهاية,الإيجار الشهري,مبلغ التأمين,الحالة,ملاحظات
```

**تفاصيل النظام:**
- **20 طبلية** في السوق المركزي
- **4 أقسام** في كل طبلية (60 سم لكل قسم)
- **60 د.ك** إيجار شهري لكل قسم
- **120 د.ك** مبلغ تأمين لكل قسم

---

## 🎨 مميزات البيانات النموذجية

### ✨ **بيانات واقعية:**
- أسماء شركات كويتية حقيقية
- عناوين في مناطق الكويت المختلفة
- أرقام هواتف وبريد إلكتروني صحيحة الشكل
- أسماء أشخاص كويتية أصيلة

### 📊 **تنوع في البيانات:**
- موردين من فئات مختلفة
- عقود بأنواع وحالات متنوعة
- إيجارات بفترات مختلفة
- مبالغ متنوعة وواقعية

### 🔄 **قابلية الاختبار:**
- بيانات كافية لاختبار جميع الوظائف
- حالات مختلفة (نشط، منتهي، قيد المراجعة)
- تواريخ متنوعة لاختبار التصفية
- علاقات صحيحة بين الجداول

---

## 🛠️ استكشاف الأخطاء

### ❌ **مشاكل شائعة:**

**1. ملف غير موجود:**
```bash
# تأكد من تشغيل أمر الإنشاء أولاً
create-sample-files.bat  # Windows
./create-sample-files.sh # Linux/Mac
```

**2. خطأ في رفع الملف:**
- تأكد من تشغيل الخادم المحلي
- تحقق من صيغة الملف (CSV أو Excel)
- تأكد من وجود الأعمدة المطلوبة

**3. بيانات لا تظهر:**
- تحديث الصفحة
- تحقق من قاعدة البيانات المحلية
- راجع رسائل الخطأ في وحدة التحكم

---

## 📞 الدعم والمساعدة

### 🏢 **جمعية المنقف التعاونية:**
- 📧 **البريد الإلكتروني**: <EMAIL>
- 📱 **الهاتف**: +965-23710272
- 📍 **العنوان**: المنقف - قطعة 4 شارع فهد الهملان

### 👨‍💻 **المطور - محمد مرزوق العقاب:**
- 📧 **البريد المباشر**: <EMAIL>


---

## 🎉 ملاحظات مهمة

### ✅ **البيانات آمنة:**
- جميع البيانات تجريبية ووهمية
- لا تحتوي على معلومات حقيقية
- آمنة للاختبار والتطوير

### 🔄 **قابلة للتعديل:**
- يمكن تعديل البيانات حسب الحاجة
- إضافة موردين أو عقود جديدة
- تغيير الفئات والأسعار

### 🚀 **جاهزة للإنتاج:**
- بنية صحيحة للبيانات
- تتوافق مع متطلبات النظام
- قابلة للتوسع والتطوير

---

**© 2024 جمعية المنقف التعاونية - جميع الحقوق محفوظة**

**تطوير وتنفيذ: محمد مرزوق العقاب**

*"بيانات نموذجية شاملة لاختبار نظام متطور"* 🇰🇼
