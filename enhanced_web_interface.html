<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>إدارة عقود الموردين والإيجارات - جمعية المنقف</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
  <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
    body { font-family: 'Cairo', sans-serif; }
    
    /* تخصيص الشريط الجانبي */
    .sidebar {
      transition: transform 0.3s ease-in-out;
      transform: translateX(-100%);
    }
    .sidebar.open {
      transform: translateX(0);
    }
    
    /* تخصيص Flatpickr للعربية */
    .flatpickr-calendar {
      direction: ltr;
      font-family: 'Cairo', sans-serif;
    }
    
    /* أيقونات النجمة الحمراء */
    .required-field::after {
      content: " *";
      color: #ef4444;
      font-weight: bold;
    }
    
    /* تحسينات الواجهة */
    .toast-success { background-color: #10b981; color: white; }
    .toast-error { background-color: #ef4444; color: white; }
    .toast-warning { background-color: #f59e0b; color: white; }
    .toast-info { background-color: #3b82f6; color: white; }
    
    .card-hover { transition: all 0.3s ease; }
    .card-hover:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
    
    .loading { opacity: 0.6; pointer-events: none; }
    .highlight { background-color: #fef3c7; }
    
    /* تحسين أزرار التنقل */
    .nav-item {
      transition: all 0.2s ease;
      border-radius: 0.5rem;
      margin-bottom: 0.25rem;
    }
    .nav-item:hover {
      background-color: rgba(59, 130, 246, 0.1);
      transform: translateX(5px);
    }
    .nav-item.active {
      background-color: #3b82f6;
      color: white;
    }
    
    /* تحسين النماذج */
    .form-group {
      margin-bottom: 1.5rem;
    }
    
    .form-input {
      transition: all 0.2s ease;
    }
    .form-input:focus {
      transform: scale(1.02);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    /* تحسين الجداول */
    .table-row:hover {
      background-color: #f8fafc;
      transform: scale(1.01);
    }
    
    /* تحسين الأزرار */
    .btn {
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;
    }
    .btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    .btn:active {
      transform: translateY(0);
    }
    
    /* تأثير الموجة للأزرار */
    .btn::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgba(255,255,255,0.3);
      transition: width 0.3s, height 0.3s, top 0.3s, left 0.3s;
      transform: translate(-50%, -50%);
    }
    .btn:active::before {
      width: 300px;
      height: 300px;
      top: 50%;
      left: 50%;
    }
    
    /* تحسين مؤشرات التحميل */
    .spinner {
      border: 2px solid #f3f3f3;
      border-top: 2px solid #3b82f6;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* تحسين الإشعارات */
    .toast {
      animation: slideInRight 0.3s ease-out;
    }
    
    @keyframes slideInRight {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
    
    /* تحسين النوافذ المنبثقة */
    .modal {
      animation: fadeIn 0.3s ease-out;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    /* تحسين الشريط الجانبي */
    .sidebar-overlay {
      background: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(4px);
    }
    
    /* تحسين حقول النماذج */
    .form-input:invalid {
      border-color: #ef4444;
      box-shadow: 0 0 0 1px #ef4444;
    }
    
    .form-input:valid {
      border-color: #10b981;
    }
    
    /* تحسين قوائم الاختيار المتعددة */
    .multi-select {
      max-height: 120px;
      overflow-y: auto;
    }
    
    /* تحسين مؤشرات الحالة */
    .status-indicator {
      position: relative;
    }
    
    .status-indicator::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      animation: pulse 2s infinite;
    }
    
    .status-online::before {
      background-color: #10b981;
    }
    
    .status-offline::before {
      background-color: #ef4444;
    }
    
    @keyframes pulse {
      0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
      }
      70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
      }
      100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
      }
    }
    
    /* تحسين البحث */
    .search-highlight {
      background-color: #fef3c7;
      padding: 2px 4px;
      border-radius: 3px;
      font-weight: bold;
    }
    
    /* تحسين الترقيم */
    .pagination-btn {
      transition: all 0.2s ease;
    }
    .pagination-btn:hover {
      transform: scale(1.1);
    }
    
    /* تحسين الفلاتر */
    .filter-active {
      background-color: #3b82f6;
      color: white;
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    }
    
    /* تحسين العناصر التفاعلية */
    .interactive:hover {
      cursor: pointer;
      background-color: rgba(59, 130, 246, 0.05);
    }
    
    /* تحسين الأيقونات */
    .icon {
      transition: all 0.2s ease;
    }
    .icon:hover {
      transform: scale(1.2);
    }
    
    /* تحسين الشاشات الصغيرة */
    @media (max-width: 768px) {
      .sidebar {
        width: 100% !important;
      }
      
      .main-content {
        padding: 1rem !important;
      }
      
      .card {
        margin: 0.5rem 0 !important;
      }
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">

  <!-- شريط التنقل العلوي -->
  <nav class="bg-blue-800 text-white p-4 shadow-lg relative z-50">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <button id="sidebarToggle" class="lg:hidden p-2 rounded-lg hover:bg-blue-700 transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
        <h1 class="text-xl lg:text-2xl font-bold">🏢 نظام إدارة عقود الموردين - جمعية المنقف</h1>
      </div>
      <div class="flex items-center space-x-4">
        <div id="connectionStatus" class="flex items-center status-indicator status-online">
          <span class="text-sm">متصل</span>
        </div>
        <span id="currentTime" class="text-sm opacity-75"></span>
        <span id="contractsCount" class="bg-blue-600 px-3 py-1 rounded-full text-sm">0 عقد</span>
      </div>
    </div>
  </nav>

  <!-- الحاوية الرئيسية -->
  <div class="flex min-h-screen">
    
    <!-- الشريط الجانبي -->
    <aside id="sidebar" class="sidebar fixed lg:relative lg:translate-x-0 w-64 bg-white shadow-lg z-40 h-full lg:h-auto">
      <div class="p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-lg font-bold text-gray-800">القوائم الرئيسية</h2>
          <button id="sidebarClose" class="lg:hidden p-1 rounded text-gray-500 hover:text-gray-700">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <nav class="space-y-2">
          <a href="#" class="nav-item active flex items-center p-3 text-gray-700" data-section="contracts">
            <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            إدارة العقود
          </a>
          
          <a href="#" class="nav-item flex items-center p-3 text-gray-700" data-section="suppliers">
            <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6"></path>
            </svg>
            إدارة الموردين
          </a>
          
          <a href="#" class="nav-item flex items-center p-3 text-gray-700" data-section="rentals">
            <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6"></path>
            </svg>
            إدارة العيون
          </a>
          
          <a href="#" class="nav-item flex items-center p-3 text-gray-700" data-section="reports">
            <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            التقارير والإحصائيات
          </a>
          
          <a href="#" class="nav-item flex items-center p-3 text-gray-700" data-section="settings">
            <svg class="w-5 h-5 ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            الإعدادات
          </a>
        </nav>
        
        <!-- إحصائيات سريعة -->
        <div class="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 class="text-sm font-medium text-blue-800 mb-3">إحصائيات سريعة</h3>
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">العقود النشطة:</span>
              <span id="activeContractsCount" class="font-medium text-blue-600">0</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">المسودات:</span>
              <span id="draftsCount" class="font-medium text-yellow-600">0</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">آخر مزامنة:</span>
              <span id="lastSync" class="font-medium text-green-600">الآن</span>
            </div>
          </div>
        </div>
      </div>
    </aside>

    <!-- طبقة تغطية للشريط الجانبي في الشاشات الصغيرة -->
    <div id="sidebarOverlay" class="sidebar-overlay fixed inset-0 z-30 lg:hidden hidden"></div>

    <!-- المحتوى الرئيسي -->
    <main class="flex-1 main-content p-6 lg:mr-0">
      
      <!-- قسم إدارة العقود -->
      <div id="contractsSection" class="section active">
        
        <!-- شريط أدوات أعلى الصفحة -->
        <div class="bg-white p-4 rounded-lg shadow-md mb-6 card-hover">
          <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <!-- أزرار التحكم الرئيسية -->
            <div class="flex flex-wrap gap-3">
              <button id="btnNewContract" class="btn bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors">
                ➕ عقد جديد
              </button>
              <button id="btnLoadDrafts" class="btn bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg transition-colors">
                📂 المسودات (<span id="draftsCountBtn">0</span>)
              </button>
              <button id="btnExportData" class="btn bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors">
                📊 تصدير البيانات
              </button>
              <button id="btnPrint" class="btn bg-gray-700 hover:bg-gray-800 text-white py-2 px-4 rounded-lg transition-colors">
                🖨️ طباعة
              </button>
              <button id="btnSync" class="btn bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-lg transition-colors">
                <span id="syncIcon">🔄</span> مزامنة
              </button>
            </div>
            
            <!-- معلومات الحالة -->
            <div class="flex items-center space-x-4 text-sm">
              <span id="lastSaved" class="text-gray-600">آخر حفظ: لم يتم الحفظ بعد</span>
            </div>
          </div>
        </div>

        <!-- نموذج إضافة/تعديل عقد -->
        <div id="contractForm" class="bg-white p-6 rounded-lg shadow-md mb-6 card-hover hidden">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-bold text-gray-800">📝 إضافة/تعديل عقد</h2>
            <button id="btnToggleForm" class="text-gray-500 hover:text-gray-700 icon">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <form id="contract-form" class="space-y-6" novalidate>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

              <!-- رقم العقد -->
              <div class="form-group">
                <label class="block text-gray-700 font-medium mb-2 required-field">رقم العقد</label>
                <div class="flex">
                  <input type="text" name="contractNumber" id="contractNumber"
                         class="form-input flex-1 border border-gray-300 rounded-r-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                         placeholder="2025-001"
                         pattern="[0-9]{4}-[0-9]{3}"
                         title="تنسيق رقم العقد: YYYY-XXX"
                         required />
                  <button type="button" id="btnGenerateNumber"
                          class="btn bg-blue-600 hover:bg-blue-700 text-white px-4 rounded-l-lg transition-colors">
                    🔄
                  </button>
                </div>
                <div class="invalid-feedback text-red-500 text-sm mt-1 hidden">رقم العقد مطلوب ويجب أن يكون بتنسيق YYYY-XXX</div>
              </div>

              <!-- اسم المورد -->
              <div class="form-group">
                <label class="block text-gray-700 font-medium mb-2 required-field">اسم المورد</label>
                <input type="text" name="supplierName" id="supplierName"
                       class="form-input w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="شركة البهارات الذهبية"
                       minlength="2"
                       maxlength="100"
                       required />
                <div class="invalid-feedback text-red-500 text-sm mt-1 hidden">اسم المورد مطلوب (2-100 حرف)</div>
              </div>

              <!-- نوع العقد -->
              <div class="form-group">
                <label class="block text-gray-700 font-medium mb-2 required-field">نوع العقد</label>
                <select name="contractType" id="contractType"
                        class="form-input w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        required>
                  <option value="">اختر نوع العقد</option>
                  <option value="توريد">📦 توريد</option>
                  <option value="إيجار">🏠 إيجار</option>
                  <option value="خدمات">🔧 خدمات</option>
                  <option value="صيانة">⚙️ صيانة</option>
                </select>
                <div class="invalid-feedback text-red-500 text-sm mt-1 hidden">نوع العقد مطلوب</div>
              </div>

              <!-- تاريخ البداية -->
              <div class="form-group">
                <label class="block text-gray-700 font-medium mb-2 required-field">تاريخ البداية</label>
                <input type="text" name="startDate" id="startDate"
                       class="form-input w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="اختر تاريخ البداية"
                       required />
                <div class="invalid-feedback text-red-500 text-sm mt-1 hidden">تاريخ البداية مطلوب</div>
              </div>

              <!-- تاريخ النهاية -->
              <div class="form-group">
                <label class="block text-gray-700 font-medium mb-2 required-field">تاريخ النهاية</label>
                <input type="text" name="endDate" id="endDate"
                       class="form-input w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="اختر تاريخ النهاية"
                       required />
                <div class="invalid-feedback text-red-500 text-sm mt-1 hidden">تاريخ النهاية مطلوب</div>
              </div>

              <!-- المبلغ الإجمالي -->
              <div class="form-group">
                <label class="block text-gray-700 font-medium mb-2">المبلغ الإجمالي (د.ك)</label>
                <input type="number" name="totalAmount" id="totalAmount" step="0.01" min="0" max="999999.99"
                       class="form-input w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="1000.00" />
                <div class="invalid-feedback text-red-500 text-sm mt-1 hidden">المبلغ يجب أن يكون رقماً موجباً</div>
              </div>

              <!-- العناصر المتعددة -->
              <div class="form-group">
                <label class="block text-gray-700 font-medium mb-2 required-field">عناصر العقد</label>
                <select name="contractItems" id="contractItems" multiple
                        class="form-input multi-select w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        required>
                  <option value="بهارات_حارة">🌶️ بهارات حارة</option>
                  <option value="بهارات_حلوة">🍯 بهارات حلوة</option>
                  <option value="توابل_شرقية">🥘 توابل شرقية</option>
                  <option value="مواد_تنظيف">🧽 مواد تنظيف</option>
                  <option value="مستلزمات_مطبخ">🍴 مستلزمات مطبخ</option>
                  <option value="أدوات_شخصية">🧴 أدوات شخصية</option>
                  <option value="أجبان_طازجة">🧀 أجبان طازجة</option>
                  <option value="منتجات_ألبان">🥛 منتجات ألبان</option>
                  <option value="أجبان_مستوردة">🧀 أجبان مستوردة</option>
                  <option value="خدمات_صيانة">🔧 خدمات صيانة</option>
                  <option value="خدمات_تنظيف">🧹 خدمات تنظيف</option>
                  <option value="خدمات_أمن">🛡️ خدمات أمن</option>
                </select>
                <div class="text-xs text-gray-500 mt-1">
                  اضغط Ctrl (أو Cmd) + النقر لاختيار عدة عناصر
                </div>
                <div class="invalid-feedback text-red-500 text-sm mt-1 hidden">يجب اختيار عنصر واحد على الأقل</div>
              </div>
            </div>

            <!-- أزرار النموذج -->
            <div class="flex flex-wrap gap-3 pt-4 border-t">
              <button type="button" id="btnSaveLocal"
                      class="btn bg-yellow-600 hover:bg-yellow-700 text-white py-3 px-6 rounded-lg transition-colors">
                💾 حفظ مؤقت
              </button>
              <button type="submit" id="btnSaveFinal"
                      class="btn bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg transition-colors">
                ✅ حفظ نهائي
              </button>
              <button type="button" id="btnClearForm"
                      class="btn bg-gray-500 hover:bg-gray-600 text-white py-3 px-6 rounded-lg transition-colors">
                🗑️ مسح النموذج
              </button>
            </div>
          </form>
        </div>

        <!-- بحث وفلاتر متقدمة -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-6 card-hover">
          <h3 class="text-lg font-bold text-gray-800 mb-4">🔍 البحث والفلاتر</h3>

          <!-- الصف الأول - البحث الأساسي -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div class="relative">
              <input type="text" id="searchInput" placeholder="ابحث باسم المورد أو رقم العقد..."
                     class="form-input w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </div>
            </div>

            <select id="filterType" class="form-input border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">كل الأنواع</option>
              <option value="توريد">📦 توريد</option>
              <option value="إيجار">🏠 إيجار</option>
              <option value="خدمات">🔧 خدمات</option>
              <option value="صيانة">⚙️ صيانة</option>
            </select>

            <select id="filterStatus" class="form-input border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">كل الحالات</option>
              <option value="نشط">✅ نشط</option>
              <option value="منتهي">⏰ منتهي</option>
              <option value="ملغي">❌ ملغي</option>
              <option value="معلق">⏸️ معلق</option>
            </select>

            <select id="filterCategory" class="form-input border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">كل الأقسام</option>
              <option value="بهارات">🌶️ بهارات</option>
              <option value="استهلاكي">🧽 مواد استهلاكية</option>
              <option value="أجبان">🧀 أجبان</option>
            </select>
          </div>

          <!-- الصف الثاني - فلاتر متقدمة -->
          <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
            <div>
              <label class="block text-sm text-gray-600 mb-1">من تاريخ</label>
              <input type="text" id="filterStartDate" placeholder="اختر التاريخ"
                     class="form-input w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
            </div>

            <div>
              <label class="block text-sm text-gray-600 mb-1">إلى تاريخ</label>
              <input type="text" id="filterEndDate" placeholder="اختر التاريخ"
                     class="form-input w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
            </div>

            <div>
              <label class="block text-sm text-gray-600 mb-1">المبلغ من</label>
              <input type="number" id="filterMinAmount" step="0.01" min="0" placeholder="0.00"
                     class="form-input w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
            </div>

            <div>
              <label class="block text-sm text-gray-600 mb-1">المبلغ إلى</label>
              <input type="number" id="filterMaxAmount" step="0.01" min="0" placeholder="∞"
                     class="form-input w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
            </div>

            <div class="flex items-end">
              <button id="btnClearFilters" class="btn w-full bg-gray-500 hover:bg-gray-600 text-white py-3 px-4 rounded-lg transition-colors">
                🗑️ مسح الفلاتر
              </button>
            </div>
          </div>

          <!-- معلومات النتائج -->
          <div class="flex items-center justify-between text-sm text-gray-600">
            <span id="resultsInfo">عرض 0 من 0 عقد</span>
            <span id="activeFilters" class="text-blue-600"></span>
          </div>
        </div>

        <!-- جدول العقود -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden card-hover">
          <div class="p-4 bg-gray-50 border-b flex items-center justify-between">
            <h3 class="text-lg font-bold text-gray-800">📋 قائمة العقود</h3>
            <div class="flex items-center space-x-2">
              <select id="itemsPerPage" class="form-input border border-gray-300 rounded p-2 text-sm">
                <option value="10">10 عقود</option>
                <option value="25">25 عقد</option>
                <option value="50">50 عقد</option>
                <option value="100">100 عقد</option>
              </select>
              <button id="btnRefresh" class="btn bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm transition-colors">
                🔄 تحديث
              </button>
            </div>
          </div>

          <div class="overflow-x-auto">
            <table class="min-w-full">
              <thead class="bg-gray-100">
                <tr>
                  <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200 interactive" data-sort="contractNumber">
                    رقم العقد ↕️
                  </th>
                  <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200 interactive" data-sort="supplierName">
                    المورد ↕️
                  </th>
                  <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200 interactive" data-sort="contractType">
                    النوع ↕️
                  </th>
                  <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200 interactive" data-sort="startDate">
                    تاريخ البداية ↕️
                  </th>
                  <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200 interactive" data-sort="totalAmount">
                    المبلغ ↕️
                  </th>
                  <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200 interactive" data-sort="contractStatus">
                    الحالة ↕️
                  </th>
                  <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody id="contractsTableBody" class="bg-white divide-y divide-gray-200">
                <!-- سيتم ملء البيانات ديناميكياً -->
              </tbody>
            </table>
          </div>

          <!-- رسالة عدم وجود بيانات -->
          <div id="noDataMessage" class="hidden p-8 text-center text-gray-500">
            <div class="text-6xl mb-4">📄</div>
            <h3 class="text-lg font-medium mb-2">لا توجد عقود</h3>
            <p class="text-sm">ابدأ بإضافة عقد جديد أو قم بتعديل الفلاتر</p>
          </div>
        </div>

      </div>

      <!-- أقسام أخرى (مخفية حالياً) -->
      <div id="suppliersSection" class="section hidden">
        <div class="bg-white p-8 rounded-lg shadow-md text-center">
          <h2 class="text-2xl font-bold text-gray-800 mb-4">🏢 إدارة الموردين</h2>
          <p class="text-gray-600">قسم إدارة الموردين قيد التطوير</p>
        </div>
      </div>

      <div id="rentalsSection" class="section hidden">
        <div class="bg-white p-8 rounded-lg shadow-md text-center">
          <h2 class="text-2xl font-bold text-gray-800 mb-4">👁️ إدارة العيون</h2>
          <p class="text-gray-600">قسم إدارة العيون قيد التطوير</p>
        </div>
      </div>

      <div id="reportsSection" class="section hidden">
        <div class="bg-white p-8 rounded-lg shadow-md text-center">
          <h2 class="text-2xl font-bold text-gray-800 mb-4">📊 التقارير والإحصائيات</h2>
          <p class="text-gray-600">قسم التقارير قيد التطوير</p>
        </div>
      </div>

      <div id="settingsSection" class="section hidden">
        <div class="bg-white p-8 rounded-lg shadow-md text-center">
          <h2 class="text-2xl font-bold text-gray-800 mb-4">⚙️ الإعدادات</h2>
          <p class="text-gray-600">قسم الإعدادات قيد التطوير</p>
        </div>
      </div>

    </main>
  </div>

  <!-- صندوق الرسائل (Toast) -->
  <div id="toast" class="toast fixed top-5 left-5 hidden p-4 rounded-lg shadow-lg z-50 max-w-sm">
    <div class="flex items-center">
      <div id="toastIcon" class="flex-shrink-0 w-6 h-6 mr-3"></div>
      <div id="toastMessage" class="text-sm font-medium flex-1"></div>
      <button id="toastClose" class="ml-auto text-white hover:text-gray-200 icon">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- السكريبت الرئيسي -->
  <script>
    // ==================== المتغيرات العامة ====================
    let contracts = JSON.parse(localStorage.getItem('contracts') || '[]');
    let drafts = JSON.parse(localStorage.getItem('drafts') || '[]');
    let currentPage = 1;
    let itemsPerPage = 10;
    let sortField = 'contractNumber';
    let sortDirection = 'asc';
    let editingContractId = null;
    let filteredContracts = [];
    let isOnline = true;
    let startDatePicker = null;
    let endDatePicker = null;
    let filterStartDatePicker = null;
    let filterEndDatePicker = null;

    // إعدادات API
    const API_BASE_URL = 'http://localhost:3000/api';

    // ==================== تهيئة التطبيق ====================
    document.addEventListener('DOMContentLoaded', function() {
      initializeApp();
      setupEventListeners();
      initializeFlatpickr();
      updateCurrentTime();
      setInterval(updateCurrentTime, 1000);
    });

    function initializeApp() {
      // إخفاء النموذج في البداية
      hideForm();

      // تحديث العرض
      applyFilters();
      updateContractsCount();
      updateDraftsCount();

      // إضافة بيانات تجريبية إذا لم تكن موجودة
      if (contracts.length === 0) {
        addSampleData();
      }

      // فحص الاتصال بالخادم
      checkServerConnection();
    }

    function initializeFlatpickr() {
      // إعداد Flatpickr للتواريخ العربية
      const flatpickrConfig = {
        locale: 'ar',
        dateFormat: 'Y-m-d',
        allowInput: true,
        clickOpens: true,
        position: 'auto'
      };

      // تهيئة منتقي التواريخ في النموذج
      startDatePicker = flatpickr('#startDate', {
        ...flatpickrConfig,
        minDate: 'today',
        onChange: function(selectedDates, dateStr, instance) {
          if (endDatePicker && selectedDates[0]) {
            endDatePicker.set('minDate', selectedDates[0]);
          }
          validateField(document.getElementById('startDate'));
        }
      });

      endDatePicker = flatpickr('#endDate', {
        ...flatpickrConfig,
        onChange: function(selectedDates, dateStr, instance) {
          validateField(document.getElementById('endDate'));
        }
      });

      // تهيئة منتقي التواريخ في الفلاتر
      filterStartDatePicker = flatpickr('#filterStartDate', {
        ...flatpickrConfig,
        onChange: function(selectedDates, dateStr, instance) {
          if (filterEndDatePicker && selectedDates[0]) {
            filterEndDatePicker.set('minDate', selectedDates[0]);
          }
          applyFilters();
        }
      });

      filterEndDatePicker = flatpickr('#filterEndDate', {
        ...flatpickrConfig,
        onChange: function(selectedDates, dateStr, instance) {
          applyFilters();
        }
      });
    }

    function addSampleData() {
      const sampleContracts = [
        {
          id: generateId(),
          contractNumber: '2025-001',
          supplierName: 'شركة البهارات الذهبية',
          contractType: 'توريد',
          supplierCategory: 'بهارات',
          contractItems: ['بهارات_حارة', 'توابل_شرقية'],
          startDate: '2025-01-01',
          endDate: '2025-12-31',
          totalAmount: 1200.00,
          contractStatus: 'نشط',
          paymentTerms: 'دفع شهري مقدم',
          notes: 'عقد توريد البهارات والتوابل الشرقية',
          createdAt: new Date().toISOString()
        },
        {
          id: generateId(),
          contractNumber: '2025-002',
          supplierName: 'مؤسسة المواد الاستهلاكية',
          contractType: 'توريد',
          supplierCategory: 'استهلاكي',
          contractItems: ['مواد_تنظيف', 'مستلزمات_مطبخ'],
          startDate: '2025-01-15',
          endDate: '2025-06-15',
          totalAmount: 800.00,
          contractStatus: 'نشط',
          paymentTerms: 'دفع عند التسليم',
          notes: 'توريد مواد التنظيف والمستلزمات',
          createdAt: new Date().toISOString()
        },
        {
          id: generateId(),
          contractNumber: '2025-003',
          supplierName: 'شركة الأجبان الطازجة',
          contractType: 'إيجار',
          supplierCategory: 'أجبان',
          contractItems: ['أجبان_طازجة', 'منتجات_ألبان'],
          startDate: '2025-02-01',
          endDate: '2025-07-31',
          totalAmount: 360.00,
          contractStatus: 'نشط',
          paymentTerms: 'دفع شهري 60 دينار',
          notes: 'إيجار عين واحدة في قسم الأجبان',
          createdAt: new Date().toISOString()
        }
      ];

      contracts = sampleContracts;
      saveContracts();
      showToast('تم إضافة بيانات تجريبية', 'success');
    }

    // ==================== إدارة الأحداث ====================
    function setupEventListeners() {
      // الشريط الجانبي
      document.getElementById('sidebarToggle').addEventListener('click', toggleSidebar);
      document.getElementById('sidebarClose').addEventListener('click', closeSidebar);
      document.getElementById('sidebarOverlay').addEventListener('click', closeSidebar);

      // التنقل بين الأقسام
      document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', (e) => {
          e.preventDefault();
          const section = item.dataset.section;
          if (section) {
            switchSection(section);
          }
        });
      });

      // أزرار التحكم الرئيسية
      document.getElementById('btnNewContract').addEventListener('click', showForm);
      document.getElementById('btnLoadDrafts').addEventListener('click', showDraftsModal);
      document.getElementById('btnExportData').addEventListener('click', exportData);
      document.getElementById('btnPrint').addEventListener('click', () => window.print());
      document.getElementById('btnSync').addEventListener('click', syncWithServer);

      // نموذج العقد
      document.getElementById('contract-form').addEventListener('submit', handleFormSubmit);
      document.getElementById('btnSaveLocal').addEventListener('click', saveDraft);
      document.getElementById('btnClearForm').addEventListener('click', clearForm);
      document.getElementById('btnToggleForm').addEventListener('click', hideForm);
      document.getElementById('btnGenerateNumber').addEventListener('click', generateContractNumber);

      // التحقق من صحة البيانات أثناء الكتابة
      document.querySelectorAll('.form-input').forEach(input => {
        input.addEventListener('blur', () => validateField(input));
        input.addEventListener('input', () => clearFieldError(input));
      });

      // البحث والفلاتر
      document.getElementById('searchInput').addEventListener('input', debounce(applyFilters, 300));
      document.getElementById('filterType').addEventListener('change', applyFilters);
      document.getElementById('filterStatus').addEventListener('change', applyFilters);
      document.getElementById('filterCategory').addEventListener('change', applyFilters);
      document.getElementById('filterMinAmount').addEventListener('input', debounce(applyFilters, 500));
      document.getElementById('filterMaxAmount').addEventListener('input', debounce(applyFilters, 500));
      document.getElementById('btnClearFilters').addEventListener('click', clearFilters);

      // الجدول والترقيم
      document.getElementById('itemsPerPage').addEventListener('change', changeItemsPerPage);
      document.getElementById('btnRefresh').addEventListener('click', () => {
        applyFilters();
        showToast('تم تحديث البيانات', 'success');
      });

      // ترتيب الجدول
      document.querySelectorAll('[data-sort]').forEach(header => {
        header.addEventListener('click', () => sortTable(header.dataset.sort));
      });

      // إغلاق Toast
      document.getElementById('toastClose').addEventListener('click', hideToast);

      // إغلاق النوافذ بالضغط على Escape
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
          hideToast();
          closeSidebar();
        }
      });
    }

    // ==================== إدارة الشريط الجانبي ====================
    function toggleSidebar() {
      const sidebar = document.getElementById('sidebar');
      const overlay = document.getElementById('sidebarOverlay');

      sidebar.classList.toggle('open');
      overlay.classList.toggle('hidden');
    }

    function closeSidebar() {
      const sidebar = document.getElementById('sidebar');
      const overlay = document.getElementById('sidebarOverlay');

      sidebar.classList.remove('open');
      overlay.classList.add('hidden');
    }

    function switchSection(sectionName) {
      // إخفاء جميع الأقسام
      document.querySelectorAll('.section').forEach(section => {
        section.classList.add('hidden');
      });

      // إزالة الحالة النشطة من جميع عناصر التنقل
      document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
      });

      // إظهار القسم المحدد
      const targetSection = document.getElementById(sectionName + 'Section');
      if (targetSection) {
        targetSection.classList.remove('hidden');
      }

      // تفعيل عنصر التنقل المحدد
      const activeNavItem = document.querySelector(`[data-section="${sectionName}"]`);
      if (activeNavItem) {
        activeNavItem.classList.add('active');
      }

      // إغلاق الشريط الجانبي في الشاشات الصغيرة
      closeSidebar();
    }

    // ==================== التحقق من صحة البيانات ====================
    function validateField(field) {
      const value = field.value.trim();
      const fieldName = field.name;
      let isValid = true;
      let errorMessage = '';

      // مسح الأخطاء السابقة
      clearFieldError(field);

      switch (fieldName) {
        case 'contractNumber':
          if (!value) {
            isValid = false;
            errorMessage = 'رقم العقد مطلوب';
          } else if (!/^[0-9]{4}-[0-9]{3}$/.test(value)) {
            isValid = false;
            errorMessage = 'تنسيق رقم العقد يجب أن يكون YYYY-XXX';
          } else if (isDuplicateContractNumber(value)) {
            isValid = false;
            errorMessage = 'رقم العقد موجود مسبقاً';
          }
          break;

        case 'supplierName':
          if (!value) {
            isValid = false;
            errorMessage = 'اسم المورد مطلوب';
          } else if (value.length < 2) {
            isValid = false;
            errorMessage = 'اسم المورد يجب أن يكون حرفين على الأقل';
          } else if (value.length > 100) {
            isValid = false;
            errorMessage = 'اسم المورد لا يجب أن يتجاوز 100 حرف';
          }
          break;

        case 'contractType':
          if (!value) {
            isValid = false;
            errorMessage = 'نوع العقد مطلوب';
          }
          break;

        case 'contractItems':
          const selectedItems = Array.from(field.selectedOptions);
          if (selectedItems.length === 0) {
            isValid = false;
            errorMessage = 'يجب اختيار عنصر واحد على الأقل';
          }
          break;

        case 'startDate':
          if (!value) {
            isValid = false;
            errorMessage = 'تاريخ البداية مطلوب';
          } else {
            const startDate = new Date(value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            if (startDate < today && !editingContractId) {
              isValid = false;
              errorMessage = 'تاريخ البداية لا يمكن أن يكون في الماضي';
            }
          }
          break;

        case 'endDate':
          if (!value) {
            isValid = false;
            errorMessage = 'تاريخ النهاية مطلوب';
          } else {
            const startDate = document.getElementById('startDate').value;
            if (startDate && new Date(value) <= new Date(startDate)) {
              isValid = false;
              errorMessage = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية';
            }
          }
          break;

        case 'totalAmount':
          if (value && (isNaN(value) || parseFloat(value) < 0)) {
            isValid = false;
            errorMessage = 'المبلغ يجب أن يكون رقماً موجباً';
          }
          break;
      }

      if (!isValid) {
        showFieldError(field, errorMessage);
      }

      return isValid;
    }

    function showFieldError(field, message) {
      field.classList.add('border-red-500');
      field.classList.remove('border-green-500');

      const errorDiv = field.parentNode.querySelector('.invalid-feedback');
      if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.classList.remove('hidden');
      }
    }

    function clearFieldError(field) {
      field.classList.remove('border-red-500');
      field.classList.add('border-green-500');

      const errorDiv = field.parentNode.querySelector('.invalid-feedback');
      if (errorDiv) {
        errorDiv.classList.add('hidden');
      }
    }

    function validateForm() {
      const form = document.getElementById('contract-form');
      const requiredFields = form.querySelectorAll('[required]');
      let isValid = true;

      requiredFields.forEach(field => {
        if (!validateField(field)) {
          isValid = false;
        }
      });

      return isValid;
    }

    function isDuplicateContractNumber(contractNumber) {
      return contracts.some(contract =>
        contract.contractNumber === contractNumber &&
        contract.id !== editingContractId
      );
    }

    // ==================== إدارة النموذج ====================
    function showForm() {
      document.getElementById('contractForm').classList.remove('hidden');
      document.getElementById('btnNewContract').textContent = '❌ إلغاء';
      document.getElementById('btnNewContract').onclick = hideForm;
      clearForm();
      generateContractNumber();
    }

    function hideForm() {
      document.getElementById('contractForm').classList.add('hidden');
      document.getElementById('btnNewContract').textContent = '➕ عقد جديد';
      document.getElementById('btnNewContract').onclick = showForm;
      clearForm();
    }

    function clearForm() {
      const form = document.getElementById('contract-form');
      form.reset();

      // مسح أخطاء التحقق
      form.querySelectorAll('.form-input').forEach(field => {
        clearFieldError(field);
      });

      // إعادة تعيين القيم الافتراضية
      document.getElementById('contractStatus').value = 'نشط';
      document.getElementById('contractPriority').value = 'متوسطة';

      // مسح التواريخ
      if (startDatePicker) startDatePicker.clear();
      if (endDatePicker) endDatePicker.clear();

      editingContractId = null;
    }

    function generateContractNumber() {
      const year = new Date().getFullYear();
      const nextNumber = contracts.length + 1;
      const contractNumber = `${year}-${nextNumber.toString().padStart(3, '0')}`;
      document.getElementById('contractNumber').value = contractNumber;
    }

    async function handleFormSubmit(e) {
      e.preventDefault();

      // التحقق من صحة البيانات باستخدام HTML5
      const form = e.target;
      if (!form.checkValidity()) {
        form.reportValidity();
        return;
      }

      // التحقق المخصص
      if (!validateForm()) {
        showToast('يرجى تصحيح الأخطاء في النموذج', 'error');
        return;
      }

      try {
        setFormLoading(true);

        const formData = new FormData(form);
        const contractData = {
          id: editingContractId || generateId(),
          contractNumber: formData.get('contractNumber'),
          supplierName: formData.get('supplierName'),
          contractType: formData.get('contractType'),
          supplierCategory: formData.get('supplierCategory'),
          contractItems: Array.from(formData.getAll('contractItems')),
          startDate: formData.get('startDate'),
          endDate: formData.get('endDate'),
          totalAmount: parseFloat(formData.get('totalAmount')) || 0,
          contractStatus: formData.get('contractStatus'),
          paymentTerms: formData.get('paymentTerms'),
          notes: formData.get('notes'),
          createdAt: editingContractId ?
            contracts.find(c => c.id === editingContractId)?.createdAt :
            new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        let success = false;

        if (editingContractId) {
          // تحديث عقد موجود
          success = await updateContract(contractData);
        } else {
          // إضافة عقد جديد
          success = await createContract(contractData);
        }

        if (success) {
          applyFilters();
          updateContractsCount();
          hideForm();
          updateLastSaved();
        }

      } catch (error) {
        console.error('Error saving contract:', error);
        showToast('حدث خطأ أثناء حفظ العقد', 'error');
      } finally {
        setFormLoading(false);
      }
    }

    // ==================== دوال API ====================
    async function checkServerConnection() {
      try {
        const response = await fetch(`${API_BASE_URL}/health`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });

        if (response.ok) {
          updateConnectionStatus(true);
          showToast('تم الاتصال بالخادم بنجاح', 'success');
        } else {
          throw new Error('Server not responding');
        }
      } catch (error) {
        updateConnectionStatus(false);
        showToast('العمل في الوضع المحلي - لا يوجد اتصال بالخادم', 'warning');
      }
    }

    async function createContract(contractData) {
      try {
        if (isOnline) {
          const response = await fetch(`${API_BASE_URL}/contracts`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(contractData)
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();
          showToast('تم حفظ العقد على الخادم بنجاح', 'success');
        }

        // حفظ محلي
        contracts.push(contractData);
        saveContracts();

        if (!isOnline) {
          showToast('تم حفظ العقد محلياً - سيتم مزامنته لاحقاً', 'warning');
        }

        return true;

      } catch (error) {
        console.error('Error creating contract:', error);

        // التراجع للحفظ المحلي
        contracts.push(contractData);
        saveContracts();
        showToast('تم حفظ العقد محلياً - سيتم مزامنته لاحقاً', 'warning');

        return true;
      }
    }

    async function updateContract(contractData) {
      try {
        if (isOnline) {
          const response = await fetch(`${API_BASE_URL}/contracts/${contractData.id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(contractData)
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          showToast('تم تحديث العقد على الخادم بنجاح', 'success');
        }

        // تحديث محلي
        const index = contracts.findIndex(c => c.id === contractData.id);
        if (index !== -1) {
          contracts[index] = contractData;
          saveContracts();
        }

        if (!isOnline) {
          showToast('تم تحديث العقد محلياً - سيتم مزامنته لاحقاً', 'warning');
        }

        return true;

      } catch (error) {
        console.error('Error updating contract:', error);

        // التراجع للتحديث المحلي
        const index = contracts.findIndex(c => c.id === contractData.id);
        if (index !== -1) {
          contracts[index] = contractData;
          saveContracts();
        }
        showToast('تم تحديث العقد محلياً - سيتم مزامنته لاحقاً', 'warning');

        return true;
      }
    }

    async function syncWithServer() {
      if (!isOnline) {
        showToast('لا يوجد اتصال بالخادم للمزامنة', 'warning');
        return;
      }

      try {
        setSyncLoading(true);

        // محاولة مزامنة البيانات
        const response = await fetch(`${API_BASE_URL}/contracts/sync`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ contracts })
        });

        if (response.ok) {
          const result = await response.json();
          contracts = result.contracts || contracts;
          saveContracts();
          applyFilters();
          updateContractsCount();
          showToast('تم مزامنة البيانات بنجاح', 'success');
        } else {
          throw new Error('Sync failed');
        }

      } catch (error) {
        console.error('Error syncing:', error);
        showToast('فشل في مزامنة البيانات', 'error');
      } finally {
        setSyncLoading(false);
      }
    }

    // ==================== دوال مساعدة ====================
    function generateId() {
      return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    function formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-GB');
    }

    function updateCurrentTime() {
      const now = new Date();
      document.getElementById('currentTime').textContent = now.toLocaleString('en-GB');
    }

    function updateContractsCount() {
      const total = contracts.length;
      const active = contracts.filter(c => c.contractStatus === 'نشط').length;

      document.getElementById('contractsCount').textContent = `${total} عقد`;
      document.getElementById('activeContractsCount').textContent = active;
    }

    function updateDraftsCount() {
      const count = drafts.length;
      document.getElementById('draftsCount').textContent = count;
      document.getElementById('draftsCountBtn').textContent = count;
    }

    function updateLastSaved() {
      const now = new Date();
      document.getElementById('lastSaved').textContent = `آخر حفظ: ${now.toLocaleTimeString('ar-SA')}`;
      document.getElementById('lastSync').textContent = now.toLocaleTimeString('ar-SA');
    }

    function updateConnectionStatus(online) {
      isOnline = online;
      const statusElement = document.getElementById('connectionStatus');

      if (online) {
        statusElement.className = 'flex items-center status-indicator status-online';
        statusElement.innerHTML = '<span class="text-sm">متصل</span>';
      } else {
        statusElement.className = 'flex items-center status-indicator status-offline';
        statusElement.innerHTML = '<span class="text-sm">غير متصل</span>';
      }
    }

    function setFormLoading(loading) {
      const submitBtn = document.getElementById('btnSaveFinal');
      const saveLocalBtn = document.getElementById('btnSaveLocal');

      if (loading) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<div class="spinner inline-block mr-2"></div> جاري الحفظ...';
        saveLocalBtn.disabled = true;
      } else {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '✅ حفظ نهائي';
        saveLocalBtn.disabled = false;
      }
    }

    function setSyncLoading(loading) {
      const syncBtn = document.getElementById('btnSync');
      const syncIcon = document.getElementById('syncIcon');

      if (loading) {
        syncBtn.disabled = true;
        syncIcon.innerHTML = '<div class="spinner inline-block"></div>';
      } else {
        syncBtn.disabled = false;
        syncIcon.innerHTML = '🔄';
      }
    }

    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

    function saveContracts() {
      localStorage.setItem('contracts', JSON.stringify(contracts));
    }

    function saveDrafts() {
      localStorage.setItem('drafts', JSON.stringify(drafts));
    }

    function saveDraft() {
      const formData = new FormData(document.getElementById('contract-form'));
      const draftData = {
        id: generateId(),
        contractNumber: formData.get('contractNumber'),
        supplierName: formData.get('supplierName'),
        contractType: formData.get('contractType'),
        supplierCategory: formData.get('supplierCategory'),
        contractItems: Array.from(formData.getAll('contractItems')),
        startDate: formData.get('startDate'),
        endDate: formData.get('endDate'),
        totalAmount: formData.get('totalAmount'),
        contractStatus: formData.get('contractStatus'),
        paymentTerms: formData.get('paymentTerms'),
        notes: formData.get('notes'),
        savedAt: new Date().toISOString()
      };

      drafts.push(draftData);
      saveDrafts();
      updateDraftsCount();
      showToast('تم حفظ المسودة بنجاح', 'success');
    }

    function showDraftsModal() {
      // تنفيذ بسيط لعرض المسودات
      if (drafts.length === 0) {
        showToast('لا توجد مسودات محفوظة', 'info');
        return;
      }

      const draftsList = drafts.map((draft, index) =>
        `${index + 1}. ${draft.contractNumber || 'غير محدد'} - ${draft.supplierName || 'غير محدد'}`
      ).join('\n');

      alert(`المسودات المحفوظة:\n\n${draftsList}`);
    }

    function exportData() {
      const dataToExport = {
        contracts: contracts,
        exportDate: new Date().toISOString(),
        totalContracts: contracts.length
      };

      const dataStr = JSON.stringify(dataToExport, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      const link = document.createElement('a');
      link.href = URL.createObjectURL(dataBlob);
      link.download = `contracts_export_${new Date().toISOString().split('T')[0]}.json`;
      link.click();

      showToast('تم تصدير البيانات بنجاح', 'success');
    }

    function showToast(message, type = 'success') {
      const toast = document.getElementById('toast');
      const toastMessage = document.getElementById('toastMessage');
      const toastIcon = document.getElementById('toastIcon');

      toastMessage.textContent = message;

      const icons = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
      };

      toastIcon.textContent = icons[type] || icons.info;
      toast.className = `toast fixed top-5 left-5 p-4 rounded-lg shadow-lg z-50 max-w-sm toast-${type}`;
      toast.classList.remove('hidden');

      setTimeout(() => {
        hideToast();
      }, 3000);
    }

    function hideToast() {
      document.getElementById('toast').classList.add('hidden');
    }

    // دوال بسيطة للبحث والفلترة (يمكن توسيعها)
    function applyFilters() {
      // تنفيذ بسيط للفلترة
      filteredContracts = contracts.filter(contract => {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const typeFilter = document.getElementById('filterType').value;
        const statusFilter = document.getElementById('filterStatus').value;

        const matchesSearch = !searchTerm ||
          contract.contractNumber.toLowerCase().includes(searchTerm) ||
          contract.supplierName.toLowerCase().includes(searchTerm);

        const matchesType = !typeFilter || contract.contractType === typeFilter;
        const matchesStatus = !statusFilter || contract.contractStatus === statusFilter;

        return matchesSearch && matchesType && matchesStatus;
      });

      renderTable();
      updateResultsInfo();
    }

    function clearFilters() {
      document.getElementById('searchInput').value = '';
      document.getElementById('filterType').value = '';
      document.getElementById('filterStatus').value = '';
      document.getElementById('filterCategory').value = '';
      document.getElementById('filterMinAmount').value = '';
      document.getElementById('filterMaxAmount').value = '';

      if (filterStartDatePicker) filterStartDatePicker.clear();
      if (filterEndDatePicker) filterEndDatePicker.clear();

      applyFilters();
      showToast('تم مسح جميع الفلاتر', 'success');
    }

    function renderTable() {
      const tbody = document.getElementById('contractsTableBody');
      const noDataMessage = document.getElementById('noDataMessage');

      if (filteredContracts.length === 0) {
        tbody.innerHTML = '';
        noDataMessage.classList.remove('hidden');
        return;
      }

      noDataMessage.classList.add('hidden');

      tbody.innerHTML = filteredContracts.map(contract => `
        <tr class="table-row hover:bg-gray-50 transition-colors">
          <td class="px-4 py-3 text-sm font-medium">${contract.contractNumber}</td>
          <td class="px-4 py-3 text-sm">${contract.supplierName}</td>
          <td class="px-4 py-3 text-sm">${contract.contractType}</td>
          <td class="px-4 py-3 text-sm">${formatDate(contract.startDate)}</td>
          <td class="px-4 py-3 text-sm">${contract.totalAmount.toFixed(2)} د.ك</td>
          <td class="px-4 py-3 text-sm">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusClass(contract.contractStatus)}">
              ${contract.contractStatus}
            </span>
          </td>
          <td class="px-4 py-3 text-sm">
            <button onclick="editContract('${contract.id}')" class="text-blue-600 hover:text-blue-800 mr-2 icon">✏️</button>
            <button onclick="deleteContract('${contract.id}')" class="text-red-600 hover:text-red-800 icon">🗑️</button>
          </td>
        </tr>
      `).join('');
    }

    function getStatusClass(status) {
      const classes = {
        'نشط': 'bg-green-100 text-green-800',
        'منتهي': 'bg-gray-100 text-gray-800',
        'ملغي': 'bg-red-100 text-red-800',
        'معلق': 'bg-yellow-100 text-yellow-800'
      };
      return classes[status] || 'bg-gray-100 text-gray-800';
    }

    function updateResultsInfo() {
      const total = filteredContracts.length;
      document.getElementById('resultsInfo').textContent = `عرض ${total} من ${contracts.length} عقد`;
    }

    function sortTable(field) {
      // تنفيذ بسيط للترتيب
      if (sortField === field) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        sortField = field;
        sortDirection = 'asc';
      }

      filteredContracts.sort((a, b) => {
        let aValue = a[field];
        let bValue = b[field];

        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
      });

      renderTable();
    }

    function changeItemsPerPage() {
      itemsPerPage = parseInt(document.getElementById('itemsPerPage').value);
      applyFilters();
    }

    function editContract(id) {
      const contract = contracts.find(c => c.id === id);
      if (!contract) return;

      editingContractId = id;

      // ملء النموذج
      document.getElementById('contractNumber').value = contract.contractNumber;
      document.getElementById('supplierName').value = contract.supplierName;
      document.getElementById('contractType').value = contract.contractType;
      document.getElementById('supplierCategory').value = contract.supplierCategory || '';
      document.getElementById('totalAmount').value = contract.totalAmount || '';
      document.getElementById('contractStatus').value = contract.contractStatus;
      document.getElementById('paymentTerms').value = contract.paymentTerms || '';
      document.getElementById('notes').value = contract.notes || '';

      // تعيين التواريخ
      if (startDatePicker && contract.startDate) {
        startDatePicker.setDate(contract.startDate);
      }
      if (endDatePicker && contract.endDate) {
        endDatePicker.setDate(contract.endDate);
      }

      // تحديد العناصر
      if (contract.contractItems) {
        const itemsSelect = document.getElementById('contractItems');
        Array.from(itemsSelect.options).forEach(option => {
          option.selected = contract.contractItems.includes(option.value);
        });
      }

      showForm();
      showToast('تم تحميل بيانات العقد للتعديل', 'success');
    }

    function deleteContract(id) {
      if (!confirm('هل أنت متأكد من حذف هذا العقد؟')) return;

      const index = contracts.findIndex(c => c.id === id);
      if (index !== -1) {
        contracts.splice(index, 1);
        saveContracts();
        applyFilters();
        updateContractsCount();
        showToast('تم حذف العقد بنجاح', 'success');
      }
    }
  </script>

</body>
</html>
