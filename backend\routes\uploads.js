/**
 * مسارات API للرفع والملفات
 * جمعية المنقف التعاونية
 */

const express = require('express');
const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const database = require('../utils/database');
const logger = require('../utils/logger');

const router = express.Router();

// =====================================================
// إعداد Multer للرفع
// =====================================================

// إعداد التخزين
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    let uploadPath = path.join(__dirname, '../uploads');
    
    // تحديد المجلد حسب نوع الملف
    if (file.fieldname === 'contract_document') {
      uploadPath = path.join(uploadPath, 'contracts');
    } else if (file.fieldname === 'supplier_image') {
      uploadPath = path.join(uploadPath, 'images');
    } else {
      uploadPath = path.join(uploadPath, 'documents');
    }

    // إنشاء المجلد إذا لم يكن موجوداً
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }

    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    // إنشاء اسم ملف فريد
    const uniqueName = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

// فلترة أنواع الملفات
const fileFilter = (req, file, cb) => {
  // أنواع الملفات المسموحة
  const allowedTypes = {
    'contract_document': ['.pdf', '.doc', '.docx'],
    'supplier_image': ['.jpg', '.jpeg', '.png', '.gif'],
    'general_document': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.txt']
  };

  const fileExt = path.extname(file.originalname).toLowerCase();
  const fieldAllowedTypes = allowedTypes[file.fieldname] || allowedTypes['general_document'];

  if (fieldAllowedTypes.includes(fileExt)) {
    cb(null, true);
  } else {
    cb(new Error(`نوع الملف غير مدعوم: ${fileExt}. الأنواع المدعومة: ${fieldAllowedTypes.join(', ')}`), false);
  }
};

// إعداد Multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 5 // حد أقصى 5 ملفات
  }
});

// =====================================================
// المسارات
// =====================================================

/**
 * POST /api/v1/uploads/contract-document
 * رفع وثيقة عقد
 */
router.post('/contract-document', upload.single('contract_document'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'لم يتم اختيار ملف'
      });
    }

    const { contract_id, document_type = 'عقد', description } = req.body;

    // التحقق من وجود العقد
    if (contract_id) {
      const contractExists = await database.query(
        'SELECT id FROM contracts WHERE id = ?',
        [contract_id]
      );

      if (contractExists.length === 0) {
        // حذف الملف المرفوع
        fs.unlinkSync(req.file.path);
        return res.status(404).json({
          success: false,
          message: 'العقد غير موجود'
        });
      }
    }

    // حفظ معلومات الملف في قاعدة البيانات
    const insertQuery = `
      INSERT INTO uploaded_files (
        contract_id, filename, original_name, file_path, file_size, 
        file_type, document_type, description, uploaded_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `;

    const result = await database.query(insertQuery, [
      contract_id || null,
      req.file.filename,
      req.file.originalname,
      req.file.path,
      req.file.size,
      req.file.mimetype,
      document_type,
      description || null
    ]);

    logger.info(`تم رفع وثيقة عقد: ${req.file.originalname} (ID: ${result.insertId})`);

    res.json({
      success: true,
      message: 'تم رفع الوثيقة بنجاح',
      data: {
        file_id: result.insertId,
        filename: req.file.filename,
        original_name: req.file.originalname,
        file_size: req.file.size,
        document_type,
        upload_url: `/uploads/contracts/${req.file.filename}`
      }
    });

  } catch (error) {
    // حذف الملف في حالة الخطأ
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    logger.error('خطأ في رفع وثيقة العقد:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في رفع الوثيقة'
    });
  }
});

/**
 * POST /api/v1/uploads/supplier-image
 * رفع صورة مورد
 */
router.post('/supplier-image', upload.single('supplier_image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'لم يتم اختيار صورة'
      });
    }

    const { supplier_id } = req.body;

    // التحقق من وجود المورد
    if (supplier_id) {
      const supplierExists = await database.query(
        'SELECT id FROM suppliers WHERE id = ?',
        [supplier_id]
      );

      if (supplierExists.length === 0) {
        // حذف الملف المرفوع
        fs.unlinkSync(req.file.path);
        return res.status(404).json({
          success: false,
          message: 'المورد غير موجود'
        });
      }
    }

    // تحسين الصورة باستخدام Sharp
    const optimizedImagePath = path.join(
      path.dirname(req.file.path),
      `optimized-${req.file.filename}`
    );

    await sharp(req.file.path)
      .resize(800, 600, { 
        fit: 'inside',
        withoutEnlargement: true 
      })
      .jpeg({ quality: 85 })
      .toFile(optimizedImagePath);

    // حذف الصورة الأصلية
    fs.unlinkSync(req.file.path);

    // الحصول على معلومات الصورة المحسنة
    const optimizedStats = fs.statSync(optimizedImagePath);

    // حفظ معلومات الصورة في قاعدة البيانات
    const insertQuery = `
      INSERT INTO uploaded_files (
        supplier_id, filename, original_name, file_path, file_size, 
        file_type, document_type, uploaded_at
      ) VALUES (?, ?, ?, ?, ?, ?, 'صورة مورد', NOW())
    `;

    const result = await database.query(insertQuery, [
      supplier_id || null,
      `optimized-${req.file.filename}`,
      req.file.originalname,
      optimizedImagePath,
      optimizedStats.size,
      'image/jpeg'
    ]);

    // تحديث صورة المورد إذا تم تحديد supplier_id
    if (supplier_id) {
      await database.query(
        'UPDATE suppliers SET image_url = ? WHERE id = ?',
        [`/uploads/images/optimized-${req.file.filename}`, supplier_id]
      );
    }

    logger.info(`تم رفع صورة مورد: ${req.file.originalname} (ID: ${result.insertId})`);

    res.json({
      success: true,
      message: 'تم رفع الصورة بنجاح',
      data: {
        file_id: result.insertId,
        filename: `optimized-${req.file.filename}`,
        original_name: req.file.originalname,
        file_size: optimizedStats.size,
        upload_url: `/uploads/images/optimized-${req.file.filename}`
      }
    });

  } catch (error) {
    // حذف الملفات في حالة الخطأ
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    logger.error('خطأ في رفع صورة المورد:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في رفع الصورة'
    });
  }
});

/**
 * POST /api/v1/uploads/multiple
 * رفع ملفات متعددة
 */
router.post('/multiple', upload.array('files', 5), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'لم يتم اختيار ملفات'
      });
    }

    const { contract_id, supplier_id, document_type = 'وثيقة عامة' } = req.body;
    const uploadedFiles = [];

    // معالجة كل ملف
    for (const file of req.files) {
      try {
        // حفظ معلومات الملف في قاعدة البيانات
        const insertQuery = `
          INSERT INTO uploaded_files (
            contract_id, supplier_id, filename, original_name, file_path, 
            file_size, file_type, document_type, uploaded_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        `;

        const result = await database.query(insertQuery, [
          contract_id || null,
          supplier_id || null,
          file.filename,
          file.originalname,
          file.path,
          file.size,
          file.mimetype,
          document_type
        ]);

        uploadedFiles.push({
          file_id: result.insertId,
          filename: file.filename,
          original_name: file.originalname,
          file_size: file.size,
          upload_url: `/uploads/documents/${file.filename}`
        });

      } catch (fileError) {
        logger.error(`خطأ في معالجة الملف ${file.originalname}:`, fileError);
        // حذف الملف في حالة الخطأ
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      }
    }

    logger.info(`تم رفع ${uploadedFiles.length} ملف بنجاح`);

    res.json({
      success: true,
      message: `تم رفع ${uploadedFiles.length} ملف بنجاح`,
      data: uploadedFiles
    });

  } catch (error) {
    // حذف جميع الملفات في حالة الخطأ
    if (req.files) {
      req.files.forEach(file => {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      });
    }

    logger.error('خطأ في رفع الملفات المتعددة:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في رفع الملفات'
    });
  }
});

/**
 * GET /api/v1/uploads/files/:id
 * جلب معلومات ملف محدد
 */
router.get('/files/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT 
        id, contract_id, supplier_id, filename, original_name, 
        file_size, file_type, document_type, description, uploaded_at
      FROM uploaded_files 
      WHERE id = ?
    `;

    const files = await database.query(query, [id]);

    if (files.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'الملف غير موجود'
      });
    }

    res.json({
      success: true,
      data: files[0]
    });

  } catch (error) {
    logger.error('خطأ في جلب معلومات الملف:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب معلومات الملف'
    });
  }
});

/**
 * DELETE /api/v1/uploads/files/:id
 * حذف ملف
 */
router.delete('/files/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // جلب معلومات الملف
    const fileInfo = await database.query(
      'SELECT file_path FROM uploaded_files WHERE id = ?',
      [id]
    );

    if (fileInfo.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'الملف غير موجود'
      });
    }

    // حذف الملف من النظام
    const filePath = fileInfo[0].file_path;
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // حذف السجل من قاعدة البيانات
    await database.query('DELETE FROM uploaded_files WHERE id = ?', [id]);

    logger.info(`تم حذف الملف: ${filePath} (ID: ${id})`);

    res.json({
      success: true,
      message: 'تم حذف الملف بنجاح'
    });

  } catch (error) {
    logger.error('خطأ في حذف الملف:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف الملف'
    });
  }
});

module.exports = router;
