-- =====================================================
-- نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
-- هيكل قاعدة البيانات الرئيسي
-- =====================================================

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS supplier_contracts 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE supplier_contracts;

-- =====================================================
-- جدول المستخدمين
-- =====================================================
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'manager', 'employee', 'viewer') DEFAULT 'employee',
    department VARCHAR(50),
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_active (is_active)
);

-- =====================================================
-- جدول فئات الموردين
-- =====================================================
CREATE TABLE supplier_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    name_en VARCHAR(100),
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(7) DEFAULT '#3b82f6',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_active (is_active)
);

-- =====================================================
-- جدول الموردين
-- =====================================================
CREATE TABLE suppliers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    name_en VARCHAR(200),
    category_id INT,
    contact_person VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    city VARCHAR(50),
    country VARCHAR(50) DEFAULT 'الكويت',
    postal_code VARCHAR(10),
    commercial_license VARCHAR(50),
    tax_number VARCHAR(50),
    bank_name VARCHAR(100),
    bank_account VARCHAR(50),
    iban VARCHAR(50),
    credit_limit DECIMAL(12,3) DEFAULT 0,
    payment_terms VARCHAR(100),
    rating TINYINT DEFAULT 5 CHECK (rating >= 1 AND rating <= 5),
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (category_id) REFERENCES supplier_categories(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_supplier_code (supplier_code),
    INDEX idx_name (name),
    INDEX idx_category (category_id),
    INDEX idx_active (is_active),
    INDEX idx_city (city)
);

-- =====================================================
-- جدول أنواع العقود
-- =====================================================
CREATE TABLE contract_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    name_en VARCHAR(100),
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(7) DEFAULT '#3b82f6',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_active (is_active)
);

-- =====================================================
-- جدول العقود الرئيسي
-- =====================================================
CREATE TABLE contracts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contract_number VARCHAR(50) UNIQUE NOT NULL,
    contract_name VARCHAR(200) NOT NULL,
    supplier_id INT NOT NULL,
    contract_type_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    total_amount DECIMAL(12,3) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'KWD',
    payment_terms VARCHAR(200),
    status ENUM('draft', 'active', 'expired', 'cancelled', 'suspended') DEFAULT 'draft',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    auto_renewal BOOLEAN DEFAULT FALSE,
    renewal_period INT DEFAULT 12, -- بالأشهر
    contract_color VARCHAR(7) DEFAULT '#3b82f6',
    description TEXT,
    terms_conditions TEXT,
    notes TEXT,
    attachment_path VARCHAR(500),
    created_by INT,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE,
    FOREIGN KEY (contract_type_id) REFERENCES contract_types(id) ON DELETE RESTRICT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_contract_number (contract_number),
    INDEX idx_supplier (supplier_id),
    INDEX idx_type (contract_type_id),
    INDEX idx_status (status),
    INDEX idx_dates (start_date, end_date),
    INDEX idx_amount (total_amount),
    INDEX idx_priority (priority)
);

-- =====================================================
-- جدول عناصر العقود
-- =====================================================
CREATE TABLE contract_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contract_id INT NOT NULL,
    item_name VARCHAR(200) NOT NULL,
    item_code VARCHAR(50),
    description TEXT,
    quantity DECIMAL(10,3) DEFAULT 1,
    unit VARCHAR(50),
    unit_price DECIMAL(10,3) DEFAULT 0,
    total_price DECIMAL(12,3) DEFAULT 0,
    specifications TEXT,
    delivery_terms VARCHAR(200),
    warranty_period INT DEFAULT 0, -- بالأشهر
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE,
    
    INDEX idx_contract (contract_id),
    INDEX idx_item_code (item_code),
    INDEX idx_item_name (item_name)
);

-- =====================================================
-- جدول مدفوعات العقود
-- =====================================================
CREATE TABLE contract_payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contract_id INT NOT NULL,
    payment_number VARCHAR(50),
    payment_date DATE NOT NULL,
    amount DECIMAL(12,3) NOT NULL,
    payment_method ENUM('cash', 'check', 'transfer', 'card') DEFAULT 'transfer',
    reference_number VARCHAR(100),
    description TEXT,
    status ENUM('pending', 'paid', 'cancelled') DEFAULT 'pending',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_contract (contract_id),
    INDEX idx_payment_date (payment_date),
    INDEX idx_status (status),
    INDEX idx_amount (amount)
);

-- =====================================================
-- جدول تجديدات العقود
-- =====================================================
CREATE TABLE contract_renewals (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contract_id INT NOT NULL,
    old_end_date DATE NOT NULL,
    new_end_date DATE NOT NULL,
    renewal_amount DECIMAL(12,3) DEFAULT 0,
    renewal_terms TEXT,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_contract (contract_id),
    INDEX idx_dates (old_end_date, new_end_date)
);

-- =====================================================
-- جدول مرفقات العقود
-- =====================================================
CREATE TABLE contract_attachments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contract_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    description TEXT,
    uploaded_by INT,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_contract (contract_id),
    INDEX idx_file_type (file_type)
);

-- =====================================================
-- جدول سجل العمليات
-- =====================================================
CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    record_id INT NOT NULL,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_table_record (table_name, record_id),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- جدول الإعدادات
-- =====================================================
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_key (setting_key),
    INDEX idx_public (is_public)
);

-- =====================================================
-- جدول الإشعارات
-- =====================================================
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    related_table VARCHAR(50),
    related_id INT,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_user (user_id),
    INDEX idx_read (is_read),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at)
);

-- =====================================================
-- إدراج البيانات الأولية
-- =====================================================

-- فئات الموردين
INSERT INTO supplier_categories (name, name_en, description, icon, color) VALUES
('بهارات', 'Spices', 'موردو البهارات والتوابل', '🌶️', '#ef4444'),
('مواد استهلاكية', 'Consumer Goods', 'موردو المواد الاستهلاكية', '🧽', '#10b981'),
('أجبان ومنتجات ألبان', 'Dairy Products', 'موردو الأجبان والألبان', '🧀', '#f59e0b'),
('خدمات', 'Services', 'مقدمو الخدمات المختلفة', '🔧', '#8b5cf6'),
('صيانة', 'Maintenance', 'شركات الصيانة والإصلاح', '⚙️', '#6b7280');

-- أنواع العقود
INSERT INTO contract_types (name, name_en, description, icon, color) VALUES
('توريد', 'Supply', 'عقود توريد البضائع والمواد', '📦', '#3b82f6'),
('إيجار', 'Rental', 'عقود إيجار العيون والمساحات', '🏠', '#10b981'),
('خدمات', 'Services', 'عقود الخدمات المختلفة', '🔧', '#f59e0b'),
('صيانة', 'Maintenance', 'عقود الصيانة والدعم الفني', '⚙️', '#ef4444');

-- المستخدم الافتراضي (admin)
INSERT INTO users (username, email, password_hash, full_name, role, department) VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', 'تقنية المعلومات');

-- إعدادات النظام الأساسية
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('company_name', 'جمعية المنقف التعاونية', 'string', 'اسم الشركة', TRUE),
('company_name_en', 'Manqaf Cooperative Society', 'string', 'اسم الشركة بالإنجليزية', TRUE),
('currency', 'KWD', 'string', 'العملة الافتراضية', TRUE),
('timezone', 'Asia/Kuwait', 'string', 'المنطقة الزمنية', FALSE),
('date_format', 'YYYY-MM-DD', 'string', 'تنسيق التاريخ', TRUE),
('contract_number_prefix', 'CNT', 'string', 'بادئة رقم العقد', FALSE),
('auto_backup', 'true', 'boolean', 'النسخ الاحتياطي التلقائي', FALSE),
('email_notifications', 'true', 'boolean', 'إشعارات البريد الإلكتروني', FALSE),
('contract_expiry_alert_days', '30', 'number', 'أيام تنبيه انتهاء العقد', FALSE),
('max_file_size', '10485760', 'number', 'الحد الأقصى لحجم الملف (بايت)', FALSE);

-- =====================================================
-- إنشاء المشاهدات (Views)
-- =====================================================

-- مشاهدة العقود مع تفاصيل الموردين
CREATE VIEW contracts_with_suppliers AS
SELECT 
    c.*,
    s.name as supplier_name,
    s.supplier_code,
    s.contact_person,
    s.phone as supplier_phone,
    s.email as supplier_email,
    sc.name as category_name,
    ct.name as contract_type_name,
    u1.full_name as created_by_name,
    u2.full_name as approved_by_name,
    DATEDIFF(c.end_date, CURDATE()) as days_to_expiry,
    CASE 
        WHEN c.end_date < CURDATE() THEN 'expired'
        WHEN DATEDIFF(c.end_date, CURDATE()) <= 30 THEN 'expiring_soon'
        ELSE 'active'
    END as expiry_status
FROM contracts c
LEFT JOIN suppliers s ON c.supplier_id = s.id
LEFT JOIN supplier_categories sc ON s.category_id = sc.id
LEFT JOIN contract_types ct ON c.contract_type_id = ct.id
LEFT JOIN users u1 ON c.created_by = u1.id
LEFT JOIN users u2 ON c.approved_by = u2.id;

-- مشاهدة إحصائيات العقود
CREATE VIEW contract_statistics AS
SELECT 
    COUNT(*) as total_contracts,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_contracts,
    COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_contracts,
    COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_contracts,
    SUM(CASE WHEN status = 'active' THEN total_amount ELSE 0 END) as active_contracts_value,
    SUM(total_amount) as total_contracts_value,
    AVG(total_amount) as average_contract_value
FROM contracts;

-- =====================================================
-- إنشاء المؤشرات المحسنة للأداء
-- =====================================================

-- مؤشرات مركبة للبحث السريع
CREATE INDEX idx_contracts_search ON contracts(contract_number, contract_name, status);
CREATE INDEX idx_suppliers_search ON suppliers(supplier_code, name, phone, email);
CREATE INDEX idx_contract_dates_status ON contracts(start_date, end_date, status);
CREATE INDEX idx_payments_contract_date ON contract_payments(contract_id, payment_date, status);

-- =====================================================
-- إنشاء المحفزات (Triggers)
-- =====================================================

-- محفز لتحديث إجمالي مبلغ العقد عند تغيير العناصر
DELIMITER //
CREATE TRIGGER update_contract_total 
AFTER INSERT ON contract_items
FOR EACH ROW
BEGIN
    UPDATE contracts 
    SET total_amount = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM contract_items 
        WHERE contract_id = NEW.contract_id
    )
    WHERE id = NEW.contract_id;
END//

CREATE TRIGGER update_contract_total_on_update
AFTER UPDATE ON contract_items
FOR EACH ROW
BEGIN
    UPDATE contracts 
    SET total_amount = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM contract_items 
        WHERE contract_id = NEW.contract_id
    )
    WHERE id = NEW.contract_id;
END//

CREATE TRIGGER update_contract_total_on_delete
AFTER DELETE ON contract_items
FOR EACH ROW
BEGIN
    UPDATE contracts 
    SET total_amount = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM contract_items 
        WHERE contract_id = OLD.contract_id
    )
    WHERE id = OLD.contract_id;
END//
DELIMITER ;

-- =====================================================
-- إنشاء الإجراءات المخزنة
-- =====================================================

-- إجراء للحصول على إحصائيات شاملة
DELIMITER //
CREATE PROCEDURE GetDashboardStats()
BEGIN
    SELECT 
        (SELECT COUNT(*) FROM contracts WHERE status = 'active') as active_contracts,
        (SELECT COUNT(*) FROM contracts WHERE status = 'expired') as expired_contracts,
        (SELECT COUNT(*) FROM contracts WHERE DATEDIFF(end_date, CURDATE()) <= 30 AND status = 'active') as expiring_soon,
        (SELECT COUNT(*) FROM suppliers WHERE is_active = TRUE) as active_suppliers,
        (SELECT SUM(total_amount) FROM contracts WHERE status = 'active') as total_active_value,
        (SELECT COUNT(*) FROM contract_payments WHERE status = 'pending') as pending_payments;
END//
DELIMITER ;

-- إجراء للبحث المتقدم في العقود
DELIMITER //
CREATE PROCEDURE SearchContracts(
    IN search_term VARCHAR(255),
    IN contract_status VARCHAR(50),
    IN supplier_id INT,
    IN date_from DATE,
    IN date_to DATE,
    IN limit_count INT,
    IN offset_count INT
)
BEGIN
    SELECT c.*, s.name as supplier_name, ct.name as contract_type_name
    FROM contracts c
    LEFT JOIN suppliers s ON c.supplier_id = s.id
    LEFT JOIN contract_types ct ON c.contract_type_id = ct.id
    WHERE 
        (search_term IS NULL OR 
         c.contract_number LIKE CONCAT('%', search_term, '%') OR
         c.contract_name LIKE CONCAT('%', search_term, '%') OR
         s.name LIKE CONCAT('%', search_term, '%'))
    AND (contract_status IS NULL OR c.status = contract_status)
    AND (supplier_id IS NULL OR c.supplier_id = supplier_id)
    AND (date_from IS NULL OR c.start_date >= date_from)
    AND (date_to IS NULL OR c.end_date <= date_to)
    ORDER BY c.created_at DESC
    LIMIT limit_count OFFSET offset_count;
END//
DELIMITER ;

-- =====================================================
-- إنشاء المستخدمين وصلاحيات قاعدة البيانات
-- =====================================================

-- مستخدم التطبيق
CREATE USER IF NOT EXISTS 'contracts_app'@'localhost' IDENTIFIED BY 'SecurePassword123!';
GRANT SELECT, INSERT, UPDATE, DELETE ON supplier_contracts.* TO 'contracts_app'@'localhost';

-- مستخدم القراءة فقط للتقارير
CREATE USER IF NOT EXISTS 'contracts_readonly'@'localhost' IDENTIFIED BY 'ReadOnlyPass123!';
GRANT SELECT ON supplier_contracts.* TO 'contracts_readonly'@'localhost';

-- تطبيق الصلاحيات
FLUSH PRIVILEGES;
