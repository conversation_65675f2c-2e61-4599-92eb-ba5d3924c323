@echo off
chcp 65001 >nul
title تطبيق نظام إدارة العقود - سطح المكتب | محمد مرزوق العقاب

echo.
echo 🎉 ===============================================
echo 💻 تطبيق نظام إدارة العقود - سطح المكتب
echo 🏢 جمعية المنقف التعاونية
echo.
echo 👨‍💻 تطوير وتنفيذ: محمد مرزوق العقاب
echo 🎯 مطور نظم معلومات محترف ومتخصص
echo 🎉 ===============================================
echo.

echo 📦 التحقق من التبعيات...
if not exist "node_modules" (
    echo 🔄 تثبيت التبعيات...
    npm install
    echo.
)

echo 🎨 إنشاء الأيقونات...
if not exist "assets" mkdir assets
node assets/create-icons.js

echo 📊 إنشاء البيانات النموذجية...
if not exist "sample_data" (
    echo 📄 إنشاء ملفات البيانات النموذجية...
    node backend/utils/create-excel-files.js
    echo.
)

echo.
echo 🎉 ===============================================
echo 🚀 بدء تشغيل تطبيق سطح المكتب
echo 💻 تطبيق Windows App متطور
echo 🌐 يعمل بدون إنترنت
echo 📊 قاعدة بيانات محلية SQLite
echo 👨‍💻 المطور: محمد مرزوق العقاب
echo ✅ جميع الوظائف نشطة ومفعلة
echo 🎉 ===============================================
echo.

echo 🔗 مميزات التطبيق:
echo    💻 واجهة Windows App احترافية
echo    🎮 قوائم وأزرار تفاعلية
echo    ⌨️ اختصارات لوحة المفاتيح
echo    📁 نوافذ حوار لاختيار الملفات
echo    🔔 إشعارات النظام
echo    🖼️ أيقونة مخصصة في شريط المهام
echo    📊 نوافذ متعددة ومرنة
echo    🎨 تصميم متجاوب ومتطور
echo.

echo 🎯 الواجهات المتاحة في التطبيق:
echo    🏠 الصفحة الرئيسية (Ctrl+H)
echo    👑 الواجهة الاحترافية (Ctrl+P)
echo    👥 إدارة الموردين (Ctrl+1)
echo    📋 إدارة العقود (Ctrl+2)
echo    👁️ إدارة العيون (Ctrl+3)
echo    📊 التقارير (Ctrl+4)
echo    📁 رفع الملفات (Ctrl+U)
echo.

echo ⌨️ اختصارات لوحة المفاتيح:
echo    F5 / Ctrl+R - إعادة تحميل
echo    F11 - ملء الشاشة
echo    F12 - أدوات المطور
echo    Ctrl+Plus - تكبير
echo    Ctrl+Minus - تصغير
echo    Ctrl+0 - الحجم الطبيعي
echo    Ctrl+Q - خروج
echo.

echo 📊 البيانات النموذجية المتاحة:
echo    👥 25 مورد (بهارات، استهلاكي، أجبان)
echo    📋 30 عقد (توريد، إيجار، خدمات)
echo    👁️ 45 إيجار عين نشط
echo    🏢 20 طبلية × 4 أقسام = 80 عين
echo    💰 إيرادات شهرية: 2,700 د.ك
echo.

echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.

echo 💡 نصائح الاستخدام:
echo    • استخدم القوائم العلوية للتنقل السريع
echo    • اضغط F11 للشاشة الكاملة
echo    • استخدم Ctrl+أرقام للانتقال السريع
echo    • ارفع الملفات من قائمة "أدوات"
echo    • تحقق من "مساعدة" للمزيد من المعلومات
echo.

timeout /t 3 /nobreak >nul

echo 🚀 تشغيل تطبيق سطح المكتب...
echo.

npm run electron
