<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>تشغيل نظام إدارة العقود - جمعية المنقف</title>
  
  <!-- CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');
    
    body {
      font-family: 'Cairo', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    
    .glass {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    }
    
    .btn {
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
    
    .btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s;
    }
    
    .btn:hover::before {
      left: 100%;
    }
    
    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }
    
    .interface-card {
      transition: all 0.4s ease;
    }
    
    .interface-card:hover {
      transform: translateY(-10px) scale(1.02);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }
    
    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-20px); }
    }
    
    .floating {
      animation: float 6s ease-in-out infinite;
    }
  </style>
</head>
<body class="flex items-center justify-center min-h-screen p-6">
  <div class="max-w-6xl w-full">
    <!-- Header -->
    <div class="text-center mb-12">
      <div class="w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6 floating">
        <i class="fas fa-building text-white text-4xl"></i>
      </div>
      <h1 class="text-5xl font-bold text-white mb-4">نظام إدارة عقود الموردين والإيجارات</h1>
      <p class="text-xl text-white/80 mb-8">جمعية المنقف التعاونية</p>

      <!-- Developer Credit -->
      <div class="glass rounded-2xl p-6 max-w-md mx-auto mb-8">
        <div class="flex items-center justify-center gap-4">
          <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
            <i class="fas fa-code text-white"></i>
          </div>
          <div class="text-right">
            <p class="text-white font-bold text-lg">تطوير وتنفيذ</p>
            <p class="text-blue-400 font-semibold text-xl">محمد مرزوق العقاب</p>
            <p class="text-white/70 text-sm">مطور نظم معلومات محترف</p>
          </div>
        </div>
      </div>

      <div class="glass rounded-2xl p-6 max-w-2xl mx-auto">
        <p class="text-white/90 leading-relaxed">
          اختر الواجهة المناسبة لاحتياجاتك. جميع الواجهات متصلة بنفس النظام الخلفي وتوفر نفس الوظائف مع تصاميم مختلفة.
        </p>
      </div>
    </div>
    
    <!-- Interface Options -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      
      <!-- Ultimate Interface -->
      <div class="interface-card glass rounded-3xl p-8 text-center">
        <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-crown text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">الواجهة المتطورة</h3>
        <p class="text-white/70 mb-6 leading-relaxed">
          واجهة حديثة ومتطورة مع تأثيرات Glass Morphism وتصميم احترافي متقدم
        </p>
        <div class="space-y-3 mb-6">
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            تصميم Glass Morphism متطور
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            قوائم منسدلة تفاعلية
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            تأثيرات بصرية متقدمة
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            تجربة مستخدم مميزة
          </div>
        </div>
        <a href="ultimate_interface.html" class="btn bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-full font-medium w-full block text-center">
          <i class="fas fa-rocket ml-2"></i>
          تشغيل الواجهة المتطورة
        </a>
      </div>
      
      <!-- Professional Interface -->
      <div class="interface-card glass rounded-3xl p-8 text-center">
        <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-briefcase text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">الواجهة الاحترافية</h3>
        <p class="text-white/70 mb-6 leading-relaxed">
          واجهة احترافية متوازنة تجمع بين الأناقة والوظائف العملية
        </p>
        <div class="space-y-3 mb-6">
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            تصميم احترافي متوازن
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            سهولة في الاستخدام
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            أداء سريع ومستقر
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            مناسب للاستخدام اليومي
          </div>
        </div>
        <a href="professional_interface.html" class="btn bg-gradient-to-r from-green-500 to-teal-600 text-white px-6 py-3 rounded-full font-medium w-full block text-center">
          <i class="fas fa-play ml-2"></i>
          تشغيل الواجهة الاحترافية
        </a>
      </div>
      
      <!-- Advanced Contracts -->
      <div class="interface-card glass rounded-3xl p-8 text-center">
        <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-file-contract text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">إدارة العقود المتقدمة</h3>
        <p class="text-white/70 mb-6 leading-relaxed">
          واجهة متخصصة في إدارة العقود مع جميع الأدوات المتقدمة
        </p>
        <div class="space-y-3 mb-6">
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            إدارة شاملة للعقود
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            تتبع المواعيد والتنبيهات
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            إدارة الدفعات والفواتير
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            تقارير مفصلة
          </div>
        </div>
        <a href="advanced_contracts_system.html" class="btn bg-gradient-to-r from-orange-500 to-red-600 text-white px-6 py-3 rounded-full font-medium w-full block text-center">
          <i class="fas fa-cogs ml-2"></i>
          تشغيل إدارة العقود
        </a>
      </div>
      
      <!-- Enhanced Web Interface -->
      <div class="interface-card glass rounded-3xl p-8 text-center">
        <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-users text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">إدارة الموردين المحسنة</h3>
        <p class="text-white/70 mb-6 leading-relaxed">
          واجهة متخصصة في إدارة الموردين مع أدوات متقدمة للبحث والتصفية
        </p>
        <div class="space-y-3 mb-6">
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            إدارة شاملة للموردين
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            تصنيف حسب الفئات
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            بحث وتصفية متقدمة
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            إدارة الوثائق والملفات
          </div>
        </div>
        <a href="enhanced_web_interface.html" class="btn bg-gradient-to-r from-purple-500 to-pink-600 text-white px-6 py-3 rounded-full font-medium w-full block text-center">
          <i class="fas fa-user-cog ml-2"></i>
          تشغيل إدارة الموردين
        </a>
      </div>
      
      <!-- Simple Web Interface -->
      <div class="interface-card glass rounded-3xl p-8 text-center">
        <div class="w-16 h-16 bg-gradient-to-r from-indigo-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-desktop text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">الواجهة البسيطة</h3>
        <p class="text-white/70 mb-6 leading-relaxed">
          واجهة بسيطة وسهلة الاستخدام للمبتدئين والاستخدام السريع
        </p>
        <div class="space-y-3 mb-6">
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            تصميم بسيط وواضح
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            سهل التعلم والاستخدام
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            أداء سريع وخفيف
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            مناسب للمبتدئين
          </div>
        </div>
        <a href="web_interface.html" class="btn bg-gradient-to-r from-indigo-500 to-blue-600 text-white px-6 py-3 rounded-full font-medium w-full block text-center">
          <i class="fas fa-mouse-pointer ml-2"></i>
          تشغيل الواجهة البسيطة
        </a>
      </div>
      
      <!-- Frontend Dashboard -->
      <div class="interface-card glass rounded-3xl p-8 text-center">
        <div class="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-chart-pie text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">لوحة التحكم الرئيسية</h3>
        <p class="text-white/70 mb-6 leading-relaxed">
          لوحة تحكم شاملة مع إحصائيات وتقارير مفصلة ورسوم بيانية
        </p>
        <div class="space-y-3 mb-6">
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            إحصائيات شاملة
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            رسوم بيانية تفاعلية
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            تقارير مالية مفصلة
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            مراقبة الأداء
          </div>
        </div>
        <a href="frontend/index.html" class="btn bg-gradient-to-r from-yellow-500 to-orange-600 text-white px-6 py-3 rounded-full font-medium w-full block text-center">
          <i class="fas fa-tachometer-alt ml-2"></i>
          تشغيل لوحة التحكم
        </a>
      </div>

      <!-- Professional Ultimate Interface -->
      <div class="interface-card glass rounded-3xl p-8 text-center">
        <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-crown text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">الواجهة الاحترافية المتطورة</h3>
        <p class="text-white/70 mb-6 leading-relaxed">
          واجهة احترافية متطورة مع تصميم Glass Morphism وألوان عالية التباين
        </p>
        <div class="space-y-3 mb-6">
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            تصميم احترافي متطور
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            ألوان واضحة ومتباينة
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            قوائم تفاعلية متقدمة
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            تجربة مستخدم مثالية
          </div>
        </div>
        <a href="professional_ultimate_interface.html" class="btn bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-full font-medium w-full block text-center">
          <i class="fas fa-crown ml-2"></i>
          الواجهة الاحترافية
        </a>
      </div>

      <!-- Advanced Management Interface -->
      <div class="interface-card glass rounded-3xl p-8 text-center">
        <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-cogs text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">واجهة الإدارة المتقدمة</h3>
        <p class="text-white/70 mb-6 leading-relaxed">
          واجهة إدارة متقدمة مع ألوان عالية التباين وتصميم احترافي للمديرين
        </p>
        <div class="space-y-3 mb-6">
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            ألوان عالية التباين
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            إدارة متقدمة للنظام
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            إحصائيات في الوقت الفعلي
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            تنبيهات ذكية متطورة
          </div>
        </div>
        <a href="advanced_management_interface.html" class="btn bg-gradient-to-r from-purple-500 to-pink-600 text-white px-6 py-3 rounded-full font-medium w-full block text-center">
          <i class="fas fa-cogs ml-2"></i>
          واجهة الإدارة المتقدمة
        </a>
      </div>

      <!-- Upload System -->
      <div class="interface-card glass rounded-3xl p-8 text-center">
        <div class="w-16 h-16 bg-gradient-to-r from-teal-500 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-cloud-upload-alt text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">رفع ملفات الموردين</h3>
        <p class="text-white/70 mb-6 leading-relaxed">
          رفع ملفات Excel أو CSV تحتوي على بيانات الموردين لإضافتهم بشكل جماعي
        </p>
        <div class="space-y-3 mb-6">
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            دعم ملفات Excel و CSV
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            التحقق من صحة البيانات
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            تقارير مفصلة للأخطاء
          </div>
          <div class="flex items-center text-white/80 text-sm">
            <i class="fas fa-check text-green-400 ml-2"></i>
            نماذج جاهزة للتحميل
          </div>
        </div>
        <a href="upload_suppliers.html" class="btn bg-gradient-to-r from-teal-500 to-cyan-600 text-white px-6 py-3 rounded-full font-medium w-full block text-center">
          <i class="fas fa-upload ml-2"></i>
          رفع ملفات الموردين
        </a>
      </div>
    </div>
    
    <!-- Footer -->
    <div class="text-center mt-12">
      <div class="glass rounded-2xl p-6">
        <p class="text-white/80 mb-4">
          <i class="fas fa-info-circle ml-2"></i>
          جميع الواجهات متصلة بنفس قاعدة البيانات والنظام الخلفي
        </p>
        <div class="flex flex-wrap justify-center gap-4 text-sm text-white/70">
          <span><i class="fas fa-server ml-1"></i> Backend API متاح</span>
          <span><i class="fas fa-database ml-1"></i> قاعدة البيانات جاهزة</span>
          <span><i class="fas fa-shield-alt ml-1"></i> نظام آمان متقدم</span>
          <span><i class="fas fa-mobile-alt ml-1"></i> متجاوب مع الجوال</span>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // Add some interactivity
    document.addEventListener('DOMContentLoaded', function() {
      // Add hover effects to cards
      const cards = document.querySelectorAll('.interface-card');
      cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0) scale(1)';
        });
      });
      
      // Add click animation to buttons
      const buttons = document.querySelectorAll('.btn');
      buttons.forEach(button => {
        button.addEventListener('click', function(e) {
          // Create ripple effect
          const ripple = document.createElement('span');
          const rect = this.getBoundingClientRect();
          const size = Math.max(rect.width, rect.height);
          const x = e.clientX - rect.left - size / 2;
          const y = e.clientY - rect.top - size / 2;
          
          ripple.style.width = ripple.style.height = size + 'px';
          ripple.style.left = x + 'px';
          ripple.style.top = y + 'px';
          ripple.classList.add('ripple');
          
          this.appendChild(ripple);
          
          setTimeout(() => {
            ripple.remove();
          }, 600);
        });
      });
    });
  </script>
  
  <style>
    .ripple {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transform: scale(0);
      animation: ripple-animation 0.6s linear;
      pointer-events: none;
    }
    
    @keyframes ripple-animation {
      to {
        transform: scale(4);
        opacity: 0;
      }
    }
  </style>
</body>
</html>
