<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>نظام إدارة عقود الموردين والإيجارات - جمعية المنقف التعاونية</title>
  
  <!-- Meta Tags -->
  <meta name="description" content="نظام متكامل لإدارة عقود الموردين وإيجار العيون - تطوير محمد مرزوق العقاب">
  <meta name="keywords" content="عقود, موردين, إيجار, جمعية المنقف, الكويت, محمد مرزوق العقاب">
  <meta name="author" content="محمد مرزوق العقاب">
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
  
  <!-- CSS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
  
  <!-- Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            'cairo': ['Cairo', 'sans-serif'],
          },
          colors: {
            'primary': {
              50: '#f0f9ff',
              100: '#e0f2fe',
              200: '#bae6fd',
              300: '#7dd3fc',
              400: '#38bdf8',
              500: '#0ea5e9',
              600: '#0284c7',
              700: '#0369a1',
              800: '#075985',
              900: '#0c4a6e',
            },
            'secondary': {
              50: '#fdf4ff',
              100: '#fae8ff',
              200: '#f5d0fe',
              300: '#f0abfc',
              400: '#e879f9',
              500: '#d946ef',
              600: '#c026d3',
              700: '#a21caf',
              800: '#86198f',
              900: '#701a75',
            },
            'accent': {
              50: '#f0fdf4',
              100: '#dcfce7',
              200: '#bbf7d0',
              300: '#86efac',
              400: '#4ade80',
              500: '#22c55e',
              600: '#16a34a',
              700: '#15803d',
              800: '#166534',
              900: '#14532d',
            },
            'dark': {
              50: '#f8fafc',
              100: '#f1f5f9',
              200: '#e2e8f0',
              300: '#cbd5e1',
              400: '#94a3b8',
              500: '#64748b',
              600: '#475569',
              700: '#334155',
              800: '#1e293b',
              900: '#0f172a',
            }
          },
          animation: {
            'float': 'float 6s ease-in-out infinite',
            'pulse-slow': 'pulse 3s ease-in-out infinite',
            'bounce-slow': 'bounce 2s ease-in-out infinite',
            'fade-in': 'fadeIn 0.8s ease-out',
            'slide-up': 'slideUp 0.8s ease-out',
            'scale-in': 'scaleIn 0.8s ease-out',
          }
        }
      }
    }
  </script>
  
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap');
    
    :root {
      /* Professional Color Palette */
      --bg-primary: #0f172a;
      --bg-secondary: #1e293b;
      --bg-tertiary: #334155;
      --bg-card: #475569;
      
      --text-primary: #ffffff;
      --text-secondary: #e2e8f0;
      --text-tertiary: #cbd5e1;
      --text-muted: #94a3b8;
      
      --accent-primary: #0ea5e9;
      --accent-secondary: #d946ef;
      --accent-success: #22c55e;
      --accent-warning: #f59e0b;
      --accent-danger: #ef4444;
      
      --border-primary: #475569;
      --border-secondary: #64748b;
      
      --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
      --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
      --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
      --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
      
      --gradient-primary: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
      --gradient-secondary: linear-gradient(135deg, #d946ef 0%, #8b5cf6 100%);
      --gradient-success: linear-gradient(135deg, #22c55e 0%, #10b981 100%);
      --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
      --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Cairo', sans-serif;
      background: var(--bg-primary);
      color: var(--text-primary);
      min-height: 100vh;
      overflow-x: hidden;
      line-height: 1.6;
    }
    
    /* Professional Background Pattern */
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: 
        radial-gradient(circle at 25% 25%, rgba(14, 165, 233, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(217, 70, 239, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(34, 197, 94, 0.05) 0%, transparent 50%);
      z-index: -1;
      animation: float 20s ease-in-out infinite;
    }
    
    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      33% { transform: translateY(-20px) rotate(120deg); }
      66% { transform: translateY(20px) rotate(240deg); }
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes slideUp {
      from { opacity: 0; transform: translateY(50px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes scaleIn {
      from { opacity: 0; transform: scale(0.9); }
      to { opacity: 1; transform: scale(1); }
    }
    
    /* Professional Cards */
    .pro-card {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: 16px;
      box-shadow: var(--shadow-lg);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
    }
    
    .pro-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: var(--gradient-primary);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    .pro-card:hover {
      transform: translateY(-8px);
      box-shadow: var(--shadow-xl);
      border-color: var(--accent-primary);
    }
    
    .pro-card:hover::before {
      opacity: 1;
    }
    
    /* Professional Buttons */
    .pro-btn {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 12px 24px;
      border: none;
      border-radius: 12px;
      font-weight: 600;
      font-size: 14px;
      text-decoration: none;
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      font-family: 'Cairo', sans-serif;
    }
    
    .pro-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }
    
    .pro-btn:hover::before {
      left: 100%;
    }
    
    .pro-btn-primary {
      background: var(--gradient-primary);
      color: var(--text-primary);
      box-shadow: 0 4px 14px 0 rgba(14, 165, 233, 0.3);
    }
    
    .pro-btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px 0 rgba(14, 165, 233, 0.4);
    }
    
    .pro-btn-secondary {
      background: var(--gradient-secondary);
      color: var(--text-primary);
      box-shadow: 0 4px 14px 0 rgba(217, 70, 239, 0.3);
    }
    
    .pro-btn-secondary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px 0 rgba(217, 70, 239, 0.4);
    }
    
    .pro-btn-success {
      background: var(--gradient-success);
      color: var(--text-primary);
      box-shadow: 0 4px 14px 0 rgba(34, 197, 94, 0.3);
    }
    
    .pro-btn-success:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px 0 rgba(34, 197, 94, 0.4);
    }
    
    .pro-btn-outline {
      background: transparent;
      color: var(--text-secondary);
      border: 2px solid var(--border-primary);
    }
    
    .pro-btn-outline:hover {
      background: var(--bg-tertiary);
      border-color: var(--accent-primary);
      color: var(--text-primary);
    }
    
    /* Professional Navigation */
    .pro-navbar {
      background: rgba(30, 41, 59, 0.95);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border-bottom: 1px solid var(--border-primary);
      box-shadow: var(--shadow-md);
    }
    
    .pro-nav-item {
      position: relative;
      padding: 12px 20px;
      border-radius: 10px;
      transition: all 0.3s ease;
      color: var(--text-secondary);
      text-decoration: none;
      font-weight: 500;
    }
    
    .pro-nav-item::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 0;
      height: 3px;
      background: var(--gradient-primary);
      transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    .pro-nav-item:hover,
    .pro-nav-item.active {
      color: var(--text-primary);
      background: rgba(14, 165, 233, 0.1);
    }
    
    .pro-nav-item:hover::before,
    .pro-nav-item.active::before {
      width: 80%;
    }
    
    /* Professional Dropdown */
    .pro-dropdown {
      position: relative;
    }
    
    .pro-dropdown-menu {
      position: absolute;
      top: 100%;
      right: 0;
      min-width: 280px;
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: 16px;
      box-shadow: var(--shadow-xl);
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px) scale(0.95);
      transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
      z-index: 1000;
      overflow: hidden;
    }
    
    .pro-dropdown:hover .pro-dropdown-menu {
      opacity: 1;
      visibility: visible;
      transform: translateY(0) scale(1);
    }
    
    .pro-dropdown-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px 20px;
      color: var(--text-secondary);
      text-decoration: none;
      transition: all 0.3s ease;
      border-radius: 12px;
      margin: 4px;
      position: relative;
      overflow: hidden;
    }
    
    .pro-dropdown-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.1), transparent);
      transition: left 0.5s;
    }
    
    .pro-dropdown-item:hover::before {
      left: 100%;
    }
    
    .pro-dropdown-item:hover {
      background: rgba(14, 165, 233, 0.1);
      color: var(--text-primary);
      transform: translateX(5px);
    }
    
    /* Professional Stats Cards */
    .pro-stats-card {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: 20px;
      padding: 32px;
      text-align: center;
      transition: all 0.4s ease;
      position: relative;
      overflow: hidden;
      box-shadow: var(--shadow-lg);
    }
    
    .pro-stats-card::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: conic-gradient(from 0deg, transparent, rgba(14, 165, 233, 0.1), transparent);
      animation: rotate 8s linear infinite;
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    .pro-stats-card:hover::before {
      opacity: 1;
    }
    
    @keyframes rotate {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .pro-stats-icon {
      width: 80px;
      height: 80px;
      margin: 0 auto 24px;
      background: var(--gradient-primary);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      color: var(--text-primary);
      position: relative;
      z-index: 1;
      box-shadow: 0 8px 25px rgba(14, 165, 233, 0.3);
    }
    
    .pro-stats-number {
      font-size: 3rem;
      font-weight: 800;
      color: var(--text-primary);
      margin-bottom: 8px;
      position: relative;
      z-index: 1;
    }
    
    .pro-stats-label {
      color: var(--text-secondary);
      font-size: 1.1rem;
      font-weight: 500;
      position: relative;
      z-index: 1;
    }
    
    .pro-stats-trend {
      margin-top: 16px;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 0.9rem;
      font-weight: 600;
      position: relative;
      z-index: 1;
    }
    
    .trend-up {
      background: rgba(34, 197, 94, 0.2);
      color: var(--accent-success);
      border: 1px solid rgba(34, 197, 94, 0.3);
    }
    
    .trend-down {
      background: rgba(239, 68, 68, 0.2);
      color: var(--accent-danger);
      border: 1px solid rgba(239, 68, 68, 0.3);
    }
    
    /* Professional Forms */
    .pro-form-group {
      position: relative;
      margin-bottom: 24px;
    }
    
    .pro-form-input {
      width: 100%;
      padding: 16px 20px;
      border: 2px solid var(--border-primary);
      border-radius: 12px;
      background: var(--bg-tertiary);
      color: var(--text-primary);
      font-size: 16px;
      font-family: 'Cairo', sans-serif;
      transition: all 0.3s ease;
    }
    
    .pro-form-input:focus {
      outline: none;
      border-color: var(--accent-primary);
      box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
      background: var(--bg-secondary);
    }
    
    .pro-form-input::placeholder {
      color: var(--text-muted);
    }
    
    .pro-form-label {
      position: absolute;
      top: -12px;
      right: 16px;
      background: var(--gradient-primary);
      padding: 6px 16px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary);
      box-shadow: var(--shadow-md);
    }
    
    /* Professional Tables */
    .pro-table {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: 16px;
      overflow: hidden;
      box-shadow: var(--shadow-lg);
    }
    
    .pro-table thead {
      background: var(--bg-tertiary);
    }
    
    .pro-table th {
      padding: 20px;
      font-weight: 700;
      color: var(--text-primary);
      border: none;
      text-align: right;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .pro-table td {
      padding: 16px 20px;
      border: none;
      border-bottom: 1px solid var(--border-primary);
      color: var(--text-secondary);
      transition: all 0.3s ease;
    }
    
    .pro-table tbody tr {
      transition: all 0.3s ease;
    }
    
    .pro-table tbody tr:hover {
      background: rgba(14, 165, 233, 0.05);
      color: var(--text-primary);
    }
    
    .pro-table tbody tr:last-child td {
      border-bottom: none;
    }
    
    /* Professional Badges */
    .pro-badge {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .pro-badge-success {
      background: rgba(34, 197, 94, 0.2);
      color: var(--accent-success);
      border: 1px solid rgba(34, 197, 94, 0.3);
    }
    
    .pro-badge-warning {
      background: rgba(245, 158, 11, 0.2);
      color: var(--accent-warning);
      border: 1px solid rgba(245, 158, 11, 0.3);
    }
    
    .pro-badge-danger {
      background: rgba(239, 68, 68, 0.2);
      color: var(--accent-danger);
      border: 1px solid rgba(239, 68, 68, 0.3);
    }
    
    .pro-badge-info {
      background: rgba(14, 165, 233, 0.2);
      color: var(--accent-primary);
      border: 1px solid rgba(14, 165, 233, 0.3);
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
      .pro-card {
        margin-bottom: 16px;
      }
      
      .pro-stats-card {
        padding: 24px;
      }
      
      .pro-stats-number {
        font-size: 2.5rem;
      }
      
      .pro-dropdown-menu {
        position: fixed;
        top: 80px;
        right: 10px;
        left: 10px;
        width: auto;
      }
      
      .pro-btn {
        padding: 10px 20px;
        font-size: 14px;
      }
    }
    
    /* Custom Scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: var(--bg-secondary);
      border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb {
      background: var(--border-secondary);
      border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: var(--accent-primary);
    }
    
    /* Loading States */
    .pro-loading {
      position: relative;
      overflow: hidden;
    }
    
    .pro-loading::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.2), transparent);
      animation: loading 1.5s infinite;
    }
    
    @keyframes loading {
      0% { left: -100%; }
      100% { left: 100%; }
    }
  </style>
</head>
<body class="font-cairo">
  <!-- Professional Navigation -->
  <nav class="pro-navbar fixed top-0 left-0 right-0 z-50 px-6 py-4">
    <div class="max-w-7xl mx-auto flex items-center justify-between">
      <!-- Logo and Brand -->
      <div class="flex items-center gap-4">
        <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
          <i class="fas fa-building text-white text-2xl"></i>
        </div>
        <div>
          <h1 class="text-white font-bold text-2xl">جمعية المنقف التعاونية</h1>
          <p class="text-slate-300 text-sm">نظام إدارة العقود والإيجارات المتطور</p>
        </div>
      </div>

      <!-- Navigation Menu -->
      <div class="hidden lg:flex items-center gap-2">
        <a href="#dashboard" class="pro-nav-item active">
          <i class="fas fa-home ml-2"></i>
          الرئيسية
        </a>

        <!-- Suppliers Dropdown -->
        <div class="pro-dropdown">
          <a href="#" class="pro-nav-item flex items-center">
            <i class="fas fa-users ml-2"></i>
            الموردين
            <i class="fas fa-chevron-down mr-2 text-sm"></i>
          </a>
          <div class="pro-dropdown-menu">
            <a href="suppliers_management.html" class="pro-dropdown-item" target="_blank">
              <i class="fas fa-list text-blue-400"></i>
              قائمة الموردين
            </a>
            <a href="suppliers_management.html" class="pro-dropdown-item" target="_blank">
              <i class="fas fa-plus text-green-400"></i>
              إضافة مورد جديد
            </a>
            <a href="suppliers_management.html" class="pro-dropdown-item" target="_blank">
              <i class="fas fa-tags text-purple-400"></i>
              فئات الموردين
            </a>
            <a href="upload_suppliers.html" class="pro-dropdown-item" target="_blank">
              <i class="fas fa-upload text-orange-400"></i>
              رفع ملف الموردين
            </a>
          </div>
        </div>

        <!-- Contracts Dropdown -->
        <div class="pro-dropdown">
          <a href="#" class="pro-nav-item flex items-center">
            <i class="fas fa-file-contract ml-2"></i>
            العقود
            <i class="fas fa-chevron-down mr-2 text-sm"></i>
          </a>
          <div class="pro-dropdown-menu">
            <a href="contracts_management.html" class="pro-dropdown-item" target="_blank">
              <i class="fas fa-list text-blue-400"></i>
              قائمة العقود
            </a>
            <a href="contracts_management.html" class="pro-dropdown-item" target="_blank">
              <i class="fas fa-plus text-green-400"></i>
              عقد جديد
            </a>
            <a href="contracts_management.html" class="pro-dropdown-item" target="_blank">
              <i class="fas fa-layer-group text-purple-400"></i>
              أنواع العقود
            </a>
            <a href="contracts_management.html" class="pro-dropdown-item" target="_blank">
              <i class="fas fa-exclamation-triangle text-red-400"></i>
              العقود المنتهية قريباً
            </a>
          </div>
        </div>

        <!-- Eyes System Dropdown -->
        <div class="pro-dropdown">
          <a href="#" class="pro-nav-item flex items-center">
            <i class="fas fa-eye ml-2"></i>
            نظام العيون
            <i class="fas fa-chevron-down mr-2 text-sm"></i>
          </a>
          <div class="pro-dropdown-menu">
            <a href="eyes_management.html" class="pro-dropdown-item" target="_blank">
              <i class="fas fa-cog text-blue-400"></i>
              إدارة العيون
            </a>
            <a href="eyes_management.html" class="pro-dropdown-item" target="_blank">
              <i class="fas fa-plus-circle text-green-400"></i>
              تأجير عين
            </a>
            <a href="eyes_management.html" class="pro-dropdown-item" target="_blank">
              <i class="fas fa-check-circle text-green-400"></i>
              العيون المتاحة
            </a>
            <a href="eyes_management.html" class="pro-dropdown-item" target="_blank">
              <i class="fas fa-clock text-orange-400"></i>
              العيون المؤجرة
            </a>
          </div>
        </div>

        <!-- Reports Dropdown -->
        <div class="pro-dropdown">
          <a href="#" class="pro-nav-item flex items-center">
            <i class="fas fa-chart-line ml-2"></i>
            التقارير
            <i class="fas fa-chevron-down mr-2 text-sm"></i>
          </a>
          <div class="pro-dropdown-menu">
            <a href="reports_dashboard.html" class="pro-dropdown-item" target="_blank">
              <i class="fas fa-dollar-sign text-green-400"></i>
              التقارير المالية
            </a>
            <a href="reports_dashboard.html" class="pro-dropdown-item" target="_blank">
              <i class="fas fa-users text-blue-400"></i>
              تقارير الموردين
            </a>
            <a href="reports_dashboard.html" class="pro-dropdown-item" target="_blank">
              <i class="fas fa-file-alt text-purple-400"></i>
              تقارير العقود
            </a>
            <a href="reports_dashboard.html" class="pro-dropdown-item" target="_blank">
              <i class="fas fa-eye text-orange-400"></i>
              تقارير العيون
            </a>
          </div>
        </div>
      </div>

      <!-- User Actions -->
      <div class="flex items-center gap-4">
        <!-- Search -->
        <div class="hidden md:block relative">
          <input type="text" placeholder="بحث سريع..."
                 class="bg-slate-700/50 border border-slate-600 rounded-full px-4 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64 transition-all">
          <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
        </div>

        <!-- Notifications -->
        <div class="relative">
          <button class="text-slate-300 hover:text-white p-3 rounded-full hover:bg-slate-700/50 transition-all relative">
            <i class="fas fa-bell text-xl"></i>
            <span class="absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full text-xs flex items-center justify-center animate-pulse text-white font-bold">3</span>
          </button>
        </div>

        <!-- User Menu -->
        <div class="pro-dropdown">
          <button class="flex items-center gap-3 text-slate-300 hover:text-white p-2 rounded-full hover:bg-slate-700/50 transition-all">
            <img src="https://via.placeholder.com/40" alt="User" class="w-10 h-10 rounded-full border-2 border-slate-600">
            <div class="hidden md:block text-right">
              <p class="text-sm font-medium text-white">أحمد محمد</p>
              <p class="text-xs text-slate-400">مدير النظام</p>
            </div>
            <i class="fas fa-chevron-down text-sm"></i>
          </button>
          <div class="pro-dropdown-menu">
            <a href="#profile" class="pro-dropdown-item">
              <i class="fas fa-user text-blue-400"></i>
              الملف الشخصي
            </a>
            <a href="#settings" class="pro-dropdown-item">
              <i class="fas fa-cog text-gray-400"></i>
              الإعدادات
            </a>
            <a href="#help" class="pro-dropdown-item">
              <i class="fas fa-question-circle text-green-400"></i>
              المساعدة
            </a>
            <div class="border-t border-slate-600 my-2"></div>
            <a href="#logout" class="pro-dropdown-item text-red-400">
              <i class="fas fa-sign-out-alt text-red-400"></i>
              تسجيل الخروج
            </a>
          </div>
        </div>

        <!-- Mobile Menu Toggle -->
        <button class="lg:hidden text-slate-300 hover:text-white p-2" onclick="toggleMobileMenu()">
          <i class="fas fa-bars text-xl"></i>
        </button>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <main class="pt-24 px-6 pb-12">
    <div class="max-w-7xl mx-auto">

      <!-- Welcome Section -->
      <div class="text-center mb-16 animate-fade-in">
        <div class="mb-8">
          <h1 class="text-5xl md:text-7xl font-black text-white mb-6 leading-tight">
            نظام إدارة العقود المتطور
          </h1>
          <p class="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto leading-relaxed mb-8">
            حلول احترافية متكاملة لإدارة عقود الموردين وإيجار العيون في السوق المركزي
          </p>
          <div class="flex flex-wrap justify-center gap-4 mb-8">
            <button class="pro-btn pro-btn-primary" onclick="showSection('add-supplier')">
              <i class="fas fa-plus"></i>
              إضافة مورد جديد
            </button>
            <button class="pro-btn pro-btn-secondary" onclick="showSection('new-contract')">
              <i class="fas fa-file-plus"></i>
              إنشاء عقد جديد
            </button>
            <button class="pro-btn pro-btn-success" onclick="showSection('upload-suppliers')">
              <i class="fas fa-upload"></i>
              رفع ملف الموردين
            </button>
          </div>
        </div>

        <!-- Developer Credit -->
        <div class="pro-card max-w-md mx-auto p-6 mb-12">
          <div class="flex items-center justify-center gap-4">
            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <i class="fas fa-code text-white"></i>
            </div>
            <div class="text-right">
              <p class="text-white font-bold text-lg">تطوير وتنفيذ</p>
              <p class="text-blue-400 font-semibold">محمد مرزوق العقاب</p>
              <p class="text-slate-400 text-sm">مطور نظم معلومات</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Professional Stats Dashboard -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
        <div class="pro-stats-card animate-slide-up" style="animation-delay: 0.1s">
          <div class="pro-stats-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="pro-stats-number">156</div>
          <div class="pro-stats-label">إجمالي الموردين</div>
          <div class="pro-stats-trend trend-up">
            <i class="fas fa-arrow-up"></i>
            +12% هذا الشهر
          </div>
        </div>

        <div class="pro-stats-card animate-slide-up" style="animation-delay: 0.2s">
          <div class="pro-stats-icon" style="background: var(--gradient-secondary);">
            <i class="fas fa-file-contract"></i>
          </div>
          <div class="pro-stats-number">89</div>
          <div class="pro-stats-label">العقود النشطة</div>
          <div class="pro-stats-trend trend-up">
            <i class="fas fa-arrow-up"></i>
            +8% هذا الشهر
          </div>
        </div>

        <div class="pro-stats-card animate-slide-up" style="animation-delay: 0.3s">
          <div class="pro-stats-icon" style="background: var(--gradient-success);">
            <i class="fas fa-eye"></i>
          </div>
          <div class="pro-stats-number">324</div>
          <div class="pro-stats-label">العيون المؤجرة</div>
          <div class="pro-stats-trend trend-up">
            <i class="fas fa-arrow-up"></i>
            +15% هذا الشهر
          </div>
        </div>

        <div class="pro-stats-card animate-slide-up" style="animation-delay: 0.4s">
          <div class="pro-stats-icon" style="background: var(--gradient-warning);">
            <i class="fas fa-dollar-sign"></i>
          </div>
          <div class="pro-stats-number">19,440</div>
          <div class="pro-stats-label">الإيرادات الشهرية (د.ك)</div>
          <div class="pro-stats-trend trend-up">
            <i class="fas fa-arrow-up"></i>
            +22% هذا الشهر
          </div>
        </div>
      </div>

      <!-- Professional Action Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
        <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.1s">
          <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <i class="fas fa-plus text-white text-3xl"></i>
          </div>
          <h3 class="text-2xl font-bold text-white mb-4">إضافة مورد جديد</h3>
          <p class="text-slate-300 mb-8 leading-relaxed">إضافة مورد جديد إلى النظام مع جميع البيانات والوثائق المطلوبة بطريقة احترافية ومنظمة</p>
          <button class="pro-btn pro-btn-primary w-full" onclick="showSection('add-supplier')">
            <i class="fas fa-plus"></i>
            إضافة الآن
          </button>
        </div>

        <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.2s">
          <div class="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <i class="fas fa-file-contract text-white text-3xl"></i>
          </div>
          <h3 class="text-2xl font-bold text-white mb-4">إنشاء عقد جديد</h3>
          <p class="text-slate-300 mb-8 leading-relaxed">إنشاء عقد توريد أو إيجار جديد مع تحديد جميع الشروط والأحكام والمواصفات المطلوبة</p>
          <button class="pro-btn pro-btn-secondary w-full" onclick="showSection('new-contract')">
            <i class="fas fa-file-plus"></i>
            إنشاء عقد
          </button>
        </div>

        <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.3s">
          <div class="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <i class="fas fa-eye text-white text-3xl"></i>
          </div>
          <h3 class="text-2xl font-bold text-white mb-4">تأجير عين جديدة</h3>
          <p class="text-slate-300 mb-8 leading-relaxed">تأجير عين في إحدى الطبليات المتاحة في الأقسام المختلفة مع حساب الإيجار تلقائياً</p>
          <button class="pro-btn pro-btn-success w-full" onclick="showSection('rent-eye')">
            <i class="fas fa-plus-circle"></i>
            تأجير عين
          </button>
        </div>

        <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.4s">
          <div class="w-20 h-20 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <i class="fas fa-upload text-white text-3xl"></i>
          </div>
          <h3 class="text-2xl font-bold text-white mb-4">رفع ملف الموردين</h3>
          <p class="text-slate-300 mb-8 leading-relaxed">رفع ملف Excel أو CSV يحتوي على بيانات الموردين لإضافتهم بشكل جماعي إلى النظام</p>
          <button class="pro-btn pro-btn-outline w-full" onclick="window.open('upload_suppliers.html', '_blank')">
            <i class="fas fa-upload"></i>
            رفع الملفات
          </button>
        </div>

        <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.5s">
          <div class="w-20 h-20 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <i class="fas fa-folder-open text-white text-3xl"></i>
          </div>
          <h3 class="text-2xl font-bold text-white mb-4">إدارة ملفات العقود</h3>
          <p class="text-slate-300 mb-8 leading-relaxed">عرض وإدارة جميع ملفات العقود PDF مع إمكانية البحث والتصفية والتحميل</p>
          <button class="pro-btn pro-btn-outline w-full" onclick="window.open('contracts_files_manager.html', '_blank')">
            <i class="fas fa-folder-open"></i>
            إدارة الملفات
          </button>
        </div>

        <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.6s">
          <div class="w-20 h-20 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <i class="fas fa-chart-line text-white text-3xl"></i>
          </div>
          <h3 class="text-2xl font-bold text-white mb-4">التقارير والإحصائيات</h3>
          <p class="text-slate-300 mb-8 leading-relaxed">عرض تقارير مفصلة وإحصائيات شاملة عن الموردين والعقود والإيرادات</p>
          <button class="pro-btn pro-btn-outline w-full" onclick="showSection('reports')">
            <i class="fas fa-chart-bar"></i>
            عرض التقارير
          </button>
        </div>

        <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.6s">
          <div class="w-20 h-20 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <i class="fas fa-cog text-white text-3xl"></i>
          </div>
          <h3 class="text-2xl font-bold text-white mb-4">إعدادات النظام</h3>
          <p class="text-slate-300 mb-8 leading-relaxed">إدارة إعدادات النظام والمستخدمين والصلاحيات والنسخ الاحتياطي</p>
          <button class="pro-btn pro-btn-outline w-full" onclick="showSection('settings')">
            <i class="fas fa-cogs"></i>
            الإعدادات
          </button>
        </div>
      </div>

      <!-- Recent Activity Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
        <!-- Recent Contracts -->
        <div class="pro-card p-8 animate-fade-in">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-2xl font-bold text-white flex items-center gap-3">
              <i class="fas fa-file-contract text-blue-400"></i>
              العقود الحديثة
            </h3>
            <a href="#contracts-list" class="pro-btn pro-btn-outline">
              <i class="fas fa-arrow-left"></i>
              عرض الكل
            </a>
          </div>

          <div class="space-y-4">
            <div class="flex items-center justify-between p-4 bg-slate-700/30 rounded-xl border border-slate-600/50 hover:border-blue-500/50 transition-all">
              <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                  <i class="fas fa-file-contract text-blue-400"></i>
                </div>
                <div>
                  <h4 class="text-white font-semibold">عقد توريد بهارات</h4>
                  <p class="text-slate-400 text-sm">شركة البهارات الذهبية</p>
                </div>
              </div>
              <div class="text-right">
                <span class="pro-badge pro-badge-success">نشط</span>
                <p class="text-slate-400 text-sm mt-1">منذ يومين</p>
              </div>
            </div>

            <div class="flex items-center justify-between p-4 bg-slate-700/30 rounded-xl border border-slate-600/50 hover:border-purple-500/50 transition-all">
              <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center">
                  <i class="fas fa-eye text-purple-400"></i>
                </div>
                <div>
                  <h4 class="text-white font-semibold">إيجار عين رقم 45</h4>
                  <p class="text-slate-400 text-sm">قسم الأجبان - طبلية 3</p>
                </div>
              </div>
              <div class="text-right">
                <span class="pro-badge pro-badge-warning">قيد المراجعة</span>
                <p class="text-slate-400 text-sm mt-1">منذ 3 أيام</p>
              </div>
            </div>

            <div class="flex items-center justify-between p-4 bg-slate-700/30 rounded-xl border border-slate-600/50 hover:border-green-500/50 transition-all">
              <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
                  <i class="fas fa-handshake text-green-400"></i>
                </div>
                <div>
                  <h4 class="text-white font-semibold">عقد خدمات تنظيف</h4>
                  <p class="text-slate-400 text-sm">شركة النظافة المتقدمة</p>
                </div>
              </div>
              <div class="text-right">
                <span class="pro-badge pro-badge-success">مكتمل</span>
                <p class="text-slate-400 text-sm mt-1">منذ أسبوع</p>
              </div>
            </div>
          </div>
        </div>

        <!-- System Notifications -->
        <div class="pro-card p-8 animate-fade-in">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-2xl font-bold text-white flex items-center gap-3">
              <i class="fas fa-bell text-yellow-400"></i>
              التنبيهات والإشعارات
            </h3>
            <button class="pro-btn pro-btn-outline">
              <i class="fas fa-check-double"></i>
              تحديد الكل كمقروء
            </button>
          </div>

          <div class="space-y-4">
            <div class="flex items-start gap-4 p-4 bg-red-500/10 border border-red-500/30 rounded-xl">
              <div class="w-10 h-10 bg-red-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-red-400"></i>
              </div>
              <div class="flex-1">
                <h4 class="text-white font-semibold mb-1">عقد منتهي الصلاحية</h4>
                <p class="text-slate-300 text-sm mb-2">عقد توريد الخضروات مع شركة الخضار الطازج سينتهي خلال 3 أيام</p>
                <p class="text-red-400 text-xs">منذ ساعتين</p>
              </div>
            </div>

            <div class="flex items-start gap-4 p-4 bg-blue-500/10 border border-blue-500/30 rounded-xl">
              <div class="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                <i class="fas fa-info-circle text-blue-400"></i>
              </div>
              <div class="flex-1">
                <h4 class="text-white font-semibold mb-1">طلب مورد جديد</h4>
                <p class="text-slate-300 text-sm mb-2">تم استلام طلب انضمام جديد من شركة المواد الغذائية المتطورة</p>
                <p class="text-blue-400 text-xs">منذ 4 ساعات</p>
              </div>
            </div>

            <div class="flex items-start gap-4 p-4 bg-green-500/10 border border-green-500/30 rounded-xl">
              <div class="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                <i class="fas fa-check-circle text-green-400"></i>
              </div>
              <div class="flex-1">
                <h4 class="text-white font-semibold mb-1">دفعة مستلمة</h4>
                <p class="text-slate-300 text-sm mb-2">تم استلام دفعة إيجار العين رقم 23 بقيمة 240 د.ك</p>
                <p class="text-green-400 text-xs">منذ يوم واحد</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Professional Footer -->
      <footer class="pro-card p-8 text-center animate-fade-in">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <!-- Company Info -->
          <div>
            <h4 class="text-xl font-bold text-white mb-4">جمعية المنقف التعاونية</h4>
            <p class="text-slate-300 leading-relaxed mb-4">
              نظام متطور ومتكامل لإدارة عقود الموردين وإيجار العيون في السوق المركزي
            </p>
            <div class="flex justify-center gap-4">
              <a href="#" class="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center text-blue-400 hover:bg-blue-500/30 transition-all">
                <i class="fab fa-facebook-f"></i>
              </a>
              <a href="#" class="w-10 h-10 bg-blue-400/20 rounded-full flex items-center justify-center text-blue-400 hover:bg-blue-400/30 transition-all">
                <i class="fab fa-twitter"></i>
              </a>
              <a href="#" class="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center text-green-400 hover:bg-green-500/30 transition-all">
                <i class="fab fa-whatsapp"></i>
              </a>
            </div>
          </div>

          <!-- Quick Links -->
          <div>
            <h4 class="text-xl font-bold text-white mb-4">روابط سريعة</h4>
            <div class="space-y-2">
              <a href="#suppliers-list" class="block text-slate-300 hover:text-white transition-all">قائمة الموردين</a>
              <a href="#contracts-list" class="block text-slate-300 hover:text-white transition-all">إدارة العقود</a>
              <a href="contracts_files_manager.html" class="block text-slate-300 hover:text-white transition-all">ملفات العقود PDF</a>
              <a href="#eyes-management" class="block text-slate-300 hover:text-white transition-all">نظام العيون</a>
              <a href="#reports" class="block text-slate-300 hover:text-white transition-all">التقارير</a>
              <a href="upload_suppliers.html" class="block text-slate-300 hover:text-white transition-all">رفع الملفات</a>
            </div>
          </div>

          <!-- Contact Info -->
          <div>
            <h4 class="text-xl font-bold text-white mb-4">معلومات الاتصال</h4>
            <div class="space-y-3 text-slate-300">
              <div class="flex items-center justify-center gap-3">
                <i class="fas fa-phone text-green-400"></i>
                <span>+965-1234567</span>
              </div>
              <div class="flex items-center justify-center gap-3">
                <i class="fas fa-envelope text-blue-400"></i>
                <span><EMAIL></span>
              </div>
              <div class="flex items-center justify-center gap-3">
                <i class="fas fa-map-marker-alt text-red-400"></i>
                <span>الكويت - المنقف</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Developer Credit -->
        <div class="border-t border-slate-600 pt-8">
          <div class="flex flex-col md:flex-row items-center justify-between gap-4">
            <p class="text-slate-400">
              © 2024 جمعية المنقف التعاونية. جميع الحقوق محفوظة.
            </p>
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <i class="fas fa-code text-white text-sm"></i>
              </div>
              <div class="text-right">
                <p class="text-white font-semibold">تطوير وتنفيذ: محمد مرزوق العقاب</p>
                <p class="text-slate-400 text-sm">مطور نظم معلومات محترف</p>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </main>

  <!-- JavaScript Libraries -->
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
  <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>

  <script>
    // Professional Application JavaScript

    // Global Variables
    let currentSection = 'dashboard';
    let mobileMenuOpen = false;

    // Initialize Application
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🚀 نظام إدارة العقود المتطور - تطوير محمد مرزوق العقاب');

      initializeAnimations();
      initializeDatePickers();
      initializeNotifications();
      initializeSearch();

      // Show welcome message
      setTimeout(() => {
        showNotification('مرحباً بك في نظام إدارة العقود المتطور! 🎉', 'success', 4000);
      }, 1000);
    });

    // Navigation Functions
    function showSection(sectionId) {
      // Update current section
      currentSection = sectionId;

      // Update navigation active state
      document.querySelectorAll('.pro-nav-item').forEach(item => {
        item.classList.remove('active');
      });

      // Close mobile menu
      toggleMobileMenu(false);

      // Smooth scroll to top
      window.scrollTo({ top: 0, behavior: 'smooth' });

      // Show notification
      showNotification(`تم الانتقال إلى ${getSectionName(sectionId)}`, 'info', 2000);

      // Simulate section loading
      simulateSectionLoad(sectionId);
    }

    // Get section name in Arabic
    function getSectionName(sectionId) {
      const names = {
        'dashboard': 'الرئيسية',
        'suppliers-list': 'قائمة الموردين',
        'add-supplier': 'إضافة مورد جديد',
        'contracts-list': 'قائمة العقود',
        'new-contract': 'إنشاء عقد جديد',
        'eyes-management': 'إدارة العيون',
        'rent-eye': 'تأجير عين',
        'reports': 'التقارير',
        'settings': 'الإعدادات'
      };
      return names[sectionId] || 'القسم المحدد';
    }

    // Mobile Menu Toggle
    function toggleMobileMenu(force = null) {
      const navbar = document.querySelector('.pro-navbar');

      if (force === false) {
        mobileMenuOpen = false;
        navbar.classList.remove('mobile-menu-open');
      } else if (force === true) {
        mobileMenuOpen = true;
        navbar.classList.add('mobile-menu-open');
      } else {
        mobileMenuOpen = !mobileMenuOpen;
        navbar.classList.toggle('mobile-menu-open');
      }
    }

    // Initialize Animations
    function initializeAnimations() {
      // Intersection Observer for scroll animations
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
          }
        });
      }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      });

      // Observe all animated elements
      document.querySelectorAll('.animate-fade-in, .animate-slide-up, .animate-scale-in').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        observer.observe(el);
      });
    }

    // Initialize Date Pickers
    function initializeDatePickers() {
      if (typeof flatpickr !== 'undefined') {
        flatpickr('.date-picker', {
          locale: 'ar',
          dateFormat: 'Y-m-d',
          allowInput: true,
          theme: 'dark'
        });
      }
    }

    // Initialize Notifications System
    function initializeNotifications() {
      // Create notifications container
      if (!document.getElementById('notifications-container')) {
        const container = document.createElement('div');
        container.id = 'notifications-container';
        container.className = 'fixed top-4 left-4 z-50 space-y-3';
        document.body.appendChild(container);
      }
    }

    // Show Notification
    function showNotification(message, type = 'info', duration = 3000) {
      const container = document.getElementById('notifications-container');
      if (!container) return;

      const notification = document.createElement('div');
      notification.className = `pro-card p-4 max-w-sm transform translate-x-full transition-transform duration-300 ${getNotificationClass(type)}`;

      notification.innerHTML = `
        <div class="flex items-center gap-3">
          <i class="fas fa-${getNotificationIcon(type)} text-lg"></i>
          <span class="flex-1 text-white font-medium">${message}</span>
          <button onclick="this.parentElement.parentElement.remove()" class="text-white/70 hover:text-white">
            <i class="fas fa-times"></i>
          </button>
        </div>
      `;

      container.appendChild(notification);

      // Animate in
      setTimeout(() => {
        notification.classList.remove('translate-x-full');
      }, 100);

      // Auto remove
      setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => notification.remove(), 300);
      }, duration);
    }

    // Get notification class
    function getNotificationClass(type) {
      const classes = {
        success: 'border-green-500/50 bg-green-500/10',
        error: 'border-red-500/50 bg-red-500/10',
        warning: 'border-yellow-500/50 bg-yellow-500/10',
        info: 'border-blue-500/50 bg-blue-500/10'
      };
      return classes[type] || classes.info;
    }

    // Get notification icon
    function getNotificationIcon(type) {
      const icons = {
        success: 'check-circle',
        error: 'times-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
      };
      return icons[type] || icons.info;
    }

    // Initialize Search
    function initializeSearch() {
      const searchInput = document.querySelector('input[placeholder="بحث سريع..."]');
      if (searchInput) {
        searchInput.addEventListener('input', function(e) {
          const query = e.target.value.trim();
          if (query.length > 2) {
            performSearch(query);
          }
        });
      }
    }

    // Perform Search
    function performSearch(query) {
      console.log('🔍 البحث عن:', query);
      // Implement search logic here
      showNotification(`جاري البحث عن: ${query}`, 'info', 2000);
    }

    // Simulate Section Loading
    function simulateSectionLoad(sectionId) {
      // Add loading state to relevant cards
      const cards = document.querySelectorAll('.pro-card');
      cards.forEach(card => {
        card.classList.add('pro-loading');
        setTimeout(() => {
          card.classList.remove('pro-loading');
        }, 1000);
      });
    }

    // Update Stats (simulate real-time updates)
    function updateStats() {
      const statsNumbers = document.querySelectorAll('.pro-stats-number');
      statsNumbers.forEach(stat => {
        const currentValue = parseInt(stat.textContent.replace(/,/g, ''));
        const newValue = currentValue + Math.floor(Math.random() * 5);
        stat.textContent = newValue.toLocaleString();
      });
    }

    // Auto-update stats every 30 seconds
    setInterval(updateStats, 30000);

    // Keyboard Shortcuts
    document.addEventListener('keydown', function(e) {
      // Ctrl/Cmd + K for search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.querySelector('input[placeholder="بحث سريع..."]');
        if (searchInput) {
          searchInput.focus();
        }
      }

      // Escape to close mobile menu
      if (e.key === 'Escape') {
        toggleMobileMenu(false);
      }
    });

    // Performance Monitoring
    if ('performance' in window) {
      window.addEventListener('load', () => {
        const loadTime = performance.now();
        console.log(`⚡ تم تحميل النظام في ${Math.round(loadTime)}ms`);

        if (loadTime > 3000) {
          showNotification('تم تحميل النظام بنجاح', 'success', 3000);
        }
      });
    }

    // Export functions for global use
    window.showSection = showSection;
    window.toggleMobileMenu = toggleMobileMenu;
    window.showNotification = showNotification;
  </script>
</body>
</html>
