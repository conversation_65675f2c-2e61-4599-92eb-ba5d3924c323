<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>نظام إدارة العقود المتطور - جمعية المنقف التعاونية</title>
  
  <!-- Meta Tags -->
  <meta name="description" content="نظام إدارة عقود الموردين والإيجارات المتطور - تطوير محمد مرزوق العقاب">
  <meta name="keywords" content="عقود, موردين, إيجارات, جمعية المنقف, الكويت">
  <meta name="author" content="محمد مرزوق العقاب">
  
  <!-- CSS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
  
  <!-- Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            'cairo': ['Cairo', 'sans-serif'],
          },
          colors: {
            'primary': {
              50: '#f0f9ff',
              500: '#0ea5e9',
              600: '#0284c7',
              700: '#0369a1',
              800: '#075985',
              900: '#0c4a6e',
            }
          }
        }
      }
    }
  </script>
  
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
  
  <style>
    body {
      font-family: 'Cairo', sans-serif;
      background: linear-gradient(135deg, #0a0f1c 0%, #1e293b 50%, #334155 100%);
      min-height: 100vh;
    }
    
    .glass-card {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
    }
    
    .pro-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(15px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .pro-card:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(59, 130, 246, 0.5);
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 30px 60px rgba(59, 130, 246, 0.2);
    }
    
    .pro-btn {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border: none;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
    
    .pro-btn:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-2px);
      box-shadow: 0 15px 35px rgba(59, 130, 246, 0.4);
    }
    
    .pro-btn-outline {
      background: transparent;
      border: 2px solid rgba(255, 255, 255, 0.3);
      color: white;
      transition: all 0.3s ease;
    }
    
    .pro-btn-outline:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(59, 130, 246, 0.8);
      color: #3b82f6;
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
    }
    
    .animate-scale-in {
      animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
      opacity: 0;
      transform: scale(0.9) translateY(20px);
    }
    
    @keyframes scaleIn {
      to {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
    }
    
    .floating-animation {
      animation: floating 6s ease-in-out infinite;
    }
    
    @keyframes floating {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }
    
    .gradient-text {
      background: linear-gradient(135deg, #3b82f6, #8b5cf6, #ec4899);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .stats-card {
      background: rgba(255, 255, 255, 0.08);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.15);
      transition: all 0.3s ease;
    }
    
    .stats-card:hover {
      background: rgba(255, 255, 255, 0.12);
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }
    
    .notification {
      position: fixed;
      top: 20px;
      left: 20px;
      right: 20px;
      z-index: 1000;
      transform: translateY(-100px);
      transition: transform 0.3s ease;
    }
    
    .notification.show {
      transform: translateY(0);
    }
    
    .loading {
      opacity: 0.6;
      pointer-events: none;
    }
    
    .section-hidden {
      display: none;
    }
    
    .form-input {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      transition: all 0.3s ease;
    }
    
    .form-input:focus {
      background: rgba(255, 255, 255, 0.15);
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .form-input::placeholder {
      color: rgba(255, 255, 255, 0.6);
    }
    
    .required-field::after {
      content: " *";
      color: #ef4444;
      font-weight: bold;
    }
    
    .table-container {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 16px;
      overflow: hidden;
    }
    
    .table-row {
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;
    }
    
    .table-row:hover {
      background: rgba(255, 255, 255, 0.08);
    }
    
    .search-input {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      transition: all 0.3s ease;
    }
    
    .search-input:focus {
      background: rgba(255, 255, 255, 0.15);
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .search-input::placeholder {
      color: rgba(255, 255, 255, 0.6);
    }
    
    .modal {
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(10px);
    }
    
    .modal-content {
      background: rgba(15, 23, 42, 0.95);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .btn-danger {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    }
    
    .btn-danger:hover {
      background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
    }
    
    .btn-success {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }
    
    .btn-success:hover {
      background: linear-gradient(135deg, #059669 0%, #047857 100%);
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
    }
    
    .btn-warning {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }
    
    .btn-warning:hover {
      background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(245, 158, 11, 0.3);
    }
    
    .pagination-btn {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      transition: all 0.3s ease;
    }
    
    .pagination-btn:hover {
      background: rgba(59, 130, 246, 0.2);
      border-color: #3b82f6;
    }
    
    .pagination-btn.active {
      background: #3b82f6;
      border-color: #3b82f6;
    }
    
    .status-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 600;
    }
    
    .status-active {
      background: rgba(16, 185, 129, 0.2);
      color: #10b981;
      border: 1px solid rgba(16, 185, 129, 0.3);
    }
    
    .status-inactive {
      background: rgba(239, 68, 68, 0.2);
      color: #ef4444;
      border: 1px solid rgba(239, 68, 68, 0.3);
    }
    
    .status-pending {
      background: rgba(245, 158, 11, 0.2);
      color: #f59e0b;
      border: 1px solid rgba(245, 158, 11, 0.3);
    }
    
    .category-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 600;
    }
    
    .category-spices {
      background: rgba(245, 158, 11, 0.2);
      color: #f59e0b;
      border: 1px solid rgba(245, 158, 11, 0.3);
    }
    
    .category-consumer {
      background: rgba(16, 185, 129, 0.2);
      color: #10b981;
      border: 1px solid rgba(16, 185, 129, 0.3);
    }
    
    .category-cheese {
      background: rgba(59, 130, 246, 0.2);
      color: #3b82f6;
      border: 1px solid rgba(59, 130, 246, 0.3);
    }
  </style>
</head>
<body class="min-h-screen">
  <!-- Header -->
  <header class="glass-card rounded-none border-x-0 border-t-0 mb-8">
    <div class="container mx-auto px-6 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4 space-x-reverse">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center floating-animation">
            <i class="fas fa-building text-white text-2xl"></i>
          </div>
          <div>
            <h1 class="text-3xl font-bold gradient-text">نظام إدارة العقود المتطور</h1>
            <p class="text-blue-200 text-lg">جمعية المنقف التعاونية</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-4 space-x-reverse">
          <div class="text-center">
            <div class="text-sm text-blue-200">التاريخ الحالي</div>
            <div id="currentDate" class="text-white font-semibold"></div>
          </div>
          <div class="text-center">
            <div class="text-sm text-blue-200">الوقت الحالي</div>
            <div id="currentTime" class="text-white font-semibold"></div>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <div class="container mx-auto px-6">
    <!-- Welcome Section -->
    <div class="glass-card rounded-3xl p-8 mb-12 text-center animate-scale-in">
      <div class="mb-6">
        <h2 class="text-4xl font-bold text-white mb-4">مرحباً بك في النظام المتطور</h2>
        <p class="text-xl text-slate-300 leading-relaxed">
          نظام شامل ومتكامل لإدارة عقود الموردين وإيجار العيون في السوق المركزي
        </p>
      </div>
      
      <!-- Quick Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-8">
        <div class="stats-card rounded-2xl p-6">
          <div class="text-3xl font-bold text-blue-400 mb-2" id="totalSuppliers">0</div>
          <div class="text-slate-300">إجمالي الموردين</div>
        </div>
        <div class="stats-card rounded-2xl p-6">
          <div class="text-3xl font-bold text-green-400 mb-2" id="activeContracts">0</div>
          <div class="text-slate-300">العقود النشطة</div>
        </div>
        <div class="stats-card rounded-2xl p-6">
          <div class="text-3xl font-bold text-purple-400 mb-2" id="totalEyes">0</div>
          <div class="text-slate-300">العيون المؤجرة</div>
        </div>
        <div class="stats-card rounded-2xl p-6">
          <div class="text-3xl font-bold text-yellow-400 mb-2" id="monthlyRevenue">0</div>
          <div class="text-slate-300">الإيرادات الشهرية</div>
        </div>
      </div>
    </div>

    <!-- Main Cards Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
      <!-- Suppliers Management -->
      <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.1s">
        <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <i class="fas fa-users text-white text-3xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">إدارة الموردين</h3>
        <p class="text-slate-300 mb-8 leading-relaxed">إضافة وتعديل وإدارة بيانات الموردين مع تصنيفهم حسب نوع البضائع</p>
        <button class="pro-btn pro-btn-outline w-full" onclick="showSection('suppliers')">
          <i class="fas fa-users"></i>
          إدارة الموردين
        </button>
      </div>

      <!-- Contracts Management -->
      <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.2s">
        <div class="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <i class="fas fa-file-contract text-white text-3xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">إدارة العقود</h3>
        <p class="text-slate-300 mb-8 leading-relaxed">إنشاء وإدارة عقود التوريد والإيجار مع متابعة تواريخ الانتهاء والتجديد</p>
        <button class="pro-btn pro-btn-outline w-full" onclick="showSection('contracts')">
          <i class="fas fa-file-contract"></i>
          إدارة العقود
        </button>
      </div>

      <!-- Eyes Management -->
      <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.3s">
        <div class="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <i class="fas fa-eye text-white text-3xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">نظام العيون</h3>
        <p class="text-slate-300 mb-8 leading-relaxed">إدارة إيجار العيون والطبليات في السوق مع حساب الإيجارات تلقائياً</p>
        <button class="pro-btn pro-btn-outline w-full" onclick="showSection('eyes')">
          <i class="fas fa-eye"></i>
          إدارة العيون
        </button>
      </div>

      <!-- File Upload -->
      <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.4s">
        <div class="w-20 h-20 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <i class="fas fa-upload text-white text-3xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">رفع ملف الموردين</h3>
        <p class="text-slate-300 mb-8 leading-relaxed">رفع ملف Excel أو CSV يحتوي على بيانات الموردين لإضافتهم بشكل جماعي إلى النظام</p>
        <button class="pro-btn pro-btn-outline w-full" onclick="window.open('upload_suppliers.html', '_blank')">
          <i class="fas fa-upload"></i>
          رفع الملفات
        </button>
      </div>

      <!-- Contract Files Management -->
      <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.5s">
        <div class="w-20 h-20 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <i class="fas fa-folder-open text-white text-3xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">إدارة ملفات العقود</h3>
        <p class="text-slate-300 mb-8 leading-relaxed">عرض وإدارة جميع ملفات العقود PDF مع إمكانية البحث والتصفية والتحميل</p>
        <button class="pro-btn pro-btn-outline w-full" onclick="window.open('contracts_files_manager.html', '_blank')">
          <i class="fas fa-folder-open"></i>
          إدارة الملفات
        </button>
      </div>

      <!-- Reports -->
      <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.6s">
        <div class="w-20 h-20 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <i class="fas fa-chart-line text-white text-3xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">التقارير والإحصائيات</h3>
        <p class="text-slate-300 mb-8 leading-relaxed">عرض تقارير مفصلة وإحصائيات شاملة عن الموردين والعقود والإيرادات</p>
        <button class="pro-btn pro-btn-outline w-full" onclick="showSection('reports')">
          <i class="fas fa-chart-bar"></i>
          عرض التقارير
        </button>
      </div>
    </div>

    <!-- Quick Links Section -->
    <div class="glass-card rounded-3xl p-8 mb-12 animate-scale-in" style="animation-delay: 0.7s">
      <h3 class="text-2xl font-bold text-white mb-6 text-center">الروابط السريعة</h3>
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <div class="text-center">
          <div class="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mx-auto mb-3">
            <i class="fas fa-users text-blue-400"></i>
          </div>
          <div class="space-y-2">
            <a href="#suppliers-list" class="block text-slate-300 hover:text-white transition-all">قائمة الموردين</a>
            <a href="#contracts-list" class="block text-slate-300 hover:text-white transition-all">إدارة العقود</a>
            <a href="contracts_files_manager.html" class="block text-slate-300 hover:text-white transition-all">ملفات العقود PDF</a>
            <a href="#eyes-management" class="block text-slate-300 hover:text-white transition-all">نظام العيون</a>
            <a href="#reports" class="block text-slate-300 hover:text-white transition-all">التقارير</a>
            <a href="upload_suppliers.html" class="block text-slate-300 hover:text-white transition-all">رفع الملفات</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact Information -->
    <div class="glass-card rounded-3xl p-8 mb-12 text-center animate-scale-in" style="animation-delay: 0.8s">
      <h3 class="text-2xl font-bold text-white mb-6">معلومات الاتصال</h3>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h4 class="text-lg font-semibold text-blue-300 mb-4">جمعية المنقف التعاونية</h4>
          <div class="space-y-3 text-slate-300">
            <div class="flex items-center justify-center gap-3">
              <i class="fas fa-phone text-green-400"></i>
              <span>+965-23710272</span>
            </div>
            <div class="flex items-center justify-center gap-3">
              <i class="fas fa-envelope text-blue-400"></i>
              <span><EMAIL></span>
            </div>
            <div class="flex items-center justify-center gap-3">
              <i class="fas fa-map-marker-alt text-red-400"></i>
              <span>المنقف - قطعة 4 شارع فهد الهملان</span>
            </div>
          </div>

          <div class="mt-6">
            <h5 class="text-md font-semibold text-slate-300 mb-3">تابعونا على</h5>
            <div class="flex justify-center gap-4">
              <a href="https://instagram.com/Mnfcoop" target="_blank" class="w-10 h-10 bg-pink-500/20 rounded-full flex items-center justify-center text-pink-400 hover:bg-pink-500/30 transition-all">
                <i class="fab fa-instagram"></i>
              </a>
              <a href="#" class="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center text-green-400 hover:bg-green-500/30 transition-all">
                <i class="fab fa-whatsapp"></i>
              </a>
            </div>
          </div>
        </div>

        <div>
          <h4 class="text-lg font-semibold text-purple-300 mb-4">تطوير وتنفيذ</h4>
          <div class="space-y-3 text-slate-300">
            <div class="flex items-center justify-center gap-3">
              <i class="fas fa-user text-purple-400"></i>
              <span class="font-semibold">محمد مرزوق العقاب</span>
            </div>
            <div class="flex items-center justify-center gap-3">
              <i class="fas fa-briefcase text-blue-400"></i>
              <span>المدير العام</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="glass-card rounded-none border-x-0 border-b-0 mt-16">
    <div class="container mx-auto px-6 py-8">
      <div class="text-center">
        <p class="text-blue-200 text-lg mb-2">
          © 2024 جمعية المنقف التعاونية - جميع الحقوق محفوظة
        </p>
        <p class="text-blue-300">
          <i class="fas fa-code mr-2"></i>
          تطوير وتنفيذ: <span class="font-bold text-white">محمد مرزوق العقاب</span>
        </p>
        <p class="text-slate-400 mt-2 text-sm">
          نظام متطور لإدارة العقود والموردين - التقويم الميلادي
        </p>
      </div>
    </div>
  </footer>

  <!-- Notification -->
  <div id="notification" class="notification">
    <div class="glass-card rounded-xl p-4 max-w-md mx-auto">
      <div class="flex items-center">
        <i id="notificationIcon" class="fas fa-info-circle text-blue-400 text-xl mr-3"></i>
        <span id="notificationText" class="text-white font-semibold"></span>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
  <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

  <!-- Custom JavaScript -->
  <script src="assets/js/utils.js"></script>
  <script src="assets/js/api.js"></script>
  <script src="assets/js/main.js"></script>
  <script>
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
      updateDateTime();
      loadDashboardStats();

      // Update time every second
      setInterval(updateDateTime, 1000);

      // Load stats every 30 seconds
      setInterval(loadDashboardStats, 30000);
    });

    // Update date and time with Gregorian calendar
    function updateDateTime() {
      const now = new Date();

      // Use Gregorian calendar (en-GB format)
      const dateOptions = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      };

      const timeOptions = {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      };

      document.getElementById('currentDate').textContent = now.toLocaleDateString('en-GB', dateOptions);
      document.getElementById('currentTime').textContent = now.toLocaleTimeString('en-GB', timeOptions);
    }

    // Load dashboard statistics
    async function loadDashboardStats() {
      try {
        // Load suppliers count
        const suppliersResponse = await fetch('/api/suppliers');
        if (suppliersResponse.ok) {
          const suppliersData = await suppliersResponse.json();
          document.getElementById('totalSuppliers').textContent = suppliersData.data?.length || 0;
        }

        // Load contracts count
        const contractsResponse = await fetch('/api/contracts');
        if (contractsResponse.ok) {
          const contractsData = await contractsResponse.json();
          const activeContracts = contractsData.data?.filter(c => c.status === 'active').length || 0;
          document.getElementById('activeContracts').textContent = activeContracts;
        }

        // Load eyes count
        const eyesResponse = await fetch('/api/eyes');
        if (eyesResponse.ok) {
          const eyesData = await eyesResponse.json();
          const rentedEyes = eyesData.data?.filter(e => e.status === 'rented').length || 0;
          document.getElementById('totalEyes').textContent = rentedEyes;

          // Calculate monthly revenue (60 KWD per eye)
          const monthlyRevenue = rentedEyes * 60;
          document.getElementById('monthlyRevenue').textContent = monthlyRevenue + ' د.ك';
        }
      } catch (error) {
        console.error('Error loading dashboard stats:', error);
      }
    }

    // Show section (placeholder for navigation)
    function showSection(section) {
      showNotification(`تم الانتقال إلى قسم ${getSectionName(section)}`, 'info');

      // Here you would implement actual navigation logic
      switch(section) {
        case 'suppliers':
          // Navigate to suppliers management
          break;
        case 'contracts':
          // Navigate to contracts management
          break;
        case 'eyes':
          // Navigate to eyes management
          break;
        case 'reports':
          // Navigate to reports
          break;
      }
    }

    // Get section name in Arabic
    function getSectionName(section) {
      const names = {
        'suppliers': 'إدارة الموردين',
        'contracts': 'إدارة العقود',
        'eyes': 'نظام العيون',
        'reports': 'التقارير والإحصائيات'
      };
      return names[section] || section;
    }

    // Show notification
    function showNotification(message, type = 'info') {
      const notification = document.getElementById('notification');
      const icon = document.getElementById('notificationIcon');
      const text = document.getElementById('notificationText');

      // Set icon and color based on type
      const config = {
        success: { icon: 'fa-check-circle', color: 'text-green-400' },
        error: { icon: 'fa-times-circle', color: 'text-red-400' },
        warning: { icon: 'fa-exclamation-triangle', color: 'text-yellow-400' },
        info: { icon: 'fa-info-circle', color: 'text-blue-400' }
      };

      const typeConfig = config[type] || config.info;
      icon.className = `fas ${typeConfig.icon} ${typeConfig.color} text-xl mr-3`;
      text.textContent = message;

      // Show notification
      notification.classList.add('show');

      // Hide after 3 seconds
      setTimeout(() => {
        notification.classList.remove('show');
      }, 3000);
    }

    // Format date for display (Gregorian)
    function formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-GB');
    }

    // Format currency
    function formatCurrency(amount) {
      return new Intl.NumberFormat('en-GB', {
        style: 'currency',
        currency: 'KWD',
        minimumFractionDigits: 2
      }).format(amount);
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
      // Alt + 1-6 for quick navigation
      if (e.altKey) {
        switch(e.key) {
          case '1':
            showSection('suppliers');
            break;
          case '2':
            showSection('contracts');
            break;
          case '3':
            showSection('eyes');
            break;
          case '4':
            showNotification('فتح نظام رفع الملفات', 'info');
            window.open('upload_suppliers.html', '_blank');
            break;
          case '5':
            showNotification('فتح إدارة ملفات العقود', 'info');
            window.open('contracts_files_manager.html', '_blank');
            break;
          case '6':
            showSection('reports');
            break;
        }
      }
    });

    // Welcome message
    setTimeout(() => {
      showNotification('مرحباً بك في النظام المتطور لإدارة العقود', 'success');
    }, 1000);
  </script>
</body>
</html>
