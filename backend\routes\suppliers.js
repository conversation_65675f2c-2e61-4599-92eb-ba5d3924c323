/**
 * مسارات API للموردين
 * جمعية المنقف التعاونية
 */

const express = require('express');
const { body, validationResult, query } = require('express-validator');
const database = require('../utils/database');
const logger = require('../utils/logger');

const router = express.Router();

// =====================================================
// مساعدات التحقق من صحة البيانات
// =====================================================

const supplierValidation = [
  body('name')
    .notEmpty()
    .withMessage('اسم المورد مطلوب')
    .isLength({ min: 2, max: 100 })
    .withMessage('اسم المورد يجب أن يكون بين 2 و 100 حرف'),
  
  body('phone')
    .notEmpty()
    .withMessage('رقم الهاتف مطلوب')
    .matches(/^[0-9+\-\s()]+$/)
    .withMessage('رقم الهاتف غير صحيح'),
  
  body('email')
    .optional()
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح'),
  
  body('category')
    .isIn(['بهارات', 'استهلاكي', 'أجبان'])
    .withMessage('فئة المورد غير صحيحة'),
  
  body('address')
    .optional()
    .isLength({ max: 200 })
    .withMessage('العنوان طويل جداً'),
  
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('الملاحظات طويلة جداً')
];

// =====================================================
// المسارات
// =====================================================

/**
 * GET /api/v1/suppliers
 * جلب جميع الموردين مع إمكانية التصفية والبحث
 */
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('رقم الصفحة غير صحيح'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('حد النتائج غير صحيح'),
  query('category').optional().isIn(['بهارات', 'استهلاكي', 'أجبان']).withMessage('الفئة غير صحيحة'),
  query('search').optional().isLength({ max: 100 }).withMessage('نص البحث طويل جداً'),
  query('status').optional().isIn(['نشط', 'غير نشط']).withMessage('الحالة غير صحيحة')
], async (req, res) => {
  try {
    // التحقق من صحة البيانات
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      category,
      search,
      status = 'نشط'
    } = req.query;

    const offset = (page - 1) * limit;

    // بناء استعلام البحث
    let whereClause = 'WHERE status = ?';
    let queryParams = [status];

    if (category) {
      whereClause += ' AND category = ?';
      queryParams.push(category);
    }

    if (search) {
      whereClause += ' AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)';
      const searchTerm = `%${search}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm);
    }

    // جلب العدد الإجمالي
    const countQuery = `SELECT COUNT(*) as total FROM suppliers ${whereClause}`;
    const countResult = await database.query(countQuery, queryParams);
    const total = countResult[0].total;

    // جلب البيانات
    const dataQuery = `
      SELECT 
        id,
        name,
        phone,
        email,
        category,
        address,
        status,
        notes,
        created_at,
        updated_at
      FROM suppliers 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    queryParams.push(parseInt(limit), parseInt(offset));
    const suppliers = await database.query(dataQuery, queryParams);

    // حساب إحصائيات سريعة
    const statsQuery = `
      SELECT 
        category,
        COUNT(*) as count
      FROM suppliers 
      WHERE status = 'نشط'
      GROUP BY category
    `;
    const stats = await database.query(statsQuery);

    res.json({
      success: true,
      data: {
        suppliers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        },
        stats: stats.reduce((acc, stat) => {
          acc[stat.category] = stat.count;
          return acc;
        }, {})
      }
    });

  } catch (error) {
    logger.error('خطأ في جلب الموردين:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب بيانات الموردين'
    });
  }
});

/**
 * GET /api/v1/suppliers/:id
 * جلب مورد محدد
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT 
        id,
        name,
        phone,
        email,
        category,
        address,
        status,
        notes,
        created_at,
        updated_at
      FROM suppliers 
      WHERE id = ?
    `;

    const suppliers = await database.query(query, [id]);

    if (suppliers.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المورد غير موجود'
      });
    }

    // جلب العقود المرتبطة بالمورد
    const contractsQuery = `
      SELECT 
        id,
        contract_number,
        start_date,
        end_date,
        total_amount,
        status
      FROM contracts 
      WHERE supplier_id = ?
      ORDER BY created_at DESC
      LIMIT 10
    `;

    const contracts = await database.query(contractsQuery, [id]);

    res.json({
      success: true,
      data: {
        supplier: suppliers[0],
        contracts
      }
    });

  } catch (error) {
    logger.error('خطأ في جلب المورد:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب بيانات المورد'
    });
  }
});

/**
 * POST /api/v1/suppliers
 * إضافة مورد جديد
 */
router.post('/', supplierValidation, async (req, res) => {
  try {
    // التحقق من صحة البيانات
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const {
      name,
      phone,
      email,
      category,
      address,
      notes
    } = req.body;

    // التحقق من عدم تكرار رقم الهاتف
    const existingSupplier = await database.query(
      'SELECT id FROM suppliers WHERE phone = ?',
      [phone]
    );

    if (existingSupplier.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'رقم الهاتف مستخدم مسبقاً'
      });
    }

    // إدراج المورد الجديد
    const insertQuery = `
      INSERT INTO suppliers (
        name, phone, email, category, address, notes, status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, 'نشط', NOW(), NOW())
    `;

    const result = await database.query(insertQuery, [
      name, phone, email || null, category, address || null, notes || null
    ]);

    // جلب المورد المُضاف
    const newSupplier = await database.query(
      'SELECT * FROM suppliers WHERE id = ?',
      [result.insertId]
    );

    logger.info(`تم إضافة مورد جديد: ${name} (ID: ${result.insertId})`);

    res.status(201).json({
      success: true,
      message: 'تم إضافة المورد بنجاح',
      data: newSupplier[0]
    });

  } catch (error) {
    logger.error('خطأ في إضافة المورد:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إضافة المورد'
    });
  }
});

/**
 * PUT /api/v1/suppliers/:id
 * تحديث مورد موجود
 */
router.put('/:id', supplierValidation, async (req, res) => {
  try {
    // التحقق من صحة البيانات
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const {
      name,
      phone,
      email,
      category,
      address,
      notes,
      status
    } = req.body;

    // التحقق من وجود المورد
    const existingSupplier = await database.query(
      'SELECT id FROM suppliers WHERE id = ?',
      [id]
    );

    if (existingSupplier.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المورد غير موجود'
      });
    }

    // التحقق من عدم تكرار رقم الهاتف (باستثناء المورد الحالي)
    const phoneCheck = await database.query(
      'SELECT id FROM suppliers WHERE phone = ? AND id != ?',
      [phone, id]
    );

    if (phoneCheck.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'رقم الهاتف مستخدم مسبقاً'
      });
    }

    // تحديث المورد
    const updateQuery = `
      UPDATE suppliers SET
        name = ?,
        phone = ?,
        email = ?,
        category = ?,
        address = ?,
        notes = ?,
        status = ?,
        updated_at = NOW()
      WHERE id = ?
    `;

    await database.query(updateQuery, [
      name,
      phone,
      email || null,
      category,
      address || null,
      notes || null,
      status || 'نشط',
      id
    ]);

    // جلب المورد المُحدث
    const updatedSupplier = await database.query(
      'SELECT * FROM suppliers WHERE id = ?',
      [id]
    );

    logger.info(`تم تحديث المورد: ${name} (ID: ${id})`);

    res.json({
      success: true,
      message: 'تم تحديث المورد بنجاح',
      data: updatedSupplier[0]
    });

  } catch (error) {
    logger.error('خطأ في تحديث المورد:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث المورد'
    });
  }
});

module.exports = router;
