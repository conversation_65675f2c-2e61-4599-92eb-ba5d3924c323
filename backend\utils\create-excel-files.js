const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

// إنشاء مجلد البيانات النموذجية إذا لم يكن موجوداً
const sampleDataDir = path.join(__dirname, '../../sample_data');
if (!fs.existsSync(sampleDataDir)) {
    fs.mkdirSync(sampleDataDir, { recursive: true });
}

// بيانات الموردين
const suppliersData = [
    {
        'الرقم': 1,
        'اسم المورد': 'شركة البهارات الكويتية',
        'الفئة': 'بهارات',
        'رقم الهاتف': '99887766',
        'البريد الإلكتروني': '<EMAIL>',
        'العنوان': 'الفروانية، شارع الرقعي',
        'السجل التجاري': '1234567890',
        'الرقم الضريبي': 'KW123456789',
        'الشخص المسؤول': 'أحمد محمد الكندري',
        'الحالة': 'نشط',
        'ملاحظات': 'مورد موثوق للبهارات المحلية والمستوردة'
    },
    {
        'الرقم': 2,
        'اسم المورد': 'مؤسسة الخليج للمواد الاستهلاكية',
        'الفئة': 'استهلاكي',
        'رقم الهاتف': '99776655',
        'البريد الإلكتروني': '<EMAIL>',
        'العنوان': 'حولي، شارع تونس',
        'السجل التجاري': '2345678901',
        'الرقم الضريبي': 'KW234567890',
        'الشخص المسؤول': 'فاطمة علي الرشيد',
        'الحالة': 'نشط',
        'ملاحظات': 'متخصص في المواد الاستهلاكية والمنظفات'
    },
    {
        'الرقم': 3,
        'اسم المورد': 'شركة الأجبان الطازجة',
        'الفئة': 'أجبان',
        'رقم الهاتف': '99665544',
        'البريد الإلكتروني': '<EMAIL>',
        'العنوان': 'الجهراء، المنطقة الصناعية',
        'السجل التجاري': '3456789012',
        'الرقم الضريبي': 'KW345678901',
        'الشخص المسؤول': 'محمد سالم العتيبي',
        'الحالة': 'نشط',
        'ملاحظات': 'أجبان طازجة ومستوردة عالية الجودة'
    },
    {
        'الرقم': 4,
        'اسم المورد': 'مؤسسة التوابل العربية',
        'الفئة': 'بهارات',
        'رقم الهاتف': '99554433',
        'البريد الإلكتروني': '<EMAIL>',
        'العنوان': 'الأحمدي، شارع الفحيحيل',
        'السجل التجاري': '4567890123',
        'الرقم الضريبي': 'KW456789012',
        'الشخص المسؤول': 'نورا خالد المطيري',
        'الحالة': 'نشط',
        'ملاحظات': 'توابل عربية أصيلة ومطحونة طازجة'
    },
    {
        'الرقم': 5,
        'اسم المورد': 'شركة المنظفات الحديثة',
        'الفئة': 'استهلاكي',
        'رقم الهاتف': '99443322',
        'البريد الإلكتروني': '<EMAIL>',
        'العنوان': 'العاصمة، شارع فهد السالم',
        'السجل التجاري': '5678901234',
        'الرقم الضريبي': 'KW567890123',
        'الشخص المسؤول': 'عبدالله أحمد الصباح',
        'الحالة': 'نشط',
        'ملاحظات': 'منظفات ومواد تنظيف متنوعة'
    }
];

// بيانات العقود
const contractsData = [
    {
        'رقم العقد': 'CON-2024-001',
        'عنوان العقد': 'عقد توريد البهارات الأساسية',
        'اسم المورد': 'شركة البهارات الكويتية',
        'نوع العقد': 'توريد',
        'تاريخ البداية': '2024-01-01',
        'تاريخ النهاية': '2024-12-31',
        'المبلغ': 15000.00,
        'العملة': 'KWD',
        'الحالة': 'نشط',
        'وصف العقد': 'توريد جميع أنواع البهارات الأساسية للجمعية',
        'الشروط والأحكام': 'التسليم خلال 48 ساعة، ضمان الجودة، إرجاع المعيب',
        'ملاحظات': 'عقد سنوي قابل للتجديد'
    },
    {
        'رقم العقد': 'CON-2024-002',
        'عنوان العقد': 'عقد إيجار عين رقم 1 - طبلية 1',
        'اسم المورد': 'مؤسسة الخليج للمواد الاستهلاكية',
        'نوع العقد': 'إيجار',
        'تاريخ البداية': '2024-01-15',
        'تاريخ النهاية': '2024-07-15',
        'المبلغ': 360.00,
        'العملة': 'KWD',
        'الحالة': 'نشط',
        'وصف العقد': 'إيجار عين في قسم المواد الاستهلاكية',
        'الشروط والأحكام': 'الإيجار 60 د.ك شهرياً، دفع مقدم، تأمين 120 د.ك',
        'ملاحظات': 'عقد إيجار لمدة 6 أشهر'
    },
    {
        'رقم العقد': 'CON-2024-003',
        'عنوان العقد': 'عقد توريد الأجبان الطازجة',
        'اسم المورد': 'شركة الأجبان الطازجة',
        'نوع العقد': 'توريد',
        'تاريخ البداية': '2024-02-01',
        'تاريخ النهاية': '2024-08-01',
        'المبلغ': 8500.00,
        'العملة': 'KWD',
        'الحالة': 'نشط',
        'وصف العقد': 'توريد أجبان طازجة ومبردة يومياً',
        'الشروط والأحكام': 'التسليم يومياً صباحاً، حفظ بارد، صلاحية 5 أيام',
        'ملاحظات': 'عقد موسمي للصيف'
    }
];

// بيانات إيجارات العيون
const eyeRentalsData = [
    {
        'رقم العين': 'EYE-001',
        'اسم المورد': 'مؤسسة الخليج للمواد الاستهلاكية',
        'رقم الطبلية': 1,
        'رقم القسم': 1,
        'الفئة': 'استهلاكي',
        'تاريخ البداية': '2024-01-15',
        'تاريخ النهاية': '2024-07-15',
        'الإيجار الشهري': 60.00,
        'مبلغ التأمين': 120.00,
        'الحالة': 'نشط',
        'ملاحظات': 'عين في موقع ممتاز بجانب المدخل الرئيسي'
    },
    {
        'رقم العين': 'EYE-002',
        'اسم المورد': 'شركة البهارات الكويتية',
        'رقم الطبلية': 1,
        'رقم القسم': 2,
        'الفئة': 'بهارات',
        'تاريخ البداية': '2024-02-01',
        'تاريخ النهاية': '2025-02-01',
        'الإيجار الشهري': 60.00,
        'مبلغ التأمين': 120.00,
        'الحالة': 'نشط',
        'ملاحظات': 'عين مخصصة للبهارات المحلية والمستوردة'
    },
    {
        'رقم العين': 'EYE-003',
        'اسم المورد': 'شركة الأجبان الطازجة',
        'رقم الطبلية': 1,
        'رقم القسم': 3,
        'الفئة': 'أجبان',
        'تاريخ البداية': '2024-02-15',
        'تاريخ النهاية': '2024-08-15',
        'الإيجار الشهري': 60.00,
        'مبلغ التأمين': 120.00,
        'الحالة': 'نشط',
        'ملاحظات': 'عين مع ثلاجة عرض للأجبان الطازجة'
    }
];

// بيانات الطبليات والأقسام
const tablesData = [
    {
        'رقم الطبلية': 1,
        'رقم القسم': 1,
        'حجم القسم (سم)': 60,
        'الفئة المخصصة': 'استهلاكي',
        'سعر الإيجار (د.ك)': 60.00,
        'الحالة': 'مؤجر',
        'المورد الحالي': 'مؤسسة الخليج للمواد الاستهلاكية',
        'تاريخ انتهاء الإيجار': '2024-07-15'
    },
    {
        'رقم الطبلية': 1,
        'رقم القسم': 2,
        'حجم القسم (سم)': 60,
        'الفئة المخصصة': 'بهارات',
        'سعر الإيجار (د.ك)': 60.00,
        'الحالة': 'مؤجر',
        'المورد الحالي': 'شركة البهارات الكويتية',
        'تاريخ انتهاء الإيجار': '2025-02-01'
    },
    {
        'رقم الطبلية': 1,
        'رقم القسم': 3,
        'حجم القسم (سم)': 60,
        'الفئة المخصصة': 'أجبان',
        'سعر الإيجار (د.ك)': 60.00,
        'الحالة': 'مؤجر',
        'المورد الحالي': 'شركة الأجبان الطازجة',
        'تاريخ انتهاء الإيجار': '2024-08-15'
    },
    {
        'رقم الطبلية': 1,
        'رقم القسم': 4,
        'حجم القسم (سم)': 60,
        'الفئة المخصصة': 'بهارات',
        'سعر الإيجار (د.ك)': 60.00,
        'الحالة': 'متاح',
        'المورد الحالي': '',
        'تاريخ انتهاء الإيجار': ''
    }
];

// إنشاء ملف Excel شامل
function createComprehensiveExcelFile() {
    const workbook = XLSX.utils.book_new();
    
    // إضافة ورقة الموردين
    const suppliersWorksheet = XLSX.utils.json_to_sheet(suppliersData);
    XLSX.utils.book_append_sheet(workbook, suppliersWorksheet, 'الموردين');
    
    // إضافة ورقة العقود
    const contractsWorksheet = XLSX.utils.json_to_sheet(contractsData);
    XLSX.utils.book_append_sheet(workbook, contractsWorksheet, 'العقود');
    
    // إضافة ورقة إيجارات العيون
    const eyeRentalsWorksheet = XLSX.utils.json_to_sheet(eyeRentalsData);
    XLSX.utils.book_append_sheet(workbook, eyeRentalsWorksheet, 'إيجارات العيون');
    
    // إضافة ورقة الطبليات والأقسام
    const tablesWorksheet = XLSX.utils.json_to_sheet(tablesData);
    XLSX.utils.book_append_sheet(workbook, tablesWorksheet, 'الطبليات والأقسام');
    
    // حفظ الملف
    const filePath = path.join(sampleDataDir, 'بيانات_جمعية_المنقف_الشاملة.xlsx');
    XLSX.writeFile(workbook, filePath);
    
    console.log('✅ تم إنشاء ملف Excel الشامل:', filePath);
    return filePath;
}

// إنشاء ملفات Excel منفصلة
function createSeparateExcelFiles() {
    // ملف الموردين
    const suppliersWorkbook = XLSX.utils.book_new();
    const suppliersWorksheet = XLSX.utils.json_to_sheet(suppliersData);
    XLSX.utils.book_append_sheet(suppliersWorkbook, suppliersWorksheet, 'الموردين');
    const suppliersPath = path.join(sampleDataDir, 'موردين_جمعية_المنقف.xlsx');
    XLSX.writeFile(suppliersWorkbook, suppliersPath);
    
    // ملف العقود
    const contractsWorkbook = XLSX.utils.book_new();
    const contractsWorksheet = XLSX.utils.json_to_sheet(contractsData);
    XLSX.utils.book_append_sheet(contractsWorkbook, contractsWorksheet, 'العقود');
    const contractsPath = path.join(sampleDataDir, 'عقود_جمعية_المنقف.xlsx');
    XLSX.writeFile(contractsWorkbook, contractsPath);
    
    // ملف إيجارات العيون
    const eyeRentalsWorkbook = XLSX.utils.book_new();
    const eyeRentalsWorksheet = XLSX.utils.json_to_sheet(eyeRentalsData);
    XLSX.utils.book_append_sheet(eyeRentalsWorkbook, eyeRentalsWorksheet, 'إيجارات العيون');
    const eyeRentalsPath = path.join(sampleDataDir, 'ايجارات_العيون_جمعية_المنقف.xlsx');
    XLSX.writeFile(eyeRentalsWorkbook, eyeRentalsPath);
    
    console.log('✅ تم إنشاء ملفات Excel المنفصلة:');
    console.log('   📄', suppliersPath);
    console.log('   📄', contractsPath);
    console.log('   📄', eyeRentalsPath);
    
    return {
        suppliers: suppliersPath,
        contracts: contractsPath,
        eyeRentals: eyeRentalsPath
    };
}

// تشغيل الدوال
if (require.main === module) {
    console.log('🚀 بدء إنشاء ملفات Excel...');
    console.log('📊 جمعية المنقف التعاونية');
    console.log('👨‍💻 تطوير: محمد مرزوق العقاب');
    console.log('');
    
    try {
        createComprehensiveExcelFile();
        createSeparateExcelFiles();
        
        console.log('');
        console.log('🎉 تم إنشاء جميع ملفات Excel بنجاح!');
        console.log('📁 المجلد: sample_data/');
        console.log('');
        console.log('📋 الملفات المنشأة:');
        console.log('   📊 بيانات_جمعية_المنقف_الشاملة.xlsx - ملف شامل');
        console.log('   👥 موردين_جمعية_المنقف.xlsx - الموردين');
        console.log('   📋 عقود_جمعية_المنقف.xlsx - العقود');
        console.log('   👁️ ايجارات_العيون_جمعية_المنقف.xlsx - إيجارات العيون');
        console.log('');
        console.log('✅ جميع الملفات جاهزة للاستخدام والاختبار!');
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء ملفات Excel:', error);
    }
}

module.exports = {
    createComprehensiveExcelFile,
    createSeparateExcelFiles,
    suppliersData,
    contractsData,
    eyeRentalsData,
    tablesData
};
