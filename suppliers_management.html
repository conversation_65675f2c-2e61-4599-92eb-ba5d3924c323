<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>إدارة الموردين - نظام إدارة العقود | محمد مرزوق العقاب</title>
  
  <!-- CSS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
  
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap');
    
    :root {
      --bg-primary: #0a0f1c;
      --bg-secondary: #1a2332;
      --bg-tertiary: #2a3441;
      --text-primary: #ffffff;
      --text-secondary: #f1f5f9;
      --accent-blue: #3b82f6;
      --accent-green: #10b981;
      --accent-red: #ef4444;
      --border-primary: #475569;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Cairo', sans-serif;
      background: var(--bg-primary);
      color: var(--text-primary);
      min-height: 100vh;
    }
    
    .glass-card {
      background: rgba(26, 35, 50, 0.8);
      backdrop-filter: blur(20px);
      border: 1px solid var(--border-primary);
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }
    
    .btn-primary {
      background: linear-gradient(135deg, var(--accent-blue), #2563eb);
      color: var(--text-primary);
      padding: 12px 24px;
      border-radius: 12px;
      border: none;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    }
    
    .btn-success {
      background: linear-gradient(135deg, var(--accent-green), #059669);
      color: var(--text-primary);
      padding: 12px 24px;
      border-radius: 12px;
      border: none;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .btn-danger {
      background: linear-gradient(135deg, var(--accent-red), #dc2626);
      color: var(--text-primary);
      padding: 12px 24px;
      border-radius: 12px;
      border: none;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .form-input {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid var(--border-primary);
      border-radius: 12px;
      background: var(--bg-tertiary);
      color: var(--text-primary);
      font-family: 'Cairo', sans-serif;
      transition: all 0.3s ease;
    }
    
    .form-input:focus {
      outline: none;
      border-color: var(--accent-blue);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .table-container {
      overflow-x: auto;
      border-radius: 16px;
      background: var(--bg-secondary);
    }
    
    .data-table {
      width: 100%;
      border-collapse: collapse;
    }
    
    .data-table th {
      background: var(--bg-tertiary);
      padding: 16px;
      text-align: right;
      font-weight: 700;
      color: var(--text-primary);
      border-bottom: 2px solid var(--border-primary);
    }
    
    .data-table td {
      padding: 12px 16px;
      border-bottom: 1px solid var(--border-primary);
      color: var(--text-secondary);
    }
    
    .data-table tbody tr:hover {
      background: rgba(59, 130, 246, 0.1);
    }
    
    .badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
    }
    
    .badge-success {
      background: rgba(16, 185, 129, 0.2);
      color: var(--accent-green);
      border: 1px solid var(--accent-green);
    }
    
    .badge-warning {
      background: rgba(245, 158, 11, 0.2);
      color: #f59e0b;
      border: 1px solid #f59e0b;
    }
    
    .loading {
      display: none;
      text-align: center;
      padding: 20px;
    }
    
    .spinner {
      border: 4px solid var(--border-primary);
      border-top: 4px solid var(--accent-blue);
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <!-- Header -->
  <header class="glass-card m-6 p-6">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
          <i class="fas fa-users text-white text-xl"></i>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-white">إدارة الموردين</h1>
          <p class="text-slate-300">جمعية المنقف التعاونية</p>
        </div>
      </div>
      <div class="text-right">
        <p class="text-white font-semibold">تطوير وتنفيذ</p>
        <p class="text-blue-400 font-bold">محمد مرزوق العقاب</p>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <div class="mx-6 mb-6">
    <!-- Controls -->
    <div class="glass-card p-6 mb-6">
      <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div class="flex gap-4">
          <button class="btn-primary" onclick="showAddModal()">
            <i class="fas fa-plus ml-2"></i>
            إضافة مورد جديد
          </button>
          <button class="btn-success" onclick="refreshData()">
            <i class="fas fa-sync ml-2"></i>
            تحديث البيانات
          </button>
        </div>
        
        <div class="flex gap-4">
          <input type="text" id="searchInput" placeholder="البحث في الموردين..." 
                 class="form-input w-64" onkeyup="searchSuppliers()">
          <select id="categoryFilter" class="form-input w-48" onchange="filterByCategory()">
            <option value="">جميع الفئات</option>
            <option value="بهارات">بهارات</option>
            <option value="استهلاكي">استهلاكي</option>
            <option value="أجبان">أجبان</option>
          </select>
        </div>
      </div>
      
      <!-- Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-blue-500/20 p-4 rounded-xl border border-blue-500/30">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-blue-400 text-sm font-medium">إجمالي الموردين</p>
              <p class="text-white text-2xl font-bold" id="totalSuppliers">0</p>
            </div>
            <i class="fas fa-users text-blue-400 text-2xl"></i>
          </div>
        </div>
        
        <div class="bg-green-500/20 p-4 rounded-xl border border-green-500/30">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-green-400 text-sm font-medium">الموردين النشطين</p>
              <p class="text-white text-2xl font-bold" id="activeSuppliers">0</p>
            </div>
            <i class="fas fa-check-circle text-green-400 text-2xl"></i>
          </div>
        </div>
        
        <div class="bg-purple-500/20 p-4 rounded-xl border border-purple-500/30">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-purple-400 text-sm font-medium">فئة البهارات</p>
              <p class="text-white text-2xl font-bold" id="spicesSuppliers">0</p>
            </div>
            <i class="fas fa-pepper-hot text-purple-400 text-2xl"></i>
          </div>
        </div>
        
        <div class="bg-orange-500/20 p-4 rounded-xl border border-orange-500/30">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-orange-400 text-sm font-medium">فئة الأجبان</p>
              <p class="text-white text-2xl font-bold" id="cheeseSuppliers">0</p>
            </div>
            <i class="fas fa-cheese text-orange-400 text-2xl"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Suppliers Table -->
    <div class="glass-card p-6">
      <h2 class="text-xl font-bold text-white mb-4">قائمة الموردين</h2>
      
      <div class="loading" id="loading">
        <div class="spinner"></div>
        <p class="text-slate-300 mt-4">جاري تحميل البيانات...</p>
      </div>
      
      <div class="table-container" id="tableContainer">
        <table class="data-table">
          <thead>
            <tr>
              <th>الرقم</th>
              <th>اسم المورد</th>
              <th>الفئة</th>
              <th>الهاتف</th>
              <th>البريد الإلكتروني</th>
              <th>الشخص المسؤول</th>
              <th>الحالة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody id="suppliersTableBody">
            <!-- سيتم ملء البيانات هنا -->
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Add/Edit Modal -->
  <div id="supplierModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
      <div class="glass-card p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-2xl font-bold text-white" id="modalTitle">إضافة مورد جديد</h3>
          <button onclick="closeModal()" class="text-slate-400 hover:text-white">
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>
        
        <form id="supplierForm" onsubmit="saveSupplier(event)">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-white font-medium mb-2">اسم المورد *</label>
              <input type="text" id="supplierName" name="name" required class="form-input">
            </div>
            
            <div>
              <label class="block text-white font-medium mb-2">الفئة *</label>
              <select id="supplierCategory" name="category" required class="form-input">
                <option value="">اختر الفئة</option>
                <option value="بهارات">بهارات</option>
                <option value="استهلاكي">استهلاكي</option>
                <option value="أجبان">أجبان</option>
              </select>
            </div>
            
            <div>
              <label class="block text-white font-medium mb-2">رقم الهاتف</label>
              <input type="tel" id="supplierPhone" name="phone" class="form-input">
            </div>
            
            <div>
              <label class="block text-white font-medium mb-2">البريد الإلكتروني</label>
              <input type="email" id="supplierEmail" name="email" class="form-input">
            </div>
            
            <div class="md:col-span-2">
              <label class="block text-white font-medium mb-2">العنوان</label>
              <input type="text" id="supplierAddress" name="address" class="form-input">
            </div>
            
            <div>
              <label class="block text-white font-medium mb-2">السجل التجاري</label>
              <input type="text" id="supplierCommercialRecord" name="commercial_record" class="form-input">
            </div>
            
            <div>
              <label class="block text-white font-medium mb-2">الرقم الضريبي</label>
              <input type="text" id="supplierTaxNumber" name="tax_number" class="form-input">
            </div>
            
            <div>
              <label class="block text-white font-medium mb-2">الشخص المسؤول</label>
              <input type="text" id="supplierContactPerson" name="contact_person" class="form-input">
            </div>
            
            <div>
              <label class="block text-white font-medium mb-2">الحالة</label>
              <select id="supplierStatus" name="status" class="form-input">
                <option value="active">نشط</option>
                <option value="inactive">غير نشط</option>
              </select>
            </div>
            
            <div class="md:col-span-2">
              <label class="block text-white font-medium mb-2">ملاحظات</label>
              <textarea id="supplierNotes" name="notes" rows="3" class="form-input"></textarea>
            </div>
          </div>
          
          <div class="flex gap-4 mt-8">
            <button type="submit" class="btn-primary flex-1">
              <i class="fas fa-save ml-2"></i>
              حفظ
            </button>
            <button type="button" onclick="closeModal()" class="btn-danger">
              <i class="fas fa-times ml-2"></i>
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <script>
    // متغيرات عامة
    let suppliers = [];
    let editingId = null;
    
    // تحميل البيانات عند بدء الصفحة
    document.addEventListener('DOMContentLoaded', function() {
      loadSuppliers();
    });
    
    // تحميل الموردين
    async function loadSuppliers() {
      try {
        showLoading(true);
        const response = await fetch('/api/suppliers');
        const result = await response.json();
        
        if (result.success) {
          suppliers = result.data;
          displaySuppliers(suppliers);
          updateStatistics();
        } else {
          throw new Error(result.error || 'فشل في تحميل البيانات');
        }
      } catch (error) {
        console.error('Error loading suppliers:', error);
        Swal.fire({
          icon: 'error',
          title: 'خطأ',
          text: 'فشل في تحميل بيانات الموردين',
          confirmButtonText: 'موافق'
        });
      } finally {
        showLoading(false);
      }
    }
    
    // عرض الموردين في الجدول
    function displaySuppliers(data) {
      const tbody = document.getElementById('suppliersTableBody');
      tbody.innerHTML = '';
      
      data.forEach((supplier, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${index + 1}</td>
          <td class="font-semibold text-white">${supplier.name}</td>
          <td>
            <span class="badge badge-success">${supplier.category}</span>
          </td>
          <td>${supplier.phone || '-'}</td>
          <td>${supplier.email || '-'}</td>
          <td>${supplier.contact_person || '-'}</td>
          <td>
            <span class="badge ${supplier.status === 'active' ? 'badge-success' : 'badge-warning'}">
              ${supplier.status === 'active' ? 'نشط' : 'غير نشط'}
            </span>
          </td>
          <td>
            <div class="flex gap-2">
              <button onclick="editSupplier(${supplier.id})" class="text-blue-400 hover:text-blue-300">
                <i class="fas fa-edit"></i>
              </button>
              <button onclick="deleteSupplier(${supplier.id})" class="text-red-400 hover:text-red-300">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </td>
        `;
        tbody.appendChild(row);
      });
    }
    
    // تحديث الإحصائيات
    function updateStatistics() {
      document.getElementById('totalSuppliers').textContent = suppliers.length;
      document.getElementById('activeSuppliers').textContent = suppliers.filter(s => s.status === 'active').length;
      document.getElementById('spicesSuppliers').textContent = suppliers.filter(s => s.category === 'بهارات').length;
      document.getElementById('cheeseSuppliers').textContent = suppliers.filter(s => s.category === 'أجبان').length;
    }
    
    // البحث في الموردين
    function searchSuppliers() {
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();
      const filtered = suppliers.filter(supplier => 
        supplier.name.toLowerCase().includes(searchTerm) ||
        supplier.category.toLowerCase().includes(searchTerm) ||
        (supplier.phone && supplier.phone.includes(searchTerm)) ||
        (supplier.email && supplier.email.toLowerCase().includes(searchTerm))
      );
      displaySuppliers(filtered);
    }
    
    // تصفية حسب الفئة
    function filterByCategory() {
      const category = document.getElementById('categoryFilter').value;
      const filtered = category ? suppliers.filter(s => s.category === category) : suppliers;
      displaySuppliers(filtered);
    }
    
    // إظهار نافذة الإضافة
    function showAddModal() {
      editingId = null;
      document.getElementById('modalTitle').textContent = 'إضافة مورد جديد';
      document.getElementById('supplierForm').reset();
      document.getElementById('supplierModal').classList.remove('hidden');
    }
    
    // تعديل مورد
    function editSupplier(id) {
      const supplier = suppliers.find(s => s.id === id);
      if (!supplier) return;
      
      editingId = id;
      document.getElementById('modalTitle').textContent = 'تعديل بيانات المورد';
      
      // ملء النموذج بالبيانات
      document.getElementById('supplierName').value = supplier.name || '';
      document.getElementById('supplierCategory').value = supplier.category || '';
      document.getElementById('supplierPhone').value = supplier.phone || '';
      document.getElementById('supplierEmail').value = supplier.email || '';
      document.getElementById('supplierAddress').value = supplier.address || '';
      document.getElementById('supplierCommercialRecord').value = supplier.commercial_record || '';
      document.getElementById('supplierTaxNumber').value = supplier.tax_number || '';
      document.getElementById('supplierContactPerson').value = supplier.contact_person || '';
      document.getElementById('supplierStatus').value = supplier.status || 'active';
      document.getElementById('supplierNotes').value = supplier.notes || '';
      
      document.getElementById('supplierModal').classList.remove('hidden');
    }
    
    // حفظ المورد
    async function saveSupplier(event) {
      event.preventDefault();
      
      const formData = new FormData(event.target);
      const supplierData = Object.fromEntries(formData.entries());
      
      try {
        const url = editingId ? `/api/suppliers/${editingId}` : '/api/suppliers';
        const method = editingId ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(supplierData)
        });
        
        const result = await response.json();
        
        if (result.success) {
          Swal.fire({
            icon: 'success',
            title: 'نجح',
            text: result.message,
            confirmButtonText: 'موافق'
          });
          
          closeModal();
          loadSuppliers();
        } else {
          throw new Error(result.error || 'فشل في حفظ البيانات');
        }
      } catch (error) {
        console.error('Error saving supplier:', error);
        Swal.fire({
          icon: 'error',
          title: 'خطأ',
          text: 'فشل في حفظ بيانات المورد',
          confirmButtonText: 'موافق'
        });
      }
    }
    
    // حذف مورد
    async function deleteSupplier(id) {
      const result = await Swal.fire({
        icon: 'warning',
        title: 'تأكيد الحذف',
        text: 'هل أنت متأكد من حذف هذا المورد؟',
        showCancelButton: true,
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        confirmButtonColor: '#ef4444'
      });
      
      if (result.isConfirmed) {
        try {
          const response = await fetch(`/api/suppliers/${id}`, {
            method: 'DELETE'
          });
          
          const result = await response.json();
          
          if (result.success) {
            Swal.fire({
              icon: 'success',
              title: 'تم الحذف',
              text: result.message,
              confirmButtonText: 'موافق'
            });
            
            loadSuppliers();
          } else {
            throw new Error(result.error || 'فشل في حذف المورد');
          }
        } catch (error) {
          console.error('Error deleting supplier:', error);
          Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'فشل في حذف المورد',
            confirmButtonText: 'موافق'
          });
        }
      }
    }
    
    // إغلاق النافذة المنبثقة
    function closeModal() {
      document.getElementById('supplierModal').classList.add('hidden');
      editingId = null;
    }
    
    // إظهار/إخفاء التحميل
    function showLoading(show) {
      document.getElementById('loading').style.display = show ? 'block' : 'none';
      document.getElementById('tableContainer').style.display = show ? 'none' : 'block';
    }
    
    // تحديث البيانات
    function refreshData() {
      loadSuppliers();
    }
  </script>
</body>
</html>
