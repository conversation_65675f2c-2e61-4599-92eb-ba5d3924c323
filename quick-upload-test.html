<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>اختبار سريع - رفع ملفات الموردين</title>
  
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');
    
    body {
      font-family: 'Cairo', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    
    .glass {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    }
    
    .btn {
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
    
    .btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s;
    }
    
    .btn:hover::before {
      left: 100%;
    }
    
    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }
    
    .feature-card {
      transition: all 0.4s ease;
    }
    
    .feature-card:hover {
      transform: translateY(-5px) scale(1.02);
    }
    
    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }
    
    .pulse-animation {
      animation: pulse 2s infinite;
    }
  </style>
</head>
<body class="flex items-center justify-center min-h-screen p-6">
  <div class="max-w-4xl w-full">
    
    <!-- Header -->
    <div class="text-center mb-12">
      <div class="w-20 h-20 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6 pulse-animation">
        <i class="fas fa-cloud-upload-alt text-white text-3xl"></i>
      </div>
      <h1 class="text-4xl font-bold text-white mb-4">🚀 اختبار سريع لنظام رفع الملفات</h1>
      <p class="text-xl text-white/80 mb-8">تأكد من عمل جميع مكونات نظام رفع ملفات الموردين</p>
    </div>
    
    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
      
      <!-- Upload Page -->
      <div class="feature-card glass rounded-2xl p-6 text-center">
        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-upload text-white"></i>
        </div>
        <h3 class="text-lg font-bold text-white mb-3">صفحة الرفع</h3>
        <p class="text-white/70 text-sm mb-4">اختبار واجهة رفع الملفات</p>
        <a href="upload_suppliers.html" class="btn bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg w-full block text-center">
          <i class="fas fa-external-link-alt ml-2"></i>
          فتح صفحة الرفع
        </a>
      </div>
      
      <!-- Download Template -->
      <div class="feature-card glass rounded-2xl p-6 text-center">
        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-download text-white"></i>
        </div>
        <h3 class="text-lg font-bold text-white mb-3">تحميل النموذج</h3>
        <p class="text-white/70 text-sm mb-4">اختبار تحميل نموذج Excel</p>
        <button onclick="testTemplateDownload()" class="btn bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg w-full">
          <i class="fas fa-file-excel ml-2"></i>
          اختبار التحميل
        </button>
      </div>
      
      <!-- API Test -->
      <div class="feature-card glass rounded-2xl p-6 text-center">
        <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-cog text-white"></i>
        </div>
        <h3 class="text-lg font-bold text-white mb-3">اختبار API</h3>
        <p class="text-white/70 text-sm mb-4">فحص اتصال الخادم</p>
        <button onclick="testAPI()" class="btn bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg w-full">
          <i class="fas fa-server ml-2"></i>
          اختبار الخادم
        </button>
      </div>
      
      <!-- Create Sample File -->
      <div class="feature-card glass rounded-2xl p-6 text-center">
        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-file-plus text-white"></i>
        </div>
        <h3 class="text-lg font-bold text-white mb-3">إنشاء ملف تجريبي</h3>
        <p class="text-white/70 text-sm mb-4">إنشاء ملف للاختبار</p>
        <button onclick="createSampleFile()" class="btn bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg w-full">
          <i class="fas fa-magic ml-2"></i>
          إنشاء ملف تجريبي
        </button>
      </div>
      
      <!-- View Suppliers -->
      <div class="feature-card glass rounded-2xl p-6 text-center">
        <div class="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-users text-white"></i>
        </div>
        <h3 class="text-lg font-bold text-white mb-3">عرض الموردين</h3>
        <p class="text-white/70 text-sm mb-4">مراجعة قائمة الموردين</p>
        <a href="enhanced_web_interface.html" class="btn bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg w-full block text-center">
          <i class="fas fa-list ml-2"></i>
          عرض الموردين
        </a>
      </div>
      
      <!-- Back to Main -->
      <div class="feature-card glass rounded-2xl p-6 text-center">
        <div class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-home text-white"></i>
        </div>
        <h3 class="text-lg font-bold text-white mb-3">العودة للرئيسية</h3>
        <p class="text-white/70 text-sm mb-4">العودة لصفحة التشغيل</p>
        <a href="launch-interface.html" class="btn bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg w-full block text-center">
          <i class="fas fa-arrow-right ml-2"></i>
          الصفحة الرئيسية
        </a>
      </div>
    </div>
    
    <!-- Test Results -->
    <div id="testResults" class="glass rounded-2xl p-6 hidden">
      <h3 class="text-xl font-bold text-white mb-4 flex items-center">
        <i class="fas fa-clipboard-check ml-3 text-green-400"></i>
        نتائج الاختبار
      </h3>
      <div id="resultsContent" class="space-y-3">
        <!-- Results will be shown here -->
      </div>
    </div>
    
    <!-- Instructions -->
    <div class="glass rounded-2xl p-6 mt-8">
      <h3 class="text-xl font-bold text-white mb-4 flex items-center">
        <i class="fas fa-info-circle ml-3 text-blue-400"></i>
        تعليمات الاختبار
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h4 class="text-lg font-medium text-white mb-3">خطوات الاختبار:</h4>
          <ol class="text-white/80 space-y-2 list-decimal list-inside">
            <li>اختبر اتصال الخادم أولاً</li>
            <li>حمل النموذج للتأكد من عمله</li>
            <li>أنشئ ملف تجريبي للاختبار</li>
            <li>افتح صفحة الرفع واختبرها</li>
            <li>تحقق من النتائج في قائمة الموردين</li>
          </ol>
        </div>
        <div>
          <h4 class="text-lg font-medium text-white mb-3">ما يجب التحقق منه:</h4>
          <ul class="text-white/80 space-y-2 list-disc list-inside">
            <li>عمل الخادم بشكل صحيح</li>
            <li>تحميل النموذج بنجاح</li>
            <li>رفع الملفات بدون أخطاء</li>
            <li>معالجة البيانات بشكل صحيح</li>
            <li>عرض النتائج والإحصائيات</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  
  <!-- JavaScript -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
  <script>
    // Test API connection
    async function testAPI() {
      showResult('🔄 جاري اختبار الاتصال بالخادم...', 'info');
      
      try {
        // Test basic server connection
        const response = await fetch('/api/v1/upload/suppliers/template', {
          method: 'HEAD'
        });
        
        if (response.ok || response.status === 405) {
          showResult('✅ الخادم يعمل بشكل صحيح', 'success');
          
          // Test upload endpoint
          try {
            const uploadTest = await fetch('/api/v1/upload/suppliers', {
              method: 'POST',
              body: new FormData()
            });
            
            if (uploadTest.status === 400) {
              showResult('✅ نقطة نهاية الرفع متاحة', 'success');
            } else {
              showResult('⚠️ نقطة نهاية الرفع تحتاج فحص', 'warning');
            }
          } catch (error) {
            showResult('❌ خطأ في نقطة نهاية الرفع: ' + error.message, 'error');
          }
          
        } else {
          throw new Error(`HTTP ${response.status}`);
        }
      } catch (error) {
        showResult('❌ فشل الاتصال بالخادم: ' + error.message, 'error');
        showResult('💡 تأكد من تشغيل الخادم: npm start', 'info');
      }
    }
    
    // Test template download
    async function testTemplateDownload() {
      showResult('🔄 جاري اختبار تحميل النموذج...', 'info');
      
      try {
        const response = await fetch('/api/v1/upload/suppliers/template');
        
        if (response.ok) {
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = 'نموذج_اختبار.xlsx';
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          window.URL.revokeObjectURL(url);
          
          showResult('✅ تم تحميل النموذج بنجاح من الخادم', 'success');
        } else {
          throw new Error(`HTTP ${response.status}`);
        }
      } catch (error) {
        showResult('⚠️ فشل تحميل النموذج من الخادم، جاري الإنشاء محلياً...', 'warning');
        
        // Fallback to local generation
        const templateData = [
          {
            'اسم المورد': 'شركة البهارات الذهبية',
            'الفئة': 'بهارات',
            'رقم الهاتف': '+965-12345678',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'الكويت - حولي',
            'الشخص المسؤول': 'أحمد محمد'
          }
        ];
        
        const ws = XLSX.utils.json_to_sheet(templateData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'الموردين');
        
        XLSX.writeFile(wb, 'نموذج_اختبار_محلي.xlsx');
        showResult('✅ تم إنشاء النموذج محلياً بنجاح', 'success');
      }
    }
    
    // Create sample file for testing
    function createSampleFile() {
      showResult('🔄 جاري إنشاء ملف تجريبي...', 'info');
      
      const sampleData = [
        {
          'اسم المورد': 'شركة البهارات الكويتية',
          'الفئة': 'بهارات',
          'رقم الهاتف': '+965-11111111',
          'البريد الإلكتروني': '<EMAIL>',
          'العنوان': 'الكويت - الجهراء',
          'الشخص المسؤول': 'سالم أحمد'
        },
        {
          'اسم المورد': 'مؤسسة المواد الغذائية',
          'الفئة': 'استهلاكي',
          'رقم الهاتف': '+965-22222222',
          'البريد الإلكتروني': '<EMAIL>',
          'العنوان': 'الكويت - الفروانية',
          'الشخص المسؤول': 'نورا محمد'
        },
        {
          'اسم المورد': 'شركة الألبان الطازجة',
          'الفئة': 'أجبان',
          'رقم الهاتف': '+965-33333333',
          'البريد الإلكتروني': '<EMAIL>',
          'العنوان': 'الكويت - حولي',
          'الشخص المسؤول': 'خالد علي'
        },
        {
          'اسم المورد': 'مورد تجريبي خاطئ',
          'الفئة': 'فئة خاطئة',
          'رقم الهاتف': 'رقم خاطئ',
          'البريد الإلكتروني': 'بريد خاطئ',
          'العنوان': '',
          'الشخص المسؤول': ''
        }
      ];
      
      const ws = XLSX.utils.json_to_sheet(sampleData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'الموردين');
      
      // Set column widths
      ws['!cols'] = [
        { width: 25 },
        { width: 15 },
        { width: 20 },
        { width: 30 },
        { width: 25 },
        { width: 20 }
      ];
      
      XLSX.writeFile(wb, 'ملف_تجريبي_للاختبار.xlsx');
      
      showResult('✅ تم إنشاء ملف تجريبي بنجاح', 'success');
      showResult('📁 الملف يحتوي على 3 موردين صحيحين و 1 خاطئ للاختبار', 'info');
      showResult('💡 استخدم هذا الملف لاختبار نظام الرفع', 'info');
    }
    
    // Show test result
    function showResult(message, type) {
      const resultsDiv = document.getElementById('testResults');
      const contentDiv = document.getElementById('resultsContent');
      
      resultsDiv.classList.remove('hidden');
      
      const resultItem = document.createElement('div');
      resultItem.className = `p-3 rounded-lg flex items-center gap-3 ${getResultClass(type)}`;
      
      const icon = getResultIcon(type);
      resultItem.innerHTML = `
        <i class="fas fa-${icon}"></i>
        <span>${message}</span>
        <span class="mr-auto text-xs opacity-70">${new Date().toLocaleTimeString('ar-KW')}</span>
      `;
      
      contentDiv.appendChild(resultItem);
      
      // Scroll to bottom
      contentDiv.scrollTop = contentDiv.scrollHeight;
    }
    
    // Get result class based on type
    function getResultClass(type) {
      const classes = {
        success: 'bg-green-500/20 border border-green-500/30 text-green-300',
        error: 'bg-red-500/20 border border-red-500/30 text-red-300',
        warning: 'bg-yellow-500/20 border border-yellow-500/30 text-yellow-300',
        info: 'bg-blue-500/20 border border-blue-500/30 text-blue-300'
      };
      return classes[type] || classes.info;
    }
    
    // Get result icon based on type
    function getResultIcon(type) {
      const icons = {
        success: 'check-circle',
        error: 'times-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
      };
      return icons[type] || icons.info;
    }
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
      showResult('🚀 مرحباً بك في اختبار نظام رفع الملفات', 'info');
      showResult('💡 ابدأ بالنقر على "اختبار الخادم" للتأكد من عمل النظام', 'info');
    });
  </script>
</body>
</html>
