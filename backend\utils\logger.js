/**
 * نظام التسجيل المتقدم
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 */

const winston = require('winston');
const path = require('path');
const fs = require('fs');

// إنشاء مجلد السجلات إذا لم يكن موجوداً
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// تنسيق السجلات باللغة العربية
const arabicFormat = winston.format.combine(
  winston.format.timestamp({
    format: () => {
      return new Date().toLocaleString('ar-SA', {
        timeZone: 'Asia/Kuwait',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let logMessage = `[${timestamp}] ${level.toUpperCase()}: ${message}`;
    
    if (Object.keys(meta).length > 0) {
      logMessage += `\nتفاصيل إضافية: ${JSON.stringify(meta, null, 2)}`;
    }
    
    return logMessage;
  })
);

// تنسيق وحدة التحكم الملون
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let logMessage = `${timestamp} ${level}: ${message}`;
    
    if (Object.keys(meta).length > 0) {
      logMessage += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return logMessage;
  })
);

// إعداد مستويات السجلات المخصصة
const customLevels = {
  levels: {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4
  },
  colors: {
    error: 'red',
    warn: 'yellow',
    info: 'green',
    http: 'magenta',
    debug: 'blue'
  }
};

winston.addColors(customLevels.colors);

// إنشاء مسجل السجلات الرئيسي
const logger = winston.createLogger({
  levels: customLevels.levels,
  level: process.env.LOG_LEVEL || 'info',
  format: arabicFormat,
  defaultMeta: {
    service: 'supplier-contracts-system',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  },
  transports: [
    // سجل الأخطاء
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      )
    }),

    // سجل التحذيرات
    new winston.transports.File({
      filename: path.join(logsDir, 'warn.log'),
      level: 'warn',
      maxsize: 5242880, // 5MB
      maxFiles: 3,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),

    // سجل عام لجميع المستويات
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 10,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      )
    }),

    // سجل طلبات HTTP
    new winston.transports.File({
      filename: path.join(logsDir, 'http.log'),
      level: 'http',
      maxsize: 10485760, // 10MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  ],

  // معالجة الاستثناءات غير المعالجة
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'exceptions.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 3
    })
  ],

  // معالجة الرفض غير المعالج
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'rejections.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 3
    })
  ]
});

// إضافة وحدة التحكم في بيئة التطوير
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    level: 'debug'
  }));
}

// دوال مساعدة للتسجيل المتقدم
const loggerHelpers = {
  /**
   * تسجيل عملية تسجيل دخول المستخدم
   */
  logUserLogin: (userId, username, ip, userAgent) => {
    logger.info('تسجيل دخول مستخدم', {
      userId,
      username,
      ip,
      userAgent,
      action: 'user_login'
    });
  },

  /**
   * تسجيل عملية تسجيل خروج المستخدم
   */
  logUserLogout: (userId, username, ip) => {
    logger.info('تسجيل خروج مستخدم', {
      userId,
      username,
      ip,
      action: 'user_logout'
    });
  },

  /**
   * تسجيل عمليات قاعدة البيانات
   */
  logDatabaseOperation: (operation, table, recordId, userId, changes = {}) => {
    logger.info(`عملية قاعدة بيانات: ${operation}`, {
      operation,
      table,
      recordId,
      userId,
      changes,
      action: 'database_operation'
    });
  },

  /**
   * تسجيل طلبات API
   */
  logApiRequest: (method, url, userId, ip, responseTime, statusCode) => {
    logger.http('طلب API', {
      method,
      url,
      userId,
      ip,
      responseTime,
      statusCode,
      action: 'api_request'
    });
  },

  /**
   * تسجيل أخطاء الأمان
   */
  logSecurityEvent: (event, details, ip, userAgent) => {
    logger.warn(`حدث أمني: ${event}`, {
      event,
      details,
      ip,
      userAgent,
      action: 'security_event'
    });
  },

  /**
   * تسجيل عمليات رفع الملفات
   */
  logFileUpload: (fileName, fileSize, userId, ip) => {
    logger.info('رفع ملف', {
      fileName,
      fileSize,
      userId,
      ip,
      action: 'file_upload'
    });
  },

  /**
   * تسجيل عمليات التصدير
   */
  logExport: (exportType, recordCount, userId, ip) => {
    logger.info(`تصدير بيانات: ${exportType}`, {
      exportType,
      recordCount,
      userId,
      ip,
      action: 'data_export'
    });
  },

  /**
   * تسجيل أخطاء التحقق من صحة البيانات
   */
  logValidationError: (field, value, error, userId, ip) => {
    logger.warn('خطأ في التحقق من صحة البيانات', {
      field,
      value,
      error,
      userId,
      ip,
      action: 'validation_error'
    });
  },

  /**
   * تسجيل عمليات النسخ الاحتياطي
   */
  logBackup: (backupType, size, duration, success) => {
    const level = success ? 'info' : 'error';
    logger[level](`نسخة احتياطية: ${backupType}`, {
      backupType,
      size,
      duration,
      success,
      action: 'backup_operation'
    });
  },

  /**
   * تسجيل أداء النظام
   */
  logPerformance: (operation, duration, memoryUsage, cpuUsage) => {
    logger.debug(`أداء النظام: ${operation}`, {
      operation,
      duration,
      memoryUsage,
      cpuUsage,
      action: 'performance_metric'
    });
  }
};

// دمج الدوال المساعدة مع المسجل الرئيسي
Object.assign(logger, loggerHelpers);

// إنشاء تدفق للاستخدام مع Morgan
logger.stream = {
  write: (message) => {
    logger.http(message.trim());
  }
};

// دالة لتنظيف السجلات القديمة
const cleanOldLogs = () => {
  const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 يوم
  const now = Date.now();

  fs.readdir(logsDir, (err, files) => {
    if (err) {
      logger.error('خطأ في قراءة مجلد السجلات:', err);
      return;
    }

    files.forEach(file => {
      const filePath = path.join(logsDir, file);
      fs.stat(filePath, (err, stats) => {
        if (err) return;

        if (now - stats.mtime.getTime() > maxAge) {
          fs.unlink(filePath, (err) => {
            if (!err) {
              logger.info(`تم حذف السجل القديم: ${file}`);
            }
          });
        }
      });
    });
  });
};

// تنظيف السجلات القديمة كل يوم
setInterval(cleanOldLogs, 24 * 60 * 60 * 1000);

// تصدير المسجل
module.exports = logger;
