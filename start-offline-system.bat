@echo off
chcp 65001 >nul
title نظام إدارة العقود المتطور - العمل بدون إنترنت | محمد مرزوق العقاب

echo.
echo 🎉 ===============================================
echo 👑 نظام إدارة عقود الموردين والإيجارات المتطور
echo 🏢 جمعية المنقف التعاونية
echo.
echo 👨‍💻 تطوير وتنفيذ: محمد مرزوق العقاب
echo 🎯 مطور نظم معلومات محترف ومتخصص
echo.
echo 🔥 النظام يعمل بدون إنترنت - جميع الوظائف نشطة!
echo 🎉 ===============================================
echo.

:menu
echo 🚀 اختر طريقة التشغيل:
echo.
echo 1. 🎮 تشغيل تفاعلي (موصى به)
echo 2. 🔧 تشغيل الخادم المحلي مباشرة
echo 3. 🧪 اختبار النظام
echo 4. 📊 عرض معلومات النظام
echo 5. 🔄 تثبيت التبعيات
echo.
echo 0. خروج
echo.
set /p choice="اختر رقم (0-5): "

if "%choice%"=="1" goto interactive
if "%choice%"=="2" goto direct
if "%choice%"=="3" goto test
if "%choice%"=="4" goto info
if "%choice%"=="5" goto install
if "%choice%"=="0" goto exit
goto invalid

:interactive
echo.
echo 🎮 تشغيل تفاعلي للنظام...
echo.
call launch-professional.bat
goto end

:direct
echo.
echo 🔧 تشغيل الخادم المحلي مباشرة...
echo.
echo 📦 التحقق من التبعيات...
if not exist "node_modules" (
    echo 🔄 تثبيت التبعيات...
    npm install
)

echo.
echo 🎉 ===============================================
echo 🚀 بدء تشغيل النظام المحلي...
echo 🌐 الخادم: http://localhost:3000
echo 📊 قاعدة البيانات: SQLite (محلية)
echo 👨‍💻 المطور: محمد مرزوق العقاب
echo ✅ جميع الوظائف نشطة ومفعلة
echo 🎉 ===============================================
echo.

echo 🔗 الواجهات المتاحة:
echo    👑 الواجهة الاحترافية: http://localhost:3000/professional_ultimate_interface.html
echo    🔧 واجهة الإدارة المتقدمة: http://localhost:3000/advanced_management_interface.html
echo    👥 إدارة الموردين: http://localhost:3000/suppliers_management.html
echo    📋 إدارة العقود: http://localhost:3000/contracts_management.html
echo    👁️ إدارة العيون: http://localhost:3000/eyes_management.html
echo    📊 التقارير: http://localhost:3000/reports_dashboard.html
echo    📁 رفع الملفات: http://localhost:3000/upload_suppliers.html
echo    🔐 تسجيل الدخول: http://localhost:3000/login.html
echo.

echo 💡 اضغط Ctrl+C لإيقاف الخادم
echo.

node backend/api/local-server.js
goto end

:test
echo.
echo 🧪 تشغيل اختبار النظام...
echo.
if exist "quick-test.bat" (
    call quick-test.bat
) else (
    echo ❌ ملف الاختبار غير موجود
    echo 💡 قم بتشغيل: npm run test:interactive
    pause
)
goto menu

:install
echo.
echo 📦 تثبيت التبعيات...
echo.
npm install
echo.
echo ✅ تم تثبيت التبعيات بنجاح
echo.
pause
goto menu

:info
echo.
echo 📊 معلومات النظام المتطور:
echo.
echo 🏢 الشركة: جمعية المنقف التعاونية
echo 👨‍💻 المطور: محمد مرزوق العقاب
echo 🎯 التخصص: مطور نظم معلومات محترف
echo 🌐 الخادم: http://localhost:3000
echo 📁 المجلد: %CD%
echo 🕒 التاريخ: %DATE% %TIME%
echo 📊 قاعدة البيانات: SQLite (محلية)
echo 🔥 حالة الإنترنت: غير مطلوب
echo.
echo 🎨 الواجهات الاحترافية المتطورة:
echo    👑 professional_ultimate_interface.html - الواجهة الاحترافية المتطورة
echo    🔧 advanced_management_interface.html - واجهة الإدارة المتقدمة
echo    👥 suppliers_management.html - إدارة الموردين
echo    📋 contracts_management.html - إدارة العقود
echo    👁️ eyes_management.html - إدارة العيون والطبليات
echo    📊 reports_dashboard.html - لوحة التقارير والإحصائيات
echo    📁 upload_suppliers.html - نظام رفع الملفات
echo    🔐 login.html - تسجيل الدخول
echo.
echo 🔧 الوظائف المتاحة:
echo    ✅ إدارة الموردين (إضافة، تعديل، حذف، بحث)
echo    ✅ إدارة العقود (توريد، إيجار، خدمات)
echo    ✅ نظام العيون (20 طبلية × 4 أقسام)
echo    ✅ حساب الإيجارات (60 د.ك لكل قسم)
echo    ✅ التقارير والإحصائيات التفاعلية
echo    ✅ رفع ملفات Excel/CSV
echo    ✅ نظام المصادقة والصلاحيات
echo    ✅ قاعدة بيانات محلية SQLite
echo.
echo 🎯 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
pause
goto menu

:invalid
echo.
echo ❌ اختيار غير صحيح
echo 💡 يرجى اختيار رقم صحيح (0-5)
timeout /t 2 /nobreak >nul
goto menu

:end
echo.
echo 🎉 تم تشغيل النظام بنجاح!
echo 💡 النظام يعمل الآن بدون إنترنت
echo 🔗 افتح المتصفح وانتقل إلى: http://localhost:3000
echo.
pause
goto menu

:exit
echo.
echo 👋 شكراً لاستخدام نظام إدارة العقود المتطور
echo 👨‍💻 تطوير: محمد مرزوق العقاب
echo 🏢 جمعية المنقف التعاونية
echo 🔥 النظام يعمل بدون إنترنت - جميع الوظائف نشطة!
echo.
timeout /t 3 /nobreak >nul
exit
