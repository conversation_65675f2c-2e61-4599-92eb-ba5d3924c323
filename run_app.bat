@echo off
echo تشغيل نظام إدارة عقود الموردين - جمعية المنقف
echo ================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

echo تم العثور على Python
echo.

REM تثبيت المكتبات المطلوبة
echo تثبيت المكتبات المطلوبة...
pip install ttkbootstrap pillow reportlab openpyxl

echo.
echo تشغيل التطبيق...
echo.

REM تشغيل التطبيق
python main.py

echo.
echo تم إغلاق التطبيق
pause
