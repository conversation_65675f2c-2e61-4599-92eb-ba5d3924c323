#!/usr/bin/env node

/**
 * سكريبت تشغيل النظام الشامل
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');
require('dotenv').config();

// إنشاء واجهة للقراءة من المستخدم
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * عرض رسالة ملونة
 */
function colorLog(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * طرح سؤال للمستخدم
 */
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

/**
 * التحقق من التبعيات
 */
function checkDependencies() {
  colorLog('🔍 التحقق من التبعيات...', 'blue');
  
  if (!fs.existsSync('node_modules')) {
    colorLog('❌ مجلد node_modules غير موجود', 'red');
    return false;
  }
  
  if (!fs.existsSync('.env')) {
    colorLog('❌ ملف .env غير موجود', 'red');
    return false;
  }
  
  colorLog('✅ التبعيات متوفرة', 'green');
  return true;
}

/**
 * تثبيت التبعيات
 */
async function installDependencies() {
  colorLog('📦 تثبيت التبعيات...', 'blue');
  
  return new Promise((resolve, reject) => {
    const npm = spawn('npm', ['install'], { stdio: 'inherit' });
    
    npm.on('close', (code) => {
      if (code === 0) {
        colorLog('✅ تم تثبيت التبعيات بنجاح', 'green');
        resolve();
      } else {
        colorLog('❌ فشل في تثبيت التبعيات', 'red');
        reject(new Error(`npm install failed with code ${code}`));
      }
    });
  });
}

/**
 * إعداد قاعدة البيانات
 */
async function setupDatabase() {
  colorLog('🗄️  إعداد قاعدة البيانات...', 'blue');
  
  return new Promise((resolve, reject) => {
    const setup = spawn('node', ['scripts/setup-database.js'], { stdio: 'inherit' });
    
    setup.on('close', (code) => {
      if (code === 0) {
        colorLog('✅ تم إعداد قاعدة البيانات بنجاح', 'green');
        resolve();
      } else {
        colorLog('❌ فشل في إعداد قاعدة البيانات', 'red');
        reject(new Error(`Database setup failed with code ${code}`));
      }
    });
  });
}

/**
 * تشغيل اختبارات النظام
 */
async function runTests() {
  colorLog('🧪 تشغيل اختبارات النظام...', 'blue');
  
  return new Promise((resolve, reject) => {
    const test = spawn('node', ['scripts/test-system.js'], { stdio: 'inherit' });
    
    test.on('close', (code) => {
      if (code === 0) {
        colorLog('✅ جميع الاختبارات نجحت', 'green');
        resolve();
      } else {
        colorLog('⚠️  بعض الاختبارات فشلت، لكن يمكن المتابعة', 'yellow');
        resolve(); // نتابع حتى لو فشلت بعض الاختبارات
      }
    });
  });
}

/**
 * تشغيل الخادم
 */
async function startServer() {
  colorLog('🚀 تشغيل الخادم...', 'blue');
  
  const server = spawn('node', ['backend/api/server.js'], { 
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'development' }
  });
  
  // معالجة إشارات الإغلاق
  process.on('SIGINT', () => {
    colorLog('\n🛑 إيقاف الخادم...', 'yellow');
    server.kill('SIGINT');
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    colorLog('\n🛑 إيقاف الخادم...', 'yellow');
    server.kill('SIGTERM');
    process.exit(0);
  });
  
  server.on('close', (code) => {
    if (code !== 0) {
      colorLog(`❌ الخادم توقف بخطأ: ${code}`, 'red');
    } else {
      colorLog('✅ تم إيقاف الخادم بنجاح', 'green');
    }
    process.exit(code);
  });
}

/**
 * فتح المتصفح
 */
function openBrowser() {
  const url = `http://localhost:${process.env.PORT || 3000}`;
  
  colorLog(`🌐 فتح المتصفح: ${url}`, 'cyan');
  
  const { exec } = require('child_process');
  const platform = process.platform;
  
  let command;
  if (platform === 'win32') {
    command = `start ${url}`;
  } else if (platform === 'darwin') {
    command = `open ${url}`;
  } else {
    command = `xdg-open ${url}`;
  }
  
  exec(command, (error) => {
    if (error) {
      colorLog(`⚠️  لم يتمكن من فتح المتصفح تلقائياً: ${url}`, 'yellow');
    }
  });
}

/**
 * عرض معلومات النظام
 */
function showSystemInfo() {
  colorLog('\n📋 معلومات النظام:', 'cyan');
  colorLog(`   🏢 الشركة: ${process.env.COMPANY_NAME || 'جمعية المنقف التعاونية'}`, 'white');
  colorLog(`   🌐 الخادم: http://localhost:${process.env.PORT || 3000}`, 'white');
  colorLog(`   🗄️  قاعدة البيانات: ${process.env.DB_NAME || 'supplier_contracts'}`, 'white');
  colorLog(`   📁 مجلد الرفع: ${process.env.UPLOAD_PATH || './backend/uploads'}`, 'white');
  colorLog(`   🔐 البيئة: ${process.env.NODE_ENV || 'development'}`, 'white');
  
  colorLog('\n🔗 الواجهات الاحترافية الجديدة:', 'cyan');
  colorLog(`   👑 الواجهة الاحترافية المتطورة: http://localhost:${process.env.PORT || 3000}/professional_ultimate_interface.html`, 'green');
  colorLog(`   🔧 واجهة الإدارة المتقدمة: http://localhost:${process.env.PORT || 3000}/advanced_management_interface.html`, 'green');
  colorLog(`   📁 نظام رفع الملفات: http://localhost:${process.env.PORT || 3000}/upload_suppliers.html`, 'green');
  colorLog(`   🧪 صفحة الاختبار السريع: http://localhost:${process.env.PORT || 3000}/quick-upload-test.html`, 'green');
  colorLog(`   🚀 صفحة التشغيل: http://localhost:${process.env.PORT || 3000}/launch-interface.html`, 'green');

  colorLog('\n💡 الواجهات الإضافية:', 'cyan');
  colorLog(`   👑 الواجهة المتطورة: http://localhost:${process.env.PORT || 3000}/ultimate_interface.html`, 'white');
  colorLog(`   💼 الواجهة الاحترافية: http://localhost:${process.env.PORT || 3000}/professional_interface.html`, 'white');
  colorLog(`   📋 إدارة العقود المتقدمة: http://localhost:${process.env.PORT || 3000}/advanced_contracts_system.html`, 'white');
  colorLog(`   👥 إدارة الموردين المحسنة: http://localhost:${process.env.PORT || 3000}/enhanced_web_interface.html`, 'white');
  colorLog(`   🖥️  الواجهة البسيطة: http://localhost:${process.env.PORT || 3000}/web_interface.html`, 'white');
  colorLog(`   📊 لوحة التحكم: http://localhost:${process.env.PORT || 3000}/frontend/`, 'white');

  colorLog('\n👨‍💻 تطوير وتنفيذ: محمد مرزوق العقاب', 'magenta');
  
  colorLog('\n⌨️  أوامر مفيدة:', 'cyan');
  colorLog('   Ctrl+C: إيقاف الخادم', 'white');
  colorLog('   npm run dev: تشغيل وضع التطوير', 'white');
  colorLog('   npm test: تشغيل الاختبارات', 'white');
  colorLog('   npm run backup: إنشاء نسخة احتياطية', 'white');
}

/**
 * الدالة الرئيسية
 */
async function main() {
  try {
    colorLog('🚀 مرحباً بك في نظام إدارة عقود الموردين والإيجارات', 'cyan');
    colorLog('جمعية المنقف التعاونية', 'cyan');
    colorLog('================================================', 'cyan');
    
    // التحقق من التبعيات
    if (!checkDependencies()) {
      const install = await askQuestion('هل تريد تثبيت التبعيات؟ (y/n): ');
      if (install.toLowerCase() === 'y') {
        await installDependencies();
      } else {
        colorLog('❌ لا يمكن المتابعة بدون التبعيات', 'red');
        process.exit(1);
      }
    }
    
    // إعداد قاعدة البيانات
    const setupDB = await askQuestion('هل تريد إعداد قاعدة البيانات؟ (y/n): ');
    if (setupDB.toLowerCase() === 'y') {
      await setupDatabase();
    }
    
    // تشغيل الاختبارات
    const runTestsChoice = await askQuestion('هل تريد تشغيل اختبارات النظام؟ (y/n): ');
    if (runTestsChoice.toLowerCase() === 'y') {
      await runTests();
    }
    
    // عرض معلومات النظام
    showSystemInfo();
    
    // فتح المتصفح
    const openBrowserChoice = await askQuestion('\nهل تريد فتح المتصفح تلقائياً؟ (y/n): ');
    if (openBrowserChoice.toLowerCase() === 'y') {
      setTimeout(openBrowser, 2000); // انتظار 2 ثانية لبدء الخادم
    }
    
    colorLog('\n🎉 بدء تشغيل النظام...', 'green');
    
    // إغلاق واجهة القراءة
    rl.close();
    
    // تشغيل الخادم
    await startServer();
    
  } catch (error) {
    colorLog(`❌ خطأ في تشغيل النظام: ${error.message}`, 'red');
    process.exit(1);
  }
}

// تشغيل النظام
if (require.main === module) {
  main();
}

module.exports = { main };
