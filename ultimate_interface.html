<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>نظام إدارة عقود الموردين والإيجارات - جمعية المنقف التعاونية</title>
  
  <!-- Meta Tags -->
  <meta name="description" content="نظام متكامل لإدارة عقود الموردين وإيجار العيون في السوق المركزي">
  <meta name="keywords" content="عقود, موردين, إيجار, جمعية المنقف, الكويت">
  <meta name="author" content="جمعية المنقف التعاونية">
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
  
  <!-- CSS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
  <link rel="stylesheet" href="frontend/assets/css/professional.css">
  
  <!-- Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            'cairo': ['Cairo', 'sans-serif'],
          },
          animation: {
            'float': 'float 6s ease-in-out infinite',
            'pulse-slow': 'pulse 3s ease-in-out infinite',
            'bounce-slow': 'bounce 2s ease-in-out infinite',
          }
        }
      }
    }
  </script>
  
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');
    
    :root {
      --primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      --success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      --warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      --danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      
      --glass: rgba(255, 255, 255, 0.1);
      --glass-border: rgba(255, 255, 255, 0.2);
      --shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Cairo', sans-serif;
      background: var(--primary);
      min-height: 100vh;
      overflow-x: hidden;
      position: relative;
    }
    
    /* Animated Background */
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
      animation: float 20s ease-in-out infinite;
      z-index: -1;
    }
    
    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      33% { transform: translateY(-30px) rotate(120deg); }
      66% { transform: translateY(30px) rotate(240deg); }
    }
    
    /* Glass Morphism */
    .glass {
      background: var(--glass);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      box-shadow: var(--shadow);
    }
    
    .glass-strong {
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(30px);
      -webkit-backdrop-filter: blur(30px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      box-shadow: 
        0 8px 32px 0 rgba(31, 38, 135, 0.37),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
    
    /* Navigation */
    .navbar {
      background: var(--glass);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid var(--glass-border);
      transition: all 0.3s ease;
    }
    
    .navbar.scrolled {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(30px);
    }
    
    .nav-item {
      position: relative;
      transition: all 0.3s ease;
      border-radius: 10px;
      padding: 8px 16px;
    }
    
    .nav-item::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 0;
      height: 3px;
      background: var(--secondary);
      transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    .nav-item:hover::before,
    .nav-item.active::before {
      width: 80%;
    }
    
    .nav-item:hover {
      transform: translateY(-2px);
      background: rgba(255, 255, 255, 0.1);
    }
    
    /* Dropdown */
    .dropdown-menu {
      position: absolute;
      top: 100%;
      right: 0;
      min-width: 280px;
      background: var(--glass-strong);
      border-radius: 20px;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px) scale(0.95);
      transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
      z-index: 1000;
      overflow: hidden;
    }
    
    .dropdown:hover .dropdown-menu {
      opacity: 1;
      visibility: visible;
      transform: translateY(0) scale(1);
    }
    
    .dropdown-item {
      padding: 15px 20px;
      color: white;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 12px;
      transition: all 0.3s ease;
      border-radius: 15px;
      margin: 5px;
      position: relative;
      overflow: hidden;
    }
    
    .dropdown-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }
    
    .dropdown-item:hover::before {
      left: 100%;
    }
    
    .dropdown-item:hover {
      background: rgba(255, 255, 255, 0.15);
      transform: translateX(5px);
    }
    
    /* Cards */
    .card {
      background: var(--glass);
      border-radius: 25px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;
      position: relative;
    }
    
    .card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--secondary);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    .card:hover::before {
      opacity: 1;
    }
    
    .card:hover {
      transform: translateY(-10px) scale(1.02);
      box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.2),
        0 0 50px rgba(102, 126, 234, 0.3);
    }
    
    /* Stats Cards */
    .stats-card {
      background: var(--glass);
      border-radius: 25px;
      padding: 30px;
      text-align: center;
      transition: all 0.4s ease;
      position: relative;
      overflow: hidden;
    }
    
    .stats-card::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: conic-gradient(from 0deg, transparent, rgba(255,255,255,0.1), transparent);
      animation: rotate 4s linear infinite;
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    .stats-card:hover::before {
      opacity: 1;
    }
    
    @keyframes rotate {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .stats-icon {
      width: 70px;
      height: 70px;
      margin: 0 auto 20px;
      background: var(--secondary);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28px;
      color: white;
      position: relative;
      z-index: 1;
    }
    
    /* Buttons */
    .btn {
      position: relative;
      padding: 15px 30px;
      border: none;
      border-radius: 50px;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 10px;
      transition: all 0.3s ease;
      overflow: hidden;
      cursor: pointer;
      font-family: 'Cairo', sans-serif;
    }
    
    .btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s;
    }
    
    .btn:hover::before {
      left: 100%;
    }
    
    .btn-primary {
      background: var(--primary);
      color: white;
    }
    
    .btn-secondary {
      background: var(--secondary);
      color: white;
    }
    
    .btn-success {
      background: var(--success);
      color: white;
    }
    
    .btn:hover {
      transform: translateY(-3px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    }
    
    /* Forms */
    .form-group {
      position: relative;
      margin-bottom: 25px;
    }
    
    .form-input {
      width: 100%;
      padding: 18px 20px;
      border: 2px solid var(--glass-border);
      border-radius: 15px;
      background: var(--glass);
      backdrop-filter: blur(10px);
      color: white;
      font-size: 16px;
      transition: all 0.3s ease;
      font-family: 'Cairo', sans-serif;
    }
    
    .form-input:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 25px rgba(102, 126, 234, 0.4);
      background: rgba(255, 255, 255, 0.15);
    }
    
    .form-input::placeholder {
      color: rgba(255, 255, 255, 0.7);
    }
    
    .form-label {
      position: absolute;
      top: -12px;
      right: 20px;
      background: var(--primary);
      padding: 8px 16px;
      border-radius: 25px;
      font-size: 14px;
      font-weight: 600;
      color: white;
    }
    
    /* Sidebar */
    .sidebar {
      position: fixed;
      top: 0;
      right: -400px;
      width: 400px;
      height: 100vh;
      background: var(--glass-strong);
      border-left: 1px solid var(--glass-border);
      transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
      z-index: 1000;
      overflow-y: auto;
    }
    
    .sidebar.active {
      right: 0;
    }
    
    .sidebar-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
      z-index: 999;
    }
    
    .sidebar-overlay.active {
      opacity: 1;
      visibility: visible;
    }
    
    /* Animations */
    .fade-in {
      animation: fadeIn 0.8s ease-out;
    }
    
    .slide-up {
      animation: slideUp 0.8s ease-out;
    }
    
    .scale-in {
      animation: scaleIn 0.8s ease-out;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    @keyframes slideUp {
      from { transform: translateY(50px); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
    
    @keyframes scaleIn {
      from { transform: scale(0.9); opacity: 0; }
      to { transform: scale(1); opacity: 1; }
    }
    
    /* Scroll Reveal */
    .scroll-reveal {
      opacity: 0;
      transform: translateY(50px);
      transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .scroll-reveal.revealed {
      opacity: 1;
      transform: translateY(0);
    }
    
    /* Section Content */
    .section-content {
      display: none;
      opacity: 0;
      transform: translateY(30px);
      transition: all 0.4s ease;
    }
    
    .section-content.active {
      opacity: 1;
      transform: translateY(0);
    }
    
    /* Loading */
    .loading {
      position: relative;
      overflow: hidden;
    }
    
    .loading::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      animation: loading 1.5s infinite;
    }
    
    @keyframes loading {
      0% { left: -100%; }
      100% { left: 100%; }
    }
    
    /* Responsive */
    @media (max-width: 768px) {
      .sidebar {
        width: 100%;
        right: -100%;
      }
      
      .dropdown-menu {
        position: fixed;
        top: 80px;
        right: 10px;
        left: 10px;
        width: auto;
      }
      
      .stats-card {
        margin-bottom: 20px;
      }
      
      .card:hover {
        transform: translateY(-5px) scale(1.01);
      }
    }
    
    /* Custom Scrollbar */
    ::-webkit-scrollbar {
      width: 10px;
    }
    
    ::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 5px;
    }
    
    ::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 5px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  </style>
</head>
<body class="font-cairo">
  <!-- Navigation -->
  <nav class="navbar fixed top-0 left-0 right-0 z-50 px-6 py-4">
    <div class="max-w-7xl mx-auto flex items-center justify-between">
      <!-- Logo -->
      <div class="flex items-center gap-4">
        <div class="w-14 h-14 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
          <i class="fas fa-building text-white text-2xl"></i>
        </div>
        <div>
          <h1 class="text-white font-bold text-2xl">جمعية المنقف</h1>
          <p class="text-white/80 text-sm">نظام إدارة العقود والإيجارات</p>
        </div>
      </div>

      <!-- Navigation Menu -->
      <div class="hidden lg:flex items-center gap-8">
        <a href="#dashboard" class="nav-item text-white font-medium active">
          <i class="fas fa-home ml-2"></i>
          الرئيسية
        </a>

        <!-- Suppliers Dropdown -->
        <div class="dropdown">
          <a href="#" class="nav-item text-white font-medium flex items-center">
            <i class="fas fa-users ml-2"></i>
            الموردين
            <i class="fas fa-chevron-down mr-2 text-sm"></i>
          </a>
          <div class="dropdown-menu">
            <a href="#suppliers-list" class="dropdown-item">
              <i class="fas fa-list"></i>
              قائمة الموردين
            </a>
            <a href="#add-supplier" class="dropdown-item">
              <i class="fas fa-plus"></i>
              إضافة مورد جديد
            </a>
            <a href="#supplier-categories" class="dropdown-item">
              <i class="fas fa-tags"></i>
              فئات الموردين
            </a>
          </div>
        </div>

        <!-- Contracts Dropdown -->
        <div class="dropdown">
          <a href="#" class="nav-item text-white font-medium flex items-center">
            <i class="fas fa-file-contract ml-2"></i>
            العقود
            <i class="fas fa-chevron-down mr-2 text-sm"></i>
          </a>
          <div class="dropdown-menu">
            <a href="#contracts-list" class="dropdown-item">
              <i class="fas fa-list"></i>
              قائمة العقود
            </a>
            <a href="#new-contract" class="dropdown-item">
              <i class="fas fa-plus"></i>
              عقد جديد
            </a>
            <a href="#expiring-contracts" class="dropdown-item">
              <i class="fas fa-exclamation-triangle"></i>
              العقود المنتهية قريباً
            </a>
          </div>
        </div>

        <!-- Eyes System Dropdown -->
        <div class="dropdown">
          <a href="#" class="nav-item text-white font-medium flex items-center">
            <i class="fas fa-eye ml-2"></i>
            نظام العيون
            <i class="fas fa-chevron-down mr-2 text-sm"></i>
          </a>
          <div class="dropdown-menu">
            <a href="#eyes-management" class="dropdown-item">
              <i class="fas fa-cog"></i>
              إدارة العيون
            </a>
            <a href="#rent-eye" class="dropdown-item">
              <i class="fas fa-plus-circle"></i>
              تأجير عين
            </a>
            <a href="#available-eyes" class="dropdown-item">
              <i class="fas fa-check-circle"></i>
              العيون المتاحة
            </a>
          </div>
        </div>

        <!-- Reports Dropdown -->
        <div class="dropdown">
          <a href="#" class="nav-item text-white font-medium flex items-center">
            <i class="fas fa-chart-line ml-2"></i>
            التقارير
            <i class="fas fa-chevron-down mr-2 text-sm"></i>
          </a>
          <div class="dropdown-menu">
            <a href="#financial-reports" class="dropdown-item">
              <i class="fas fa-dollar-sign"></i>
              التقارير المالية
            </a>
            <a href="#supplier-reports" class="dropdown-item">
              <i class="fas fa-users"></i>
              تقارير الموردين
            </a>
            <a href="#contract-reports" class="dropdown-item">
              <i class="fas fa-file-alt"></i>
              تقارير العقود
            </a>
          </div>
        </div>
      </div>

      <!-- User Actions -->
      <div class="flex items-center gap-4">
        <!-- Search -->
        <div class="hidden md:block relative">
          <input type="text" placeholder="بحث سريع..."
                 class="bg-white/10 border border-white/20 rounded-full px-4 py-2 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/30 w-64">
          <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-white/70"></i>
        </div>

        <!-- Notifications -->
        <div class="relative">
          <button class="text-white p-3 rounded-full hover:bg-white/10 transition-all relative">
            <i class="fas fa-bell text-xl"></i>
            <span class="absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full text-xs flex items-center justify-center animate-pulse">3</span>
          </button>
        </div>

        <!-- User Menu -->
        <div class="dropdown">
          <button class="flex items-center gap-3 text-white p-2 rounded-full hover:bg-white/10 transition-all">
            <img src="https://via.placeholder.com/40" alt="User" class="w-10 h-10 rounded-full border-2 border-white/30">
            <div class="hidden md:block text-right">
              <p class="text-sm font-medium">أحمد محمد</p>
              <p class="text-xs text-white/70">مدير النظام</p>
            </div>
            <i class="fas fa-chevron-down text-sm"></i>
          </button>
          <div class="dropdown-menu">
            <a href="#profile" class="dropdown-item">
              <i class="fas fa-user"></i>
              الملف الشخصي
            </a>
            <a href="#settings" class="dropdown-item">
              <i class="fas fa-cog"></i>
              الإعدادات
            </a>
            <div class="border-t border-white/20 my-2"></div>
            <a href="#logout" class="dropdown-item text-red-400">
              <i class="fas fa-sign-out-alt"></i>
              تسجيل الخروج
            </a>
          </div>
        </div>

        <!-- Mobile Menu Toggle -->
        <button class="lg:hidden text-white p-2" onclick="toggleSidebar()">
          <i class="fas fa-bars text-xl"></i>
        </button>
      </div>
    </div>
  </nav>

  <!-- Mobile Sidebar -->
  <div class="sidebar-overlay" onclick="toggleSidebar()"></div>
  <div class="sidebar" id="mobileSidebar">
    <div class="p-6">
      <div class="flex items-center justify-between mb-8">
        <h2 class="text-white text-2xl font-bold">القائمة الرئيسية</h2>
        <button onclick="toggleSidebar()" class="text-white p-2 hover:bg-white/10 rounded-full">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>

      <!-- Mobile Navigation Items -->
      <div class="space-y-2">
        <a href="#dashboard" class="block text-white p-4 rounded-xl hover:bg-white/10 transition-all">
          <i class="fas fa-home ml-3 text-lg"></i>
          الرئيسية
        </a>
        <a href="#suppliers-list" class="block text-white p-4 rounded-xl hover:bg-white/10 transition-all">
          <i class="fas fa-users ml-3 text-lg"></i>
          الموردين
        </a>
        <a href="#contracts-list" class="block text-white p-4 rounded-xl hover:bg-white/10 transition-all">
          <i class="fas fa-file-contract ml-3 text-lg"></i>
          العقود
        </a>
        <a href="#eyes-management" class="block text-white p-4 rounded-xl hover:bg-white/10 transition-all">
          <i class="fas fa-eye ml-3 text-lg"></i>
          نظام العيون
        </a>
        <a href="#financial-reports" class="block text-white p-4 rounded-xl hover:bg-white/10 transition-all">
          <i class="fas fa-chart-line ml-3 text-lg"></i>
          التقارير
        </a>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <main class="pt-24 px-6 pb-12">
    <div class="max-w-7xl mx-auto">

      <!-- Dashboard Section -->
      <section id="dashboard" class="section-content active">
        <!-- Welcome Header -->
        <div class="text-center mb-12 fade-in">
          <h1 class="text-5xl md:text-7xl font-bold text-white mb-6 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
            مرحباً بك في نظام إدارة العقود
          </h1>
          <p class="text-xl md:text-2xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            نظام متكامل ومتطور لإدارة عقود الموردين وإيجار العيون في السوق المركزي
          </p>
          <div class="mt-8 flex flex-wrap justify-center gap-4">
            <button class="btn btn-primary btn-lg" onclick="showSection('add-supplier')">
              <i class="fas fa-plus"></i>
              إضافة مورد جديد
            </button>
            <button class="btn btn-secondary btn-lg" onclick="showSection('new-contract')">
              <i class="fas fa-file-plus"></i>
              إنشاء عقد جديد
            </button>
          </div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          <div class="stats-card scroll-reveal">
            <div class="stats-icon">
              <i class="fas fa-users"></i>
            </div>
            <h3 class="text-3xl font-bold text-white mb-2">156</h3>
            <p class="text-white/70 text-lg">إجمالي الموردين</p>
            <div class="mt-4">
              <span class="text-green-400 text-sm font-medium">
                <i class="fas fa-arrow-up"></i> +12% هذا الشهر
              </span>
            </div>
          </div>

          <div class="stats-card scroll-reveal" style="animation-delay: 0.1s">
            <div class="stats-icon">
              <i class="fas fa-file-contract"></i>
            </div>
            <h3 class="text-3xl font-bold text-white mb-2">89</h3>
            <p class="text-white/70 text-lg">العقود النشطة</p>
            <div class="mt-4">
              <span class="text-blue-400 text-sm font-medium">
                <i class="fas fa-arrow-up"></i> +8% هذا الشهر
              </span>
            </div>
          </div>

          <div class="stats-card scroll-reveal" style="animation-delay: 0.2s">
            <div class="stats-icon">
              <i class="fas fa-eye"></i>
            </div>
            <h3 class="text-3xl font-bold text-white mb-2">324</h3>
            <p class="text-white/70 text-lg">العيون المؤجرة</p>
            <div class="mt-4">
              <span class="text-yellow-400 text-sm font-medium">
                <i class="fas fa-arrow-up"></i> +15% هذا الشهر
              </span>
            </div>
          </div>

          <div class="stats-card scroll-reveal" style="animation-delay: 0.3s">
            <div class="stats-icon">
              <i class="fas fa-dollar-sign"></i>
            </div>
            <h3 class="text-3xl font-bold text-white mb-2">19,440</h3>
            <p class="text-white/70 text-lg">الإيرادات الشهرية (د.ك)</p>
            <div class="mt-4">
              <span class="text-green-400 text-sm font-medium">
                <i class="fas fa-arrow-up"></i> +22% هذا الشهر
              </span>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <div class="card scroll-reveal hover:scale-105 transition-all duration-300">
            <div class="p-10 text-center">
              <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                <i class="fas fa-plus text-white text-3xl"></i>
              </div>
              <h3 class="text-2xl font-bold text-white mb-4">إضافة مورد جديد</h3>
              <p class="text-white/70 mb-8 leading-relaxed">إضافة مورد جديد إلى النظام مع جميع البيانات والوثائق المطلوبة</p>
              <button class="btn btn-primary w-full" onclick="showSection('add-supplier')">
                <i class="fas fa-plus"></i>
                إضافة الآن
              </button>
            </div>
          </div>

          <div class="card scroll-reveal hover:scale-105 transition-all duration-300" style="animation-delay: 0.1s">
            <div class="p-10 text-center">
              <div class="w-20 h-20 bg-gradient-to-r from-green-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                <i class="fas fa-file-contract text-white text-3xl"></i>
              </div>
              <h3 class="text-2xl font-bold text-white mb-4">إنشاء عقد جديد</h3>
              <p class="text-white/70 mb-8 leading-relaxed">إنشاء عقد توريد أو إيجار جديد مع تحديد جميع الشروط والأحكام</p>
              <button class="btn btn-success w-full" onclick="showSection('new-contract')">
                <i class="fas fa-file-plus"></i>
                إنشاء عقد
              </button>
            </div>
          </div>

          <div class="card scroll-reveal hover:scale-105 transition-all duration-300" style="animation-delay: 0.2s">
            <div class="p-10 text-center">
              <div class="w-20 h-20 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                <i class="fas fa-eye text-white text-3xl"></i>
              </div>
              <h3 class="text-2xl font-bold text-white mb-4">تأجير عين جديدة</h3>
              <p class="text-white/70 mb-8 leading-relaxed">تأجير عين في إحدى الطبليات المتاحة في الأقسام المختلفة</p>
              <button class="btn btn-secondary w-full" onclick="showSection('rent-eye')">
                <i class="fas fa-plus-circle"></i>
                تأجير عين
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </main>

  <!-- JavaScript Libraries -->
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
  <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
  <script src="frontend/assets/js/professional.js"></script>

  <script>
    // Main Application JavaScript

    // Navigation functionality
    function showSection(sectionId) {
      // Hide all sections
      document.querySelectorAll('.section-content').forEach(section => {
        section.classList.remove('active');
        section.style.display = 'none';
      });

      // Show target section
      const targetSection = document.getElementById(sectionId);
      if (targetSection) {
        targetSection.style.display = 'block';
        setTimeout(() => {
          targetSection.classList.add('active');
        }, 10);
      }

      // Update navigation
      document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
      });

      // Close mobile sidebar
      toggleSidebar(false);

      // Scroll to top smoothly
      window.scrollTo({ top: 0, behavior: 'smooth' });

      // Show notification
      if (window.notificationManager) {
        notificationManager.show(`تم الانتقال إلى ${getSectionName(sectionId)}`, 'info', 2000);
      }
    }

    // Get section name in Arabic
    function getSectionName(sectionId) {
      const names = {
        'dashboard': 'الرئيسية',
        'suppliers-list': 'قائمة الموردين',
        'add-supplier': 'إضافة مورد جديد',
        'contracts-list': 'قائمة العقود',
        'new-contract': 'إنشاء عقد جديد',
        'eyes-management': 'إدارة العيون',
        'rent-eye': 'تأجير عين',
        'financial-reports': 'التقارير المالية'
      };
      return names[sectionId] || 'القسم المحدد';
    }

    // Sidebar toggle
    function toggleSidebar(force = null) {
      const sidebar = document.getElementById('mobileSidebar');
      const overlay = document.querySelector('.sidebar-overlay');

      if (force === false) {
        sidebar.classList.remove('active');
        overlay.classList.remove('active');
      } else if (force === true) {
        sidebar.classList.add('active');
        overlay.classList.add('active');
      } else {
        sidebar.classList.toggle('active');
        overlay.classList.toggle('active');
      }
    }

    // Scroll animations
    function revealOnScroll() {
      const reveals = document.querySelectorAll('.scroll-reveal');

      reveals.forEach((element, index) => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;

        if (elementTop < window.innerHeight - elementVisible) {
          setTimeout(() => {
            element.classList.add('revealed');
          }, index * 100);
        }
      });
    }

    // Navbar scroll effect
    function handleNavbarScroll() {
      const navbar = document.querySelector('.navbar');
      if (window.scrollY > 50) {
        navbar.classList.add('scrolled');
      } else {
        navbar.classList.remove('scrolled');
      }
    }

    // Initialize application
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🚀 تم تحميل نظام إدارة عقود الموردين والإيجارات - جمعية المنقف');

      // Show dashboard by default
      showSection('dashboard');

      // Initialize scroll animations
      revealOnScroll();

      // Initialize date pickers
      if (typeof flatpickr !== 'undefined') {
        flatpickr('.date-picker', {
          locale: 'ar',
          dateFormat: 'Y-m-d',
          allowInput: true
        });
      }

      // Add event listeners
      window.addEventListener('scroll', () => {
        revealOnScroll();
        handleNavbarScroll();
      });

      // Navigation click handlers
      document.querySelectorAll('a[href^="#"]').forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          const targetId = this.getAttribute('href').substring(1);
          if (targetId) {
            showSection(targetId);
          }
        });
      });

      // Close dropdowns when clicking outside
      document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
          document.querySelectorAll('.dropdown').forEach(dropdown => {
            dropdown.classList.remove('active');
          });
        }
      });

      // Dropdown hover/click handlers
      document.querySelectorAll('.dropdown').forEach(dropdown => {
        dropdown.addEventListener('mouseenter', function() {
          this.classList.add('active');
        });

        dropdown.addEventListener('mouseleave', function() {
          this.classList.remove('active');
        });
      });

      // Welcome animation
      setTimeout(() => {
        if (window.notificationManager) {
          notificationManager.show('مرحباً بك في نظام إدارة العقود! 🎉', 'success', 4000);
        }
      }, 1000);

      // Auto-save functionality
      let autoSaveInterval;
      function startAutoSave() {
        autoSaveInterval = setInterval(() => {
          // Auto-save logic here
          console.log('💾 حفظ تلقائي...');
        }, 30000); // Every 30 seconds
      }

      // Keyboard shortcuts
      document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + S for save
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
          e.preventDefault();
          console.log('💾 حفظ سريع');
          if (window.notificationManager) {
            notificationManager.show('تم الحفظ بنجاح', 'success', 2000);
          }
        }

        // Escape to close modals/sidebars
        if (e.key === 'Escape') {
          toggleSidebar(false);
          // Close any open modals
          document.querySelectorAll('.modal').forEach(modal => {
            modal.remove();
          });
        }
      });

      // Performance monitoring
      if ('performance' in window) {
        window.addEventListener('load', () => {
          const loadTime = performance.now();
          console.log(`⚡ تم تحميل التطبيق في ${Math.round(loadTime)}ms`);
        });
      }
    });

    // Export functions for global use
    window.showSection = showSection;
    window.toggleSidebar = toggleSidebar;
  </script>
</body>
</html>
