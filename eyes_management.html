<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>إدارة العيون والطبليات - نظام إدارة العقود | محمد مرزوق العقاب</title>

  <!-- CSS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">

  <style>
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap');

    :root {
      --bg-primary: #0a0f1c;
      --bg-secondary: #1a2332;
      --bg-tertiary: #2a3441;
      --text-primary: #ffffff;
      --text-secondary: #f1f5f9;
      --accent-blue: #3b82f6;
      --accent-green: #10b981;
      --accent-red: #ef4444;
      --accent-orange: #f59e0b;
      --border-primary: #475569;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Cairo', sans-serif;
      background: var(--bg-primary);
      color: var(--text-primary);
      min-height: 100vh;
    }

    .glass-card {
      background: rgba(26, 35, 50, 0.8);
      backdrop-filter: blur(20px);
      border: 1px solid var(--border-primary);
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--accent-blue), #2563eb);
      color: var(--text-primary);
      padding: 12px 24px;
      border-radius: 12px;
      border: none;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    }

    .btn-success {
      background: linear-gradient(135deg, var(--accent-green), #059669);
      color: var(--text-primary);
      padding: 12px 24px;
      border-radius: 12px;
      border: none;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .btn-warning {
      background: linear-gradient(135deg, var(--accent-orange), #d97706);
      color: var(--text-primary);
      padding: 12px 24px;
      border-radius: 12px;
      border: none;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .btn-danger {
      background: linear-gradient(135deg, var(--accent-red), #dc2626);
      color: var(--text-primary);
      padding: 12px 24px;
      border-radius: 12px;
      border: none;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .form-input {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid var(--border-primary);
      border-radius: 12px;
      background: var(--bg-tertiary);
      color: var(--text-primary);
      font-family: 'Cairo', sans-serif;
      transition: all 0.3s ease;
    }

    .form-input:focus {
      outline: none;
      border-color: var(--accent-blue);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .table-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
    }

    .table-card {
      background: var(--bg-secondary);
      border: 2px solid var(--border-primary);
      border-radius: 16px;
      padding: 20px;
      transition: all 0.3s ease;
    }

    .table-card:hover {
      border-color: var(--accent-blue);
      transform: translateY(-4px);
      box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
    }

    .section-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
      margin-top: 15px;
    }

    .section-item {
      padding: 12px;
      border-radius: 8px;
      text-align: center;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;
    }

    .section-available {
      background: rgba(16, 185, 129, 0.2);
      color: var(--accent-green);
      border-color: var(--accent-green);
    }

    .section-rented {
      background: rgba(239, 68, 68, 0.2);
      color: var(--accent-red);
      border-color: var(--accent-red);
    }

    .section-item:hover {
      transform: scale(1.05);
    }

    .badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
    }

    .badge-success {
      background: rgba(16, 185, 129, 0.2);
      color: var(--accent-green);
      border: 1px solid var(--accent-green);
    }

    .badge-danger {
      background: rgba(239, 68, 68, 0.2);
      color: var(--accent-red);
      border: 1px solid var(--accent-red);
    }

    .badge-warning {
      background: rgba(245, 158, 11, 0.2);
      color: var(--accent-orange);
      border: 1px solid var(--accent-orange);
    }

    .loading {
      display: none;
      text-align: center;
      padding: 20px;
    }

    .spinner {
      border: 4px solid var(--border-primary);
      border-top: 4px solid var(--accent-blue);
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <!-- Header -->
  <header class="glass-card m-6 p-6">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
          <i class="fas fa-eye text-white text-xl"></i>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-white">إدارة العيون والطبليات</h1>
          <p class="text-slate-300">جمعية المنقف التعاونية - نظام تأجير العيون</p>
        </div>
      </div>
      <div class="text-right">
        <p class="text-white font-semibold">تطوير وتنفيذ</p>
        <p class="text-blue-400 font-bold">محمد مرزوق العقاب</p>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <div class="mx-6 mb-6">
    <!-- Controls -->
    <div class="glass-card p-6 mb-6">
      <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div class="flex gap-4">
          <button class="btn-primary" onclick="showRentModal()">
            <i class="fas fa-plus ml-2"></i>
            تأجير عين جديدة
          </button>
          <button class="btn-success" onclick="refreshData()">
            <i class="fas fa-sync ml-2"></i>
            تحديث البيانات
          </button>
          <button class="btn-warning" onclick="showRentalsReport()">
            <i class="fas fa-chart-bar ml-2"></i>
            تقرير الإيجارات
          </button>
        </div>

        <div class="flex gap-4">
          <select id="categoryFilter" class="form-input w-48" onchange="filterByCategory()">
            <option value="">جميع الفئات</option>
            <option value="بهارات">بهارات</option>
            <option value="استهلاكي">استهلاكي</option>
            <option value="أجبان">أجبان</option>
          </select>
          <select id="statusFilter" class="form-input w-48" onchange="filterByStatus()">
            <option value="">جميع الحالات</option>
            <option value="available">متاحة</option>
            <option value="rented">مؤجرة</option>
          </select>
        </div>
      </div>

      <!-- Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-blue-500/20 p-4 rounded-xl border border-blue-500/30">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-blue-400 text-sm font-medium">إجمالي الطبليات</p>
              <p class="text-white text-2xl font-bold" id="totalTables">20</p>
            </div>
            <i class="fas fa-table text-blue-400 text-2xl"></i>
          </div>
        </div>

        <div class="bg-green-500/20 p-4 rounded-xl border border-green-500/30">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-green-400 text-sm font-medium">العيون المتاحة</p>
              <p class="text-white text-2xl font-bold" id="availableEyes">0</p>
            </div>
            <i class="fas fa-check-circle text-green-400 text-2xl"></i>
          </div>
        </div>

        <div class="bg-red-500/20 p-4 rounded-xl border border-red-500/30">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-red-400 text-sm font-medium">العيون المؤجرة</p>
              <p class="text-white text-2xl font-bold" id="rentedEyes">0</p>
            </div>
            <i class="fas fa-home text-red-400 text-2xl"></i>
          </div>
        </div>

        <div class="bg-orange-500/20 p-4 rounded-xl border border-orange-500/30">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-orange-400 text-sm font-medium">الإيرادات الشهرية</p>
              <p class="text-white text-2xl font-bold" id="monthlyRevenue">0</p>
            </div>
            <i class="fas fa-dollar-sign text-orange-400 text-2xl"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Tables Grid -->
    <div class="glass-card p-6">
      <h2 class="text-xl font-bold text-white mb-4">خريطة الطبليات والعيون</h2>
      <p class="text-slate-300 mb-6">كل طبلية تحتوي على 4 أقسام، كل قسم 60 سم بإيجار 60 د.ك شهرياً</p>

      <div class="loading" id="loading">
        <div class="spinner"></div>
        <p class="text-slate-300 mt-4">جاري تحميل البيانات...</p>
      </div>

      <div class="table-grid" id="tablesGrid">
        <!-- سيتم ملء الطبليات هنا -->
      </div>
    </div>
  </div>

  <!-- Rent Modal -->
  <div id="rentModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
      <div class="glass-card p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-2xl font-bold text-white">تأجير عين جديدة</h3>
          <button onclick="closeRentModal()" class="text-slate-400 hover:text-white">
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>

        <form id="rentForm" onsubmit="saveRental(event)">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-white font-medium mb-2">المورد *</label>
              <select id="rentSupplier" name="supplier_id" required class="form-input">
                <option value="">اختر المورد</option>
                <!-- سيتم ملء الموردين هنا -->
              </select>
            </div>

            <div>
              <label class="block text-white font-medium mb-2">الطبلية والقسم *</label>
              <select id="rentTable" name="table_id" required class="form-input">
                <option value="">اختر الطبلية والقسم</option>
                <!-- سيتم ملء الطبليات المتاحة هنا -->
              </select>
            </div>

            <div>
              <label class="block text-white font-medium mb-2">رقم العين *</label>
              <input type="text" id="rentEyeNumber" name="eye_number" required class="form-input" placeholder="EYE-001">
            </div>

            <div>
              <label class="block text-white font-medium mb-2">تاريخ البداية *</label>
              <input type="date" id="rentStartDate" name="start_date" required class="form-input">
            </div>

            <div>
              <label class="block text-white font-medium mb-2">تاريخ النهاية</label>
              <input type="date" id="rentEndDate" name="end_date" class="form-input">
            </div>

            <div>
              <label class="block text-white font-medium mb-2">الإيجار الشهري (د.ك) *</label>
              <input type="number" id="rentMonthlyRent" name="monthly_rent" value="60" step="0.01" required class="form-input">
            </div>

            <div>
              <label class="block text-white font-medium mb-2">مبلغ التأمين (د.ك)</label>
              <input type="number" id="rentDeposit" name="deposit_amount" value="120" step="0.01" class="form-input">
            </div>

            <div>
              <label class="block text-white font-medium mb-2">الحالة</label>
              <select id="rentStatus" name="status" class="form-input">
                <option value="active">نشط</option>
                <option value="pending">قيد المراجعة</option>
              </select>
            </div>

            <div class="md:col-span-2">
              <label class="block text-white font-medium mb-2">ملاحظات</label>
              <textarea id="rentNotes" name="notes" rows="3" class="form-input" placeholder="ملاحظات إضافية..."></textarea>
            </div>
          </div>

          <div class="flex gap-4 mt-8">
            <button type="submit" class="btn-primary flex-1">
              <i class="fas fa-save ml-2"></i>
              حفظ الإيجار
            </button>
            <button type="button" onclick="closeRentModal()" class="btn-danger">
              <i class="fas fa-times ml-2"></i>
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

  <script>
    // متغيرات عامة
    let tables = [];
    let rentals = [];
    let suppliers = [];

    // تحميل البيانات عند بدء الصفحة
    document.addEventListener('DOMContentLoaded', function() {
      loadTables();
      loadRentals();
      loadSuppliers();
    });

    // تحميل الطبليات
    async function loadTables() {
      try {
        showLoading(true);
        const response = await fetch('/api/tables/available');
        const result = await response.json();

        if (result.success) {
          tables = result.data;
          displayTables();
          updateStatistics();
        } else {
          throw new Error(result.error || 'فشل في تحميل البيانات');
        }
      } catch (error) {
        console.error('Error loading tables:', error);
        // إنشاء بيانات تجريبية للطبليات
        createSampleTables();
      } finally {
        showLoading(false);
      }
    }

    // إنشاء بيانات تجريبية للطبليات
    function createSampleTables() {
      tables = [];
      const categories = ['بهارات', 'استهلاكي', 'أجبان'];

      for (let tableNum = 1; tableNum <= 20; tableNum++) {
        for (let sectionNum = 1; sectionNum <= 4; sectionNum++) {
          const category = categories[Math.floor(Math.random() * categories.length)];
          const isRented = Math.random() > 0.7; // 30% احتمال أن تكون مؤجرة

          tables.push({
            id: (tableNum - 1) * 4 + sectionNum,
            table_number: tableNum,
            section_number: sectionNum,
            section_size: 60,
            category: category,
            rent_price: 60.00,
            status: isRented ? 'rented' : 'available'
          });
        }
      }

      displayTables();
      updateStatistics();
    }

    // تحميل الإيجارات
    async function loadRentals() {
      try {
        const response = await fetch('/api/rentals');
        const result = await response.json();

        if (result.success) {
          rentals = result.data;
        }
      } catch (error) {
        console.error('Error loading rentals:', error);
        rentals = [];
      }
    }

    // تحميل الموردين
    async function loadSuppliers() {
      try {
        const response = await fetch('/api/suppliers');
        const result = await response.json();

        if (result.success) {
          suppliers = result.data;
          populateSupplierSelect();
          populateTableSelect();
        }
      } catch (error) {
        console.error('Error loading suppliers:', error);
        suppliers = [];
      }
    }

    // ملء قائمة الموردين
    function populateSupplierSelect() {
      const select = document.getElementById('rentSupplier');
      select.innerHTML = '<option value="">اختر المورد</option>';

      suppliers.forEach(supplier => {
        const option = document.createElement('option');
        option.value = supplier.id;
        option.textContent = supplier.name;
        select.appendChild(option);
      });
    }

    // ملء قائمة الطبليات المتاحة
    function populateTableSelect() {
      const select = document.getElementById('rentTable');
      select.innerHTML = '<option value="">اختر الطبلية والقسم</option>';

      const availableTables = tables.filter(t => t.status === 'available');
      availableTables.forEach(table => {
        const option = document.createElement('option');
        option.value = table.id;
        option.textContent = `طبلية ${table.table_number} - قسم ${table.section_number} (${table.category})`;
        select.appendChild(option);
      });
    }

    // عرض الطبليات
    function displayTables() {
      const grid = document.getElementById('tablesGrid');
      grid.innerHTML = '';

      // تجميع الطبليات حسب الرقم
      const tableGroups = {};
      tables.forEach(table => {
        if (!tableGroups[table.table_number]) {
          tableGroups[table.table_number] = [];
        }
        tableGroups[table.table_number].push(table);
      });

      // عرض كل طبلية
      Object.keys(tableGroups).sort((a, b) => parseInt(a) - parseInt(b)).forEach(tableNum => {
        const tableSections = tableGroups[tableNum];
        const tableCard = document.createElement('div');
        tableCard.className = 'table-card';

        tableCard.innerHTML = `
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-bold text-white">طبلية ${tableNum}</h3>
            <span class="badge ${getTableStatusBadge(tableSections)}">
              ${getTableStatusText(tableSections)}
            </span>
          </div>

          <div class="section-grid">
            ${tableSections.map(section => `
              <div class="section-item ${section.status === 'available' ? 'section-available' : 'section-rented'}"
                   onclick="handleSectionClick(${section.id}, '${section.status}')">
                <div class="text-sm font-bold">قسم ${section.section_number}</div>
                <div class="text-xs">${section.category}</div>
                <div class="text-xs">${section.status === 'available' ? 'متاح' : 'مؤجر'}</div>
                <div class="text-xs font-bold">${section.rent_price} د.ك</div>
              </div>
            `).join('')}
          </div>

          <div class="mt-4 text-center">
            <p class="text-slate-400 text-sm">
              ${tableSections.filter(s => s.status === 'available').length} متاح من أصل ${tableSections.length}
            </p>
          </div>
        `;

        grid.appendChild(tableCard);
      });
    }

    // التعامل مع النقر على القسم
    function handleSectionClick(sectionId, status) {
      const section = tables.find(t => t.id === sectionId);
      if (!section) return;

      if (status === 'available') {
        // فتح نافذة التأجير
        showRentModal();
        document.getElementById('rentTable').value = sectionId;
      } else {
        // عرض تفاصيل الإيجار
        showRentalDetails(sectionId);
      }
    }

    // عرض تفاصيل الإيجار
    function showRentalDetails(sectionId) {
      const rental = rentals.find(r => r.table_id === sectionId);
      const section = tables.find(t => t.id === sectionId);

      if (!rental || !section) {
        Swal.fire({
          icon: 'info',
          title: 'لا توجد تفاصيل',
          text: 'لا توجد تفاصيل إيجار لهذا القسم',
          confirmButtonText: 'موافق'
        });
        return;
      }

      Swal.fire({
        title: 'تفاصيل الإيجار',
        html: `
          <div class="text-right">
            <p><strong>رقم العين:</strong> ${rental.eye_number}</p>
            <p><strong>المورد:</strong> ${rental.supplier_name || 'غير محدد'}</p>
            <p><strong>الطبلية:</strong> ${section.table_number} - قسم ${section.section_number}</p>
            <p><strong>الفئة:</strong> ${section.category}</p>
            <p><strong>الإيجار الشهري:</strong> ${rental.monthly_rent} د.ك</p>
            <p><strong>تاريخ البداية:</strong> ${formatDate(rental.start_date)}</p>
            <p><strong>تاريخ النهاية:</strong> ${rental.end_date ? formatDate(rental.end_date) : 'غير محدد'}</p>
            <p><strong>الحالة:</strong> ${rental.status === 'active' ? 'نشط' : 'غير نشط'}</p>
          </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'إنهاء الإيجار',
        cancelButtonText: 'إغلاق',
        confirmButtonColor: '#ef4444'
      }).then((result) => {
        if (result.isConfirmed) {
          endRental(rental.id);
        }
      });
    }

    // تحديث الإحصائيات
    function updateStatistics() {
      const totalSections = tables.length;
      const available = tables.filter(t => t.status === 'available').length;
      const rented = tables.filter(t => t.status === 'rented').length;
      const monthlyRevenue = rented * 60; // كل قسم مؤجر × 60 د.ك

      document.getElementById('availableEyes').textContent = available;
      document.getElementById('rentedEyes').textContent = rented;
      document.getElementById('monthlyRevenue').textContent = monthlyRevenue.toLocaleString();
    }

    // تصفية حسب الفئة
    function filterByCategory() {
      const category = document.getElementById('categoryFilter').value;
      // تطبيق التصفية على العرض
      displayTables();
    }

    // تصفية حسب الحالة
    function filterByStatus() {
      const status = document.getElementById('statusFilter').value;
      // تطبيق التصفية على العرض
      displayTables();
    }

    // إظهار نافذة التأجير
    function showRentModal() {
      document.getElementById('rentForm').reset();
      document.getElementById('rentModal').classList.remove('hidden');

      // تعيين التاريخ الحالي كتاريخ بداية
      const today = new Date().toISOString().split('T')[0];
      document.getElementById('rentStartDate').value = today;
    }

    // حفظ الإيجار
    async function saveRental(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const rentalData = Object.fromEntries(formData.entries());

      try {
        const response = await fetch('/api/rentals', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(rentalData)
        });

        const result = await response.json();

        if (result.success) {
          Swal.fire({
            icon: 'success',
            title: 'نجح',
            text: result.message,
            confirmButtonText: 'موافق'
          });

          closeRentModal();
          refreshData();
        } else {
          throw new Error(result.error || 'فشل في حفظ البيانات');
        }
      } catch (error) {
        console.error('Error saving rental:', error);
        Swal.fire({
          icon: 'error',
          title: 'خطأ',
          text: 'فشل في حفظ بيانات الإيجار',
          confirmButtonText: 'موافق'
        });
      }
    }

    // إنهاء الإيجار
    async function endRental(rentalId) {
      try {
        const response = await fetch(`/api/rentals/${rentalId}`, {
          method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
          Swal.fire({
            icon: 'success',
            title: 'تم إنهاء الإيجار',
            text: 'تم إنهاء الإيجار بنجاح',
            confirmButtonText: 'موافق'
          });

          refreshData();
        } else {
          throw new Error(result.error || 'فشل في إنهاء الإيجار');
        }
      } catch (error) {
        console.error('Error ending rental:', error);
        Swal.fire({
          icon: 'error',
          title: 'خطأ',
          text: 'فشل في إنهاء الإيجار',
          confirmButtonText: 'موافق'
        });
      }
    }

    // عرض تقرير الإيجارات
    function showRentalsReport() {
      const totalRented = tables.filter(t => t.status === 'rented').length;
      const totalRevenue = totalRented * 60;
      const occupancyRate = ((totalRented / tables.length) * 100).toFixed(1);

      Swal.fire({
        title: 'تقرير الإيجارات',
        html: `
          <div class="text-right">
            <h4 class="font-bold mb-4">إحصائيات الإيجارات</h4>
            <p><strong>إجمالي العيون:</strong> ${tables.length}</p>
            <p><strong>العيون المؤجرة:</strong> ${totalRented}</p>
            <p><strong>العيون المتاحة:</strong> ${tables.length - totalRented}</p>
            <p><strong>معدل الإشغال:</strong> ${occupancyRate}%</p>
            <p><strong>الإيرادات الشهرية:</strong> ${totalRevenue.toLocaleString()} د.ك</p>
            <p><strong>الإيرادات السنوية المتوقعة:</strong> ${(totalRevenue * 12).toLocaleString()} د.ك</p>
          </div>
        `,
        confirmButtonText: 'إغلاق'
      });
    }

    // دوال مساعدة
    function getTableStatusBadge(sections) {
      const available = sections.filter(s => s.status === 'available').length;
      if (available === sections.length) return 'badge-success';
      if (available === 0) return 'badge-danger';
      return 'badge-warning';
    }

    function getTableStatusText(sections) {
      const available = sections.filter(s => s.status === 'available').length;
      if (available === sections.length) return 'متاحة بالكامل';
      if (available === 0) return 'مؤجرة بالكامل';
      return `${available} متاح`;
    }

    function formatDate(dateString) {
      if (!dateString) return '-';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-GB');
    }

    // إغلاق نافذة التأجير
    function closeRentModal() {
      document.getElementById('rentModal').classList.add('hidden');
    }

    // إظهار/إخفاء التحميل
    function showLoading(show) {
      document.getElementById('loading').style.display = show ? 'block' : 'none';
      document.getElementById('tablesGrid').style.display = show ? 'none' : 'grid';
    }

    // تحديث البيانات
    function refreshData() {
      loadTables();
      loadRentals();
      loadSuppliers();
    }
  </script>
</body>
</html>