/**
 * خادم محلي متكامل لنظام إدارة عقود الموردين والإيجارات
 * جمعية المنقف التعاونية
 * تطوير: محمد مرزوق العقاب
 */

const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const XLSX = require('xlsx');
const csv = require('csv-parser');

// استيراد قاعدة البيانات
const { db, initializeDatabase, insertSampleData, dbOperations } = require('../database/database');

const app = express();
const PORT = process.env.PORT || 3000;
const JWT_SECRET = 'manqaf-coop-secret-key-mohammed-alaqab';

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files
app.use(express.static(path.join(__dirname, '../../')));

// تهيئة قاعدة البيانات عند بدء التشغيل
async function initializeApp() {
  try {
    console.log('🔄 تهيئة قاعدة البيانات...');
    await initializeDatabase();
    await insertSampleData();
    console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
    console.log('👨‍💻 تطوير: محمد مرزوق العقاب');
  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
  }
}

// File upload configuration
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 },
  fileFilter: function (req, file, cb) {
    const allowedTypes = /xlsx|xls|csv/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    if (extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only Excel and CSV files are allowed!'));
    }
  }
});

// Routes الأساسية
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../../launch-interface.html'));
});

// API Routes
app.get('/api/test', (req, res) => {
  res.json({ 
    message: 'API is working!', 
    timestamp: new Date().toISOString(),
    developer: 'محمد مرزوق العقاب',
    database: 'SQLite Local Database',
    status: 'Active',
    version: '2.0.0'
  });
});

// مسارات المصادقة
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    // للتطوير: قبول admin/admin123
    if (username === 'admin' && password === 'admin123') {
      const token = jwt.sign(
        { id: 1, username: 'admin', role: 'admin' },
        JWT_SECRET,
        { expiresIn: '24h' }
      );
      
      return res.json({
        success: true,
        token,
        user: {
          id: 1,
          username: 'admin',
          full_name: 'مدير النظام',
          role: 'admin',
          permissions: 'all'
        }
      });
    }
    
    db.get("SELECT * FROM users WHERE username = ? AND status = 'active'", [username], async (err, user) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      
      if (!user || !await bcrypt.compare(password, user.password)) {
        return res.status(401).json({ error: 'Invalid credentials' });
      }
      
      const token = jwt.sign(
        { id: user.id, username: user.username, role: user.role },
        JWT_SECRET,
        { expiresIn: '24h' }
      );
      
      res.json({
        success: true,
        token,
        user: {
          id: user.id,
          username: user.username,
          full_name: user.full_name,
          role: user.role,
          permissions: user.permissions
        }
      });
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// مسارات الموردين
app.get('/api/suppliers', async (req, res) => {
  try {
    const suppliers = await dbOperations.getAllSuppliers();
    res.json({
      success: true,
      data: suppliers,
      total: suppliers.length,
      developer: 'محمد مرزوق العقاب'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/suppliers/:id', async (req, res) => {
  try {
    const supplier = await dbOperations.getSupplierById(req.params.id);
    if (!supplier) {
      return res.status(404).json({ error: 'Supplier not found' });
    }
    res.json({ success: true, data: supplier });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/suppliers', async (req, res) => {
  try {
    const supplier = await dbOperations.addSupplier(req.body);
    res.json({
      success: true,
      message: 'تم إضافة المورد بنجاح',
      data: supplier
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.put('/api/suppliers/:id', async (req, res) => {
  try {
    const supplier = await dbOperations.updateSupplier(req.params.id, req.body);
    res.json({
      success: true,
      message: 'تم تحديث المورد بنجاح',
      data: supplier
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.delete('/api/suppliers/:id', async (req, res) => {
  try {
    await dbOperations.deleteSupplier(req.params.id);
    res.json({
      success: true,
      message: 'تم حذف المورد بنجاح'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// مسارات العقود
app.get('/api/contracts', async (req, res) => {
  try {
    const contracts = await dbOperations.getAllContracts();
    res.json({
      success: true,
      data: contracts,
      total: contracts.length
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/contracts', async (req, res) => {
  try {
    const contract = await dbOperations.addContract(req.body);
    res.json({
      success: true,
      message: 'تم إضافة العقد بنجاح',
      data: contract
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// مسارات العيون والإيجارات
app.get('/api/rentals', async (req, res) => {
  try {
    const rentals = await dbOperations.getAllRentals();
    res.json({
      success: true,
      data: rentals,
      total: rentals.length
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/tables/available', async (req, res) => {
  try {
    const tables = await dbOperations.getAvailableTables();
    res.json({
      success: true,
      data: tables,
      total: tables.length
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/rentals', async (req, res) => {
  try {
    const rental = await dbOperations.addRental(req.body);
    res.json({
      success: true,
      message: 'تم تأجير العين بنجاح',
      data: rental
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// مسار الإحصائيات
app.get('/api/statistics', async (req, res) => {
  try {
    const stats = await dbOperations.getStatistics();
    res.json({
      success: true,
      data: stats,
      developer: 'محمد مرزوق العقاب'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// رفع الملفات
app.post('/api/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const filePath = req.file.path;
    const fileExtension = path.extname(req.file.originalname).toLowerCase();
    let data = [];

    if (fileExtension === '.csv') {
      // معالجة ملف CSV
      const results = [];
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (row) => results.push(row))
        .on('end', async () => {
          try {
            for (const row of results) {
              if (row.name && row.category) {
                await dbOperations.addSupplier({
                  name: row.name || row['اسم المورد'] || '',
                  category: row.category || row['الفئة'] || '',
                  phone: row.phone || row['الهاتف'] || '',
                  email: row.email || row['البريد الإلكتروني'] || '',
                  address: row.address || row['العنوان'] || '',
                  commercial_record: row.commercial_record || row['السجل التجاري'] || '',
                  tax_number: row.tax_number || row['الرقم الضريبي'] || '',
                  contact_person: row.contact_person || row['الشخص المسؤول'] || '',
                  notes: row.notes || row['ملاحظات'] || ''
                });
              }
            }
            
            res.json({
              success: true,
              message: `تم رفع ${results.length} مورد بنجاح`,
              processed: results.length,
              developer: 'محمد مرزوق العقاب'
            });
          } catch (error) {
            res.status(500).json({ error: error.message });
          }
        });
    } else if (fileExtension === '.xlsx' || fileExtension === '.xls') {
      // معالجة ملف Excel
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      for (const row of jsonData) {
        if (row.name || row['اسم المورد']) {
          await dbOperations.addSupplier({
            name: row.name || row['اسم المورد'] || '',
            category: row.category || row['الفئة'] || '',
            phone: row.phone || row['الهاتف'] || '',
            email: row.email || row['البريد الإلكتروني'] || '',
            address: row.address || row['العنوان'] || '',
            commercial_record: row.commercial_record || row['السجل التجاري'] || '',
            tax_number: row.tax_number || row['الرقم الضريبي'] || '',
            contact_person: row.contact_person || row['الشخص المسؤول'] || '',
            notes: row.notes || row['ملاحظات'] || ''
          });
        }
      }

      res.json({
        success: true,
        message: `تم رفع ${jsonData.length} مورد بنجاح`,
        processed: jsonData.length,
        developer: 'محمد مرزوق العقاب'
      });
    }

    // حذف الملف المؤقت
    fs.unlinkSync(filePath);

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Error:', error);
  res.status(500).json({ 
    error: error.message,
    developer: 'محمد مرزوق العقاب'
  });
});

// Start server
async function startServer() {
  await initializeApp();
  
  app.listen(PORT, () => {
    console.log('🎉 ===============================================');
    console.log('🚀 نظام إدارة العقود المتطور يعمل بنجاح!');
    console.log(`🌐 الخادم: http://localhost:${PORT}`);
    console.log('👨‍💻 تطوير وتنفيذ: محمد مرزوق العقاب');
    console.log('🏢 جمعية المنقف التعاونية');
    console.log('📊 قاعدة البيانات: SQLite (محلية)');
    console.log('✅ جميع الوظائف نشطة ومفعلة');
    console.log('🎉 ===============================================');
  });
}

startServer();

module.exports = app;
