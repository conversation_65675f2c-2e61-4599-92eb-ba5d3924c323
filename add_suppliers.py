#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة موردين إلى النظام
Add Suppliers to the System
"""

import sys
import os

# إضافة مجلد المشروع إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from models.supplier import Supplier

def add_sample_suppliers():
    """إضافة موردين تجريبيين"""
    
    # الاتصال بقاعدة البيانات
    db_manager = DatabaseManager("supplier_contracts.db")
    supplier_model = Supplier(db_manager)
    
    # موردين البهارات
    spice_suppliers = [
        {
            'supplier_name': 'شركة البهارات الذهبية',
            'supplier_category': 'بهارات',
            'contact_person': 'أح<PERSON><PERSON> محمد الخليفي',
            'phone': '99887766',
            'email': '<EMAIL>',
            'address': 'السوق المركزي - المنقف - الكويت',
            'national_id': '123456789',
            'commercial_registration': 'CR001',
            'status': 'نشط',
            'notes': 'متخصص في البهارات العربية والهندية'
        },
        {
            'supplier_name': 'مؤسسة التوابل الشرقية',
            'supplier_category': 'بهارات',
            'contact_person': 'فاطمة علي السالم',
            'phone': '99887767',
            'email': '<EMAIL>',
            'address': 'السوق المركزي - المنقف - الكويت',
            'national_id': '123456790',
            'commercial_registration': 'CR002',
            'status': 'نشط',
            'notes': 'توابل شرقية أصيلة ومستوردة'
        },
        {
            'supplier_name': 'بيت البهارات الكويتي',
            'supplier_category': 'بهارات',
            'contact_person': 'محمد سالم العتيبي',
            'phone': '99887768',
            'email': '<EMAIL>',
            'address': 'السوق المركزي - المنقف - الكويت',
            'national_id': '123456791',
            'commercial_registration': 'CR003',
            'status': 'نشط',
            'notes': 'بهارات كويتية تقليدية'
        }
    ]
    
    # موردين المواد الاستهلاكية
    consumer_suppliers = [
        {
            'supplier_name': 'شركة المواد الاستهلاكية المتحدة',
            'supplier_category': 'استهلاكي',
            'contact_person': 'خالد أحمد الرشيد',
            'phone': '99887769',
            'email': '<EMAIL>',
            'address': 'السوق المركزي - المنقف - الكويت',
            'national_id': '123456792',
            'commercial_registration': 'CR004',
            'status': 'نشط',
            'notes': 'مواد تنظيف ومستلزمات منزلية'
        },
        {
            'supplier_name': 'مؤسسة الخليج للمواد الاستهلاكية',
            'supplier_category': 'استهلاكي',
            'contact_person': 'نورا محمد الصباح',
            'phone': '99887770',
            'email': '<EMAIL>',
            'address': 'السوق المركزي - المنقف - الكويت',
            'national_id': '123456793',
            'commercial_registration': 'CR005',
            'status': 'نشط',
            'notes': 'أدوات مطبخ ومستلزمات شخصية'
        },
        {
            'supplier_name': 'بيت المستلزمات الحديثة',
            'supplier_category': 'استهلاكي',
            'contact_person': 'عبدالله يوسف المطيري',
            'phone': '99887771',
            'email': '<EMAIL>',
            'address': 'السوق المركزي - المنقف - الكويت',
            'national_id': '123456794',
            'commercial_registration': 'CR006',
            'status': 'نشط',
            'notes': 'مستلزمات حديثة ومبتكرة'
        }
    ]
    
    # موردين الأجبان
    cheese_suppliers = [
        {
            'supplier_name': 'شركة الأجبان الطازجة',
            'supplier_category': 'أجبان',
            'contact_person': 'سارة أحمد الزهراني',
            'phone': '99887772',
            'email': '<EMAIL>',
            'address': 'السوق المركزي - المنقف - الكويت',
            'national_id': '123456795',
            'commercial_registration': 'CR007',
            'status': 'نشط',
            'notes': 'أجبان طازجة يومياً من مزارع محلية'
        },
        {
            'supplier_name': 'مؤسسة منتجات الألبان الذهبية',
            'supplier_category': 'أجبان',
            'contact_person': 'حسام محمد العجمي',
            'phone': '99887773',
            'email': '<EMAIL>',
            'address': 'السوق المركزي - المنقف - الكويت',
            'national_id': '123456796',
            'commercial_registration': 'CR008',
            'status': 'نشط',
            'notes': 'منتجات ألبان وأجبان مستوردة وطازجة'
        },
        {
            'supplier_name': 'بيت الجبن الأوروبي',
            'supplier_category': 'أجبان',
            'contact_person': 'ليلى سالم الحربي',
            'phone': '99887774',
            'email': '<EMAIL>',
            'address': 'السوق المركزي - المنقف - الكويت',
            'national_id': '123456797',
            'commercial_registration': 'CR009',
            'status': 'نشط',
            'notes': 'أجبان أوروبية مستوردة عالية الجودة'
        }
    ]
    
    # دمج جميع الموردين
    all_suppliers = spice_suppliers + consumer_suppliers + cheese_suppliers
    
    print("🚀 بدء إضافة الموردين...")
    print("=" * 50)
    
    added_count = 0
    failed_count = 0
    
    for supplier_data in all_suppliers:
        try:
            # التحقق من وجود المورد مسبقاً
            existing = supplier_model.db_manager.fetch_one(
                "SELECT supplier_id FROM suppliers WHERE supplier_name = ?",
                (supplier_data['supplier_name'],)
            )
            
            if existing:
                print(f"⚠️  المورد موجود مسبقاً: {supplier_data['supplier_name']}")
                continue
            
            # إضافة المورد
            supplier_id = supplier_model.create_supplier(supplier_data)
            
            if supplier_id:
                print(f"✅ تم إضافة: {supplier_data['supplier_name']} ({supplier_data['supplier_category']})")
                added_count += 1
            else:
                print(f"❌ فشل في إضافة: {supplier_data['supplier_name']}")
                failed_count += 1
                
        except Exception as e:
            print(f"❌ خطأ في إضافة {supplier_data['supplier_name']}: {str(e)}")
            failed_count += 1
    
    print("\n" + "=" * 50)
    print("📊 ملخص العملية:")
    print(f"✅ تم إضافة: {added_count} مورد")
    print(f"❌ فشل في الإضافة: {failed_count} مورد")
    print(f"📝 إجمالي الموردين المحاولة: {len(all_suppliers)}")
    
    # عرض إحصائيات الموردين حسب القسم
    print("\n📈 إحصائيات الموردين حسب الأقسام:")
    for category in ['بهارات', 'استهلاكي', 'أجبان']:
        suppliers = supplier_model.get_suppliers_by_category(category)
        print(f"   {category}: {len(suppliers)} مورد")
    
    db_manager.disconnect()
    print("\n🎉 انتهت عملية إضافة الموردين!")

def add_custom_supplier():
    """إضافة مورد مخصص"""
    print("📝 إضافة مورد جديد")
    print("=" * 30)
    
    # الاتصال بقاعدة البيانات
    db_manager = DatabaseManager("supplier_contracts.db")
    supplier_model = Supplier(db_manager)
    
    try:
        # جمع البيانات من المستخدم
        supplier_data = {}
        
        supplier_data['supplier_name'] = input("اسم المورد (مطلوب): ").strip()
        if not supplier_data['supplier_name']:
            print("❌ اسم المورد مطلوب!")
            return
        
        print("اختر القسم:")
        print("1. بهارات")
        print("2. استهلاكي") 
        print("3. أجبان")
        category_choice = input("أدخل رقم القسم (1-3): ").strip()
        
        categories = {'1': 'بهارات', '2': 'استهلاكي', '3': 'أجبان'}
        supplier_data['supplier_category'] = categories.get(category_choice, 'بهارات')
        
        supplier_data['contact_person'] = input("الشخص المسؤول: ").strip()
        
        supplier_data['phone'] = input("رقم الهاتف (مطلوب): ").strip()
        if not supplier_data['phone']:
            print("❌ رقم الهاتف مطلوب!")
            return
        
        supplier_data['email'] = input("البريد الإلكتروني: ").strip()
        supplier_data['address'] = input("العنوان: ").strip()
        supplier_data['national_id'] = input("الهوية الوطنية: ").strip()
        supplier_data['commercial_registration'] = input("السجل التجاري: ").strip()
        supplier_data['status'] = 'نشط'
        supplier_data['notes'] = input("ملاحظات: ").strip()
        
        # إضافة المورد
        supplier_id = supplier_model.create_supplier(supplier_data)
        
        if supplier_id:
            print(f"\n✅ تم إضافة المورد بنجاح!")
            print(f"   المعرف: {supplier_id}")
            print(f"   الاسم: {supplier_data['supplier_name']}")
            print(f"   القسم: {supplier_data['supplier_category']}")
        else:
            print("\n❌ فشل في إضافة المورد!")
            
    except Exception as e:
        print(f"\n❌ خطأ: {str(e)}")
    finally:
        db_manager.disconnect()

def view_suppliers():
    """عرض الموردين الموجودين"""
    print("👥 الموردين الموجودين في النظام")
    print("=" * 40)
    
    # الاتصال بقاعدة البيانات
    db_manager = DatabaseManager("supplier_contracts.db")
    supplier_model = Supplier(db_manager)
    
    try:
        for category in ['بهارات', 'استهلاكي', 'أجبان']:
            suppliers = supplier_model.get_suppliers_by_category(category)
            print(f"\n📂 قسم {category} ({len(suppliers)} مورد):")
            
            if suppliers:
                for supplier in suppliers:
                    print(f"   • {supplier['supplier_name']}")
                    print(f"     الهاتف: {supplier['phone']}")
                    print(f"     المسؤول: {supplier['contact_person'] or 'غير محدد'}")
                    print(f"     الحالة: {supplier['status']}")
                    print()
            else:
                print("   لا توجد موردين في هذا القسم")
                
    except Exception as e:
        print(f"❌ خطأ في عرض الموردين: {str(e)}")
    finally:
        db_manager.disconnect()

def main():
    """القائمة الرئيسية"""
    while True:
        print("\n" + "=" * 50)
        print("🏪 نظام إدارة الموردين - جمعية المنقف")
        print("=" * 50)
        print("1. إضافة موردين تجريبيين (9 موردين)")
        print("2. إضافة مورد مخصص")
        print("3. عرض الموردين الموجودين")
        print("4. خروج")
        print("=" * 50)
        
        choice = input("اختر العملية المطلوبة (1-4): ").strip()
        
        if choice == '1':
            add_sample_suppliers()
        elif choice == '2':
            add_custom_supplier()
        elif choice == '3':
            view_suppliers()
        elif choice == '4':
            print("👋 شكراً لاستخدام النظام!")
            break
        else:
            print("❌ اختيار غير صحيح! يرجى اختيار رقم من 1 إلى 4")

if __name__ == "__main__":
    main()
