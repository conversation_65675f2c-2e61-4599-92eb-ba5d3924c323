#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة العيون
Eyes Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import os
from datetime import datetime
from models.rented_eye import RentedEye
from models.rental_item import RentalItem
from models.supplier import Supplier
from models.contract import Contract

class EyesManagementWindow:
    """نافذة إدارة العيون"""
    
    def __init__(self, parent, db_manager):
        """
        تهيئة نافذة إدارة العيون
        
        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
        """
        self.parent = parent
        self.db_manager = db_manager
        self.rented_eye_model = RentedEye(db_manager)
        self.rental_item_model = RentalItem(db_manager)
        self.supplier_model = Supplier(db_manager)
        self.contract_model = Contract(db_manager)
        
        # إنشاء النافذة
        self.window = ttk_bs.Toplevel(parent)
        self.window.title("إدارة العيون - نظام الطبليات المتطور")
        self.window.geometry("1400x900")
        self.window.resizable(True, True)
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_tables()
        self.load_suppliers()
        
        # تعيين النافذة في المقدمة
        self.window.transient(parent)
        self.window.grab_set()
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        # متغيرات تأجير العين
        self.selected_table_id = tk.IntVar()
        self.selected_supplier_id = tk.IntVar()
        self.selected_contract_id = tk.IntVar()
        self.selected_eye_number = tk.IntVar()
        self.rental_price = tk.DoubleVar(value=60.0)
        self.start_date = tk.StringVar()
        self.end_date = tk.StringVar()
        self.rental_notes = tk.StringVar()
        
        # فلاتر
        self.category_filter = tk.StringVar(value="الكل")
        self.table_filter = tk.StringVar(value="الكل")
        
        # قوائم البيانات
        self.tables_list = []
        self.suppliers_list = []
        self.contracts_list = []
    
    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=10)
        main_frame.pack(fill=BOTH, expand=True)
        
        # عنوان النافذة
        title_label = ttk_bs.Label(
            main_frame,
            text="🏪 إدارة العيون - نظام الطبليات المتطور",
            font=("Arial", 16, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 10))

        # وصف النظام
        desc_label = ttk_bs.Label(
            main_frame,
            text="نظام تأجير العيون المتطور: كل طبلية = 4 عيون | العين الواحدة = 60 سم × 60 سم × 60 دينار شهرياً",
            font=("Arial", 11),
            bootstyle=SECONDARY
        )
        desc_label.pack(pady=(0, 20))
        
        # إنشاء التبويبات
        notebook = ttk_bs.Notebook(main_frame)
        notebook.pack(fill=BOTH, expand=True)
        
        # تبويب تأجير العيون
        self.create_rent_eyes_tab(notebook)
        
        # تبويب العيون المؤجرة
        self.create_rented_eyes_tab(notebook)
        
        # تبويب إحصائيات العيون
        self.create_eyes_statistics_tab(notebook)
    
    def create_rent_eyes_tab(self, notebook):
        """إنشاء تبويب تأجير العيون"""
        rent_frame = ttk_bs.Frame(notebook, padding=10)
        notebook.add(rent_frame, text="تأجير العيون")
        
        # إطار المحتوى
        content_frame = ttk_bs.Frame(rent_frame)
        content_frame.pack(fill=BOTH, expand=True)
        
        # الجانب الأيسر - اختيار الطبلية والعيون
        left_frame = ttk_bs.Frame(content_frame)
        left_frame.pack(side=LEFT, fill=BOTH, expand=True, padx=(0, 10))
        
        # فلاتر الطبليات
        filters_frame = ttk_bs.Frame(left_frame)
        filters_frame.pack(fill=X, pady=(0, 10))
        
        ttk_bs.Label(filters_frame, text="القسم:").pack(side=LEFT, padx=(0, 5))
        category_combo = ttk_bs.Combobox(
            filters_frame,
            textvariable=self.category_filter,
            values=["الكل", "بهارات", "استهلاكي", "أجبان"],
            state="readonly",
            width=15
        )
        category_combo.pack(side=LEFT, padx=(0, 20))
        category_combo.bind('<<ComboboxSelected>>', self.on_category_filter_change)
        
        ttk_bs.Button(
            filters_frame,
            text="تحديث الطبليات",
            command=self.load_tables,
            bootstyle=INFO
        ).pack(side=LEFT)
        
        # جدول الطبليات
        self.create_tables_list(left_frame)
        
        # الجانب الأيمن - نموذج التأجير
        right_frame = ttk_bs.Frame(content_frame)
        right_frame.pack(side=RIGHT, fill=Y, padx=(10, 0))
        
        self.create_rental_form(right_frame)
    
    def create_tables_list(self, parent):
        """إنشاء قائمة الطبليات"""
        # عنوان
        ttk_bs.Label(parent, text="الطبليات المتاحة", font=("Arial", 12, "bold")).pack(pady=(0, 10))
        
        # إطار الجدول
        table_frame = ttk_bs.Frame(parent)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ('ID', 'اسم الطبلية', 'الموقع', 'عدد العيون', 'العيون المتاحة', 'السعر/عين')
        self.tables_tree = ttk_bs.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=15
        )
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tables_tree.heading(col, text=col)
            if col == 'ID':
                self.tables_tree.column(col, width=50, anchor=CENTER)
            elif col == 'اسم الطبلية':
                self.tables_tree.column(col, width=150, anchor=W)
            elif col == 'الموقع':
                self.tables_tree.column(col, width=120, anchor=W)
            elif col == 'عدد العيون':
                self.tables_tree.column(col, width=80, anchor=CENTER)
            elif col == 'العيون المتاحة':
                self.tables_tree.column(col, width=100, anchor=CENTER)
            elif col == 'السعر/عين':
                self.tables_tree.column(col, width=80, anchor=E)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.tables_tree.yview)
        self.tables_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.tables_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط الأحداث
        self.tables_tree.bind('<<TreeviewSelect>>', self.on_table_select)
    
    def create_rental_form(self, parent):
        """إنشاء نموذج التأجير"""
        # إطار النموذج
        form_frame = ttk_bs.LabelFrame(parent, text="تأجير عين", padding=15)
        form_frame.pack(fill=BOTH, expand=True)
        
        # اختيار المورد
        ttk_bs.Label(form_frame, text="المورد *:").pack(anchor=W, pady=(0, 5))
        self.supplier_combo = ttk_bs.Combobox(
            form_frame,
            state="readonly",
            width=30
        )
        self.supplier_combo.pack(fill=X, pady=(0, 10))
        self.supplier_combo.bind('<<ComboboxSelected>>', self.on_supplier_select)
        
        # اختيار العقد
        ttk_bs.Label(form_frame, text="العقد *:").pack(anchor=W, pady=(0, 5))
        self.contract_combo = ttk_bs.Combobox(
            form_frame,
            state="readonly",
            width=30
        )
        self.contract_combo.pack(fill=X, pady=(0, 10))
        
        # اختيار رقم العين
        ttk_bs.Label(form_frame, text="رقم العين *:").pack(anchor=W, pady=(0, 5))
        self.eye_combo = ttk_bs.Combobox(
            form_frame,
            state="readonly",
            width=30
        )
        self.eye_combo.pack(fill=X, pady=(0, 10))
        
        # سعر الإيجار
        ttk_bs.Label(form_frame, text="سعر الإيجار (دينار):").pack(anchor=W, pady=(0, 5))
        ttk_bs.Entry(
            form_frame,
            textvariable=self.rental_price,
            width=30
        ).pack(fill=X, pady=(0, 10))
        
        # تاريخ البداية
        ttk_bs.Label(form_frame, text="تاريخ البداية (YYYY-MM-DD):").pack(anchor=W, pady=(0, 5))
        ttk_bs.Entry(
            form_frame,
            textvariable=self.start_date,
            width=30
        ).pack(fill=X, pady=(0, 10))
        
        # تاريخ النهاية
        ttk_bs.Label(form_frame, text="تاريخ النهاية (YYYY-MM-DD):").pack(anchor=W, pady=(0, 5))
        ttk_bs.Entry(
            form_frame,
            textvariable=self.end_date,
            width=30
        ).pack(fill=X, pady=(0, 10))
        
        # ملاحظات
        ttk_bs.Label(form_frame, text="ملاحظات:").pack(anchor=W, pady=(0, 5))
        ttk_bs.Entry(
            form_frame,
            textvariable=self.rental_notes,
            width=30
        ).pack(fill=X, pady=(0, 20))
        
        # أزرار النموذج
        ttk_bs.Button(
            form_frame,
            text="تأجير العين",
            command=self.rent_eye,
            bootstyle=SUCCESS
        ).pack(fill=X, pady=(0, 10))
        
        ttk_bs.Button(
            form_frame,
            text="مسح النموذج",
            command=self.clear_rental_form,
            bootstyle=SECONDARY
        ).pack(fill=X)
    
    def create_rented_eyes_tab(self, notebook):
        """إنشاء تبويب العيون المؤجرة"""
        rented_frame = ttk_bs.Frame(notebook, padding=10)
        notebook.add(rented_frame, text="العيون المؤجرة")
        
        # فلاتر
        filters_frame = ttk_bs.Frame(rented_frame)
        filters_frame.pack(fill=X, pady=(0, 10))
        
        ttk_bs.Label(filters_frame, text="القسم:").pack(side=LEFT, padx=(0, 5))
        rented_category_combo = ttk_bs.Combobox(
            filters_frame,
            values=["الكل", "بهارات", "استهلاكي", "أجبان"],
            state="readonly",
            width=15
        )
        rented_category_combo.pack(side=LEFT, padx=(0, 20))
        rented_category_combo.set("الكل")
        
        ttk_bs.Button(
            filters_frame,
            text="تحديث",
            command=self.load_rented_eyes,
            bootstyle=INFO
        ).pack(side=LEFT)
        
        # جدول العيون المؤجرة
        self.create_rented_eyes_table(rented_frame)
        
        # أزرار العمليات
        buttons_frame = ttk_bs.Frame(rented_frame)
        buttons_frame.pack(fill=X, pady=(10, 0))
        
        ttk_bs.Button(
            buttons_frame,
            text="إنهاء الإيجار",
            command=self.end_rental,
            bootstyle=DANGER
        ).pack(side=LEFT, padx=(0, 10))
        
        ttk_bs.Button(
            buttons_frame,
            text="طباعة التقرير",
            command=self.print_rented_eyes_report,
            bootstyle=INFO
        ).pack(side=LEFT)
    
    def create_rented_eyes_table(self, parent):
        """إنشاء جدول العيون المؤجرة"""
        # إطار الجدول
        table_frame = ttk_bs.Frame(parent)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ('ID', 'الطبلية', 'رقم العين', 'المورد', 'القسم', 'العقد', 'السعر', 'من تاريخ', 'إلى تاريخ', 'الحالة')
        self.rented_eyes_tree = ttk_bs.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=20
        )
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.rented_eyes_tree.heading(col, text=col)
            if col == 'ID':
                self.rented_eyes_tree.column(col, width=50, anchor=CENTER)
            elif col == 'الطبلية':
                self.rented_eyes_tree.column(col, width=120, anchor=W)
            elif col == 'رقم العين':
                self.rented_eyes_tree.column(col, width=70, anchor=CENTER)
            elif col == 'المورد':
                self.rented_eyes_tree.column(col, width=150, anchor=W)
            elif col == 'القسم':
                self.rented_eyes_tree.column(col, width=80, anchor=CENTER)
            elif col == 'العقد':
                self.rented_eyes_tree.column(col, width=100, anchor=CENTER)
            elif col == 'السعر':
                self.rented_eyes_tree.column(col, width=70, anchor=E)
            elif col == 'من تاريخ':
                self.rented_eyes_tree.column(col, width=90, anchor=CENTER)
            elif col == 'إلى تاريخ':
                self.rented_eyes_tree.column(col, width=90, anchor=CENTER)
            elif col == 'الحالة':
                self.rented_eyes_tree.column(col, width=70, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.rented_eyes_tree.yview)
        self.rented_eyes_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.rented_eyes_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
    
    def create_eyes_statistics_tab(self, notebook):
        """إنشاء تبويب إحصائيات العيون"""
        stats_frame = ttk_bs.Frame(notebook, padding=20)
        notebook.add(stats_frame, text="الإحصائيات")
        
        # عنوان
        title_label = ttk_bs.Label(
            stats_frame,
            text="إحصائيات العيون والإيجارات",
            font=("Arial", 14, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 20))
        
        # إطار الإحصائيات
        stats_container = ttk_bs.Frame(stats_frame)
        stats_container.pack(fill=BOTH, expand=True)
        
        # إحصائيات عامة
        general_stats_frame = ttk_bs.LabelFrame(stats_container, text="إحصائيات عامة", padding=15)
        general_stats_frame.pack(fill=X, pady=(0, 20))
        
        self.general_stats_label = ttk_bs.Label(
            general_stats_frame,
            text="سيتم تحميل الإحصائيات...",
            font=("Arial", 12)
        )
        self.general_stats_label.pack()
        
        # إحصائيات الأقسام
        category_stats_frame = ttk_bs.LabelFrame(stats_container, text="إحصائيات الأقسام", padding=15)
        category_stats_frame.pack(fill=X, pady=(0, 20))
        
        self.category_stats_label = ttk_bs.Label(
            category_stats_frame,
            text="سيتم تحميل الإحصائيات...",
            font=("Arial", 12)
        )
        self.category_stats_label.pack()
        
        # زر تحديث الإحصائيات
        ttk_bs.Button(
            stats_frame,
            text="تحديث الإحصائيات",
            command=self.update_statistics,
            bootstyle=INFO
        ).pack(pady=20)
        
        # تحميل الإحصائيات
        self.update_statistics()

    def load_tables(self):
        """تحميل قائمة الطبليات"""
        try:
            # مسح البيانات الحالية
            for item in self.tables_tree.get_children():
                self.tables_tree.delete(item)

            # تحميل الطبليات
            category = self.category_filter.get()
            if category == "الكل":
                tables = self.rental_item_model.get_all_items(item_type="طبلية")
            else:
                # فلترة حسب الموقع (القسم)
                all_tables = self.rental_item_model.get_all_items(item_type="طبلية")
                tables = [t for t in all_tables if category in (t['location'] or '')]

            self.tables_list = tables

            for table in tables:
                # حساب العيون المتاحة
                available_eyes = len(self.rented_eye_model.get_available_eyes(
                    table['item_id'], '2025-01-01', '2025-12-31'
                ))

                self.tables_tree.insert('', 'end', values=(
                    table['item_id'],
                    table['item_name'],
                    table['location'] or '',
                    table['total_eyes'] or 0,
                    available_eyes,
                    f"{table['price_per_eye']:.2f}" if table['price_per_eye'] else "60.00"
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الطبليات:\n{str(e)}")

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            self.suppliers_list = self.supplier_model.get_all_suppliers("نشط")
            supplier_names = [f"{s['supplier_id']} - {s['supplier_name']} ({s['supplier_category']})"
                            for s in self.suppliers_list]
            self.supplier_combo['values'] = supplier_names
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الموردين:\n{str(e)}")

    def load_rented_eyes(self):
        """تحميل العيون المؤجرة"""
        try:
            # مسح البيانات الحالية
            for item in self.rented_eyes_tree.get_children():
                self.rented_eyes_tree.delete(item)

            # تحميل العيون المؤجرة
            rented_eyes = self.rented_eye_model.get_all_active_rented_eyes()

            for eye in rented_eyes:
                self.rented_eyes_tree.insert('', 'end', values=(
                    eye['rented_eye_id'],
                    eye['item_name'],
                    eye['eye_number'],
                    eye['supplier_name'],
                    eye['supplier_category'],
                    eye['contract_number'],
                    f"{eye['rental_price']:.2f}",
                    eye['start_date'],
                    eye['end_date'],
                    eye['status']
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل العيون المؤجرة:\n{str(e)}")

    def on_category_filter_change(self, event):
        """تغيير فلتر القسم"""
        self.load_tables()

    def on_table_select(self, event):
        """عند اختيار طبلية"""
        selected = self.tables_tree.selection()
        if selected:
            item = self.tables_tree.item(selected[0])
            table_id = item['values'][0]
            self.selected_table_id.set(table_id)

            # تحديث العيون المتاحة
            self.update_available_eyes()

    def on_supplier_select(self, event):
        """عند اختيار مورد"""
        supplier_selection = self.supplier_combo.get()
        if supplier_selection:
            supplier_id = int(supplier_selection.split(' - ')[0])
            self.selected_supplier_id.set(supplier_id)

            # تحميل عقود المورد
            self.load_supplier_contracts(supplier_id)

    def load_supplier_contracts(self, supplier_id):
        """تحميل عقود المورد"""
        try:
            contracts = self.supplier_model.get_supplier_contracts(supplier_id)
            active_contracts = [c for c in contracts if c['contract_status'] == 'نشط']

            contract_names = [f"{c['contract_id']} - {c['contract_number']}"
                            for c in active_contracts]
            self.contract_combo['values'] = contract_names
            self.contracts_list = active_contracts

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل عقود المورد:\n{str(e)}")

    def update_available_eyes(self):
        """تحديث العيون المتاحة"""
        table_id = self.selected_table_id.get()
        start_date = self.start_date.get() or '2025-01-01'
        end_date = self.end_date.get() or '2025-12-31'

        if table_id:
            try:
                available_eyes = self.rented_eye_model.get_available_eyes(table_id, start_date, end_date)
                eye_numbers = [str(eye) for eye in available_eyes]
                self.eye_combo['values'] = eye_numbers

                if eye_numbers:
                    self.eye_combo.set(eye_numbers[0])
                else:
                    self.eye_combo.set("")
                    messagebox.showwarning("تحذير", "لا توجد عيون متاحة في هذه الطبلية للفترة المحددة")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحديث العيون المتاحة:\n{str(e)}")

    def rent_eye(self):
        """تأجير عين"""
        try:
            # التحقق من البيانات
            if not self.selected_table_id.get():
                messagebox.showerror("خطأ", "يرجى اختيار طبلية")
                return

            if not self.selected_supplier_id.get():
                messagebox.showerror("خطأ", "يرجى اختيار مورد")
                return

            contract_selection = self.contract_combo.get()
            if not contract_selection:
                messagebox.showerror("خطأ", "يرجى اختيار عقد")
                return

            contract_id = int(contract_selection.split(' - ')[0])

            eye_number = self.eye_combo.get()
            if not eye_number:
                messagebox.showerror("خطأ", "يرجى اختيار رقم العين")
                return

            if not self.start_date.get() or not self.end_date.get():
                messagebox.showerror("خطأ", "يرجى إدخال تواريخ البداية والنهاية")
                return

            # بيانات العين المؤجرة
            eye_data = {
                'item_id': self.selected_table_id.get(),
                'contract_id': contract_id,
                'supplier_id': self.selected_supplier_id.get(),
                'eye_number': int(eye_number),
                'rental_price': self.rental_price.get(),
                'start_date': self.start_date.get(),
                'end_date': self.end_date.get(),
                'status': 'نشط',
                'notes': self.rental_notes.get()
            }

            # تأجير العين
            rented_eye_id = self.rented_eye_model.rent_eye(eye_data)

            if rented_eye_id:
                messagebox.showinfo("نجح", "تم تأجير العين بنجاح!")
                self.clear_rental_form()
                self.load_tables()
                self.load_rented_eyes()
            else:
                messagebox.showerror("خطأ", "فشل في تأجير العين. قد تكون العين مؤجرة بالفعل في هذه الفترة.")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تأجير العين:\n{str(e)}")

    def clear_rental_form(self):
        """مسح نموذج التأجير"""
        self.selected_table_id.set(0)
        self.selected_supplier_id.set(0)
        self.selected_contract_id.set(0)
        self.selected_eye_number.set(0)
        self.rental_price.set(60.0)
        self.start_date.set("")
        self.end_date.set("")
        self.rental_notes.set("")

        self.supplier_combo.set("")
        self.contract_combo.set("")
        self.eye_combo.set("")
        self.contract_combo['values'] = []
        self.eye_combo['values'] = []

    def end_rental(self):
        """إنهاء إيجار عين"""
        selected = self.rented_eyes_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عين لإنهاء إيجارها")
            return

        # تأكيد الإنهاء
        if not messagebox.askyesno("تأكيد الإنهاء", "هل أنت متأكد من إنهاء إيجار هذه العين؟"):
            return

        # الحصول على معرف العين المؤجرة
        item = self.rented_eyes_tree.item(selected[0])
        rented_eye_id = item['values'][0]

        try:
            if self.rented_eye_model.end_eye_rental(rented_eye_id):
                messagebox.showinfo("نجح", "تم إنهاء إيجار العين بنجاح")
                self.load_rented_eyes()
                self.load_tables()
            else:
                messagebox.showerror("خطأ", "فشل في إنهاء إيجار العين")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنهاء إيجار العين:\n{str(e)}")

    def print_rented_eyes_report(self):
        """طباعة تقرير العيون المؤجرة"""
        try:
            from reports.report_generator import ReportGenerator
            report_gen = ReportGenerator(self.db_manager)

            # توليد تقرير مخصص للعيون المؤجرة
            rented_eyes = self.rented_eye_model.get_all_active_rented_eyes()

            if not rented_eyes:
                messagebox.showinfo("معلومات", "لا توجد عيون مؤجرة لطباعة التقرير")
                return

            # إنشاء تقرير Excel
            filename = f"rented_eyes_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            filepath = os.path.join(report_gen.reports_dir, filename)

            # سيتم تطوير هذا لاحقاً
            messagebox.showinfo("تقرير العيون", f"سيتم توليد تقرير العيون المؤجرة:\n{filepath}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير:\n{str(e)}")

    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            # إحصائيات عامة
            total_tables_query = "SELECT COUNT(*) as count FROM rental_items WHERE item_type = 'طبلية'"
            total_tables_result = self.db_manager.fetch_one(total_tables_query)
            total_tables = total_tables_result['count'] if total_tables_result else 0

            total_eyes_query = "SELECT SUM(total_eyes) as total FROM rental_items WHERE item_type = 'طبلية'"
            total_eyes_result = self.db_manager.fetch_one(total_eyes_query)
            total_eyes = total_eyes_result['total'] if total_eyes_result and total_eyes_result['total'] else 0

            rented_eyes_query = "SELECT COUNT(*) as count FROM rented_eyes WHERE status = 'نشط'"
            rented_eyes_result = self.db_manager.fetch_one(rented_eyes_query)
            rented_eyes = rented_eyes_result['count'] if rented_eyes_result else 0

            available_eyes = total_eyes - rented_eyes

            revenue_query = "SELECT SUM(rental_price) as revenue FROM rented_eyes WHERE status = 'نشط'"
            revenue_result = self.db_manager.fetch_one(revenue_query)
            total_revenue = revenue_result['revenue'] if revenue_result and revenue_result['revenue'] else 0

            general_text = f"إجمالي الطبليات: {total_tables}\n"
            general_text += f"إجمالي العيون: {total_eyes}\n"
            general_text += f"العيون المؤجرة: {rented_eyes}\n"
            general_text += f"العيون المتاحة: {available_eyes}\n"
            general_text += f"إجمالي الإيرادات الشهرية: {total_revenue:.2f} د.ك"

            self.general_stats_label.config(text=general_text)

            # إحصائيات الأقسام
            category_summary = self.rented_eye_model.get_rental_summary_by_category()

            category_text = ""
            for category, stats in category_summary.items():
                category_text += f"{category}:\n"
                category_text += f"  العيون المؤجرة: {stats['total_rented_eyes']}\n"
                category_text += f"  الإيرادات: {stats['total_revenue']:.2f} د.ك\n"
                category_text += f"  الموردين النشطين: {stats['active_suppliers']}\n\n"

            if not category_text:
                category_text = "لا توجد إيجارات نشطة حالياً"

            self.category_stats_label.config(text=category_text)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث الإحصائيات:\n{str(e)}")
