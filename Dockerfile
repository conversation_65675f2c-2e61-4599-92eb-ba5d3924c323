# =====================================================
# Dockerfile لنظام إدارة عقود الموردين والإيجارات
# جمعية المنقف التعاونية
# =====================================================

# استخدام Node.js 18 LTS كصورة أساسية
FROM node:18-alpine AS base

# تعيين متغيرات البيئة
ENV NODE_ENV=production
ENV PORT=3000
ENV TZ=Asia/Kuwait

# إنشاء مستخدم غير جذر للأمان
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# تثبيت الأدوات الأساسية
RUN apk add --no-cache \
    dumb-init \
    curl \
    tzdata \
    && rm -rf /var/cache/apk/*

# تعيين المنطقة الزمنية
RUN cp /usr/share/zoneinfo/Asia/Kuwait /etc/localtime
RUN echo "Asia/Kuwait" > /etc/timezone

# =====================================================
# مرحلة بناء التبعيات
# =====================================================
FROM base AS deps

WORKDIR /app

# نسخ ملفات package.json و package-lock.json
COPY package*.json ./

# تثبيت التبعيات
RUN npm ci --only=production && npm cache clean --force

# =====================================================
# مرحلة بناء التطبيق
# =====================================================
FROM base AS builder

WORKDIR /app

# نسخ ملفات package.json
COPY package*.json ./

# تثبيت جميع التبعيات (بما في ذلك dev dependencies)
RUN npm ci

# نسخ الكود المصدري
COPY . .

# بناء التطبيق
RUN npm run build

# =====================================================
# مرحلة الإنتاج النهائية
# =====================================================
FROM base AS runner

WORKDIR /app

# نسخ التبعيات من مرحلة deps
COPY --from=deps --chown=nodejs:nodejs /app/node_modules ./node_modules

# نسخ الكود المبني من مرحلة builder
COPY --from=builder --chown=nodejs:nodejs /app/backend ./backend
COPY --from=builder --chown=nodejs:nodejs /app/frontend ./frontend
COPY --from=builder --chown=nodejs:nodejs /app/package*.json ./

# إنشاء المجلدات المطلوبة
RUN mkdir -p /app/backend/logs \
    && mkdir -p /app/backend/uploads \
    && mkdir -p /app/backend/uploads/contracts \
    && mkdir -p /app/backend/uploads/documents \
    && mkdir -p /app/backend/uploads/images \
    && chown -R nodejs:nodejs /app

# تعيين الصلاحيات
RUN chmod -R 755 /app/backend/uploads
RUN chmod -R 755 /app/backend/logs

# التبديل إلى المستخدم غير الجذر
USER nodejs

# كشف المنفذ
EXPOSE 3000

# فحص صحة التطبيق
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# تشغيل التطبيق باستخدام dumb-init
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "backend/api/server.js"]

# =====================================================
# إعدادات إضافية
# =====================================================

# تسميات الصورة
LABEL maintainer="جمعية المنقف التعاونية <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="نظام إدارة عقود الموردين والإيجارات"
LABEL org.opencontainers.image.title="Supplier Contracts System"
LABEL org.opencontainers.image.description="نظام متكامل لإدارة عقود الموردين والإيجارات"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.vendor="جمعية المنقف التعاونية"
LABEL org.opencontainers.image.licenses="MIT"
LABEL org.opencontainers.image.source="https://github.com/manqaf-coop/supplier-contracts-system"

# متغيرات البيئة الافتراضية
ENV DB_HOST=localhost
ENV DB_PORT=3306
ENV DB_NAME=supplier_contracts
ENV DB_USER=contracts_app
ENV JWT_SECRET=your-secret-key-here
ENV JWT_EXPIRES_IN=24h
ENV LOG_LEVEL=info
