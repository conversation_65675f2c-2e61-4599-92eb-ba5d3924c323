/**
 * خادم مبسط لتشغيل النسخة النهائية
 * جمعية المنقف التعاونية
 * تطوير: محمد مرزوق العقاب
 */

const http = require('http');
const path = require('path');
const fs = require('fs');
const url = require('url');

const PORT = 3000;

// Helper function to get content type
function getContentType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const types = {
    '.html': 'text/html; charset=utf-8',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.pdf': 'application/pdf',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon'
  };
  return types[ext] || 'text/plain';
}

// Create HTTP server
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;

  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

// API لفهرسة ملفات العقود PDF
app.get('/api/contracts/files', (req, res) => {
  try {
    const contractsDir = path.join(__dirname, '../ملفات العقود التوريد');
    
    if (!fs.existsSync(contractsDir)) {
      return res.json({ success: true, data: [], count: 0, message: 'مجلد العقود غير موجود' });
    }
    
    const files = fs.readdirSync(contractsDir)
      .filter(file => file.endsWith('.pdf'))
      .map(file => {
        const filePath = path.join(contractsDir, file);
        const stats = fs.statSync(filePath);
        
        // استخراج معلومات من اسم الملف
        const fileName = file.replace('.pdf', '');
        const parts = fileName.split(') ');
        const contractNumber = parts[0];
        const companyName = parts[1] || fileName;
        
        return {
          id: contractNumber,
          fileName: file,
          filePath: `/contracts-files/${encodeURIComponent(file)}`,
          companyName: companyName,
          contractNumber: contractNumber,
          fileSize: Math.round(stats.size / 1024), // KB
          lastModified: stats.mtime,
          createdAt: stats.birthtime
        };
      })
      .sort((a, b) => parseInt(a.contractNumber) - parseInt(b.contractNumber));
    
    res.json({ 
      success: true, 
      data: files, 
      count: files.length,
      developer: 'محمد مرزوق العقاب'
    });
  } catch (error) {
    console.error('خطأ في قراءة ملفات العقود:', error);
    res.status(500).json({ success: false, error: 'خطأ في قراءة ملفات العقود' });
  }
});

// API لتحميل ملفات العقود PDF
app.get('/contracts-files/:filename', (req, res) => {
  try {
    const filename = decodeURIComponent(req.params.filename);
    const filePath = path.join(__dirname, '../ملفات العقود التوريد', filename);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ success: false, error: 'الملف غير موجود' });
    }
    
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `inline; filename="${encodeURIComponent(filename)}"`);
    res.sendFile(filePath);
  } catch (error) {
    console.error('خطأ في تحميل الملف:', error);
    res.status(500).json({ success: false, error: 'خطأ في تحميل الملف' });
  }
});

// API للموردين (بيانات تجريبية)
app.get('/api/suppliers', (req, res) => {
  const suppliers = [
    { id: 1, name: 'شركة البهارات الذهبية', category: 'بهارات', phone: '99887766' },
    { id: 2, name: 'مؤسسة المواد الاستهلاكية', category: 'استهلاكي', phone: '99776655' },
    { id: 3, name: 'شركة الأجبان الطازجة', category: 'أجبان', phone: '99665544' }
  ];
  res.json({ success: true, data: suppliers });
});

// API للعقود (بيانات تجريبية)
app.get('/api/contracts', (req, res) => {
  const contracts = [
    { id: 1, supplier_id: 1, type: 'توريد', status: 'active', start_date: '2024-01-01', end_date: '2024-12-31' },
    { id: 2, supplier_id: 2, type: 'إيجار', status: 'active', start_date: '2024-01-01', end_date: '2024-12-31' }
  ];
  res.json({ success: true, data: contracts });
});

// API للعيون (بيانات تجريبية)
app.get('/api/eyes', (req, res) => {
  const eyes = [
    { id: 1, table_number: 1, section: 1, status: 'rented', monthly_rent: 60 },
    { id: 2, table_number: 1, section: 2, status: 'rented', monthly_rent: 60 },
    { id: 3, table_number: 1, section: 3, status: 'available', monthly_rent: 60 }
  ];
  res.json({ success: true, data: eyes });
});

// استيراد الموردين (محاكاة)
app.post('/api/import/suppliers', (req, res) => {
  res.json({
    success: true,
    message: 'تم استيراد الموردين بنجاح',
    processed: 2201,
    errors: 0,
    total: 2201,
    developer: 'محمد مرزوق العقاب'
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error('خطأ في الخادم:', err);
  res.status(500).json({ 
    success: false, 
    error: 'خطأ داخلي في الخادم',
    developer: 'محمد مرزوق العقاب'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ 
    success: false, 
    error: 'الصفحة غير موجودة',
    developer: 'محمد مرزوق العقاب'
  });
});

// Start server
app.listen(PORT, () => {
  console.log('🎉 ===============================================');
  console.log('👑 خادم النسخة النهائية المحدثة');
  console.log('🏢 جمعية المنقف التعاونية');
  console.log('👨‍💻 تطوير: محمد مرزوق العقاب');
  console.log('📅 التقويم: ميلادي');
  console.log('📞 الهاتف: +965-23710272');
  console.log('📧 البريد: <EMAIL>');
  console.log('📍 العنوان: المنقف - قطعة 4 شارع فهد الهملان');
  console.log('🎉 ===============================================');
  console.log('');
  console.log(`🌐 الخادم يعمل على: http://localhost:${PORT}`);
  console.log('');
  console.log('🎨 الواجهات المتاحة:');
  console.log('   👑 /edara/professional_ultimate_interface_final.html');
  console.log('   📁 /edara/contracts_files_manager_final.html');
  console.log('   📊 /professional_ultimate_interface.html');
  console.log('   🔧 /advanced_management_interface.html');
  console.log('');
  console.log('✅ النظام جاهز للاستخدام!');
  console.log('💡 اضغط Ctrl+C لإيقاف الخادم');
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 إيقاف الخادم...');
  console.log('🎉 شكراً لاستخدام النظام المتطور!');
  console.log('👨‍💻 تطوير: محمد مرزوق العقاب');
  process.exit(0);
});

module.exports = app;
