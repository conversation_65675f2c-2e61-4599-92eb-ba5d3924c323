/**
 * خادم Node.js الرئيسي لنظام إدارة عقود الموردين والإيجارات
 * جمعية المنقف التعاونية
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// استيراد الوحدات المخصصة
const database = require('../utils/database');
const logger = require('../utils/logger');
const authMiddleware = require('../middleware/auth');

// استيراد المسارات
const authRoutes = require('../routes/auth');
const contractRoutes = require('../routes/contracts');
const supplierRoutes = require('../routes/suppliers');
const reportRoutes = require('../routes/reports');
const uploadRoutes = require('../routes/upload');

// إنشاء تطبيق Express
const app = express();

// إعدادات البيئة
const PORT = process.env.PORT || 3000;
const NODE_ENV = process.env.NODE_ENV || 'development';
const API_VERSION = process.env.API_VERSION || 'v1';

// =====================================================
// إعداد الوسطاء (Middlewares)
// =====================================================

// أمان التطبيق
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://fonts.googleapis.com"],
      scriptSrc: ["'self'", "https://cdn.jsdelivr.net", "https://cdnjs.cloudflare.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"]
    }
  },
  crossOriginEmbedderPolicy: false
}));

// ضغط الاستجابات
app.use(compression());

// تسجيل الطلبات
if (NODE_ENV === 'production') {
  app.use(morgan('combined', { stream: logger.stream }));
} else {
  app.use(morgan('dev'));
}

// إعداد CORS
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:8080',
      'https://contracts.manqaf-coop.com',
      'https://manqaf-coop.com'
    ];
    
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('غير مسموح بواسطة CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));

// تحديد معدل الطلبات
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: NODE_ENV === 'production' ? 100 : 1000, // حد الطلبات
  message: {
    error: 'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقاً',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use('/api/', limiter);

// تحليل البيانات
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// خدمة الملفات الثابتة
app.use('/static', express.static(path.join(__dirname, '../../frontend')));
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// =====================================================
// إعداد المسارات
// =====================================================

// الصفحة الرئيسية
app.get('/', (req, res) => {
  res.json({
    message: 'مرحباً بك في نظام إدارة عقود الموردين والإيجارات',
    version: '1.0.0',
    organization: 'جمعية المنقف التعاونية',
    api_version: API_VERSION,
    status: 'running',
    timestamp: new Date().toISOString()
  });
});

// فحص حالة الخادم
app.get('/api/health', async (req, res) => {
  try {
    // فحص اتصال قاعدة البيانات
    await database.query('SELECT 1');
    
    res.json({
      status: 'healthy',
      database: 'connected',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      database: 'disconnected',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// مسارات API
app.use(`/api/${API_VERSION}/auth`, authRoutes);
app.use(`/api/${API_VERSION}/contracts`, authMiddleware, contractRoutes);
app.use(`/api/${API_VERSION}/suppliers`, authMiddleware, supplierRoutes);
app.use(`/api/${API_VERSION}/reports`, authMiddleware, reportRoutes);
app.use(`/api/${API_VERSION}/uploads`, authMiddleware, uploadRoutes);

// مسار لخدمة الواجهة الأمامية
app.get('*', (req, res) => {
  const indexPath = path.join(__dirname, '../../frontend/index.html');
  if (fs.existsSync(indexPath)) {
    res.sendFile(indexPath);
  } else {
    res.status(404).json({
      error: 'الصفحة غير موجودة',
      message: 'الملف المطلوب غير متوفر'
    });
  }
});

// =====================================================
// معالجة الأخطاء
// =====================================================

// معالج الأخطاء 404
app.use((req, res) => {
  res.status(404).json({
    error: 'المسار غير موجود',
    message: `المسار ${req.originalUrl} غير متوفر`,
    timestamp: new Date().toISOString()
  });
});

// معالج الأخطاء العام
app.use((error, req, res, next) => {
  logger.error('Server Error:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // عدم إظهار تفاصيل الخطأ في الإنتاج
  const isDevelopment = NODE_ENV === 'development';
  
  res.status(error.status || 500).json({
    error: 'خطأ في الخادم',
    message: isDevelopment ? error.message : 'حدث خطأ غير متوقع',
    ...(isDevelopment && { stack: error.stack }),
    timestamp: new Date().toISOString()
  });
});

// =====================================================
// بدء تشغيل الخادم
// =====================================================

async function startServer() {
  try {
    // اختبار اتصال قاعدة البيانات
    await database.testConnection();
    logger.info('تم الاتصال بقاعدة البيانات بنجاح');

    // إنشاء مجلدات الرفع إذا لم تكن موجودة
    const uploadDirs = [
      path.join(__dirname, '../uploads'),
      path.join(__dirname, '../uploads/contracts'),
      path.join(__dirname, '../uploads/documents'),
      path.join(__dirname, '../uploads/images')
    ];

    uploadDirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        logger.info(`تم إنشاء مجلد: ${dir}`);
      }
    });

    // بدء تشغيل الخادم
    const server = app.listen(PORT, () => {
      logger.info(`🚀 الخادم يعمل على المنفذ ${PORT}`);
      logger.info(`🌍 البيئة: ${NODE_ENV}`);
      logger.info(`📡 API Version: ${API_VERSION}`);
      logger.info(`🔗 الرابط: http://localhost:${PORT}`);
      
      if (NODE_ENV === 'development') {
        logger.info(`📊 لوحة التحكم: http://localhost:${PORT}/static/pages/dashboard.html`);
        logger.info(`📋 إدارة العقود: http://localhost:${PORT}/static/pages/contracts.html`);
      }
    });

    // معالجة إغلاق الخادم بأمان
    const gracefulShutdown = (signal) => {
      logger.info(`تم استلام إشارة ${signal}، جاري إغلاق الخادم...`);
      
      server.close(async () => {
        logger.info('تم إغلاق الخادم');
        
        try {
          await database.close();
          logger.info('تم إغلاق اتصال قاعدة البيانات');
        } catch (error) {
          logger.error('خطأ في إغلاق قاعدة البيانات:', error);
        }
        
        process.exit(0);
      });

      // إجبار الإغلاق بعد 10 ثوان
      setTimeout(() => {
        logger.error('إجبار إغلاق الخادم');
        process.exit(1);
      }, 10000);
    };

    // الاستماع لإشارات الإغلاق
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // معالجة الأخطاء غير المعالجة
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      gracefulShutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      gracefulShutdown('unhandledRejection');
    });

  } catch (error) {
    logger.error('فشل في بدء تشغيل الخادم:', error);
    process.exit(1);
  }
}

// بدء التشغيل
if (require.main === module) {
  startServer();
}

module.exports = app;
