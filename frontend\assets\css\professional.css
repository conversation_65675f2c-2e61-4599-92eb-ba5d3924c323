/**
 * Professional Interface Styles
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 */

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');

/* Advanced Animations */
@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromBottom {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Enhanced Glass Effects */
.glass-ultra {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px 0 rgba(31, 38, 135, 0.37),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-card {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px 0 rgba(31, 38, 135, 0.37),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.08) 100%
  );
  transform: translateY(-5px);
  box-shadow: 
    0 15px 45px 0 rgba(31, 38, 135, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Advanced Button Styles */
.btn-gradient {
  background: linear-gradient(45deg, #667eea, #764ba2);
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
  position: relative;
  overflow: hidden;
}

.btn-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s;
}

.btn-gradient:hover::before {
  left: 100%;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.btn-neon {
  background: transparent;
  border: 2px solid #667eea;
  color: #667eea;
  text-shadow: 0 0 10px #667eea;
  box-shadow: 
    0 0 20px rgba(102, 126, 234, 0.3),
    inset 0 0 20px rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.btn-neon:hover {
  background: rgba(102, 126, 234, 0.1);
  color: white;
  text-shadow: 0 0 20px #667eea;
  box-shadow: 
    0 0 30px rgba(102, 126, 234, 0.6),
    inset 0 0 30px rgba(102, 126, 234, 0.2);
  transform: scale(1.05);
}

/* Enhanced Form Styles */
.form-floating {
  position: relative;
}

.form-floating input,
.form-floating select,
.form-floating textarea {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 20px 15px 10px;
  color: white;
  font-size: 16px;
  transition: all 0.3s ease;
}

.form-floating label {
  position: absolute;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  pointer-events: none;
  transition: all 0.3s ease;
  background: transparent;
  padding: 0 5px;
}

.form-floating input:focus,
.form-floating input:not(:placeholder-shown),
.form-floating select:focus,
.form-floating textarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

.form-floating input:focus + label,
.form-floating input:not(:placeholder-shown) + label,
.form-floating select:focus + label,
.form-floating textarea:focus + label {
  top: 0;
  font-size: 12px;
  color: #667eea;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Advanced Table Styles */
.table-modern {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
}

.table-modern thead {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));
}

.table-modern th {
  padding: 20px;
  font-weight: 600;
  color: white;
  border: none;
  position: relative;
}

.table-modern th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.table-modern td {
  padding: 15px 20px;
  border: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  transition: all 0.3s ease;
}

.table-modern tbody tr {
  transition: all 0.3s ease;
}

.table-modern tbody tr:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.01);
}

/* Enhanced Cards */
.card-hover-effect {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card-hover-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.card-hover-effect:hover::before {
  transform: translateX(100%);
}

.card-hover-effect:hover {
  transform: translateY(-10px) rotateX(5deg);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 50px rgba(102, 126, 234, 0.2);
}

/* Loading Animations */
.skeleton {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.1) 25%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* Progress Bars */
.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: progressShine 2s infinite;
}

@keyframes progressShine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Status Badges */
.badge-animated {
  position: relative;
  overflow: hidden;
}

.badge-animated::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s;
}

.badge-animated:hover::before {
  left: 100%;
}

/* Notification Styles */
.notification-slide {
  animation: slideInFromRight 0.5s ease-out;
}

.notification-slide.removing {
  animation: slideOutToRight 0.3s ease-in;
}

@keyframes slideOutToRight {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .card-hover-effect:hover {
    transform: translateY(-5px);
  }
  
  .table-modern {
    font-size: 14px;
  }
  
  .table-modern th,
  .table-modern td {
    padding: 10px;
  }
  
  .btn {
    padding: 10px 20px;
    font-size: 14px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .glass-card {
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.3) 0%,
      rgba(0, 0, 0, 0.1) 100%
    );
  }
}

/* Print Styles */
@media print {
  .navbar,
  .sidebar,
  .btn,
  .dropdown {
    display: none !important;
  }
  
  .card {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}
