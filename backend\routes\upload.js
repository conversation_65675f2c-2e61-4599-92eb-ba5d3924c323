/**
 * Upload Routes - نظام رفع الملفات
 * جمعية المنقف التعاونية
 */

const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const XLSX = require('xlsx');
const csv = require('csv-parser');
const { body, validationResult } = require('express-validator');
const router = express.Router();

// إعداد Multer لرفع الملفات
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(__dirname, '../uploads/suppliers');
    // إنشاء المجلد إذا لم يكن موجوداً
    fs.mkdir(uploadPath, { recursive: true })
      .then(() => cb(null, uploadPath))
      .catch(err => cb(err));
  },
  filename: function (req, file, cb) {
    // إنشاء اسم ملف فريد
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `suppliers-${uniqueSuffix}${ext}`);
  }
});

// فلتر أنواع الملفات المسموحة
const fileFilter = (req, file, cb) => {
  const allowedTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel', // .xls
    'text/csv' // .csv
  ];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('نوع الملف غير مدعوم. يرجى رفع ملف Excel أو CSV'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 5 // حد أقصى 5 ملفات
  }
});

/**
 * رفع ملفات الموردين
 * POST /api/v1/upload/suppliers
 */
router.post('/suppliers', upload.array('files', 5), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'لم يتم رفع أي ملفات'
      });
    }

    const results = [];

    // معالجة كل ملف
    for (const file of req.files) {
      try {
        const result = await processSupplierFile(file);
        results.push(result);
      } catch (error) {
        results.push({
          fileName: file.originalname,
          success: false,
          error: error.message,
          total: 0,
          processed: 0,
          errors: [],
          duplicates: []
        });
      }
    }

    // حساب الإحصائيات الإجمالية
    const summary = {
      totalFiles: results.length,
      totalRecords: results.reduce((sum, r) => sum + r.total, 0),
      totalProcessed: results.reduce((sum, r) => sum + r.processed, 0),
      totalErrors: results.reduce((sum, r) => sum + r.errors.length, 0),
      totalDuplicates: results.reduce((sum, r) => sum + r.duplicates.length, 0)
    };

    res.json({
      success: true,
      message: 'تم معالجة الملفات بنجاح',
      summary: summary,
      results: results
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء رفع الملفات',
      error: error.message
    });
  }
});

/**
 * معالجة ملف الموردين
 */
async function processSupplierFile(file) {
  const result = {
    fileName: file.originalname,
    filePath: file.path,
    total: 0,
    processed: 0,
    errors: [],
    duplicates: [],
    success: true
  };

  try {
    // قراءة البيانات من الملف
    let data;
    if (file.mimetype.includes('csv')) {
      data = await readCSVFile(file.path);
    } else {
      data = await readExcelFile(file.path);
    }

    result.total = data.length;

    // معالجة كل صف
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowNumber = i + 2; // +2 للعنوان والفهرس الصفري

      try {
        // التحقق من صحة البيانات
        const validation = validateSupplierData(row);
        if (!validation.isValid) {
          result.errors.push({
            row: rowNumber,
            data: row,
            errors: validation.errors
          });
          continue;
        }

        // التحقق من التكرار
        const isDuplicate = await checkDuplicateSupplier(row);
        if (isDuplicate) {
          result.duplicates.push({
            row: rowNumber,
            data: row,
            reason: 'مورد موجود بالفعل'
          });
          continue;
        }

        // إدراج المورد في قاعدة البيانات
        await insertSupplier(row);
        result.processed++;

      } catch (error) {
        result.errors.push({
          row: rowNumber,
          data: row,
          errors: [error.message]
        });
      }
    }

    // حذف الملف المؤقت
    await fs.unlink(file.path);

  } catch (error) {
    result.success = false;
    result.error = error.message;
  }

  return result;
}

/**
 * قراءة ملف CSV
 */
function readCSVFile(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

/**
 * قراءة ملف Excel
 */
async function readExcelFile(filePath) {
  try {
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);
    return data;
  } catch (error) {
    throw new Error('فشل في قراءة ملف Excel: ' + error.message);
  }
}

/**
 * التحقق من صحة بيانات المورد
 */
function validateSupplierData(data) {
  const errors = [];
  const requiredFields = ['اسم المورد', 'الفئة', 'رقم الهاتف'];
  const validCategories = ['بهارات', 'استهلاكي', 'أجبان'];

  // التحقق من الحقول المطلوبة
  requiredFields.forEach(field => {
    if (!data[field] || data[field].toString().trim() === '') {
      errors.push(`الحقل "${field}" مطلوب`);
    }
  });

  // التحقق من الفئة
  if (data['الفئة'] && !validCategories.includes(data['الفئة'].trim())) {
    errors.push(`الفئة "${data['الفئة']}" غير صحيحة. الفئات المتاحة: ${validCategories.join(', ')}`);
  }

  // التحقق من رقم الهاتف
  if (data['رقم الهاتف']) {
    const phone = data['رقم الهاتف'].toString().trim();
    if (!/^[0-9+\-\s()]+$/.test(phone)) {
      errors.push('رقم الهاتف غير صحيح');
    }
  }

  // التحقق من البريد الإلكتروني
  if (data['البريد الإلكتروني']) {
    const email = data['البريد الإلكتروني'].toString().trim();
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.push('البريد الإلكتروني غير صحيح');
    }
  }

  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

/**
 * التحقق من تكرار المورد
 */
async function checkDuplicateSupplier(data) {
  try {
    const db = require('../database/connection');
    
    const query = `
      SELECT id FROM suppliers 
      WHERE name = ? OR phone = ?
    `;
    
    const [rows] = await db.execute(query, [
      data['اسم المورد'].trim(),
      data['رقم الهاتف'].toString().trim()
    ]);
    
    return rows.length > 0;
  } catch (error) {
    console.error('Error checking duplicate:', error);
    return false;
  }
}

/**
 * إدراج مورد جديد
 */
async function insertSupplier(data) {
  try {
    const db = require('../database/connection');
    
    const query = `
      INSERT INTO suppliers (
        name, category, phone, email, address, contact_person, 
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;
    
    const values = [
      data['اسم المورد'].trim(),
      data['الفئة'].trim(),
      data['رقم الهاتف'].toString().trim(),
      data['البريد الإلكتروني'] ? data['البريد الإلكتروني'].trim() : null,
      data['العنوان'] ? data['العنوان'].trim() : null,
      data['الشخص المسؤول'] ? data['الشخص المسؤول'].trim() : null
    ];
    
    await db.execute(query, values);
  } catch (error) {
    throw new Error('فشل في إدراج المورد: ' + error.message);
  }
}

/**
 * تحميل نموذج الموردين
 * GET /api/v1/upload/suppliers/template
 */
router.get('/suppliers/template', (req, res) => {
  try {
    // إنشاء نموذج Excel
    const templateData = [
      {
        'اسم المورد': 'شركة البهارات الذهبية',
        'الفئة': 'بهارات',
        'رقم الهاتف': '+965-12345678',
        'البريد الإلكتروني': '<EMAIL>',
        'العنوان': 'الكويت - حولي',
        'الشخص المسؤول': 'أحمد محمد'
      },
      {
        'اسم المورد': 'مؤسسة المواد الاستهلاكية',
        'الفئة': 'استهلاكي',
        'رقم الهاتف': '+965-87654321',
        'البريد الإلكتروني': '<EMAIL>',
        'العنوان': 'الكويت - الفروانية',
        'الشخص المسؤول': 'فاطمة علي'
      },
      {
        'اسم المورد': 'شركة الأجبان الطازجة',
        'الفئة': 'أجبان',
        'رقم الهاتف': '+965-11223344',
        'البريد الإلكتروني': '<EMAIL>',
        'العنوان': 'الكويت - الأحمدي',
        'الشخص المسؤول': 'محمد خالد'
      }
    ];

    const ws = XLSX.utils.json_to_sheet(templateData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'الموردين');

    // تعيين عرض الأعمدة
    ws['!cols'] = [
      { width: 25 }, // اسم المورد
      { width: 15 }, // الفئة
      { width: 20 }, // رقم الهاتف
      { width: 30 }, // البريد الإلكتروني
      { width: 25 }, // العنوان
      { width: 20 }  // الشخص المسؤول
    ];

    // إنشاء buffer
    const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

    // إعداد headers للتحميل
    res.setHeader('Content-Disposition', 'attachment; filename="نموذج_الموردين.xlsx"');
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    
    res.send(buffer);

  } catch (error) {
    console.error('Template generation error:', error);
    res.status(500).json({
      success: false,
      message: 'فشل في إنشاء النموذج',
      error: error.message
    });
  }
});

/**
 * معالجة أخطاء Multer
 */
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'حجم الملف كبير جداً (الحد الأقصى 10 ميجابايت)'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: 'عدد الملفات كبير جداً (الحد الأقصى 5 ملفات)'
      });
    }
  }
  
  res.status(400).json({
    success: false,
    message: error.message
  });
});

module.exports = router;
