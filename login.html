<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>تسجيل الدخول - نظام إدارة العقود | محمد مرزوق العقاب</title>
  
  <!-- CSS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
  
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap');
    
    :root {
      --bg-primary: #0a0f1c;
      --bg-secondary: #1a2332;
      --bg-tertiary: #2a3441;
      --text-primary: #ffffff;
      --text-secondary: #f1f5f9;
      --accent-blue: #3b82f6;
      --accent-green: #10b981;
      --border-primary: #475569;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Cairo', sans-serif;
      background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
      color: var(--text-primary);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .login-container {
      background: rgba(26, 35, 50, 0.9);
      backdrop-filter: blur(20px);
      border: 1px solid var(--border-primary);
      border-radius: 24px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
      padding: 48px;
      width: 100%;
      max-width: 500px;
      margin: 20px;
    }
    
    .logo {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, var(--accent-blue), var(--accent-green));
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 32px;
      box-shadow: 0 12px 30px rgba(59, 130, 246, 0.3);
    }
    
    .form-input {
      width: 100%;
      padding: 16px 20px;
      border: 2px solid var(--border-primary);
      border-radius: 16px;
      background: var(--bg-tertiary);
      color: var(--text-primary);
      font-family: 'Cairo', sans-serif;
      font-size: 16px;
      transition: all 0.3s ease;
      margin-bottom: 20px;
    }
    
    .form-input:focus {
      outline: none;
      border-color: var(--accent-blue);
      box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    }
    
    .form-input::placeholder {
      color: #94a3b8;
    }
    
    .btn-login {
      width: 100%;
      background: linear-gradient(135deg, var(--accent-blue), var(--accent-green));
      color: var(--text-primary);
      padding: 16px 24px;
      border-radius: 16px;
      border: none;
      font-weight: 700;
      font-size: 18px;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-top: 20px;
    }
    
    .btn-login:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 30px rgba(59, 130, 246, 0.4);
    }
    
    .btn-login:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }
    
    .demo-credentials {
      background: rgba(59, 130, 246, 0.1);
      border: 1px solid rgba(59, 130, 246, 0.3);
      border-radius: 12px;
      padding: 16px;
      margin-top: 24px;
      text-align: center;
    }
    
    .loading {
      display: none;
      text-align: center;
    }
    
    .spinner {
      border: 3px solid var(--border-primary);
      border-top: 3px solid var(--accent-blue);
      border-radius: 50%;
      width: 24px;
      height: 24px;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="login-container">
    <!-- Logo -->
    <div class="logo">
      <i class="fas fa-building text-white text-3xl"></i>
    </div>
    
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-3xl font-black text-white mb-2">تسجيل الدخول</h1>
      <p class="text-slate-300 text-lg">نظام إدارة عقود الموردين والإيجارات</p>
      <p class="text-blue-400 font-semibold mt-2">جمعية المنقف التعاونية</p>
    </div>
    
    <!-- Login Form -->
    <form id="loginForm" onsubmit="handleLogin(event)">
      <div class="mb-6">
        <label class="block text-white font-semibold mb-3">اسم المستخدم</label>
        <input type="text" id="username" name="username" required 
               class="form-input" placeholder="أدخل اسم المستخدم">
      </div>
      
      <div class="mb-6">
        <label class="block text-white font-semibold mb-3">كلمة المرور</label>
        <input type="password" id="password" name="password" required 
               class="form-input" placeholder="أدخل كلمة المرور">
      </div>
      
      <div class="flex items-center justify-between mb-6">
        <label class="flex items-center text-slate-300">
          <input type="checkbox" id="rememberMe" class="ml-2">
          تذكرني
        </label>
        <a href="#" class="text-blue-400 hover:text-blue-300 font-medium">نسيت كلمة المرور؟</a>
      </div>
      
      <button type="submit" id="loginBtn" class="btn-login">
        <span id="loginText">
          <i class="fas fa-sign-in-alt ml-2"></i>
          تسجيل الدخول
        </span>
        <div id="loginLoading" class="loading">
          <div class="spinner"></div>
        </div>
      </button>
    </form>
    
    <!-- Demo Credentials -->
    <div class="demo-credentials">
      <h4 class="text-white font-bold mb-3">بيانات تجريبية للاختبار:</h4>
      <div class="text-slate-300 space-y-2">
        <p><strong>اسم المستخدم:</strong> admin</p>
        <p><strong>كلمة المرور:</strong> admin123</p>
      </div>
      <button type="button" onclick="fillDemoCredentials()" 
              class="mt-3 text-blue-400 hover:text-blue-300 font-medium">
        <i class="fas fa-magic ml-1"></i>
        ملء البيانات التجريبية
      </button>
    </div>
    
    <!-- Developer Credit -->
    <div class="text-center mt-8 pt-6 border-t border-slate-600">
      <p class="text-slate-400 text-sm mb-2">تطوير وتنفيذ</p>
      <p class="text-blue-400 font-bold text-lg">محمد مرزوق العقاب</p>
      <p class="text-slate-500 text-sm">مطور نظم معلومات محترف ومتخصص</p>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  
  <script>
    // التحقق من وجود token مسبقاً
    document.addEventListener('DOMContentLoaded', function() {
      const token = localStorage.getItem('authToken');
      if (token) {
        // إعادة توجيه إلى الصفحة الرئيسية
        window.location.href = 'launch-interface.html';
      }
    });
    
    // معالجة تسجيل الدخول
    async function handleLogin(event) {
      event.preventDefault();
      
      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;
      const rememberMe = document.getElementById('rememberMe').checked;
      
      if (!username || !password) {
        Swal.fire({
          icon: 'error',
          title: 'خطأ',
          text: 'يرجى إدخال اسم المستخدم وكلمة المرور',
          confirmButtonText: 'موافق'
        });
        return;
      }
      
      setLoading(true);
      
      try {
        const response = await fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ username, password })
        });
        
        const result = await response.json();
        
        if (result.success) {
          // حفظ التوكن
          if (rememberMe) {
            localStorage.setItem('authToken', result.token);
          } else {
            sessionStorage.setItem('authToken', result.token);
          }
          
          // حفظ بيانات المستخدم
          localStorage.setItem('userData', JSON.stringify(result.user));
          
          // رسالة نجاح
          Swal.fire({
            icon: 'success',
            title: 'مرحباً بك!',
            text: `أهلاً وسهلاً ${result.user.full_name}`,
            confirmButtonText: 'متابعة',
            timer: 2000
          }).then(() => {
            // إعادة توجيه حسب دور المستخدم
            if (result.user.role === 'admin') {
              window.location.href = 'advanced_management_interface.html';
            } else {
              window.location.href = 'professional_ultimate_interface.html';
            }
          });
          
        } else {
          throw new Error(result.error || 'فشل في تسجيل الدخول');
        }
        
      } catch (error) {
        console.error('Login error:', error);
        Swal.fire({
          icon: 'error',
          title: 'خطأ في تسجيل الدخول',
          text: 'اسم المستخدم أو كلمة المرور غير صحيحة',
          confirmButtonText: 'موافق'
        });
      } finally {
        setLoading(false);
      }
    }
    
    // ملء البيانات التجريبية
    function fillDemoCredentials() {
      document.getElementById('username').value = 'admin';
      document.getElementById('password').value = 'admin123';
      
      // تأثير بصري
      const inputs = document.querySelectorAll('.form-input');
      inputs.forEach(input => {
        input.style.borderColor = '#10b981';
        setTimeout(() => {
          input.style.borderColor = '#475569';
        }, 1000);
      });
    }
    
    // إظهار/إخفاء حالة التحميل
    function setLoading(loading) {
      const btn = document.getElementById('loginBtn');
      const text = document.getElementById('loginText');
      const loadingSpinner = document.getElementById('loginLoading');
      
      if (loading) {
        btn.disabled = true;
        text.style.display = 'none';
        loadingSpinner.style.display = 'block';
      } else {
        btn.disabled = false;
        text.style.display = 'block';
        loadingSpinner.style.display = 'none';
      }
    }
    
    // التعامل مع Enter key
    document.addEventListener('keypress', function(event) {
      if (event.key === 'Enter') {
        const form = document.getElementById('loginForm');
        if (form) {
          form.dispatchEvent(new Event('submit'));
        }
      }
    });
    
    // تأثيرات بصرية للحقول
    document.querySelectorAll('.form-input').forEach(input => {
      input.addEventListener('focus', function() {
        this.style.transform = 'scale(1.02)';
      });
      
      input.addEventListener('blur', function() {
        this.style.transform = 'scale(1)';
      });
    });
    
    // رسالة ترحيب عند تحميل الصفحة
    setTimeout(() => {
      const welcomeMessage = document.createElement('div');
      welcomeMessage.innerHTML = `
        <div style="position: fixed; top: 20px; right: 20px; background: rgba(59, 130, 246, 0.9); color: white; padding: 16px 24px; border-radius: 12px; font-family: Cairo; font-weight: 600; z-index: 1000; animation: slideIn 0.5s ease;">
          <i class="fas fa-info-circle" style="margin-left: 8px;"></i>
          مرحباً بك في نظام إدارة العقود المتطور
        </div>
      `;
      
      document.body.appendChild(welcomeMessage);
      
      setTimeout(() => {
        welcomeMessage.remove();
      }, 4000);
    }, 1000);
  </script>
  
  <style>
    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  </style>
</body>
</html>
