const { app, BrowserWindow, Menu, ipcMain, dialog, shell, screen } = require('electron');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');

// متغيرات عامة
let mainWindow;
let serverProcess;
const isDev = process.argv.includes('--dev');
const serverPort = 3000;

// إعداد التطبيق
app.setName('نظام إدارة العقود - جمعية المنقف');

// منع تشغيل عدة نسخ من التطبيق
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    app.quit();
} else {
    app.on('second-instance', () => {
        // إذا حاول المستخدم تشغيل نسخة ثانية، ركز على النافذة الموجودة
        if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.focus();
        }
    });
}

// دالة إنشاء النافذة الرئيسية
function createMainWindow() {
    // الحصول على أبعاد الشاشة
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    
    // إنشاء النافذة الرئيسية
    mainWindow = new BrowserWindow({
        width: Math.min(1400, width - 100),
        height: Math.min(900, height - 100),
        minWidth: 1200,
        minHeight: 800,
        center: true,
        show: false, // لا تظهر النافذة حتى تكون جاهزة
        icon: path.join(__dirname, '../assets/icon.png'),
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'preload.js'),
            webSecurity: false // للسماح بتحميل الملفات المحلية
        },
        titleBarStyle: 'default',
        frame: true,
        resizable: true,
        maximizable: true,
        minimizable: true,
        closable: true,
        autoHideMenuBar: false,
        title: 'نظام إدارة العقود - جمعية المنقف التعاونية'
    });

    // إعداد القائمة
    createMenu();

    // تحميل الصفحة الرئيسية
    loadMainPage();

    // إظهار النافذة عند الجاهزية
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        if (isDev) {
            mainWindow.webContents.openDevTools();
        }
        
        // رسالة ترحيب
        showWelcomeMessage();
    });

    // التعامل مع إغلاق النافذة
    mainWindow.on('closed', () => {
        mainWindow = null;
        stopServer();
    });

    // التعامل مع الروابط الخارجية
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });

    // منع التنقل خارج التطبيق
    mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        
        if (parsedUrl.origin !== `http://localhost:${serverPort}` && !navigationUrl.startsWith('file://')) {
            event.preventDefault();
            shell.openExternal(navigationUrl);
        }
    });
}

// دالة تحميل الصفحة الرئيسية
function loadMainPage() {
    // التحقق من تشغيل الخادم المحلي
    checkServerAndLoad();
}

// دالة التحقق من الخادم وتحميل الصفحة
function checkServerAndLoad() {
    const http = require('http');
    
    const checkServer = () => {
        const req = http.get(`http://localhost:${serverPort}`, (res) => {
            // الخادم يعمل، تحميل الصفحة الرئيسية
            mainWindow.loadURL(`http://localhost:${serverPort}/launch-interface.html`);
        });
        
        req.on('error', () => {
            // الخادم لا يعمل، بدء تشغيله
            startServer().then(() => {
                // انتظار قليل ثم المحاولة مرة أخرى
                setTimeout(checkServer, 2000);
            });
        });
        
        req.setTimeout(1000, () => {
            req.destroy();
            // انتهت المهلة، بدء تشغيل الخادم
            startServer().then(() => {
                setTimeout(checkServer, 2000);
            });
        });
    };
    
    checkServer();
}

// دالة بدء تشغيل الخادم المحلي
function startServer() {
    return new Promise((resolve) => {
        if (serverProcess) {
            resolve();
            return;
        }
        
        const serverPath = path.join(__dirname, '../backend/api/local-server.js');
        
        serverProcess = spawn('node', [serverPath], {
            cwd: path.join(__dirname, '..'),
            stdio: isDev ? 'inherit' : 'ignore'
        });
        
        serverProcess.on('error', (error) => {
            console.error('خطأ في تشغيل الخادم:', error);
        });
        
        serverProcess.on('exit', (code) => {
            console.log(`الخادم توقف برمز: ${code}`);
            serverProcess = null;
        });
        
        // انتظار قليل لبدء تشغيل الخادم
        setTimeout(resolve, 3000);
    });
}

// دالة إيقاف الخادم
function stopServer() {
    if (serverProcess) {
        serverProcess.kill();
        serverProcess = null;
    }
}

// دالة إنشاء القائمة
function createMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'الصفحة الرئيسية',
                    accelerator: 'CmdOrCtrl+H',
                    click: () => {
                        mainWindow.loadURL(`http://localhost:${serverPort}/launch-interface.html`);
                    }
                },
                {
                    label: 'الواجهة الاحترافية',
                    accelerator: 'CmdOrCtrl+P',
                    click: () => {
                        mainWindow.loadURL(`http://localhost:${serverPort}/professional_ultimate_interface.html`);
                    }
                },
                { type: 'separator' },
                {
                    label: 'إعادة تحميل',
                    accelerator: 'CmdOrCtrl+R',
                    click: () => {
                        mainWindow.reload();
                    }
                },
                {
                    label: 'ملء الشاشة',
                    accelerator: 'F11',
                    click: () => {
                        mainWindow.setFullScreen(!mainWindow.isFullScreen());
                    }
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'إدارة',
            submenu: [
                {
                    label: 'إدارة الموردين',
                    accelerator: 'CmdOrCtrl+1',
                    click: () => {
                        mainWindow.loadURL(`http://localhost:${serverPort}/suppliers_management.html`);
                    }
                },
                {
                    label: 'إدارة العقود',
                    accelerator: 'CmdOrCtrl+2',
                    click: () => {
                        mainWindow.loadURL(`http://localhost:${serverPort}/contracts_management.html`);
                    }
                },
                {
                    label: 'إدارة العيون',
                    accelerator: 'CmdOrCtrl+3',
                    click: () => {
                        mainWindow.loadURL(`http://localhost:${serverPort}/eyes_management.html`);
                    }
                },
                {
                    label: 'التقارير',
                    accelerator: 'CmdOrCtrl+4',
                    click: () => {
                        mainWindow.loadURL(`http://localhost:${serverPort}/reports_dashboard.html`);
                    }
                }
            ]
        },
        {
            label: 'أدوات',
            submenu: [
                {
                    label: 'رفع الملفات',
                    accelerator: 'CmdOrCtrl+U',
                    click: () => {
                        mainWindow.loadURL(`http://localhost:${serverPort}/upload_suppliers.html`);
                    }
                },
                {
                    label: 'إنشاء بيانات نموذجية',
                    click: async () => {
                        const result = await dialog.showMessageBox(mainWindow, {
                            type: 'question',
                            buttons: ['نعم', 'لا'],
                            defaultId: 0,
                            title: 'إنشاء بيانات نموذجية',
                            message: 'هل تريد إنشاء ملفات البيانات النموذجية؟',
                            detail: 'سيتم إنشاء ملفات Excel و CSV تحتوي على بيانات تجريبية للاختبار.'
                        });
                        
                        if (result.response === 0) {
                            createSampleData();
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'إعدادات',
                    accelerator: 'CmdOrCtrl+,',
                    click: () => {
                        // فتح نافذة الإعدادات
                        showSettingsWindow();
                    }
                }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                {
                    label: 'تكبير',
                    accelerator: 'CmdOrCtrl+Plus',
                    click: () => {
                        const currentZoom = mainWindow.webContents.getZoomLevel();
                        mainWindow.webContents.setZoomLevel(currentZoom + 0.5);
                    }
                },
                {
                    label: 'تصغير',
                    accelerator: 'CmdOrCtrl+-',
                    click: () => {
                        const currentZoom = mainWindow.webContents.getZoomLevel();
                        mainWindow.webContents.setZoomLevel(currentZoom - 0.5);
                    }
                },
                {
                    label: 'الحجم الطبيعي',
                    accelerator: 'CmdOrCtrl+0',
                    click: () => {
                        mainWindow.webContents.setZoomLevel(0);
                    }
                },
                { type: 'separator' },
                {
                    label: 'أدوات المطور',
                    accelerator: 'F12',
                    click: () => {
                        mainWindow.webContents.toggleDevTools();
                    }
                }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'حول التطبيق',
                    click: () => {
                        showAboutDialog();
                    }
                },
                {
                    label: 'دليل الاستخدام',
                    click: () => {
                        shell.openExternal('https://github.com/manqaf-coop/supplier-contracts-system');
                    }
                },
                { type: 'separator' },
                {
                    label: 'تحقق من التحديثات',
                    click: () => {
                        checkForUpdates();
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// دالة إظهار رسالة الترحيب
function showWelcomeMessage() {
    setTimeout(() => {
        dialog.showMessageBox(mainWindow, {
            type: 'info',
            title: 'مرحباً بك',
            message: 'نظام إدارة العقود - جمعية المنقف التعاونية',
            detail: 'تم تشغيل التطبيق بنجاح!\n\nتطوير وتنفيذ: محمد مرزوق العقاب\nمطور نظم معلومات محترف ومتخصص',
            buttons: ['موافق']
        });
    }, 2000);
}

// دالة إنشاء البيانات النموذجية
function createSampleData() {
    const { spawn } = require('child_process');
    const createDataPath = path.join(__dirname, '../backend/utils/create-excel-files.js');
    
    const createProcess = spawn('node', [createDataPath], {
        cwd: path.join(__dirname, '..')
    });
    
    createProcess.on('close', (code) => {
        if (code === 0) {
            dialog.showMessageBox(mainWindow, {
                type: 'info',
                title: 'تم بنجاح',
                message: 'تم إنشاء ملفات البيانات النموذجية',
                detail: 'تم إنشاء الملفات في مجلد sample_data',
                buttons: ['موافق']
            });
        } else {
            dialog.showMessageBox(mainWindow, {
                type: 'error',
                title: 'خطأ',
                message: 'فشل في إنشاء البيانات النموذجية',
                buttons: ['موافق']
            });
        }
    });
}

// دالة إظهار نافذة الإعدادات
function showSettingsWindow() {
    // يمكن تطوير نافذة إعدادات منفصلة هنا
    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: 'الإعدادات',
        message: 'نافذة الإعدادات قيد التطوير',
        detail: 'ستتوفر إعدادات متقدمة في الإصدارات القادمة',
        buttons: ['موافق']
    });
}

// دالة إظهار معلومات التطبيق
function showAboutDialog() {
    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: 'حول التطبيق',
        message: 'نظام إدارة عقود الموردين والإيجارات',
        detail: `الإصدار: 1.0.0
جمعية المنقف التعاونية

تطوير وتنفيذ: محمد مرزوق العقاب
مطور نظم معلومات محترف ومتخصص

© 2024 جميع الحقوق محفوظة`,
        buttons: ['موافق']
    });
}

// دالة التحقق من التحديثات
function checkForUpdates() {
    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: 'التحديثات',
        message: 'أنت تستخدم أحدث إصدار',
        detail: 'لا توجد تحديثات متاحة حالياً',
        buttons: ['موافق']
    });
}

// أحداث التطبيق
app.whenReady().then(() => {
    createMainWindow();
    
    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createMainWindow();
        }
    });
});

app.on('window-all-closed', () => {
    stopServer();
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('before-quit', () => {
    stopServer();
});

// التعامل مع IPC
ipcMain.handle('get-app-version', () => {
    return app.getVersion();
});

ipcMain.handle('show-message-box', async (event, options) => {
    const result = await dialog.showMessageBox(mainWindow, options);
    return result;
});

ipcMain.handle('show-open-dialog', async (event, options) => {
    const result = await dialog.showOpenDialog(mainWindow, options);
    return result;
});

ipcMain.handle('show-save-dialog', async (event, options) => {
    const result = await dialog.showSaveDialog(mainWindow, options);
    return result;
});
