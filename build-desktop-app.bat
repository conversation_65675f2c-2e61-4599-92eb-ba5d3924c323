@echo off
chcp 65001 >nul
title بناء تطبيق سطح المكتب | محمد مرزوق العقاب

echo.
echo 🎉 ===============================================
echo 🏗️ بناء تطبيق نظام إدارة العقود - سطح المكتب
echo 🏢 جمعية المنقف التعاونية
echo.
echo 👨‍💻 تطوير وتنفيذ: محمد مرزوق العقاب
echo 🎯 مطور نظم معلومات محترف ومتخصص
echo 🎉 ===============================================
echo.

echo 📦 التحقق من التبعيات...
if not exist "node_modules" (
    echo 🔄 تثبيت التبعيات...
    npm install
    echo.
)

echo 🎨 إنشاء الأيقونات...
if not exist "assets" mkdir assets
node assets/create-icons.js

echo 📊 إنشاء البيانات النموذجية...
if not exist "sample_data" (
    echo 📄 إنشاء ملفات البيانات النموذجية...
    node backend/utils/create-excel-files.js
    echo.
)

echo.
echo 🏗️ بدء عملية البناء...
echo.

:menu
echo 🎯 اختر نوع البناء:
echo.
echo 1. 🖥️ بناء للـ Windows (EXE + Installer)
echo 2. 🍎 بناء للـ macOS (DMG)
echo 3. 🐧 بناء للـ Linux (AppImage)
echo 4. 🌍 بناء لجميع المنصات
echo 5. 🧪 بناء تجريبي (بدون تثبيت)
echo.
echo 0. خروج
echo.
set /p choice="اختر رقم (0-5): "

if "%choice%"=="1" goto windows
if "%choice%"=="2" goto macos
if "%choice%"=="3" goto linux
if "%choice%"=="4" goto all
if "%choice%"=="5" goto test
if "%choice%"=="0" goto exit
goto invalid

:windows
echo.
echo 🖥️ بناء تطبيق Windows...
echo.
echo 📋 معلومات البناء:
echo    📦 النوع: Windows Executable + NSIS Installer
echo    📁 المجلد: dist/
echo    🎯 الهدف: win32-x64
echo    📊 الحجم المتوقع: ~150-200 MB
echo.
npm run electron:build -- --win
goto success

:macos
echo.
echo 🍎 بناء تطبيق macOS...
echo.
echo 📋 معلومات البناء:
echo    📦 النوع: macOS DMG
echo    📁 المجلد: dist/
echo    🎯 الهدف: darwin-x64
echo    📊 الحجم المتوقع: ~150-200 MB
echo.
npm run electron:build -- --mac
goto success

:linux
echo.
echo 🐧 بناء تطبيق Linux...
echo.
echo 📋 معلومات البناء:
echo    📦 النوع: Linux AppImage
echo    📁 المجلد: dist/
echo    🎯 الهدف: linux-x64
echo    📊 الحجم المتوقع: ~150-200 MB
echo.
npm run electron:build -- --linux
goto success

:all
echo.
echo 🌍 بناء لجميع المنصات...
echo.
echo ⚠️ تحذير: هذا قد يستغرق وقتاً طويلاً ومساحة كبيرة!
echo.
set /p confirm="هل أنت متأكد؟ (y/n): "
if /i "%confirm%"=="y" (
    echo.
    echo 📋 معلومات البناء:
    echo    📦 الأنواع: Windows EXE + macOS DMG + Linux AppImage
    echo    📁 المجلد: dist/
    echo    🎯 الأهداف: win32-x64, darwin-x64, linux-x64
    echo    📊 الحجم المتوقع: ~450-600 MB
    echo.
    npm run electron:build -- --win --mac --linux
    goto success
) else (
    goto menu
)

:test
echo.
echo 🧪 بناء تجريبي...
echo.
echo 📋 معلومات البناء:
echo    📦 النوع: مجلد قابل للتشغيل (بدون installer)
echo    📁 المجلد: dist/
echo    🎯 الهدف: المنصة الحالية
echo    📊 الحجم المتوقع: ~100-150 MB
echo.
npm run electron:build -- --dir
goto success

:success
echo.
echo 🎉 ===============================================
echo ✅ تم بناء التطبيق بنجاح!
echo 📁 الملفات متاحة في مجلد: dist/
echo 🎉 ===============================================
echo.

echo 📋 الملفات المنشأة:
if exist "dist" (
    dir /b dist
    echo.
)

echo 📊 معلومات التطبيق المبني:
echo    📦 الاسم: نظام إدارة العقود - جمعية المنقف
echo    🔢 الإصدار: 1.0.0
echo    👨‍💻 المطور: محمد مرزوق العقاب
echo    🏢 الشركة: جمعية المنقف التعاونية
echo    📅 تاريخ البناء: %DATE% %TIME%
echo.

echo 🚀 طرق التشغيل:
echo    💻 Windows: تشغيل ملف .exe من مجلد dist
echo    🍎 macOS: فتح ملف .dmg وسحب التطبيق إلى Applications
echo    🐧 Linux: تشغيل ملف .AppImage مباشرة
echo.

echo 📦 توزيع التطبيق:
echo    • يمكن توزيع الملفات من مجلد dist
echo    • التطبيق يعمل بدون إنترنت
echo    • يحتوي على قاعدة بيانات محلية
echo    • يشمل جميع البيانات النموذجية
echo.

goto end

:invalid
echo.
echo ❌ اختيار غير صحيح
echo 💡 يرجى اختيار رقم صحيح (0-5)
timeout /t 2 /nobreak >nul
goto menu

:end
echo 💡 نصائح للتوزيع:
echo    • اختبر التطبيق على أجهزة مختلفة
echo    • تأكد من عمل جميع الوظائف
echo    • قم بإنشاء دليل مستخدم
echo    • وفر الدعم الفني للمستخدمين
echo.
pause
goto menu

:exit
echo.
echo 👋 شكراً لاستخدام أداة بناء التطبيق
echo 👨‍💻 تطوير: محمد مرزوق العقاب
echo 🏢 جمعية المنقف التعاونية
echo.
timeout /t 3 /nobreak >nul
exit
