const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// إنشاء قاعدة البيانات المحلية
const dbPath = path.join(__dirname, 'supplier_contracts.db');
const db = new sqlite3.Database(dbPath);

// إنشاء الجداول
function initializeDatabase() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // جدول الموردين
      db.run(`CREATE TABLE IF NOT EXISTS suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        address TEXT,
        commercial_record TEXT,
        tax_number TEXT,
        contact_person TEXT,
        notes TEXT,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`);

      // جدول العقود
      db.run(`CREATE TABLE IF NOT EXISTS contracts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        supplier_id INTEGER,
        contract_number TEXT UNIQUE NOT NULL,
        contract_type TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        amount DECIMAL(10,2),
        currency TEXT DEFAULT 'KWD',
        status TEXT DEFAULT 'active',
        terms TEXT,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
      )`);

      // جدول الطبليات
      db.run(`CREATE TABLE IF NOT EXISTS tables_sections (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        table_number INTEGER NOT NULL,
        section_number INTEGER NOT NULL,
        section_size INTEGER DEFAULT 60,
        category TEXT NOT NULL,
        rent_price DECIMAL(8,2) DEFAULT 60.00,
        status TEXT DEFAULT 'available',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`);

      // جدول العيون (الإيجارات)
      db.run(`CREATE TABLE IF NOT EXISTS eye_rentals (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        supplier_id INTEGER,
        table_id INTEGER,
        eye_number TEXT NOT NULL,
        start_date DATE NOT NULL,
        end_date DATE,
        monthly_rent DECIMAL(8,2) NOT NULL,
        deposit_amount DECIMAL(8,2),
        status TEXT DEFAULT 'active',
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
        FOREIGN KEY (table_id) REFERENCES tables_sections (id)
      )`);

      // جدول الدفعات
      db.run(`CREATE TABLE IF NOT EXISTS payments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        contract_id INTEGER,
        rental_id INTEGER,
        payment_type TEXT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_date DATE NOT NULL,
        payment_method TEXT,
        reference_number TEXT,
        notes TEXT,
        status TEXT DEFAULT 'completed',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (contract_id) REFERENCES contracts (id),
        FOREIGN KEY (rental_id) REFERENCES eye_rentals (id)
      )`);

      // جدول المستخدمين
      db.run(`CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        full_name TEXT NOT NULL,
        email TEXT,
        role TEXT DEFAULT 'user',
        permissions TEXT,
        status TEXT DEFAULT 'active',
        last_login DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`);

      // جدول سجل العمليات
      db.run(`CREATE TABLE IF NOT EXISTS activity_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        action TEXT NOT NULL,
        table_name TEXT,
        record_id INTEGER,
        old_values TEXT,
        new_values TEXT,
        ip_address TEXT,
        user_agent TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )`);

      // جدول الإعدادات
      db.run(`CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_key TEXT UNIQUE NOT NULL,
        setting_value TEXT,
        description TEXT,
        category TEXT,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`);

      console.log('✅ تم إنشاء جميع الجداول بنجاح');
      resolve();
    });
  });
}

// إدراج البيانات التجريبية
function insertSampleData() {
  return new Promise((resolve, reject) => {
    // فحص إذا كانت البيانات موجودة بالفعل
    db.get("SELECT COUNT(*) as count FROM suppliers", (err, row) => {
      if (err) {
        reject(err);
        return;
      }

      // إذا كانت البيانات موجودة، لا نحتاج لإدراجها مرة أخرى
      if (row.count > 0) {
        console.log('✅ البيانات التجريبية موجودة بالفعل');
        resolve();
        return;
      }

      db.serialize(() => {
        // بيانات تجريبية للموردين
        const suppliers = [
          ['شركة البهارات الذهبية', 'بهارات', '99887766', '<EMAIL>', 'السالمية - الكويت', 'CR123456', 'TAX789', 'أحمد محمد', 'مورد بهارات عالية الجودة'],
          ['مؤسسة المواد الاستهلاكية', 'استهلاكي', '99776655', '<EMAIL>', 'حولي - الكويت', 'CR234567', 'TAX890', 'فاطمة أحمد', 'مواد استهلاكية متنوعة'],
          ['شركة الأجبان الطازجة', 'أجبان', '99665544', '<EMAIL>', 'الفروانية - الكويت', 'CR345678', 'TAX901', 'محمد علي', 'أجبان طازجة ومستوردة'],
          ['مؤسسة التوابل العربية', 'بهارات', '99554433', '<EMAIL>', 'الجهراء - الكويت', 'CR456789', 'TAX012', 'سارة خالد', 'توابل عربية أصيلة'],
          ['شركة المنظفات الحديثة', 'استهلاكي', '99443322', '<EMAIL>', 'مبارك الكبير - الكويت', 'CR567890', 'TAX123', 'عبدالله سالم', 'منظفات ومواد تنظيف']
        ];

        const supplierStmt = db.prepare(`INSERT INTO suppliers (name, category, phone, email, address, commercial_record, tax_number, contact_person, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`);
        suppliers.forEach(supplier => {
          supplierStmt.run(supplier);
        });
        supplierStmt.finalize();

      // بيانات تجريبية للطبليات
      const tables = [];
      for (let tableNum = 1; tableNum <= 20; tableNum++) {
        for (let sectionNum = 1; sectionNum <= 4; sectionNum++) {
          const categories = ['بهارات', 'استهلاكي', 'أجبان'];
          const category = categories[Math.floor(Math.random() * categories.length)];
          tables.push([tableNum, sectionNum, 60, category, 60.00]);
        }
      }

      const tableStmt = db.prepare(`INSERT INTO tables_sections (table_number, section_number, section_size, category, rent_price) VALUES (?, ?, ?, ?, ?)`);
      tables.forEach(table => {
        tableStmt.run(table);
      });
      tableStmt.finalize();

      // بيانات تجريبية للعقود
      const contracts = [
        [1, 'CON-2024-001', 'توريد', 'عقد توريد بهارات', 'توريد بهارات متنوعة للسوق', '2024-01-01', '2024-12-31', 15000.00, 'KWD', 'active'],
        [2, 'CON-2024-002', 'توريد', 'عقد توريد مواد استهلاكية', 'توريد مواد تنظيف ومستلزمات', '2024-02-01', '2024-12-31', 25000.00, 'KWD', 'active'],
        [3, 'CON-2024-003', 'توريد', 'عقد توريد أجبان', 'توريد أجبان طازجة ومستوردة', '2024-01-15', '2024-12-31', 18000.00, 'KWD', 'active']
      ];

      const contractStmt = db.prepare(`INSERT INTO contracts (supplier_id, contract_number, contract_type, title, description, start_date, end_date, amount, currency, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`);
      contracts.forEach(contract => {
        contractStmt.run(contract);
      });
      contractStmt.finalize();

      // بيانات تجريبية للعيون المؤجرة
      const rentals = [
        [1, 1, 'EYE-001', '2024-01-01', '2024-12-31', 60.00, 120.00, 'active', 'عين في قسم البهارات'],
        [2, 5, 'EYE-002', '2024-02-01', '2024-12-31', 60.00, 120.00, 'active', 'عين في قسم المواد الاستهلاكية'],
        [3, 9, 'EYE-003', '2024-01-15', '2024-12-31', 60.00, 120.00, 'active', 'عين في قسم الأجبان']
      ];

      const rentalStmt = db.prepare(`INSERT INTO eye_rentals (supplier_id, table_id, eye_number, start_date, end_date, monthly_rent, deposit_amount, status, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`);
      rentals.forEach(rental => {
        rentalStmt.run(rental);
      });
      rentalStmt.finalize();

      // مستخدم افتراضي
      const bcrypt = require('bcryptjs');
      const hashedPassword = bcrypt.hashSync('admin123', 10);
      db.run(`INSERT OR IGNORE INTO users (username, password, full_name, email, role, permissions) VALUES (?, ?, ?, ?, ?, ?)`, 
        ['admin', hashedPassword, 'مدير النظام', '<EMAIL>', 'admin', 'all']);

      // إعدادات النظام
      const settings = [
        ['company_name', 'جمعية المنقف التعاونية', 'اسم الشركة', 'general'],
        ['developer_name', 'محمد مرزوق العقاب', 'اسم المطور', 'general'],
        ['default_currency', 'KWD', 'العملة الافتراضية', 'financial'],
        ['eye_rent_price', '60.00', 'سعر إيجار العين الواحدة', 'financial'],
        ['table_sections', '4', 'عدد أقسام الطبلية الواحدة', 'general']
      ];

      const settingStmt = db.prepare(`INSERT OR IGNORE INTO settings (setting_key, setting_value, description, category) VALUES (?, ?, ?, ?)`);
      settings.forEach(setting => {
        settingStmt.run(setting);
      });
      settingStmt.finalize();

        console.log('✅ تم إدراج البيانات التجريبية بنجاح');
        resolve();
      });
    });
  });
}

// دوال قاعدة البيانات
const dbOperations = {
  // الموردين
  getAllSuppliers: () => {
    return new Promise((resolve, reject) => {
      db.all("SELECT * FROM suppliers ORDER BY created_at DESC", (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  },

  getSupplierById: (id) => {
    return new Promise((resolve, reject) => {
      db.get("SELECT * FROM suppliers WHERE id = ?", [id], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  },

  addSupplier: (supplier) => {
    return new Promise((resolve, reject) => {
      const stmt = db.prepare(`INSERT INTO suppliers (name, category, phone, email, address, commercial_record, tax_number, contact_person, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`);
      stmt.run([supplier.name, supplier.category, supplier.phone, supplier.email, supplier.address, supplier.commercial_record, supplier.tax_number, supplier.contact_person, supplier.notes], function(err) {
        if (err) reject(err);
        else resolve({ id: this.lastID, ...supplier });
      });
      stmt.finalize();
    });
  },

  updateSupplier: (id, supplier) => {
    return new Promise((resolve, reject) => {
      const stmt = db.prepare(`UPDATE suppliers SET name=?, category=?, phone=?, email=?, address=?, commercial_record=?, tax_number=?, contact_person=?, notes=?, updated_at=CURRENT_TIMESTAMP WHERE id=?`);
      stmt.run([supplier.name, supplier.category, supplier.phone, supplier.email, supplier.address, supplier.commercial_record, supplier.tax_number, supplier.contact_person, supplier.notes, id], function(err) {
        if (err) reject(err);
        else resolve({ id, ...supplier });
      });
      stmt.finalize();
    });
  },

  deleteSupplier: (id) => {
    return new Promise((resolve, reject) => {
      db.run("DELETE FROM suppliers WHERE id = ?", [id], function(err) {
        if (err) reject(err);
        else resolve({ deleted: this.changes });
      });
    });
  },

  // العقود
  getAllContracts: () => {
    return new Promise((resolve, reject) => {
      db.all(`SELECT c.*, s.name as supplier_name FROM contracts c 
              LEFT JOIN suppliers s ON c.supplier_id = s.id 
              ORDER BY c.created_at DESC`, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  },

  addContract: (contract) => {
    return new Promise((resolve, reject) => {
      const stmt = db.prepare(`INSERT INTO contracts (supplier_id, contract_number, contract_type, title, description, start_date, end_date, amount, currency, terms, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`);
      stmt.run([contract.supplier_id, contract.contract_number, contract.contract_type, contract.title, contract.description, contract.start_date, contract.end_date, contract.amount, contract.currency, contract.terms, contract.notes], function(err) {
        if (err) reject(err);
        else resolve({ id: this.lastID, ...contract });
      });
      stmt.finalize();
    });
  },

  // العيون والإيجارات
  getAllRentals: () => {
    return new Promise((resolve, reject) => {
      db.all(`SELECT r.*, s.name as supplier_name, t.table_number, t.section_number 
              FROM eye_rentals r 
              LEFT JOIN suppliers s ON r.supplier_id = s.id 
              LEFT JOIN tables_sections t ON r.table_id = t.id 
              ORDER BY r.created_at DESC`, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  },

  getAvailableTables: () => {
    return new Promise((resolve, reject) => {
      db.all(`SELECT * FROM tables_sections WHERE status = 'available' ORDER BY table_number, section_number`, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  },

  addRental: (rental) => {
    return new Promise((resolve, reject) => {
      db.serialize(() => {
        // تحديث حالة الطبلية إلى مؤجرة
        db.run("UPDATE tables_sections SET status = 'rented' WHERE id = ?", [rental.table_id]);
        
        // إضافة الإيجار
        const stmt = db.prepare(`INSERT INTO eye_rentals (supplier_id, table_id, eye_number, start_date, end_date, monthly_rent, deposit_amount, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`);
        stmt.run([rental.supplier_id, rental.table_id, rental.eye_number, rental.start_date, rental.end_date, rental.monthly_rent, rental.deposit_amount, rental.notes], function(err) {
          if (err) reject(err);
          else resolve({ id: this.lastID, ...rental });
        });
        stmt.finalize();
      });
    });
  },

  // الإحصائيات
  getStatistics: () => {
    return new Promise((resolve, reject) => {
      const stats = {};
      
      db.serialize(() => {
        db.get("SELECT COUNT(*) as total FROM suppliers WHERE status = 'active'", (err, row) => {
          stats.totalSuppliers = row ? row.total : 0;
        });
        
        db.get("SELECT COUNT(*) as total FROM contracts WHERE status = 'active'", (err, row) => {
          stats.activeContracts = row ? row.total : 0;
        });
        
        db.get("SELECT COUNT(*) as total FROM eye_rentals WHERE status = 'active'", (err, row) => {
          stats.rentedEyes = row ? row.total : 0;
        });
        
        db.get("SELECT SUM(monthly_rent) as total FROM eye_rentals WHERE status = 'active'", (err, row) => {
          stats.monthlyRevenue = row ? row.total : 0;
          resolve(stats);
        });
      });
    });
  }
};

module.exports = {
  db,
  initializeDatabase,
  insertSampleData,
  dbOperations
};
