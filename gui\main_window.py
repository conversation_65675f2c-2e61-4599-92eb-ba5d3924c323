#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية لتطبيق إدارة عقود الموردين
Main Window for Supplier Contract Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *

class MainWindow:
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self, db_manager):
        """
        تهيئة النافذة الرئيسية
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db_manager = db_manager
        self.root = None
        self.notebook = None
        
        # إنشاء النافذة الرئيسية
        self.create_main_window()
        self.create_menu()
        self.create_main_interface()
    
    def create_main_window(self):
        """إنشاء النافذة الرئيسية"""
        self.root = ttk_bs.Window(
            title="نظام إدارة عقود الموردين - جمعية المنقف",
            themename="cosmo",
            size=(1400, 900),
            position=(100, 50)
        )
        
        # تعيين أيقونة النافذة (اختياري)
        try:
            self.root.iconbitmap("assets/icon.ico")
        except:
            pass
        
        # تعيين الحد الأدنى لحجم النافذة
        self.root.minsize(1200, 700)

        # تعيين النافذة في المنتصف
        self.center_window()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="جديد", command=self.new_file)
        file_menu.add_command(label="فتح", command=self.open_file)
        file_menu.add_command(label="حفظ", command=self.save_file)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.exit_app)
        
        # قائمة البيانات
        data_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="البيانات", menu=data_menu)
        data_menu.add_command(label="نسخ احتياطي", command=self.backup_data)
        data_menu.add_command(label="استعادة البيانات", command=self.restore_data)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="تقرير الموردين", command=self.suppliers_report)
        reports_menu.add_command(label="تقرير العقود", command=self.contracts_report)
        reports_menu.add_command(label="تقرير الإيجارات", command=self.rentals_report)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.user_guide)
        help_menu.add_command(label="حول البرنامج", command=self.about)
    
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # إنشاء إطار رئيسي
        main_frame = ttk_bs.Frame(self.root, padding=10)
        main_frame.pack(fill=BOTH, expand=True)
        
        # عنوان التطبيق
        title_label = ttk_bs.Label(
            main_frame,
            text="نظام إدارة عقود الموردين - جمعية المنقف",
            font=("Arial", 16, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 30))
        
        # إنشاء التبويبات
        self.notebook = ttk_bs.Notebook(main_frame, bootstyle="primary")
        self.notebook.pack(fill=BOTH, expand=True)
        
        # تبويب الموردين
        self.create_suppliers_tab()
        
        # تبويب العقود
        self.create_contracts_tab()
        
        # تبويب الطبليات والعناصر
        self.create_items_tab()
        
        # تبويب التقارير
        self.create_reports_tab()
        
        # شريط الحالة
        self.create_status_bar(main_frame)
    
    def create_suppliers_tab(self):
        """إنشاء تبويب الموردين"""
        suppliers_frame = ttk_bs.Frame(self.notebook, padding=10)
        self.notebook.add(suppliers_frame, text="الموردين")

        # عنوان القسم
        title_label = ttk_bs.Label(
            suppliers_frame,
            text="إدارة الموردين حسب الأقسام",
            font=("Arial", 14, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 20))

        # وصف القسم
        desc_label = ttk_bs.Label(
            suppliers_frame,
            text="إدارة موردين البهارات والمواد الاستهلاكية والأجبان",
            font=("Arial", 11),
            bootstyle=SECONDARY
        )
        desc_label.pack(pady=(0, 30))

        # أزرار الإدارة
        buttons_frame = ttk_bs.Frame(suppliers_frame)
        buttons_frame.pack(pady=20)

        # إنشاء شبكة من الأزرار
        button_grid = ttk_bs.Frame(buttons_frame)
        button_grid.pack()

        # الصف الأول
        row1 = ttk_bs.Frame(button_grid)
        row1.pack(pady=10)

        ttk_bs.Button(
            row1,
            text="📋 إدارة الموردين",
            command=self.open_suppliers_window,
            bootstyle=PRIMARY,
            width=25
        ).pack(side=LEFT, padx=10)

        ttk_bs.Button(
            row1,
            text="➕ إضافة مورد جديد",
            command=self.add_new_supplier,
            bootstyle=SUCCESS,
            width=25
        ).pack(side=LEFT, padx=10)

        # الصف الثاني
        row2 = ttk_bs.Frame(button_grid)
        row2.pack(pady=10)

        ttk_bs.Button(
            row2,
            text="🔍 البحث في الموردين",
            command=self.search_suppliers,
            bootstyle=INFO,
            width=25
        ).pack(side=LEFT, padx=10)

        ttk_bs.Button(
            row2,
            text="📊 إحصائيات الموردين",
            command=self.suppliers_statistics,
            bootstyle=WARNING,
            width=25
        ).pack(side=LEFT, padx=10)

        # ملخص الموردين
        summary_frame = ttk_bs.LabelFrame(suppliers_frame, text="ملخص الموردين", padding=10)
        summary_frame.pack(fill=X, pady=20)

        # سيتم إضافة إحصائيات الموردين هنا لاحقاً
        summary_label = ttk_bs.Label(
            summary_frame,
            text="سيتم عرض إحصائيات الموردين حسب الأقسام هنا",
            font=("Arial", 11),
            bootstyle=SECONDARY
        )
        summary_label.pack()
    
    def create_contracts_tab(self):
        """إنشاء تبويب العقود"""
        contracts_frame = ttk_bs.Frame(self.notebook, padding=10)
        self.notebook.add(contracts_frame, text="العقود")

        # عنوان القسم
        title_label = ttk_bs.Label(
            contracts_frame,
            text="إدارة العقود والإيجارات",
            font=("Arial", 14, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 20))

        # وصف القسم
        desc_label = ttk_bs.Label(
            contracts_frame,
            text="إدارة عقود الموردين وتأجير الطبليات والعيون",
            font=("Arial", 11),
            bootstyle=SECONDARY
        )
        desc_label.pack(pady=(0, 30))

        # أزرار الإدارة
        buttons_frame = ttk_bs.Frame(contracts_frame)
        buttons_frame.pack(pady=20)

        # إنشاء شبكة من الأزرار
        button_grid = ttk_bs.Frame(buttons_frame)
        button_grid.pack()

        # الصف الأول
        row1 = ttk_bs.Frame(button_grid)
        row1.pack(pady=10)

        ttk_bs.Button(
            row1,
            text="📄 إدارة العقود",
            command=self.open_contracts_window,
            bootstyle=PRIMARY,
            width=25
        ).pack(side=LEFT, padx=10)

        ttk_bs.Button(
            row1,
            text="➕ إضافة عقد جديد",
            command=self.add_new_contract,
            bootstyle=SUCCESS,
            width=25
        ).pack(side=LEFT, padx=10)

        # الصف الثاني
        row2 = ttk_bs.Frame(button_grid)
        row2.pack(pady=10)

        ttk_bs.Button(
            row2,
            text="🔍 البحث في العقود",
            command=self.search_contracts,
            bootstyle=INFO,
            width=25
        ).pack(side=LEFT, padx=10)

        ttk_bs.Button(
            row2,
            text="📊 تقارير العقود",
            command=self.contracts_reports,
            bootstyle=WARNING,
            width=25
        ).pack(side=LEFT, padx=10)

        ttk_bs.Button(
            buttons_frame,
            text="العقود المنتهية قريباً",
            command=self.view_expiring_contracts,
            bootstyle=WARNING,
            width=20
        ).pack(pady=10)

        # ملخص العقود
        summary_frame = ttk_bs.LabelFrame(contracts_frame, text="ملخص العقود", padding=10)
        summary_frame.pack(fill=X, pady=20)

        # سيتم إضافة إحصائيات العقود هنا لاحقاً
        summary_label = ttk_bs.Label(
            summary_frame,
            text="سيتم عرض ملخص العقود هنا",
            font=("Arial", 12)
        )
        summary_label.pack()
    
    def create_items_tab(self):
        """إنشاء تبويب الطبليات والعناصر"""
        items_frame = ttk_bs.Frame(self.notebook, padding=10)
        self.notebook.add(items_frame, text="الطبليات والعناصر")

        # عنوان القسم
        title_label = ttk_bs.Label(
            items_frame,
            text="إدارة الطبليات ونظام العيون",
            font=("Arial", 14, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 20))

        # وصف القسم
        desc_label = ttk_bs.Label(
            items_frame,
            text="إدارة الطبليات والعناصر مع نظام العيون المتطور (4 عيون × 60 دينار)",
            font=("Arial", 11),
            bootstyle=SECONDARY
        )
        desc_label.pack(pady=(0, 30))

        # أزرار الإدارة
        buttons_frame = ttk_bs.Frame(items_frame)
        buttons_frame.pack(pady=20)

        # إنشاء شبكة من الأزرار
        button_grid = ttk_bs.Frame(buttons_frame)
        button_grid.pack()

        # الصف الأول
        row1 = ttk_bs.Frame(button_grid)
        row1.pack(pady=10)

        ttk_bs.Button(
            row1,
            text="🏪 إدارة الطبليات والعناصر",
            command=self.open_items_window,
            bootstyle=PRIMARY,
            width=30
        ).pack(side=LEFT, padx=10)

        ttk_bs.Button(
            row1,
            text="➕ إضافة طبلية جديدة",
            command=self.add_new_item,
            bootstyle=SUCCESS,
            width=30
        ).pack(side=LEFT, padx=10)

        # الصف الثاني
        row2 = ttk_bs.Frame(button_grid)
        row2.pack(pady=10)

        ttk_bs.Button(
            row2,
            text="⚙️ إدارة أنواع الأنشطة",
            command=self.manage_activity_types,
            bootstyle=INFO,
            width=30
        ).pack(side=LEFT, padx=10)

        ttk_bs.Button(
            row2,
            text="📋 العناصر المؤجرة",
            command=self.view_rented_items,
            bootstyle=WARNING,
            width=30
        ).pack(side=LEFT, padx=10)

        # الصف الثالث - نظام العيون الجديد
        row3 = ttk_bs.Frame(button_grid)
        row3.pack(pady=15)

        # زر مميز لنظام العيون
        eyes_button = ttk_bs.Button(
            row3,
            text="👁️ إدارة العيون (نظام الطبليات الجديد)",
            command=self.open_eyes_management,
            bootstyle=DANGER,
            width=62
        )
        eyes_button.pack()

        # ملخص الطبليات والعناصر
        summary_frame = ttk_bs.LabelFrame(items_frame, text="ملخص الطبليات والعناصر", padding=10)
        summary_frame.pack(fill=X, pady=20)

        # سيتم إضافة إحصائيات الطبليات هنا لاحقاً
        summary_label = ttk_bs.Label(
            summary_frame,
            text="سيتم عرض ملخص الطبليات والعناصر هنا",
            font=("Arial", 12)
        )
        summary_label.pack()
    
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        reports_frame = ttk_bs.Frame(self.notebook, padding=10)
        self.notebook.add(reports_frame, text="التقارير")

        # عنوان القسم
        title_label = ttk_bs.Label(
            reports_frame,
            text="التقارير والإحصائيات المتقدمة",
            font=("Arial", 14, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 20))

        # وصف القسم
        desc_label = ttk_bs.Label(
            reports_frame,
            text="تقارير شاملة للموردين والعقود والعيون مع إحصائيات الأقسام",
            font=("Arial", 11),
            bootstyle=SECONDARY
        )
        desc_label.pack(pady=(0, 30))

        # أزرار التقارير
        buttons_frame = ttk_bs.Frame(reports_frame)
        buttons_frame.pack(pady=20)

        # إنشاء شبكة من الأزرار
        button_grid = ttk_bs.Frame(buttons_frame)
        button_grid.pack()

        # الصف الأول
        row1 = ttk_bs.Frame(button_grid)
        row1.pack(pady=10)

        ttk_bs.Button(
            row1,
            text="📊 مركز التقارير",
            command=self.open_reports_window,
            bootstyle=PRIMARY,
            width=25
        ).pack(side=LEFT, padx=10)

        ttk_bs.Button(
            row1,
            text="👥 تقرير الموردين",
            command=self.quick_suppliers_report,
            bootstyle=SUCCESS,
            width=25
        ).pack(side=LEFT, padx=10)

        # الصف الثاني
        row2 = ttk_bs.Frame(button_grid)
        row2.pack(pady=10)

        ttk_bs.Button(
            row2,
            text="📄 تقرير العقود النشطة",
            command=self.quick_active_contracts_report,
            bootstyle=INFO,
            width=25
        ).pack(side=LEFT, padx=10)

        ttk_bs.Button(
            row2,
            text="💰 التقرير المالي الشهري",
            command=self.quick_monthly_financial_report,
            bootstyle=WARNING,
            width=25
        ).pack(side=LEFT, padx=10)

        # الصف الثالث - تقارير العيون الجديدة
        row3 = ttk_bs.Frame(button_grid)
        row3.pack(pady=15)

        ttk_bs.Button(
            row3,
            text="👁️ تقرير العيون والأقسام",
            command=self.eyes_reports,
            bootstyle=DANGER,
            width=52
        ).pack()

        # ملخص سريع
        summary_frame = ttk_bs.LabelFrame(reports_frame, text="إحصائيات سريعة", padding=15)
        summary_frame.pack(fill=X, pady=20)

        # سيتم إضافة إحصائيات سريعة هنا لاحقاً
        self.quick_stats_label = ttk_bs.Label(
            summary_frame,
            text="سيتم عرض الإحصائيات السريعة للموردين والعقود والعيون هنا",
            font=("Arial", 11),
            bootstyle=SECONDARY
        )
        self.quick_stats_label.pack()

        # تحديث الإحصائيات السريعة
        self.update_quick_stats()
    
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = ttk_bs.Frame(parent)
        status_frame.pack(fill=X, side=BOTTOM, pady=(10, 0))
        
        self.status_label = ttk_bs.Label(
            status_frame,
            text="جاهز",
            relief=SUNKEN,
            anchor=W
        )
        self.status_label.pack(fill=X, side=LEFT)
        
        # تاريخ ووقت
        import datetime
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
        time_label = ttk_bs.Label(
            status_frame,
            text=current_time,
            relief=SUNKEN,
            anchor=E
        )
        time_label.pack(side=RIGHT, padx=(10, 0))

    # دوال القوائم
    def new_file(self):
        """إنشاء ملف جديد"""
        messagebox.showinfo("جديد", "سيتم تطوير هذه الميزة قريباً")

    def open_file(self):
        """فتح ملف"""
        messagebox.showinfo("فتح", "سيتم تطوير هذه الميزة قريباً")

    def save_file(self):
        """حفظ الملف"""
        messagebox.showinfo("حفظ", "سيتم تطوير هذه الميزة قريباً")

    def exit_app(self):
        """الخروج من التطبيق"""
        if messagebox.askokcancel("خروج", "هل تريد الخروج من التطبيق؟"):
            self.root.quit()

    def backup_data(self):
        """نسخ احتياطي للبيانات"""
        messagebox.showinfo("نسخ احتياطي", "سيتم تطوير هذه الميزة قريباً")

    def restore_data(self):
        """استعادة البيانات"""
        messagebox.showinfo("استعادة", "سيتم تطوير هذه الميزة قريباً")

    def suppliers_report(self):
        """تقرير الموردين"""
        messagebox.showinfo("تقرير الموردين", "سيتم تطوير هذه الميزة قريباً")

    def contracts_report(self):
        """تقرير العقود"""
        messagebox.showinfo("تقرير العقود", "سيتم تطوير هذه الميزة قريباً")

    def rentals_report(self):
        """تقرير الإيجارات"""
        messagebox.showinfo("تقرير الإيجارات", "سيتم تطوير هذه الميزة قريباً")

    def user_guide(self):
        """دليل المستخدم"""
        messagebox.showinfo("دليل المستخدم", "سيتم تطوير هذه الميزة قريباً")

    def about(self):
        """حول البرنامج"""
        about_text = """
نظام إدارة عقود الموردين
جمعية المنقف

الإصدار: 1.0
تاريخ الإصدار: 2025

تم تطوير هذا النظام لإدارة عقود الموردين
في السوق المركزي لجمعية المنقف
        """
        messagebox.showinfo("حول البرنامج", about_text)

    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def open_suppliers_window(self):
        """فتح نافذة إدارة الموردين"""
        try:
            from gui.suppliers_window import SuppliersWindow
            SuppliersWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة الموردين:\n{str(e)}")

    def add_new_supplier(self):
        """إضافة مورد جديد"""
        self.open_suppliers_window()

    def open_contracts_window(self):
        """فتح نافذة إدارة العقود"""
        try:
            from gui.contracts_window import ContractsWindow
            ContractsWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة العقود:\n{str(e)}")

    def add_new_contract(self):
        """إضافة عقد جديد"""
        self.open_contracts_window()

    def view_expiring_contracts(self):
        """عرض العقود المنتهية قريباً"""
        try:
            from models.contract import Contract
            contract_model = Contract(self.db_manager)
            expiring_contracts = contract_model.get_expiring_contracts(30)

            if expiring_contracts:
                message = "العقود المنتهية خلال 30 يوماً:\n\n"
                for contract in expiring_contracts:
                    message += f"• {contract['contract_number']} - {contract['supplier_name']} (ينتهي: {contract['end_date']})\n"
                messagebox.showinfo("العقود المنتهية قريباً", message)
            else:
                messagebox.showinfo("العقود المنتهية قريباً", "لا توجد عقود منتهية خلال الـ 30 يوماً القادمة")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل العقود المنتهية:\n{str(e)}")

    def open_items_window(self):
        """فتح نافذة إدارة الطبليات والعناصر"""
        try:
            from gui.items_window import ItemsWindow
            ItemsWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة الطبليات والعناصر:\n{str(e)}")

    def add_new_item(self):
        """إضافة طبلية جديدة"""
        self.open_items_window()

    def manage_activity_types(self):
        """إدارة أنواع الأنشطة"""
        self.open_items_window()

    def view_rented_items(self):
        """عرض العناصر المؤجرة"""
        try:
            from models.rental_item import RentalItem
            rental_item_model = RentalItem(self.db_manager)
            rented_items = rental_item_model.get_rented_items()

            if rented_items:
                message = "العناصر المؤجرة حالياً:\n\n"
                for item in rented_items:
                    message += f"• {item['item_name']} ({item['item_type']}) - {item['supplier_name']}\n"
                    message += f"  العقد: {item['contract_number']} (من {item['start_date']} إلى {item['end_date']})\n\n"
                messagebox.showinfo("العناصر المؤجرة", message)
            else:
                messagebox.showinfo("العناصر المؤجرة", "لا توجد عناصر مؤجرة حالياً")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل العناصر المؤجرة:\n{str(e)}")

    def open_eyes_management(self):
        """فتح نافذة إدارة العيون"""
        try:
            from gui.eyes_management_window import EyesManagementWindow
            EyesManagementWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة إدارة العيون:\n{str(e)}")

    def search_suppliers(self):
        """البحث في الموردين"""
        try:
            from gui.suppliers_window import SuppliersWindow
            suppliers_window = SuppliersWindow(self.root, self.db_manager)
            # التركيز على حقل البحث
            messagebox.showinfo("البحث", "استخدم نافذة إدارة الموردين للبحث والفلترة")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح البحث:\n{str(e)}")

    def suppliers_statistics(self):
        """إحصائيات الموردين"""
        try:
            from models.supplier import Supplier
            supplier_model = Supplier(self.db_manager)

            stats_text = "إحصائيات الموردين حسب الأقسام:\n\n"
            for category in ['بهارات', 'استهلاكي', 'أجبان']:
                suppliers = supplier_model.get_suppliers_by_category(category)
                active_suppliers = [s for s in suppliers if s['status'] == 'نشط']
                stats_text += f"📂 {category}:\n"
                stats_text += f"   إجمالي الموردين: {len(suppliers)}\n"
                stats_text += f"   الموردين النشطين: {len(active_suppliers)}\n\n"

            messagebox.showinfo("إحصائيات الموردين", stats_text)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض الإحصائيات:\n{str(e)}")

    def search_contracts(self):
        """البحث في العقود"""
        try:
            from gui.contracts_window import ContractsWindow
            ContractsWindow(self.root, self.db_manager)
            messagebox.showinfo("البحث", "استخدم نافذة إدارة العقود للبحث والفلترة")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح البحث:\n{str(e)}")

    def contracts_reports(self):
        """تقارير العقود"""
        try:
            from gui.reports_window import ReportsWindow
            ReportsWindow(self.root, self.db_manager)
            messagebox.showinfo("التقارير", "استخدم مركز التقارير لتوليد تقارير العقود المختلفة")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح التقارير:\n{str(e)}")

    def eyes_reports(self):
        """تقارير العيون والأقسام"""
        try:
            from models.rented_eye import RentedEye
            rented_eye_model = RentedEye(self.db_manager)

            # إحصائيات العيون
            summary = rented_eye_model.get_rental_summary_by_category()

            if summary:
                report_text = "تقرير العيون المؤجرة حسب الأقسام:\n\n"
                total_eyes = 0
                total_revenue = 0

                for category, stats in summary.items():
                    report_text += f"📂 قسم {category}:\n"
                    report_text += f"   العيون المؤجرة: {stats['total_rented_eyes']}\n"
                    report_text += f"   الإيرادات الشهرية: {stats['total_revenue']:.2f} د.ك\n"
                    report_text += f"   الموردين النشطين: {stats['active_suppliers']}\n\n"

                    total_eyes += stats['total_rented_eyes']
                    total_revenue += stats['total_revenue']

                report_text += f"📊 الإجمالي:\n"
                report_text += f"   إجمالي العيون المؤجرة: {total_eyes}\n"
                report_text += f"   إجمالي الإيرادات الشهرية: {total_revenue:.2f} د.ك\n"
                report_text += f"   الإيرادات السنوية المتوقعة: {total_revenue * 12:.2f} د.ك"

                messagebox.showinfo("تقرير العيون والأقسام", report_text)
            else:
                messagebox.showinfo("تقرير العيون", "لا توجد عيون مؤجرة حالياً")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في توليد تقرير العيون:\n{str(e)}")

    def open_reports_window(self):
        """فتح نافذة التقارير"""
        try:
            from gui.reports_window import ReportsWindow
            ReportsWindow(self.root, self.db_manager)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التقارير:\n{str(e)}")

    def quick_suppliers_report(self):
        """تقرير سريع للموردين"""
        try:
            from reports.report_generator import ReportGenerator
            report_gen = ReportGenerator(self.db_manager)
            filepath = report_gen.generate_suppliers_report("excel")
            messagebox.showinfo("تم التوليد", f"تم توليد تقرير الموردين:\n{filepath}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في توليد التقرير:\n{str(e)}")

    def quick_active_contracts_report(self):
        """تقرير سريع للعقود النشطة"""
        try:
            from reports.report_generator import ReportGenerator
            report_gen = ReportGenerator(self.db_manager)
            filepath = report_gen.generate_contracts_report(status="نشط", output_format="excel")
            messagebox.showinfo("تم التوليد", f"تم توليد تقرير العقود النشطة:\n{filepath}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في توليد التقرير:\n{str(e)}")

    def quick_monthly_financial_report(self):
        """تقرير مالي شهري سريع"""
        try:
            from reports.report_generator import ReportGenerator
            report_gen = ReportGenerator(self.db_manager)
            current_year = datetime.now().year
            filepath = report_gen.generate_financial_summary_report(current_year, "excel")
            messagebox.showinfo("تم التوليد", f"تم توليد التقرير المالي لعام {current_year}:\n{filepath}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في توليد التقرير:\n{str(e)}")

    def update_quick_stats(self):
        """تحديث الإحصائيات السريعة"""
        try:
            # إحصائيات الموردين
            suppliers_count = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM suppliers WHERE status = 'نشط'")
            suppliers_total = suppliers_count['count'] if suppliers_count else 0

            # إحصائيات العقود
            contracts_count = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM contracts WHERE contract_status = 'نشط'")
            contracts_total = contracts_count['count'] if contracts_count else 0

            # إحصائيات العناصر
            items_count = self.db_manager.fetch_one("SELECT COUNT(*) as count FROM rental_items WHERE status = 'مؤجر'")
            rented_items = items_count['count'] if items_count else 0

            # إجمالي الإيرادات الشهرية
            current_month = datetime.now().strftime('%Y-%m')
            revenue_query = """
            SELECT SUM(total_amount) as revenue
            FROM contracts
            WHERE strftime('%Y-%m', start_date) = ?
            """
            revenue_result = self.db_manager.fetch_one(revenue_query, (current_month,))
            monthly_revenue = revenue_result['revenue'] if revenue_result and revenue_result['revenue'] else 0

            # تحديث النص
            stats_text = f"الموردين النشطين: {suppliers_total}\n"
            stats_text += f"العقود النشطة: {contracts_total}\n"
            stats_text += f"العناصر المؤجرة: {rented_items}\n"
            stats_text += f"إيرادات الشهر الحالي: {monthly_revenue:.2f} د.ك"

            self.quick_stats_label.config(text=stats_text)

        except Exception as e:
            self.quick_stats_label.config(text=f"خطأ في تحميل الإحصائيات: {str(e)}")

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()
