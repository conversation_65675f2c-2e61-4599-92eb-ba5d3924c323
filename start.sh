#!/bin/bash

# =====================================================
# سكريبت التشغيل السريع لنظام إدارة عقود الموردين والإيجارات
# جمعية المنقف التعاونية
# =====================================================

set -e  # إيقاف التنفيذ عند حدوث خطأ

# الألوان للرسائل
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# رموز للرسائل
SUCCESS="✅"
ERROR="❌"
WARNING="⚠️"
INFO="ℹ️"
ROCKET="🚀"
GEAR="⚙️"

# دالة طباعة الرسائل الملونة
print_message() {
    local color=$1
    local icon=$2
    local message=$3
    echo -e "${color}${icon} ${message}${NC}"
}

print_header() {
    echo -e "${CYAN}"
    echo "=============================================="
    echo "  نظام إدارة عقود الموردين والإيجارات"
    echo "        جمعية المنقف التعاونية"
    echo "=============================================="
    echo -e "${NC}"
}

print_separator() {
    echo -e "${BLUE}----------------------------------------------${NC}"
}

# دالة التحقق من وجود الأوامر المطلوبة
check_requirements() {
    print_message $BLUE $GEAR "التحقق من المتطلبات..."
    
    local missing_requirements=()
    
    # التحقق من Node.js
    if ! command -v node &> /dev/null; then
        missing_requirements+=("Node.js")
    else
        local node_version=$(node --version | cut -d'v' -f2)
        local required_version="18.0.0"
        if ! printf '%s\n%s\n' "$required_version" "$node_version" | sort -V -C; then
            print_message $WARNING $WARNING "إصدار Node.js ($node_version) أقل من المطلوب ($required_version)"
        else
            print_message $GREEN $SUCCESS "Node.js: $node_version"
        fi
    fi
    
    # التحقق من npm
    if ! command -v npm &> /dev/null; then
        missing_requirements+=("npm")
    else
        local npm_version=$(npm --version)
        print_message $GREEN $SUCCESS "npm: $npm_version"
    fi
    
    # التحقق من MySQL
    if ! command -v mysql &> /dev/null; then
        missing_requirements+=("MySQL")
    else
        print_message $GREEN $SUCCESS "MySQL متوفر"
    fi
    
    # التحقق من Docker (اختياري)
    if command -v docker &> /dev/null; then
        local docker_version=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
        print_message $GREEN $SUCCESS "Docker: $docker_version"
    else
        print_message $YELLOW $INFO "Docker غير متوفر (اختياري)"
    fi
    
    # التحقق من Docker Compose (اختياري)
    if command -v docker-compose &> /dev/null; then
        local compose_version=$(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1)
        print_message $GREEN $SUCCESS "Docker Compose: $compose_version"
    else
        print_message $YELLOW $INFO "Docker Compose غير متوفر (اختياري)"
    fi
    
    # إذا كانت هناك متطلبات مفقودة
    if [ ${#missing_requirements[@]} -ne 0 ]; then
        print_message $RED $ERROR "المتطلبات التالية مفقودة:"
        for req in "${missing_requirements[@]}"; do
            echo -e "  ${RED}- $req${NC}"
        done
        echo ""
        print_message $YELLOW $INFO "يرجى تثبيت المتطلبات المفقودة قبل المتابعة"
        exit 1
    fi
    
    print_message $GREEN $SUCCESS "جميع المتطلبات متوفرة!"
}

# دالة إنشاء ملف .env إذا لم يكن موجوداً
setup_env() {
    if [ ! -f .env ]; then
        print_message $YELLOW $INFO "إنشاء ملف .env من القالب..."
        cp .env.example .env
        print_message $GREEN $SUCCESS "تم إنشاء ملف .env"
        print_message $YELLOW $WARNING "يرجى تحديث إعدادات قاعدة البيانات في ملف .env"
    else
        print_message $GREEN $SUCCESS "ملف .env موجود"
    fi
}

# دالة تثبيت التبعيات
install_dependencies() {
    print_message $BLUE $GEAR "تثبيت التبعيات..."
    
    if [ -f package-lock.json ]; then
        npm ci
    else
        npm install
    fi
    
    print_message $GREEN $SUCCESS "تم تثبيت التبعيات بنجاح"
}

# دالة إعداد قاعدة البيانات
setup_database() {
    print_message $BLUE $GEAR "إعداد قاعدة البيانات..."
    
    # قراءة إعدادات قاعدة البيانات من .env
    if [ -f .env ]; then
        source .env
    fi
    
    # التحقق من الاتصال بقاعدة البيانات
    if mysql -h"${DB_HOST:-localhost}" -P"${DB_PORT:-3306}" -u"${DB_USER:-root}" -p"${DB_PASSWORD}" -e "SELECT 1;" &> /dev/null; then
        print_message $GREEN $SUCCESS "الاتصال بقاعدة البيانات ناجح"
        
        # تنفيذ schema.sql إذا كان موجوداً
        if [ -f database/schema.sql ]; then
            print_message $BLUE $GEAR "إنشاء هيكل قاعدة البيانات..."
            mysql -h"${DB_HOST:-localhost}" -P"${DB_PORT:-3306}" -u"${DB_USER:-root}" -p"${DB_PASSWORD}" < database/schema.sql
            print_message $GREEN $SUCCESS "تم إنشاء هيكل قاعدة البيانات"
        fi
    else
        print_message $RED $ERROR "فشل في الاتصال بقاعدة البيانات"
        print_message $YELLOW $INFO "يرجى التحقق من إعدادات قاعدة البيانات في ملف .env"
        return 1
    fi
}

# دالة إنشاء المجلدات المطلوبة
create_directories() {
    print_message $BLUE $GEAR "إنشاء المجلدات المطلوبة..."
    
    local directories=(
        "backend/logs"
        "backend/uploads"
        "backend/uploads/contracts"
        "backend/uploads/documents"
        "backend/uploads/images"
        "data"
        "data/mysql"
        "data/redis"
        "data/uploads"
        "data/logs"
        "data/backups"
        "data/nginx-logs"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_message $GREEN $SUCCESS "تم إنشاء المجلد: $dir"
        fi
    done
}

# دالة عرض قائمة الخيارات
show_menu() {
    print_separator
    echo -e "${CYAN}اختر طريقة التشغيل:${NC}"
    echo ""
    echo -e "${GREEN}1)${NC} تشغيل عادي (Node.js مباشرة)"
    echo -e "${GREEN}2)${NC} تشغيل للتطوير (مع nodemon)"
    echo -e "${GREEN}3)${NC} تشغيل باستخدام Docker"
    echo -e "${GREEN}4)${NC} تشغيل باستخدام Docker مع المراقبة"
    echo -e "${GREEN}5)${NC} إعداد أولي فقط"
    echo -e "${GREEN}6)${NC} عرض السجلات"
    echo -e "${GREEN}7)${NC} إيقاف الخدمات"
    echo -e "${GREEN}0)${NC} خروج"
    echo ""
    print_separator
}

# دالة تشغيل النظام عادي
start_normal() {
    print_message $BLUE $ROCKET "تشغيل النظام..."
    npm start
}

# دالة تشغيل النظام للتطوير
start_dev() {
    print_message $BLUE $ROCKET "تشغيل النظام في وضع التطوير..."
    npm run dev
}

# دالة تشغيل النظام باستخدام Docker
start_docker() {
    print_message $BLUE $ROCKET "تشغيل النظام باستخدام Docker..."
    
    if ! command -v docker-compose &> /dev/null; then
        print_message $RED $ERROR "Docker Compose غير متوفر"
        return 1
    fi
    
    docker-compose up -d
    
    print_message $GREEN $SUCCESS "تم تشغيل النظام باستخدام Docker"
    print_message $CYAN $INFO "يمكنك الوصول للنظام على: http://localhost:3000"
    print_message $CYAN $INFO "لعرض السجلات: docker-compose logs -f app"
}

# دالة تشغيل النظام باستخدام Docker مع المراقبة
start_docker_monitoring() {
    print_message $BLUE $ROCKET "تشغيل النظام باستخدام Docker مع المراقبة..."
    
    if ! command -v docker-compose &> /dev/null; then
        print_message $RED $ERROR "Docker Compose غير متوفر"
        return 1
    fi
    
    docker-compose --profile monitoring up -d
    
    print_message $GREEN $SUCCESS "تم تشغيل النظام مع خدمات المراقبة"
    print_message $CYAN $INFO "النظام: http://localhost:3000"
    print_message $CYAN $INFO "Prometheus: http://localhost:9090"
    print_message $CYAN $INFO "Grafana: http://localhost:3001"
}

# دالة عرض السجلات
show_logs() {
    print_separator
    echo -e "${CYAN}اختر نوع السجلات:${NC}"
    echo ""
    echo -e "${GREEN}1)${NC} سجلات التطبيق (ملفات)"
    echo -e "${GREEN}2)${NC} سجلات Docker"
    echo -e "${GREEN}3)${NC} سجلات قاعدة البيانات"
    echo ""
    read -p "اختر رقم الخيار: " log_choice
    
    case $log_choice in
        1)
            if [ -f backend/logs/combined.log ]; then
                tail -f backend/logs/combined.log
            else
                print_message $YELLOW $WARNING "ملف السجلات غير موجود"
            fi
            ;;
        2)
            if command -v docker-compose &> /dev/null; then
                docker-compose logs -f app
            else
                print_message $RED $ERROR "Docker Compose غير متوفر"
            fi
            ;;
        3)
            if command -v docker-compose &> /dev/null; then
                docker-compose logs -f database
            else
                print_message $RED $ERROR "Docker Compose غير متوفر"
            fi
            ;;
        *)
            print_message $RED $ERROR "خيار غير صحيح"
            ;;
    esac
}

# دالة إيقاف الخدمات
stop_services() {
    print_message $BLUE $GEAR "إيقاف الخدمات..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose down
        print_message $GREEN $SUCCESS "تم إيقاف خدمات Docker"
    fi
    
    # إيقاف العمليات التي تستخدم المنفذ 3000
    if command -v lsof &> /dev/null; then
        local pid=$(lsof -ti:3000)
        if [ ! -z "$pid" ]; then
            kill -9 $pid
            print_message $GREEN $SUCCESS "تم إيقاف العملية على المنفذ 3000"
        fi
    fi
}

# دالة الإعداد الأولي
initial_setup() {
    print_message $BLUE $GEAR "بدء الإعداد الأولي..."
    
    check_requirements
    setup_env
    create_directories
    install_dependencies
    
    # محاولة إعداد قاعدة البيانات
    if setup_database; then
        print_message $GREEN $SUCCESS "تم الإعداد الأولي بنجاح!"
    else
        print_message $YELLOW $WARNING "تم الإعداد الأولي مع تحذيرات"
        print_message $CYAN $INFO "يرجى إعداد قاعدة البيانات يدوياً"
    fi
}

# الدالة الرئيسية
main() {
    print_header
    
    # التحقق من وجود ملف package.json
    if [ ! -f package.json ]; then
        print_message $RED $ERROR "ملف package.json غير موجود"
        print_message $CYAN $INFO "تأكد من تشغيل السكريبت من المجلد الجذر للمشروع"
        exit 1
    fi
    
    while true; do
        show_menu
        read -p "اختر رقم الخيار: " choice
        
        case $choice in
            1)
                initial_setup
                start_normal
                ;;
            2)
                initial_setup
                start_dev
                ;;
            3)
                create_directories
                start_docker
                ;;
            4)
                create_directories
                start_docker_monitoring
                ;;
            5)
                initial_setup
                ;;
            6)
                show_logs
                ;;
            7)
                stop_services
                ;;
            0)
                print_message $CYAN $INFO "شكراً لاستخدام النظام!"
                exit 0
                ;;
            *)
                print_message $RED $ERROR "خيار غير صحيح، يرجى المحاولة مرة أخرى"
                ;;
        esac
        
        echo ""
        read -p "اضغط Enter للمتابعة..."
        clear
        print_header
    done
}

# تشغيل الدالة الرئيسية
main "$@"
