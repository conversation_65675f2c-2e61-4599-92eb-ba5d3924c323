#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة العقود
Contracts Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime, date
import json
import os
from models.contract import Contract
from models.supplier import Supplier
from models.rental_item import RentalItem
from models.activity_type import ActivityType

class ContractsWindow:
    """نافذة إدارة العقود"""
    
    def __init__(self, parent, db_manager):
        """
        تهيئة نافذة العقود
        
        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
        """
        self.parent = parent
        self.db_manager = db_manager
        self.contract_model = Contract(db_manager)
        self.supplier_model = Supplier(db_manager)
        self.rental_item_model = RentalItem(db_manager)
        self.activity_type_model = ActivityType(db_manager)
        
        # إنشاء النافذة
        self.window = ttk_bs.Toplevel(parent)
        self.window.title("إدارة العقود")
        self.window.geometry("1200x800")
        self.window.resizable(True, True)
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_contracts()
        self.load_suppliers()
        
        # تعيين النافذة في المقدمة
        self.window.transient(parent)
        self.window.grab_set()
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        # متغيرات العقد
        self.contract_id = tk.IntVar()
        self.supplier_id = tk.IntVar()
        self.contract_number = tk.StringVar()
        self.start_date = tk.StringVar()
        self.end_date = tk.StringVar()
        self.total_amount = tk.DoubleVar()
        self.payment_terms = tk.StringVar()
        self.contract_status = tk.StringVar(value="نشط")
        self.terms_conditions = tk.StringVar()
        self.notes = tk.StringVar()
        self.search_term = tk.StringVar()
        
        # قوائم البيانات
        self.suppliers_list = []
        self.rental_items_list = []
        self.activity_types_list = []

        # إعدادات الحفظ المؤقت
        self.drafts_dir = "contract_drafts"
        self.ensure_drafts_directory()

    def ensure_drafts_directory(self):
        """التأكد من وجود مجلد المسودات"""
        if not os.path.exists(self.drafts_dir):
            os.makedirs(self.drafts_dir)

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=10)
        main_frame.pack(fill=BOTH, expand=True)
        
        # عنوان النافذة
        title_label = ttk_bs.Label(
            main_frame,
            text="إدارة العقود",
            font=("Arial", 16, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 20))
        
        # إنشاء التبويبات
        notebook = ttk_bs.Notebook(main_frame)
        notebook.pack(fill=BOTH, expand=True)
        
        # تبويب قائمة العقود
        self.create_contracts_list_tab(notebook)
        
        # تبويب إضافة/تعديل عقد
        self.create_contract_form_tab(notebook)
        
        # تبويب عناصر العقد
        self.create_contract_items_tab(notebook)
    
    def create_contracts_list_tab(self, notebook):
        """إنشاء تبويب قائمة العقود"""
        list_frame = ttk_bs.Frame(notebook, padding=10)
        notebook.add(list_frame, text="قائمة العقود")
        
        # إطار البحث والفلترة
        search_frame = ttk_bs.Frame(list_frame)
        search_frame.pack(fill=X, pady=(0, 10))
        
        # البحث الديناميكي
        ttk_bs.Label(search_frame, text="🔍 بحث:").pack(side=LEFT, padx=(0, 5))
        search_entry = ttk_bs.Entry(
            search_frame,
            textvariable=self.search_term,
            width=40
        )
        search_entry.pack(side=LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.on_dynamic_search)
        search_entry.insert(0, "ابحث باسم المورد أو رقم العقد أو المبلغ...")
        search_entry.bind('<FocusIn>', self.on_search_focus_in)
        search_entry.bind('<FocusOut>', self.on_search_focus_out)

        # حفظ مرجع لحقل البحث
        self.search_entry = search_entry

        ttk_bs.Button(
            search_frame,
            text="🔄 تحديث",
            command=self.load_contracts,
            bootstyle=INFO
        ).pack(side=LEFT, padx=(0, 10))
        
        # فلتر الحالة
        ttk_bs.Label(search_frame, text="📊 الحالة:").pack(side=LEFT, padx=(20, 5))
        self.status_filter = ttk_bs.Combobox(
            search_frame,
            values=["الكل", "نشط", "منتهي", "ملغي", "معلق"],
            state="readonly",
            width=15
        )
        self.status_filter.pack(side=LEFT, padx=(0, 10))
        self.status_filter.set("الكل")
        self.status_filter.bind('<<ComboboxSelected>>', self.on_status_filter_change)

        # عداد النتائج
        self.results_count_label = ttk_bs.Label(
            search_frame,
            text="",
            font=("Arial", 10),
            bootstyle=SECONDARY
        )
        self.results_count_label.pack(side=RIGHT, padx=(10, 0))

        # الصف الثاني - الفلاتر المتقدمة
        advanced_filters_frame = ttk_bs.Frame(list_frame)
        advanced_filters_frame.pack(fill=X, pady=(0, 10))

        # فلتر نوع العقد
        ttk_bs.Label(advanced_filters_frame, text="📋 نوع العقد:").pack(side=LEFT, padx=(0, 5))
        self.contract_type_filter = ttk_bs.Combobox(
            advanced_filters_frame,
            values=["كل الأنواع", "توريد", "إيجار", "خدمات", "صيانة"],
            state="readonly",
            width=15
        )
        self.contract_type_filter.pack(side=LEFT, padx=(0, 15))
        self.contract_type_filter.set("كل الأنواع")
        self.contract_type_filter.bind('<<ComboboxSelected>>', self.on_advanced_filter_change)

        # فلتر تاريخ البداية
        ttk_bs.Label(advanced_filters_frame, text="📅 من تاريخ:").pack(side=LEFT, padx=(0, 5))
        self.start_date_filter = ttk_bs.DateEntry(
            advanced_filters_frame,
            width=12,
            dateformat='%Y-%m-%d'
        )
        self.start_date_filter.pack(side=LEFT, padx=(0, 15))
        self.start_date_filter.bind('<<DateEntrySelected>>', self.on_advanced_filter_change)

        # فلتر تاريخ النهاية
        ttk_bs.Label(advanced_filters_frame, text="📅 إلى تاريخ:").pack(side=LEFT, padx=(0, 5))
        self.end_date_filter = ttk_bs.DateEntry(
            advanced_filters_frame,
            width=12,
            dateformat='%Y-%m-%d'
        )
        self.end_date_filter.pack(side=LEFT, padx=(0, 15))
        self.end_date_filter.bind('<<DateEntrySelected>>', self.on_advanced_filter_change)

        # فلتر المبلغ
        ttk_bs.Label(advanced_filters_frame, text="💰 المبلغ من:").pack(side=LEFT, padx=(0, 5))
        self.min_amount_var = tk.DoubleVar()
        min_amount_entry = ttk_bs.Entry(
            advanced_filters_frame,
            textvariable=self.min_amount_var,
            width=10
        )
        min_amount_entry.pack(side=LEFT, padx=(0, 5))
        min_amount_entry.bind('<KeyRelease>', self.on_amount_filter_change)

        ttk_bs.Label(advanced_filters_frame, text="إلى:").pack(side=LEFT, padx=(0, 5))
        self.max_amount_var = tk.DoubleVar()
        max_amount_entry = ttk_bs.Entry(
            advanced_filters_frame,
            textvariable=self.max_amount_var,
            width=10
        )
        max_amount_entry.pack(side=LEFT, padx=(0, 15))
        max_amount_entry.bind('<KeyRelease>', self.on_amount_filter_change)

        # أزرار الفلاتر
        ttk_bs.Button(
            advanced_filters_frame,
            text="🔍 تطبيق الفلاتر",
            command=self.apply_advanced_filters,
            bootstyle=PRIMARY
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bs.Button(
            advanced_filters_frame,
            text="🗑️ مسح الفلاتر",
            command=self.clear_filters,
            bootstyle=SECONDARY
        ).pack(side=LEFT, padx=(0, 10))
        
        # جدول العقود
        self.create_contracts_table(list_frame)
        
        # أزرار العمليات
        buttons_frame = ttk_bs.Frame(list_frame)
        buttons_frame.pack(fill=X, pady=(10, 0))
        
        ttk_bs.Button(
            buttons_frame,
            text="تعديل",
            command=self.edit_contract,
            bootstyle=WARNING
        ).pack(side=LEFT, padx=(0, 10))
        
        ttk_bs.Button(
            buttons_frame,
            text="حذف",
            command=self.delete_contract,
            bootstyle=DANGER
        ).pack(side=LEFT, padx=(0, 10))
        
        ttk_bs.Button(
            buttons_frame,
            text="عرض التفاصيل",
            command=self.view_contract_details,
            bootstyle=INFO
        ).pack(side=LEFT, padx=(0, 10))
        
        ttk_bs.Button(
            buttons_frame,
            text="طباعة العقد",
            command=self.print_contract,
            bootstyle=SECONDARY
        ).pack(side=LEFT)
    
    def create_contracts_table(self, parent):
        """إنشاء جدول العقود"""
        # إطار الجدول
        table_frame = ttk_bs.Frame(parent)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ('ID', 'رقم العقد', 'المورد', 'تاريخ البداية', 'تاريخ النهاية', 'المبلغ الإجمالي', 'الحالة')
        self.contracts_tree = ttk_bs.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=15
        )
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.contracts_tree.heading(col, text=col)
            if col == 'ID':
                self.contracts_tree.column(col, width=50, anchor=CENTER)
            elif col == 'رقم العقد':
                self.contracts_tree.column(col, width=120, anchor=CENTER)
            elif col == 'المورد':
                self.contracts_tree.column(col, width=200, anchor=W)
            elif col == 'تاريخ البداية':
                self.contracts_tree.column(col, width=100, anchor=CENTER)
            elif col == 'تاريخ النهاية':
                self.contracts_tree.column(col, width=100, anchor=CENTER)
            elif col == 'المبلغ الإجمالي':
                self.contracts_tree.column(col, width=120, anchor=E)
            elif col == 'الحالة':
                self.contracts_tree.column(col, width=80, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.contracts_tree.yview)
        self.contracts_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.contracts_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط الأحداث
        self.contracts_tree.bind('<Double-1>', self.on_contract_double_click)
    
    def create_contract_form_tab(self, notebook):
        """إنشاء تبويب نموذج العقد"""
        form_frame = ttk_bs.Frame(notebook, padding=20)
        notebook.add(form_frame, text="إضافة/تعديل عقد")
        
        # إطار النموذج
        form_container = ttk_bs.Frame(form_frame)
        form_container.pack(fill=BOTH, expand=True)
        
        # الصف الأول
        row1 = ttk_bs.Frame(form_container)
        row1.pack(fill=X, pady=(0, 10))
        
        # رقم العقد
        ttk_bs.Label(row1, text="رقم العقد *:").pack(side=LEFT, padx=(0, 5))
        contract_number_entry = ttk_bs.Entry(
            row1,
            textvariable=self.contract_number,
            width=20
        )
        contract_number_entry.pack(side=LEFT, padx=(0, 20))
        
        # زر توليد رقم العقد
        ttk_bs.Button(
            row1,
            text="توليد رقم",
            command=self.generate_contract_number,
            bootstyle=INFO
        ).pack(side=LEFT, padx=(0, 20))
        
        # المورد
        ttk_bs.Label(row1, text="المورد *:").pack(side=LEFT, padx=(0, 5))
        self.supplier_combo = ttk_bs.Combobox(
            row1,
            state="readonly",
            width=30
        )
        self.supplier_combo.pack(side=LEFT)
        
        # الصف الثاني
        row2 = ttk_bs.Frame(form_container)
        row2.pack(fill=X, pady=(0, 10))
        
        # تاريخ البداية
        ttk_bs.Label(row2, text="تاريخ البداية *:").pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            row2,
            textvariable=self.start_date,
            width=20
        ).pack(side=LEFT, padx=(0, 20))
        
        # تاريخ النهاية
        ttk_bs.Label(row2, text="تاريخ النهاية *:").pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            row2,
            textvariable=self.end_date,
            width=20
        ).pack(side=LEFT, padx=(0, 20))
        
        # الحالة
        ttk_bs.Label(row2, text="الحالة:").pack(side=LEFT, padx=(0, 5))
        status_combo = ttk_bs.Combobox(
            row2,
            textvariable=self.contract_status,
            values=["نشط", "منتهي", "ملغي", "معلق"],
            state="readonly",
            width=15
        )
        status_combo.pack(side=LEFT)
        
        # الصف الثالث
        row3 = ttk_bs.Frame(form_container)
        row3.pack(fill=X, pady=(0, 10))
        
        # المبلغ الإجمالي
        ttk_bs.Label(row3, text="المبلغ الإجمالي:").pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            row3,
            textvariable=self.total_amount,
            width=20
        ).pack(side=LEFT, padx=(0, 20))
        
        # شروط الدفع
        ttk_bs.Label(row3, text="شروط الدفع:").pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            row3,
            textvariable=self.payment_terms,
            width=30
        ).pack(side=LEFT)

        # الصف الرابع - الشروط والأحكام
        row4 = ttk_bs.Frame(form_container)
        row4.pack(fill=X, pady=(0, 10))

        ttk_bs.Label(row4, text="الشروط والأحكام:").pack(side=TOP, anchor=W)
        terms_entry = ttk_bs.Entry(
            row4,
            textvariable=self.terms_conditions,
            width=80
        )
        terms_entry.pack(fill=X, pady=(5, 0))

        # الصف الخامس - الملاحظات
        row5 = ttk_bs.Frame(form_container)
        row5.pack(fill=X, pady=(0, 20))

        ttk_bs.Label(row5, text="الملاحظات:").pack(side=TOP, anchor=W)
        notes_text = tk.Text(
            row5,
            height=4,
            width=80,
            wrap=tk.WORD
        )
        notes_text.pack(fill=X, pady=(5, 0))

        # ربط النص بالمتغير
        def update_notes(*args):
            self.notes.set(notes_text.get("1.0", tk.END).strip())

        notes_text.bind('<KeyRelease>', update_notes)
        self.notes_text = notes_text

        # أزرار النموذج
        buttons_frame = ttk_bs.Frame(form_container)
        buttons_frame.pack(fill=X, pady=(10, 0))

        ttk_bs.Button(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_contract,
            bootstyle=SUCCESS
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bs.Button(
            buttons_frame,
            text="📝 حفظ مؤقت",
            command=self.save_draft,
            bootstyle=WARNING
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bs.Button(
            buttons_frame,
            text="📂 تحميل مسودة",
            command=self.load_draft,
            bootstyle=INFO
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bs.Button(
            buttons_frame,
            text="🗑️ حذف المسودات",
            command=self.delete_drafts,
            bootstyle=DANGER
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bs.Button(
            buttons_frame,
            text="🆕 جديد",
            command=self.clear_form,
            bootstyle=INFO
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.cancel_form,
            bootstyle=SECONDARY
        ).pack(side=LEFT)

    def create_contract_items_tab(self, notebook):
        """إنشاء تبويب عناصر العقد"""
        items_frame = ttk_bs.Frame(notebook, padding=10)
        notebook.add(items_frame, text="عناصر العقد")

        # سيتم تطوير هذا التبويب لاحقاً
        label = ttk_bs.Label(items_frame, text="إدارة عناصر العقد", font=("Arial", 14))
        label.pack(pady=20)

    def load_contracts(self):
        """تحميل قائمة العقود"""
        try:
            # مسح البيانات الحالية
            for item in self.contracts_tree.get_children():
                self.contracts_tree.delete(item)

            # تحميل العقود
            contracts = self.contract_model.get_all_contracts()

            for contract in contracts:
                self.contracts_tree.insert('', 'end', values=(
                    contract['contract_id'],
                    contract['contract_number'],
                    contract['supplier_name'],
                    contract['start_date'],
                    contract['end_date'],
                    f"{contract['total_amount']:.2f}" if contract['total_amount'] else "0.00",
                    contract['contract_status']
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل العقود:\n{str(e)}")

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            self.suppliers_list = self.supplier_model.get_all_suppliers("نشط")
            supplier_names = [f"{s['supplier_id']} - {s['supplier_name']}" for s in self.suppliers_list]
            self.supplier_combo['values'] = supplier_names
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الموردين:\n{str(e)}")

    def generate_contract_number(self):
        """توليد رقم عقد جديد"""
        try:
            contract_number = self.contract_model.generate_contract_number()
            self.contract_number.set(contract_number)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في توليد رقم العقد:\n{str(e)}")

    def search_contracts(self):
        """البحث في العقود"""
        search_term = self.search_term.get().strip()

        try:
            # مسح البيانات الحالية
            for item in self.contracts_tree.get_children():
                self.contracts_tree.delete(item)

            if search_term:
                contracts = self.contract_model.search_contracts(search_term)
            else:
                contracts = self.contract_model.get_all_contracts()

            for contract in contracts:
                self.contracts_tree.insert('', 'end', values=(
                    contract['contract_id'],
                    contract['contract_number'],
                    contract['supplier_name'],
                    contract['start_date'],
                    contract['end_date'],
                    f"{contract['total_amount']:.2f}" if contract['total_amount'] else "0.00",
                    contract['contract_status']
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث:\n{str(e)}")

    def on_search(self, event):
        """البحث عند الكتابة"""
        self.window.after(500, self.search_contracts)

    def on_status_filter_change(self, event):
        """تغيير فلتر الحالة"""
        status = self.status_filter.get()

        try:
            # مسح البيانات الحالية
            for item in self.contracts_tree.get_children():
                self.contracts_tree.delete(item)

            if status == "الكل":
                contracts = self.contract_model.get_all_contracts()
            else:
                contracts = self.contract_model.get_all_contracts(status)

            for contract in contracts:
                self.contracts_tree.insert('', 'end', values=(
                    contract['contract_id'],
                    contract['contract_number'],
                    contract['supplier_name'],
                    contract['start_date'],
                    contract['end_date'],
                    f"{contract['total_amount']:.2f}" if contract['total_amount'] else "0.00",
                    contract['contract_status']
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تطبيق الفلتر:\n{str(e)}")

    def save_contract(self):
        """حفظ بيانات العقد"""
        try:
            # الحصول على معرف المورد
            supplier_selection = self.supplier_combo.get()
            if not supplier_selection:
                messagebox.showerror("خطأ", "يرجى اختيار المورد")
                return

            supplier_id = int(supplier_selection.split(' - ')[0])

            # جمع البيانات
            contract_data = {
                'supplier_id': supplier_id,
                'contract_number': self.contract_number.get().strip(),
                'start_date': self.start_date.get().strip(),
                'end_date': self.end_date.get().strip(),
                'total_amount': self.total_amount.get(),
                'payment_terms': self.payment_terms.get().strip(),
                'contract_status': self.contract_status.get(),
                'terms_conditions': self.terms_conditions.get().strip(),
                'notes': self.notes_text.get("1.0", tk.END).strip()
            }

            # التحقق من البيانات الأساسية
            if not contract_data['contract_number']:
                messagebox.showerror("خطأ", "رقم العقد مطلوب")
                return

            if not contract_data['start_date']:
                messagebox.showerror("خطأ", "تاريخ البداية مطلوب")
                return

            if not contract_data['end_date']:
                messagebox.showerror("خطأ", "تاريخ النهاية مطلوب")
                return

            # حفظ أو تحديث
            if self.contract_id.get() == 0:
                # إضافة عقد جديد
                contract_id = self.contract_model.create_contract(contract_data)
                if contract_id:
                    messagebox.showinfo("نجح", "تم إضافة العقد بنجاح")
                    self.clear_form()
                    self.load_contracts()
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة العقد")
            else:
                # تحديث عقد موجود
                if self.contract_model.update_contract(self.contract_id.get(), contract_data):
                    messagebox.showinfo("نجح", "تم تحديث العقد بنجاح")
                    self.clear_form()
                    self.load_contracts()
                else:
                    messagebox.showerror("خطأ", "فشل في تحديث العقد")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ العقد:\n{str(e)}")

    def clear_form(self):
        """مسح النموذج"""
        self.contract_id.set(0)
        self.supplier_combo.set("")
        self.contract_number.set("")
        self.start_date.set("")
        self.end_date.set("")
        self.total_amount.set(0.0)
        self.payment_terms.set("")
        self.contract_status.set("نشط")
        self.terms_conditions.set("")
        self.notes_text.delete("1.0", tk.END)

    def cancel_form(self):
        """إلغاء النموذج"""
        self.clear_form()

    def edit_contract(self):
        """تعديل العقد المحدد"""
        selected = self.contracts_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عقد للتعديل")
            return

        # الحصول على معرف العقد
        item = self.contracts_tree.item(selected[0])
        contract_id = item['values'][0]

        # تحميل بيانات العقد
        contract = self.contract_model.get_contract_by_id(contract_id)
        if contract:
            self.load_contract_to_form(contract)
        else:
            messagebox.showerror("خطأ", "فشل في تحميل بيانات العقد")

    def load_contract_to_form(self, contract):
        """تحميل بيانات العقد في النموذج"""
        self.contract_id.set(contract['contract_id'])
        self.contract_number.set(contract['contract_number'] or "")
        self.start_date.set(contract['start_date'] or "")
        self.end_date.set(contract['end_date'] or "")
        self.total_amount.set(contract['total_amount'] or 0.0)
        self.payment_terms.set(contract['payment_terms'] or "")
        self.contract_status.set(contract['contract_status'] or "نشط")
        self.terms_conditions.set(contract['terms_conditions'] or "")

        # تحديد المورد
        for supplier in self.suppliers_list:
            if supplier['supplier_id'] == contract['supplier_id']:
                self.supplier_combo.set(f"{supplier['supplier_id']} - {supplier['supplier_name']}")
                break

        # تحميل الملاحظات
        self.notes_text.delete("1.0", tk.END)
        if contract['notes']:
            self.notes_text.insert("1.0", contract['notes'])

    def delete_contract(self):
        """حذف العقد المحدد"""
        selected = self.contracts_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عقد للحذف")
            return

        # تأكيد الحذف
        if not messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا العقد؟\nسيتم حذف جميع عناصر العقد والمدفوعات المرتبطة به."):
            return

        # الحصول على معرف العقد
        item = self.contracts_tree.item(selected[0])
        contract_id = item['values'][0]

        try:
            if self.contract_model.delete_contract(contract_id):
                messagebox.showinfo("نجح", "تم حذف العقد بنجاح")
                self.load_contracts()
            else:
                messagebox.showerror("خطأ", "فشل في حذف العقد")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف العقد:\n{str(e)}")

    def view_contract_details(self):
        """عرض تفاصيل العقد"""
        selected = self.contracts_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عقد لعرض تفاصيله")
            return

        # الحصول على معرف العقد
        item = self.contracts_tree.item(selected[0])
        contract_id = item['values'][0]

        # عرض التفاصيل (سيتم تطوير هذا لاحقاً)
        messagebox.showinfo("تفاصيل العقد", f"سيتم عرض تفاصيل العقد رقم {contract_id}")

    def print_contract(self):
        """طباعة العقد"""
        selected = self.contracts_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عقد للطباعة")
            return

        # الحصول على معرف العقد
        item = self.contracts_tree.item(selected[0])
        contract_id = item['values'][0]

        # طباعة العقد (سيتم تطوير هذا لاحقاً)
        messagebox.showinfo("طباعة العقد", f"سيتم طباعة العقد رقم {contract_id}")

    def on_contract_double_click(self, event):
        """عند النقر المزدوج على عقد"""
        self.edit_contract()

    # ==================== دوال الحفظ المؤقت ====================

    def save_draft(self):
        """حفظ مسودة العقد محلياً"""
        try:
            # جمع بيانات النموذج
            draft_data = {
                'contract_number': self.contract_number.get(),
                'supplier_id': self.supplier_id.get(),
                'start_date': self.start_date.get(),
                'end_date': self.end_date.get(),
                'total_amount': self.total_amount.get(),
                'payment_terms': self.payment_terms.get(),
                'contract_status': self.contract_status.get(),
                'terms_conditions': self.terms_conditions.get(),
                'notes': self.notes_text.get("1.0", tk.END).strip() if hasattr(self, 'notes_text') else '',
                'timestamp': datetime.now().isoformat(),
                'draft_name': f"مسودة_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            }

            # إنشاء اسم ملف فريد
            filename = f"draft_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            filepath = os.path.join(self.drafts_dir, filename)

            # حفظ المسودة
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(draft_data, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("حفظ مؤقت", f"تم حفظ المسودة بنجاح!\nاسم الملف: {filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ المسودة:\n{str(e)}")

    def load_draft(self):
        """تحميل مسودة محفوظة"""
        try:
            # الحصول على قائمة المسودات
            if not os.path.exists(self.drafts_dir):
                messagebox.showinfo("لا توجد مسودات", "لا توجد مسودات محفوظة")
                return

            draft_files = [f for f in os.listdir(self.drafts_dir) if f.endswith('.json')]

            if not draft_files:
                messagebox.showinfo("لا توجد مسودات", "لا توجد مسودات محفوظة")
                return

            # إنشاء نافذة اختيار المسودة
            self.show_draft_selection_window(draft_files)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المسودات:\n{str(e)}")

    def show_draft_selection_window(self, draft_files):
        """عرض نافذة اختيار المسودة"""
        # إنشاء نافذة فرعية
        draft_window = ttk_bs.Toplevel(self.window)
        draft_window.title("اختيار مسودة")
        draft_window.geometry("600x400")
        draft_window.transient(self.window)
        draft_window.grab_set()

        # عنوان النافذة
        title_label = ttk_bs.Label(
            draft_window,
            text="اختر المسودة المراد تحميلها",
            font=("Arial", 14, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=10)

        # إطار القائمة
        list_frame = ttk_bs.Frame(draft_window)
        list_frame.pack(fill=BOTH, expand=True, padx=20, pady=10)

        # قائمة المسودات
        columns = ('اسم الملف', 'التاريخ', 'رقم العقد', 'المبلغ')
        drafts_tree = ttk_bs.Treeview(
            list_frame,
            columns=columns,
            show='headings',
            height=10
        )

        for col in columns:
            drafts_tree.heading(col, text=col)
            if col == 'اسم الملف':
                drafts_tree.column(col, width=150)
            elif col == 'التاريخ':
                drafts_tree.column(col, width=120)
            elif col == 'رقم العقد':
                drafts_tree.column(col, width=100)
            elif col == 'المبلغ':
                drafts_tree.column(col, width=100)

        # تحميل بيانات المسودات
        for filename in draft_files:
            try:
                filepath = os.path.join(self.drafts_dir, filename)
                with open(filepath, 'r', encoding='utf-8') as f:
                    draft_data = json.load(f)

                # استخراج التاريخ من اسم الملف
                date_str = filename.replace('draft_', '').replace('.json', '')
                try:
                    date_obj = datetime.strptime(date_str, '%Y%m%d_%H%M%S')
                    formatted_date = date_obj.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_date = date_str

                drafts_tree.insert('', 'end', values=(
                    filename,
                    formatted_date,
                    draft_data.get('contract_number', ''),
                    f"{draft_data.get('total_amount', 0):.2f}"
                ))

            except Exception as e:
                print(f"خطأ في قراءة المسودة {filename}: {e}")

        drafts_tree.pack(fill=BOTH, expand=True)

        # أزرار النافذة
        buttons_frame = ttk_bs.Frame(draft_window)
        buttons_frame.pack(fill=X, padx=20, pady=10)

        def load_selected_draft():
            selected = drafts_tree.selection()
            if not selected:
                messagebox.showwarning("تحذير", "يرجى اختيار مسودة")
                return

            item = drafts_tree.item(selected[0])
            filename = item['values'][0]

            try:
                filepath = os.path.join(self.drafts_dir, filename)
                with open(filepath, 'r', encoding='utf-8') as f:
                    draft_data = json.load(f)

                # تحميل البيانات في النموذج
                self.load_draft_data(draft_data)
                draft_window.destroy()
                messagebox.showinfo("نجح", "تم تحميل المسودة بنجاح!")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل المسودة:\n{str(e)}")

        def delete_selected_draft():
            selected = drafts_tree.selection()
            if not selected:
                messagebox.showwarning("تحذير", "يرجى اختيار مسودة")
                return

            if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه المسودة؟"):
                item = drafts_tree.item(selected[0])
                filename = item['values'][0]

                try:
                    filepath = os.path.join(self.drafts_dir, filename)
                    os.remove(filepath)
                    drafts_tree.delete(selected[0])
                    messagebox.showinfo("نجح", "تم حذف المسودة بنجاح!")

                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في حذف المسودة:\n{str(e)}")

        ttk_bs.Button(
            buttons_frame,
            text="📂 تحميل",
            command=load_selected_draft,
            bootstyle=SUCCESS
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bs.Button(
            buttons_frame,
            text="🗑️ حذف",
            command=delete_selected_draft,
            bootstyle=DANGER
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=draft_window.destroy,
            bootstyle=SECONDARY
        ).pack(side=RIGHT)

    def load_draft_data(self, draft_data):
        """تحميل بيانات المسودة في النموذج"""
        try:
            self.contract_number.set(draft_data.get('contract_number', ''))
            self.supplier_id.set(draft_data.get('supplier_id', 0))
            self.start_date.set(draft_data.get('start_date', ''))
            self.end_date.set(draft_data.get('end_date', ''))
            self.total_amount.set(draft_data.get('total_amount', 0.0))
            self.payment_terms.set(draft_data.get('payment_terms', ''))
            self.contract_status.set(draft_data.get('contract_status', 'نشط'))
            self.terms_conditions.set(draft_data.get('terms_conditions', ''))

            # تحميل الملاحظات
            if hasattr(self, 'notes_text'):
                self.notes_text.delete("1.0", tk.END)
                self.notes_text.insert("1.0", draft_data.get('notes', ''))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات المسودة:\n{str(e)}")

    def delete_drafts(self):
        """حذف جميع المسودات"""
        if not os.path.exists(self.drafts_dir):
            messagebox.showinfo("لا توجد مسودات", "لا توجد مسودات لحذفها")
            return

        draft_files = [f for f in os.listdir(self.drafts_dir) if f.endswith('.json')]

        if not draft_files:
            messagebox.showinfo("لا توجد مسودات", "لا توجد مسودات لحذفها")
            return

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف جميع المسودات؟\nعدد المسودات: {len(draft_files)}"):
            try:
                for filename in draft_files:
                    filepath = os.path.join(self.drafts_dir, filename)
                    os.remove(filepath)

                messagebox.showinfo("نجح", f"تم حذف {len(draft_files)} مسودة بنجاح!")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المسودات:\n{str(e)}")

    # ==================== دوال البحث الديناميكي ====================

    def on_search_focus_in(self, event):
        """عند التركيز على حقل البحث"""
        if self.search_entry.get() == "ابحث باسم المورد أو رقم العقد أو المبلغ...":
            self.search_entry.delete(0, tk.END)
            self.search_entry.config(foreground='black')

    def on_search_focus_out(self, event):
        """عند فقدان التركيز من حقل البحث"""
        if not self.search_entry.get():
            self.search_entry.insert(0, "ابحث باسم المورد أو رقم العقد أو المبلغ...")
            self.search_entry.config(foreground='gray')

    def on_dynamic_search(self, event):
        """البحث الديناميكي أثناء الكتابة"""
        # تأخير قصير لتجنب البحث مع كل حرف
        if hasattr(self, '_search_timer'):
            self.window.after_cancel(self._search_timer)

        self._search_timer = self.window.after(300, self.perform_dynamic_search)

    def perform_dynamic_search(self):
        """تنفيذ البحث الديناميكي"""
        try:
            search_term = self.search_term.get().strip().lower()
            status_filter = self.status_filter.get()

            # إذا كان النص هو النص التوضيحي، تجاهله
            if search_term == "ابحث باسم المورد أو رقم العقد أو المبلغ...":
                search_term = ""

            # مسح النتائج الحالية
            for item in self.contracts_tree.get_children():
                self.contracts_tree.delete(item)

            # الحصول على جميع العقود
            all_contracts = self.contract_model.get_all_contracts()
            filtered_contracts = []

            for contract in all_contracts:
                # فلترة حسب الحالة
                if status_filter != "الكل" and contract['contract_status'] != status_filter:
                    continue

                # البحث النصي
                if search_term:
                    # البحث في رقم العقد
                    if search_term in str(contract['contract_number']).lower():
                        filtered_contracts.append(contract)
                        continue

                    # البحث في اسم المورد
                    supplier_name = contract.get('supplier_name', '').lower()
                    if search_term in supplier_name:
                        filtered_contracts.append(contract)
                        continue

                    # البحث في المبلغ
                    if search_term in str(contract['total_amount']):
                        filtered_contracts.append(contract)
                        continue

                    # البحث في الملاحظات
                    notes = contract.get('notes', '').lower()
                    if search_term in notes:
                        filtered_contracts.append(contract)
                        continue
                else:
                    # إذا لم يكن هناك نص بحث، أضف جميع العقود المفلترة حسب الحالة
                    filtered_contracts.append(contract)

            # عرض النتائج
            for contract in filtered_contracts:
                self.contracts_tree.insert('', 'end', values=(
                    contract['contract_id'],
                    contract['contract_number'],
                    contract.get('supplier_name', 'غير محدد'),
                    contract['start_date'],
                    contract['end_date'],
                    f"{contract['total_amount']:.2f}",
                    contract['contract_status']
                ))

            # تحديث عداد النتائج
            self.update_results_count(len(filtered_contracts), len(all_contracts))

        except Exception as e:
            print(f"خطأ في البحث الديناميكي: {e}")

    def update_results_count(self, filtered_count, total_count):
        """تحديث عداد النتائج"""
        if filtered_count == total_count:
            count_text = f"📊 إجمالي: {total_count} عقد"
        else:
            count_text = f"📊 النتائج: {filtered_count} من {total_count} عقد"

        self.results_count_label.config(text=count_text)

    def on_status_filter_change(self, event):
        """عند تغيير فلتر الحالة"""
        self.perform_dynamic_search()

    def search_contracts(self):
        """البحث في العقود (الدالة القديمة - محدثة)"""
        self.perform_dynamic_search()

    # ==================== دوال الفلاتر المتقدمة ====================

    def on_advanced_filter_change(self, event):
        """عند تغيير أي من الفلاتر المتقدمة"""
        # تأخير قصير لتجنب التطبيق المفرط
        if hasattr(self, '_filter_timer'):
            self.window.after_cancel(self._filter_timer)

        self._filter_timer = self.window.after(500, self.apply_advanced_filters)

    def on_amount_filter_change(self, event):
        """عند تغيير فلاتر المبلغ"""
        # تأخير أطول للمبالغ لأن المستخدم قد يكتب رقم طويل
        if hasattr(self, '_amount_filter_timer'):
            self.window.after_cancel(self._amount_filter_timer)

        self._amount_filter_timer = self.window.after(1000, self.apply_advanced_filters)

    def apply_advanced_filters(self):
        """تطبيق الفلاتر المتقدمة"""
        try:
            # جمع معايير الفلترة
            search_term = self.search_term.get().strip().lower()
            status_filter = self.status_filter.get()
            contract_type_filter = self.contract_type_filter.get()

            # فلاتر التاريخ
            start_date_filter = None
            end_date_filter = None

            try:
                if hasattr(self.start_date_filter, 'entry') and self.start_date_filter.entry.get():
                    start_date_filter = self.start_date_filter.entry.get()
            except:
                pass

            try:
                if hasattr(self.end_date_filter, 'entry') and self.end_date_filter.entry.get():
                    end_date_filter = self.end_date_filter.entry.get()
            except:
                pass

            # فلاتر المبلغ
            min_amount = None
            max_amount = None

            try:
                if self.min_amount_var.get() > 0:
                    min_amount = self.min_amount_var.get()
            except:
                pass

            try:
                if self.max_amount_var.get() > 0:
                    max_amount = self.max_amount_var.get()
            except:
                pass

            # إذا كان النص هو النص التوضيحي، تجاهله
            if search_term == "ابحث باسم المورد أو رقم العقد أو المبلغ...":
                search_term = ""

            # مسح النتائج الحالية
            for item in self.contracts_tree.get_children():
                self.contracts_tree.delete(item)

            # الحصول على جميع العقود
            all_contracts = self.contract_model.get_all_contracts()
            filtered_contracts = []

            for contract in all_contracts:
                # فلترة حسب الحالة
                if status_filter != "الكل" and contract['contract_status'] != status_filter:
                    continue

                # فلترة حسب نوع العقد (إذا كان هناك حقل نوع في قاعدة البيانات)
                if contract_type_filter != "كل الأنواع":
                    # يمكن إضافة منطق فلترة نوع العقد هنا
                    # حالياً سنتجاهل هذا الفلتر حتى يتم إضافة حقل نوع العقد
                    pass

                # فلترة حسب التاريخ
                if start_date_filter:
                    try:
                        contract_start = datetime.strptime(contract['start_date'], '%Y-%m-%d').date()
                        filter_start = datetime.strptime(start_date_filter, '%Y-%m-%d').date()
                        if contract_start < filter_start:
                            continue
                    except:
                        pass

                if end_date_filter:
                    try:
                        contract_end = datetime.strptime(contract['end_date'], '%Y-%m-%d').date()
                        filter_end = datetime.strptime(end_date_filter, '%Y-%m-%d').date()
                        if contract_end > filter_end:
                            continue
                    except:
                        pass

                # فلترة حسب المبلغ
                contract_amount = contract.get('total_amount', 0)
                if min_amount is not None and contract_amount < min_amount:
                    continue

                if max_amount is not None and contract_amount > max_amount:
                    continue

                # البحث النصي
                if search_term:
                    # البحث في رقم العقد
                    if search_term in str(contract['contract_number']).lower():
                        filtered_contracts.append(contract)
                        continue

                    # البحث في اسم المورد
                    supplier_name = contract.get('supplier_name', '').lower()
                    if search_term in supplier_name:
                        filtered_contracts.append(contract)
                        continue

                    # البحث في المبلغ
                    if search_term in str(contract['total_amount']):
                        filtered_contracts.append(contract)
                        continue

                    # البحث في الملاحظات
                    notes = contract.get('notes', '').lower()
                    if search_term in notes:
                        filtered_contracts.append(contract)
                        continue

                    # البحث في شروط الدفع
                    payment_terms = contract.get('payment_terms', '').lower()
                    if search_term in payment_terms:
                        filtered_contracts.append(contract)
                        continue
                else:
                    # إذا لم يكن هناك نص بحث، أضف العقد إذا مر جميع الفلاتر الأخرى
                    filtered_contracts.append(contract)

            # عرض النتائج
            for contract in filtered_contracts:
                self.contracts_tree.insert('', 'end', values=(
                    contract['contract_id'],
                    contract['contract_number'],
                    contract.get('supplier_name', 'غير محدد'),
                    contract['start_date'],
                    contract['end_date'],
                    f"{contract['total_amount']:.2f}",
                    contract['contract_status']
                ))

            # تحديث عداد النتائج
            self.update_results_count_advanced(len(filtered_contracts), len(all_contracts))

        except Exception as e:
            print(f"خطأ في تطبيق الفلاتر المتقدمة: {e}")

    def update_results_count_advanced(self, filtered_count, total_count):
        """تحديث عداد النتائج مع معلومات الفلاتر"""
        if filtered_count == total_count:
            count_text = f"📊 إجمالي: {total_count} عقد"
        else:
            count_text = f"🔍 النتائج: {filtered_count} من {total_count} عقد"

        # إضافة معلومات الفلاتر النشطة
        active_filters = []

        if self.status_filter.get() != "الكل":
            active_filters.append(f"الحالة: {self.status_filter.get()}")

        if self.contract_type_filter.get() != "كل الأنواع":
            active_filters.append(f"النوع: {self.contract_type_filter.get()}")

        try:
            if self.min_amount_var.get() > 0:
                active_filters.append(f"المبلغ ≥ {self.min_amount_var.get():.0f}")
        except:
            pass

        try:
            if self.max_amount_var.get() > 0:
                active_filters.append(f"المبلغ ≤ {self.max_amount_var.get():.0f}")
        except:
            pass

        if active_filters:
            count_text += f" | الفلاتر: {', '.join(active_filters)}"

        self.results_count_label.config(text=count_text)

    def clear_filters(self):
        """مسح جميع الفلاتر"""
        try:
            # مسح فلاتر النص
            self.search_term.set("")
            self.search_entry.delete(0, tk.END)
            self.search_entry.insert(0, "ابحث باسم المورد أو رقم العقد أو المبلغ...")
            self.search_entry.config(foreground='gray')

            # مسح فلاتر القوائم
            self.status_filter.set("الكل")
            self.contract_type_filter.set("كل الأنواع")

            # مسح فلاتر التاريخ
            try:
                if hasattr(self.start_date_filter, 'entry'):
                    self.start_date_filter.entry.delete(0, tk.END)
                if hasattr(self.end_date_filter, 'entry'):
                    self.end_date_filter.entry.delete(0, tk.END)
            except:
                pass

            # مسح فلاتر المبلغ
            self.min_amount_var.set(0.0)
            self.max_amount_var.set(0.0)

            # إعادة تحميل جميع العقود
            self.load_contracts()

            messagebox.showinfo("مسح الفلاتر", "تم مسح جميع الفلاتر وإعادة تحميل البيانات")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في مسح الفلاتر:\n{str(e)}")
