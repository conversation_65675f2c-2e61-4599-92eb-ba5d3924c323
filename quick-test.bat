@echo off
chcp 65001 >nul
title اختبار نظام رفع ملفات الموردين - جمعية المنقف

echo.
echo 🧪 اختبار نظام رفع ملفات الموردين
echo جمعية المنقف التعاونية
echo ============================================
echo.

:menu
echo اختر نوع الاختبار:
echo.
echo 1. اختبار شامل للنظام
echo 2. اختبار سريع للملفات
echo 3. تشغيل الخادم واختبار يدوي
echo 4. إنشاء ملفات اختبار فقط
echo 5. فتح صفحة الاختبار
echo 6. عرض حالة النظام
echo 7. تثبيت التبعيات
echo 0. خروج
echo.
set /p choice="اختر رقم (0-7): "

if "%choice%"=="1" goto full_test
if "%choice%"=="2" goto quick_test
if "%choice%"=="3" goto server_test
if "%choice%"=="4" goto create_files
if "%choice%"=="5" goto open_page
if "%choice%"=="6" goto system_status
if "%choice%"=="7" goto install_deps
if "%choice%"=="0" goto exit
goto invalid

:full_test
echo.
echo 🔄 تشغيل الاختبار الشامل...
echo.
node test-upload-system.js
echo.
echo ✅ الاختبار الشامل اكتمل
goto pause_menu

:quick_test
echo.
echo ⚡ تشغيل الاختبار السريع...
echo.

echo 📁 التحقق من الملفات الأساسية:
if exist "upload_suppliers.html" (
    echo    ✅ upload_suppliers.html
) else (
    echo    ❌ upload_suppliers.html - غير موجود
)

if exist "quick-upload-test.html" (
    echo    ✅ quick-upload-test.html
) else (
    echo    ❌ quick-upload-test.html - غير موجود
)

if exist "backend\routes\upload.js" (
    echo    ✅ backend\routes\upload.js
) else (
    echo    ❌ backend\routes\upload.js - غير موجود
)

if exist "package.json" (
    echo    ✅ package.json
) else (
    echo    ❌ package.json - غير موجود
)

echo.
echo 📦 التحقق من التبعيات:
if exist "node_modules" (
    echo    ✅ node_modules موجود
    if exist "node_modules\multer" (
        echo    ✅ multer
    ) else (
        echo    ❌ multer - غير مثبت
    )
    if exist "node_modules\xlsx" (
        echo    ✅ xlsx
    ) else (
        echo    ❌ xlsx - غير مثبت
    )
) else (
    echo    ❌ node_modules غير موجود
    echo    💡 قم بتشغيل: npm install
)

echo.
echo ✅ الاختبار السريع اكتمل
goto pause_menu

:server_test
echo.
echo 🚀 تشغيل الخادم...
echo.

if not exist "node_modules" (
    echo ❌ node_modules غير موجود. قم بتشغيل: npm install
    goto pause_menu
)

echo 💡 سيتم تشغيل الخادم وفتح صفحات الاختبار
echo 💡 اضغط Ctrl+C لإيقاف الخادم
echo.
timeout /t 3 /nobreak >nul

start "" http://localhost:3000/quick-upload-test.html
timeout /t 2 /nobreak >nul
start "" http://localhost:3000/upload_suppliers.html

npm start
goto menu

:create_files
echo.
echo 📄 إنشاء ملفات الاختبار...
echo.

echo اسم المورد,الفئة,رقم الهاتف,البريد الإلكتروني,العنوان,الشخص المسؤول > test-suppliers.csv
echo شركة الاختبار الأولى,بهارات,+965-11111111,<EMAIL>,الكويت - منطقة الاختبار,مدير الاختبار >> test-suppliers.csv
echo مؤسسة الاختبار الثانية,استهلاكي,+965-22222222,<EMAIL>,الكويت - منطقة الاختبار 2,مدير الاختبار 2 >> test-suppliers.csv
echo شركة الاختبار الثالثة,أجبان,+965-33333333,<EMAIL>,الكويت - منطقة الاختبار 3,مدير الاختبار 3 >> test-suppliers.csv
echo مورد خاطئ للاختبار,فئة خاطئة,رقم خاطئ,بريد خاطئ,, >> test-suppliers.csv

echo    ✅ تم إنشاء test-suppliers.csv

if exist "node_modules\xlsx" (
    node -e "const XLSX=require('xlsx');const data=[{'اسم المورد':'شركة الاختبار','الفئة':'بهارات','رقم الهاتف':'+965-11111111','البريد الإلكتروني':'<EMAIL>','العنوان':'الكويت','الشخص المسؤول':'مدير'}];const ws=XLSX.utils.json_to_sheet(data);const wb=XLSX.utils.book_new();XLSX.utils.book_append_sheet(wb,ws,'الموردين');XLSX.writeFile(wb,'test-suppliers.xlsx');"
    echo    ✅ تم إنشاء test-suppliers.xlsx
) else (
    echo    ⚠️ لم يتم إنشاء ملف Excel (XLSX غير متاح)
)

echo.
echo 📁 ملفات الاختبار جاهزة للاستخدام!
goto pause_menu

:open_page
echo.
echo 🌐 فتح صفحة الاختبار...
echo.

start "" http://localhost:3000/quick-upload-test.html
echo ✅ تم فتح صفحة الاختبار في المتصفح
echo 💡 تأكد من تشغيل الخادم أولاً: npm start

goto pause_menu

:system_status
echo.
echo 📊 حالة النظام:
echo.

echo 📁 الملفات:
if exist "upload_suppliers.html" (echo    ✅ upload_suppliers.html) else (echo    ❌ upload_suppliers.html)
if exist "quick-upload-test.html" (echo    ✅ quick-upload-test.html) else (echo    ❌ quick-upload-test.html)
if exist "backend\routes\upload.js" (echo    ✅ backend\routes\upload.js) else (echo    ❌ backend\routes\upload.js)
if exist "backend\api\server.js" (echo    ✅ backend\api\server.js) else (echo    ❌ backend\api\server.js)
if exist "package.json" (echo    ✅ package.json) else (echo    ❌ package.json)

echo.
echo 📦 التبعيات:
if exist "node_modules" (
    echo    ✅ node_modules موجود
    if exist "node_modules\multer" (echo    ✅ multer) else (echo    ❌ multer)
    if exist "node_modules\xlsx" (echo    ✅ xlsx) else (echo    ❌ xlsx)
    if exist "node_modules\csv-parser" (echo    ✅ csv-parser) else (echo    ❌ csv-parser)
) else (
    echo    ❌ node_modules غير موجود
    echo    💡 قم بتشغيل: npm install
)

echo.
echo 🧪 ملفات الاختبار:
if exist "test-suppliers.csv" (echo    ✅ test-suppliers.csv) else (echo    ❌ test-suppliers.csv)
if exist "test-suppliers.xlsx" (echo    ✅ test-suppliers.xlsx) else (echo    ❌ test-suppliers.xlsx)

goto pause_menu

:install_deps
echo.
echo 📦 تثبيت التبعيات...
echo.

if not exist "package.json" (
    echo ❌ package.json غير موجود
    goto pause_menu
)

echo 🔄 جاري تثبيت التبعيات...
npm install

if %errorlevel%==0 (
    echo ✅ تم تثبيت التبعيات بنجاح
) else (
    echo ❌ فشل في تثبيت التبعيات
)

goto pause_menu

:invalid
echo.
echo ❌ اختيار غير صحيح
goto pause_menu

:pause_menu
echo.
echo ⏸️ اضغط أي مفتاح للمتابعة...
pause >nul
cls
goto menu

:exit
echo.
echo 👋 وداعاً!
echo.
timeout /t 2 /nobreak >nul
exit
