-- =====================================================
-- هيكل قاعدة البيانات - نظام إدارة عقود الموردين والإيجارات
-- جمعية المنقف التعاونية
-- =====================================================

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS supplier_contracts 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE supplier_contracts;

-- =====================================================
-- جدول المستخدمين
-- =====================================================
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'manager', 'employee', 'viewer') DEFAULT 'employee',
    department VARCHAR(50),
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_active (is_active)
) ENGINE=InnoDB;

-- =====================================================
-- جدول فئات الموردين
-- =====================================================
CREATE TABLE supplier_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    name_en VARCHAR(100),
    description TEXT,
    color VARCHAR(7) DEFAULT '#3b82f6',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_active (is_active)
) ENGINE=InnoDB;

-- =====================================================
-- جدول الموردين
-- =====================================================
CREATE TABLE suppliers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    name_en VARCHAR(200),
    category_id INT NOT NULL,
    contact_person VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    commercial_registration VARCHAR(50),
    tax_number VARCHAR(50),
    bank_account VARCHAR(50),
    bank_name VARCHAR(100),
    rating DECIMAL(3,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (category_id) REFERENCES supplier_categories(id) ON DELETE RESTRICT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_supplier_code (supplier_code),
    INDEX idx_name (name),
    INDEX idx_category (category_id),
    INDEX idx_active (is_active),
    INDEX idx_phone (phone),
    INDEX idx_email (email)
) ENGINE=InnoDB;

-- =====================================================
-- جدول أنواع العقود
-- =====================================================
CREATE TABLE contract_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    name_en VARCHAR(100),
    description TEXT,
    default_duration_months INT DEFAULT 12,
    requires_approval BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_active (is_active)
) ENGINE=InnoDB;

-- =====================================================
-- جدول العقود
-- =====================================================
CREATE TABLE contracts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contract_number VARCHAR(50) UNIQUE NOT NULL,
    contract_name VARCHAR(200) NOT NULL,
    supplier_id INT NOT NULL,
    contract_type_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    total_amount DECIMAL(15,3) DEFAULT 0.000,
    currency VARCHAR(3) DEFAULT 'KWD',
    payment_terms TEXT,
    status ENUM('draft', 'active', 'expired', 'cancelled', 'suspended') DEFAULT 'draft',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    auto_renewal BOOLEAN DEFAULT FALSE,
    renewal_period INT DEFAULT 12,
    contract_color VARCHAR(7) DEFAULT '#3b82f6',
    description TEXT,
    terms_conditions TEXT,
    notes TEXT,
    created_by INT,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE RESTRICT,
    FOREIGN KEY (contract_type_id) REFERENCES contract_types(id) ON DELETE RESTRICT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_contract_number (contract_number),
    INDEX idx_supplier (supplier_id),
    INDEX idx_type (contract_type_id),
    INDEX idx_status (status),
    INDEX idx_dates (start_date, end_date),
    INDEX idx_priority (priority),
    INDEX idx_created_by (created_by)
) ENGINE=InnoDB;

-- =====================================================
-- جدول عناصر العقود
-- =====================================================
CREATE TABLE contract_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contract_id INT NOT NULL,
    item_name VARCHAR(200) NOT NULL,
    item_code VARCHAR(50),
    description TEXT,
    quantity DECIMAL(10,3) DEFAULT 1.000,
    unit VARCHAR(50),
    unit_price DECIMAL(15,3) DEFAULT 0.000,
    total_price DECIMAL(15,3) DEFAULT 0.000,
    specifications TEXT,
    delivery_terms TEXT,
    warranty_period INT DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE,
    
    INDEX idx_contract (contract_id),
    INDEX idx_item_code (item_code),
    INDEX idx_item_name (item_name)
) ENGINE=InnoDB;

-- =====================================================
-- جدول الطاولات والعناصر القابلة للإيجار
-- =====================================================
CREATE TABLE rental_tables (
    id INT PRIMARY KEY AUTO_INCREMENT,
    table_number VARCHAR(20) UNIQUE NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    category_id INT NOT NULL,
    location VARCHAR(100),
    total_sections INT DEFAULT 4,
    section_size_cm INT DEFAULT 60,
    section_price DECIMAL(10,3) DEFAULT 60.000,
    currency VARCHAR(3) DEFAULT 'KWD',
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (category_id) REFERENCES supplier_categories(id) ON DELETE RESTRICT,
    
    INDEX idx_table_number (table_number),
    INDEX idx_category (category_id),
    INDEX idx_active (is_active)
) ENGINE=InnoDB;

-- =====================================================
-- جدول أقسام الطاولات المؤجرة
-- =====================================================
CREATE TABLE rented_table_sections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    table_id INT NOT NULL,
    contract_id INT NOT NULL,
    section_number INT NOT NULL,
    rental_price DECIMAL(10,3) NOT NULL,
    currency VARCHAR(3) DEFAULT 'KWD',
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (table_id) REFERENCES rental_tables(id) ON DELETE CASCADE,
    FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_table_section (table_id, section_number, start_date),
    INDEX idx_table (table_id),
    INDEX idx_contract (contract_id),
    INDEX idx_dates (start_date, end_date),
    INDEX idx_status (status)
) ENGINE=InnoDB;

-- =====================================================
-- جدول المدفوعات
-- =====================================================
CREATE TABLE contract_payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contract_id INT NOT NULL,
    payment_date DATE NOT NULL,
    amount DECIMAL(15,3) NOT NULL,
    currency VARCHAR(3) DEFAULT 'KWD',
    payment_method ENUM('cash', 'bank_transfer', 'check', 'card') DEFAULT 'cash',
    reference_number VARCHAR(100),
    description TEXT,
    status ENUM('pending', 'completed', 'cancelled') DEFAULT 'completed',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_contract (contract_id),
    INDEX idx_payment_date (payment_date),
    INDEX idx_status (status),
    INDEX idx_reference (reference_number)
) ENGINE=InnoDB;

-- =====================================================
-- جدول مرفقات العقود
-- =====================================================
CREATE TABLE contract_attachments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contract_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    description TEXT,
    uploaded_by INT,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_contract (contract_id),
    INDEX idx_file_type (file_type),
    INDEX idx_uploaded_by (uploaded_by)
) ENGINE=InnoDB;

-- =====================================================
-- جدول تجديدات العقود
-- =====================================================
CREATE TABLE contract_renewals (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contract_id INT NOT NULL,
    old_end_date DATE NOT NULL,
    new_end_date DATE NOT NULL,
    renewal_amount DECIMAL(15,3) DEFAULT 0.000,
    renewal_terms TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (contract_id) REFERENCES contracts(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_contract (contract_id),
    INDEX idx_dates (old_end_date, new_end_date)
) ENGINE=InnoDB;

-- =====================================================
-- جدول سجل العمليات
-- =====================================================
CREATE TABLE activity_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_table (table_name),
    INDEX idx_record (record_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB;

-- =====================================================
-- إدراج البيانات الأولية
-- =====================================================

-- إدراج المستخدم الإداري الافتراضي
INSERT INTO users (username, email, password_hash, full_name, role, is_active) VALUES
('admin', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL8.vKzFu', 'مدير النظام', 'admin', TRUE);

-- إدراج فئات الموردين
INSERT INTO supplier_categories (name, name_en, description, color) VALUES
('التوابل والبهارات', 'Spices & Seasonings', 'موردي التوابل والبهارات والمنكهات', '#f59e0b'),
('السلع الاستهلاكية', 'Consumer Goods', 'موردي السلع الاستهلاكية والمنزلية', '#10b981'),
('الأجبان ومنتجات الألبان', 'Cheese & Dairy', 'موردي الأجبان ومنتجات الألبان', '#3b82f6');

-- إدراج أنواع العقود
INSERT INTO contract_types (name, name_en, description, default_duration_months) VALUES
('عقد توريد', 'Supply Contract', 'عقد توريد البضائع والمنتجات', 12),
('عقد إيجار طاولة', 'Table Rental Contract', 'عقد إيجار طاولة في السوق المركزي', 12),
('عقد خدمات', 'Service Contract', 'عقد تقديم الخدمات', 6);

-- =====================================================
-- إنشاء المؤشرات المتقدمة
-- =====================================================

-- مؤشرات للبحث النصي
CREATE FULLTEXT INDEX idx_suppliers_search ON suppliers(name, contact_person, notes);
CREATE FULLTEXT INDEX idx_contracts_search ON contracts(contract_name, description, notes);

-- =====================================================
-- إنشاء الإجراءات المخزنة
-- =====================================================

DELIMITER //

-- إجراء للحصول على إحصائيات لوحة التحكم
CREATE PROCEDURE GetDashboardStats()
BEGIN
    SELECT 
        (SELECT COUNT(*) FROM contracts) as total_contracts,
        (SELECT COUNT(*) FROM contracts WHERE status = 'active') as active_contracts,
        (SELECT COUNT(*) FROM contracts WHERE status = 'draft') as draft_contracts,
        (SELECT COUNT(*) FROM contracts WHERE end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND status = 'active') as expiring_contracts,
        (SELECT COUNT(*) FROM suppliers WHERE is_active = TRUE) as active_suppliers,
        (SELECT SUM(total_amount) FROM contracts WHERE status = 'active') as total_active_value,
        (SELECT COUNT(*) FROM rented_table_sections WHERE status = 'active') as rented_sections,
        (SELECT SUM(rental_price) FROM rented_table_sections WHERE status = 'active') as monthly_rental_income;
END //

-- إجراء للحصول على العقود المنتهية قريباً
CREATE PROCEDURE GetExpiringContracts(IN days_ahead INT)
BEGIN
    SELECT 
        c.*,
        s.name as supplier_name,
        ct.name as contract_type_name,
        DATEDIFF(c.end_date, CURDATE()) as days_to_expiry
    FROM contracts c
    LEFT JOIN suppliers s ON c.supplier_id = s.id
    LEFT JOIN contract_types ct ON c.contract_type_id = ct.id
    WHERE c.status = 'active' 
        AND c.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL days_ahead DAY)
    ORDER BY c.end_date ASC;
END //

DELIMITER ;

-- =====================================================
-- إنشاء المشاهدات (Views)
-- =====================================================

-- مشاهدة تفاصيل العقود مع معلومات الموردين
CREATE VIEW contract_details AS
SELECT 
    c.*,
    s.name as supplier_name,
    s.supplier_code,
    s.phone as supplier_phone,
    s.email as supplier_email,
    sc.name as supplier_category,
    ct.name as contract_type_name,
    u1.full_name as created_by_name,
    u2.full_name as approved_by_name,
    DATEDIFF(c.end_date, CURDATE()) as days_to_expiry,
    CASE 
        WHEN c.end_date < CURDATE() THEN 'منتهي'
        WHEN DATEDIFF(c.end_date, CURDATE()) <= 30 THEN 'ينتهي قريباً'
        ELSE 'نشط'
    END as expiry_status
FROM contracts c
LEFT JOIN suppliers s ON c.supplier_id = s.id
LEFT JOIN supplier_categories sc ON s.category_id = sc.id
LEFT JOIN contract_types ct ON c.contract_type_id = ct.id
LEFT JOIN users u1 ON c.created_by = u1.id
LEFT JOIN users u2 ON c.approved_by = u2.id;

-- مشاهدة إحصائيات الطاولات
CREATE VIEW table_statistics AS
SELECT 
    rt.id,
    rt.table_number,
    rt.table_name,
    sc.name as category_name,
    rt.total_sections,
    COUNT(rts.id) as rented_sections,
    (rt.total_sections - COUNT(rts.id)) as available_sections,
    SUM(rts.rental_price) as monthly_income
FROM rental_tables rt
LEFT JOIN supplier_categories sc ON rt.category_id = sc.id
LEFT JOIN rented_table_sections rts ON rt.id = rts.table_id AND rts.status = 'active'
WHERE rt.is_active = TRUE
GROUP BY rt.id, rt.table_number, rt.table_name, sc.name, rt.total_sections;

-- =====================================================
-- إنشاء المحفزات (Triggers)
-- =====================================================

DELIMITER //

-- محفز لتحديث إجمالي مبلغ العقد عند إضافة/تعديل عنصر
CREATE TRIGGER update_contract_total_after_item_insert
AFTER INSERT ON contract_items
FOR EACH ROW
BEGIN
    UPDATE contracts 
    SET total_amount = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM contract_items 
        WHERE contract_id = NEW.contract_id
    )
    WHERE id = NEW.contract_id;
END //

CREATE TRIGGER update_contract_total_after_item_update
AFTER UPDATE ON contract_items
FOR EACH ROW
BEGIN
    UPDATE contracts 
    SET total_amount = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM contract_items 
        WHERE contract_id = NEW.contract_id
    )
    WHERE id = NEW.contract_id;
END //

CREATE TRIGGER update_contract_total_after_item_delete
AFTER DELETE ON contract_items
FOR EACH ROW
BEGIN
    UPDATE contracts 
    SET total_amount = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM contract_items 
        WHERE contract_id = OLD.contract_id
    )
    WHERE id = OLD.contract_id;
END //

DELIMITER ;
