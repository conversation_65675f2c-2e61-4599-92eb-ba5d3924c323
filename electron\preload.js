const { contextBridge, ipcRenderer } = require('electron');

// تعريض APIs آمنة للواجهة الأمامية
contextBridge.exposeInMainWorld('electronAPI', {
    // معلومات التطبيق
    getAppVersion: () => ipcRenderer.invoke('get-app-version'),
    
    // نوافذ الحوار
    showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
    showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
    showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
    
    // معلومات النظام
    platform: process.platform,
    isElectron: true,
    
    // دوال مساعدة
    openExternal: (url) => {
        // سيتم التعامل معها في main process
        window.open(url, '_blank');
    },
    
    // إشعارات
    showNotification: (title, body) => {
        if (Notification.permission === 'granted') {
            new Notification(title, { body });
        } else if (Notification.permission !== 'denied') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    new Notification(title, { body });
                }
            });
        }
    }
});

// إضافة معلومات Electron للنافذة
contextBridge.exposeInMainWorld('isElectronApp', true);

// دوال مساعدة للتطبيق
contextBridge.exposeInMainWorld('appHelpers', {
    // تحديد ما إذا كان التطبيق يعمل في Electron
    isDesktopApp: () => true,
    
    // الحصول على معلومات البيئة
    getEnvironment: () => ({
        isElectron: true,
        platform: process.platform,
        nodeVersion: process.versions.node,
        electronVersion: process.versions.electron
    }),
    
    // دوال التنقل المحسنة للتطبيق
    navigateTo: (page) => {
        const baseUrl = 'http://localhost:3000';
        const fullUrl = page.startsWith('http') ? page : `${baseUrl}/${page}`;
        window.location.href = fullUrl;
    },
    
    // دوال الملفات
    selectFile: async (filters = []) => {
        try {
            const result = await ipcRenderer.invoke('show-open-dialog', {
                properties: ['openFile'],
                filters: filters.length > 0 ? filters : [
                    { name: 'Excel Files', extensions: ['xlsx', 'xls'] },
                    { name: 'CSV Files', extensions: ['csv'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });
            return result;
        } catch (error) {
            console.error('Error selecting file:', error);
            return { canceled: true };
        }
    },
    
    // حفظ ملف
    saveFile: async (defaultPath = '', filters = []) => {
        try {
            const result = await ipcRenderer.invoke('show-save-dialog', {
                defaultPath,
                filters: filters.length > 0 ? filters : [
                    { name: 'Excel Files', extensions: ['xlsx'] },
                    { name: 'CSV Files', extensions: ['csv'] },
                    { name: 'PDF Files', extensions: ['pdf'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });
            return result;
        } catch (error) {
            console.error('Error saving file:', error);
            return { canceled: true };
        }
    },
    
    // إظهار رسائل مخصصة
    showAlert: async (message, type = 'info') => {
        const iconType = type === 'error' ? 'error' : 
                        type === 'warning' ? 'warning' : 
                        type === 'question' ? 'question' : 'info';
        
        try {
            const result = await ipcRenderer.invoke('show-message-box', {
                type: iconType,
                title: 'نظام إدارة العقود',
                message: message,
                buttons: ['موافق']
            });
            return result;
        } catch (error) {
            console.error('Error showing alert:', error);
            // fallback إلى alert عادي
            alert(message);
            return { response: 0 };
        }
    },
    
    // تأكيد العمليات
    showConfirm: async (message, title = 'تأكيد') => {
        try {
            const result = await ipcRenderer.invoke('show-message-box', {
                type: 'question',
                title: title,
                message: message,
                buttons: ['نعم', 'لا'],
                defaultId: 0,
                cancelId: 1
            });
            return result.response === 0;
        } catch (error) {
            console.error('Error showing confirm:', error);
            // fallback إلى confirm عادي
            return confirm(message);
        }
    },
    
    // إشعارات النظام
    notify: (title, body, icon = null) => {
        try {
            if (window.electronAPI && window.electronAPI.showNotification) {
                window.electronAPI.showNotification(title, body);
            } else {
                // fallback للمتصفح العادي
                if ('Notification' in window) {
                    if (Notification.permission === 'granted') {
                        new Notification(title, { body, icon });
                    } else if (Notification.permission !== 'denied') {
                        Notification.requestPermission().then(permission => {
                            if (permission === 'granted') {
                                new Notification(title, { body, icon });
                            }
                        });
                    }
                }
            }
        } catch (error) {
            console.error('Error showing notification:', error);
        }
    }
});

// إضافة ستايلات خاصة بالتطبيق
document.addEventListener('DOMContentLoaded', () => {
    // إضافة كلاس للجسم ليعرف أنه في Electron
    document.body.classList.add('electron-app');
    
    // إضافة ستايلات CSS مخصصة للتطبيق
    const style = document.createElement('style');
    style.textContent = `
        .electron-app {
            /* ستايلات خاصة بتطبيق Electron */
            user-select: none; /* منع تحديد النص */
        }
        
        .electron-app input,
        .electron-app textarea,
        .electron-app [contenteditable] {
            user-select: text; /* السماح بتحديد النص في الحقول */
        }
        
        /* تحسين شريط التمرير */
        .electron-app ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        .electron-app ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }
        
        .electron-app ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }
        
        .electron-app ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        
        /* تحسين الأزرار */
        .electron-app button {
            cursor: pointer;
        }
        
        .electron-app button:disabled {
            cursor: not-allowed;
            opacity: 0.6;
        }
        
        /* تأثيرات التركيز */
        .electron-app *:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }
        
        /* إخفاء شريط التمرير الأفقي إذا لم يكن مطلوباً */
        .electron-app body {
            overflow-x: hidden;
        }
    `;
    document.head.appendChild(style);
    
    // إضافة معلومات التطبيق إلى العنوان
    const originalTitle = document.title;
    document.title = `${originalTitle} - تطبيق سطح المكتب`;
    
    // إضافة معالج للروابط الخارجية
    document.addEventListener('click', (event) => {
        const target = event.target.closest('a');
        if (target && target.href && target.href.startsWith('http') && !target.href.includes('localhost')) {
            event.preventDefault();
            if (window.electronAPI && window.electronAPI.openExternal) {
                window.electronAPI.openExternal(target.href);
            } else {
                window.open(target.href, '_blank');
            }
        }
    });
    
    // إضافة اختصارات لوحة المفاتيح
    document.addEventListener('keydown', (event) => {
        // Ctrl+R أو F5 لإعادة التحميل
        if ((event.ctrlKey && event.key === 'r') || event.key === 'F5') {
            event.preventDefault();
            window.location.reload();
        }
        
        // F11 للشاشة الكاملة (سيتم التعامل معها في main process)
        if (event.key === 'F11') {
            event.preventDefault();
        }
        
        // Ctrl+Plus/Minus للتكبير/التصغير (سيتم التعامل معها في main process)
        if (event.ctrlKey && (event.key === '+' || event.key === '-' || event.key === '0')) {
            event.preventDefault();
        }
    });
});

// إضافة معلومات إضافية للتطبيق
window.addEventListener('load', () => {
    // إضافة معلومات النسخة إلى الصفحة إذا وجد عنصر مخصص لذلك
    const versionElement = document.querySelector('.app-version');
    if (versionElement && window.electronAPI) {
        window.electronAPI.getAppVersion().then(version => {
            versionElement.textContent = `الإصدار ${version}`;
        });
    }
    
    // إضافة معلومات البيئة
    const envElement = document.querySelector('.app-environment');
    if (envElement && window.appHelpers) {
        const env = window.appHelpers.getEnvironment();
        envElement.textContent = `تطبيق سطح المكتب - ${env.platform}`;
    }
    
    // تحسين تجربة المستخدم
    console.log('🚀 تطبيق نظام إدارة العقود جاهز!');
    console.log('👨‍💻 تطوير: محمد مرزوق العقاب');
    console.log('🏢 جمعية المنقف التعاونية');
});
