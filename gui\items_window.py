#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة الطبليات والعناصر
Items Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from models.rental_item import RentalItem
from models.activity_type import ActivityType

class ItemsWindow:
    """نافذة إدارة الطبليات والعناصر"""
    
    def __init__(self, parent, db_manager):
        """
        تهيئة نافذة الطبليات والعناصر
        
        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
        """
        self.parent = parent
        self.db_manager = db_manager
        self.rental_item_model = RentalItem(db_manager)
        self.activity_type_model = ActivityType(db_manager)
        
        # إنشاء النافذة
        self.window = ttk_bs.Toplevel(parent)
        self.window.title("إدارة الطبليات والعناصر")
        self.window.geometry("1200x800")
        self.window.resizable(True, True)
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_items()
        self.load_activity_types()
        
        # تعيين النافذة في المقدمة
        self.window.transient(parent)
        self.window.grab_set()
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        # متغيرات العنصر
        self.item_id = tk.IntVar()
        self.item_type = tk.StringVar(value="طبلية")
        self.item_name = tk.StringVar()
        self.location = tk.StringVar()
        self.area_sqm = tk.DoubleVar()
        self.status = tk.StringVar(value="متاح")
        self.description = tk.StringVar()
        self.search_term = tk.StringVar()
        
        # متغيرات نوع النشاط
        self.activity_type_id = tk.IntVar()
        self.activity_name = tk.StringVar()
        self.activity_type_category = tk.StringVar(value="استهلاكي")
        self.base_price_per_sqm = tk.DoubleVar()
        self.activity_description = tk.StringVar()
        
        # قوائم البيانات
        self.activity_types_list = []
    
    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=10)
        main_frame.pack(fill=BOTH, expand=True)
        
        # عنوان النافذة
        title_label = ttk_bs.Label(
            main_frame,
            text="إدارة الطبليات والعناصر",
            font=("Arial", 16, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 20))
        
        # إنشاء التبويبات
        notebook = ttk_bs.Notebook(main_frame)
        notebook.pack(fill=BOTH, expand=True)
        
        # تبويب الطبليات والعناصر
        self.create_items_tab(notebook)
        
        # تبويب أنواع الأنشطة
        self.create_activity_types_tab(notebook)
        
        # تبويب الإحصائيات
        self.create_statistics_tab(notebook)
    
    def create_items_tab(self, notebook):
        """إنشاء تبويب الطبليات والعناصر"""
        items_frame = ttk_bs.Frame(notebook, padding=10)
        notebook.add(items_frame, text="الطبليات والعناصر")
        
        # إطار البحث والفلترة
        search_frame = ttk_bs.Frame(items_frame)
        search_frame.pack(fill=X, pady=(0, 10))
        
        # البحث
        ttk_bs.Label(search_frame, text="البحث:").pack(side=LEFT, padx=(0, 5))
        search_entry = ttk_bs.Entry(
            search_frame,
            textvariable=self.search_term,
            width=30
        )
        search_entry.pack(side=LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.on_search)
        
        ttk_bs.Button(
            search_frame,
            text="بحث",
            command=self.search_items,
            bootstyle=INFO
        ).pack(side=LEFT, padx=(0, 10))
        
        # فلتر النوع
        ttk_bs.Label(search_frame, text="النوع:").pack(side=LEFT, padx=(20, 5))
        type_filter = ttk_bs.Combobox(
            search_frame,
            values=["الكل", "طبلية", "قاطع", "جندولة", "استناد"],
            state="readonly",
            width=15
        )
        type_filter.pack(side=LEFT, padx=(0, 10))
        type_filter.set("الكل")
        type_filter.bind('<<ComboboxSelected>>', self.on_type_filter_change)
        self.type_filter = type_filter
        
        # فلتر الحالة
        ttk_bs.Label(search_frame, text="الحالة:").pack(side=LEFT, padx=(20, 5))
        status_filter = ttk_bs.Combobox(
            search_frame,
            values=["الكل", "متاح", "مؤجر", "صيانة", "غير متاح"],
            state="readonly",
            width=15
        )
        status_filter.pack(side=LEFT, padx=(0, 10))
        status_filter.set("الكل")
        status_filter.bind('<<ComboboxSelected>>', self.on_status_filter_change)
        self.status_filter = status_filter
        
        ttk_bs.Button(
            search_frame,
            text="تحديث",
            command=self.load_items,
            bootstyle=SUCCESS
        ).pack(side=LEFT)
        
        # إطار المحتوى
        content_frame = ttk_bs.Frame(items_frame)
        content_frame.pack(fill=BOTH, expand=True)
        
        # الجانب الأيسر - الجدول
        left_frame = ttk_bs.Frame(content_frame)
        left_frame.pack(side=LEFT, fill=BOTH, expand=True, padx=(0, 10))
        
        # جدول العناصر
        self.create_items_table(left_frame)
        
        # أزرار العمليات
        buttons_frame = ttk_bs.Frame(left_frame)
        buttons_frame.pack(fill=X, pady=(10, 0))
        
        ttk_bs.Button(
            buttons_frame,
            text="تعديل",
            command=self.edit_item,
            bootstyle=WARNING
        ).pack(side=LEFT, padx=(0, 10))
        
        ttk_bs.Button(
            buttons_frame,
            text="حذف",
            command=self.delete_item,
            bootstyle=DANGER
        ).pack(side=LEFT, padx=(0, 10))
        
        ttk_bs.Button(
            buttons_frame,
            text="عرض التاريخ",
            command=self.view_item_history,
            bootstyle=INFO
        ).pack(side=LEFT)
        
        # الجانب الأيمن - النموذج
        right_frame = ttk_bs.Frame(content_frame)
        right_frame.pack(side=RIGHT, fill=Y, padx=(10, 0))
        
        self.create_item_form(right_frame)
    
    def create_items_table(self, parent):
        """إنشاء جدول العناصر"""
        # إطار الجدول
        table_frame = ttk_bs.Frame(parent)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ('ID', 'النوع', 'الاسم', 'الموقع', 'المساحة (م²)', 'الحالة')
        self.items_tree = ttk_bs.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=20
        )
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.items_tree.heading(col, text=col)
            if col == 'ID':
                self.items_tree.column(col, width=50, anchor=CENTER)
            elif col == 'النوع':
                self.items_tree.column(col, width=80, anchor=CENTER)
            elif col == 'الاسم':
                self.items_tree.column(col, width=150, anchor=W)
            elif col == 'الموقع':
                self.items_tree.column(col, width=120, anchor=W)
            elif col == 'المساحة (م²)':
                self.items_tree.column(col, width=100, anchor=E)
            elif col == 'الحالة':
                self.items_tree.column(col, width=80, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.items_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط الأحداث
        self.items_tree.bind('<Double-1>', self.on_item_double_click)
        self.items_tree.bind('<<TreeviewSelect>>', self.on_item_select)

    def create_item_form(self, parent):
        """إنشاء نموذج العنصر"""
        # إطار النموذج
        form_frame = ttk_bs.LabelFrame(parent, text="إضافة/تعديل عنصر", padding=10)
        form_frame.pack(fill=BOTH, expand=True)

        # نوع العنصر
        ttk_bs.Label(form_frame, text="نوع العنصر *:").pack(anchor=W, pady=(0, 5))
        type_combo = ttk_bs.Combobox(
            form_frame,
            textvariable=self.item_type,
            values=["طبلية", "قاطع", "جندولة", "استناد"],
            state="readonly",
            width=25
        )
        type_combo.pack(fill=X, pady=(0, 10))

        # اسم العنصر
        ttk_bs.Label(form_frame, text="اسم العنصر *:").pack(anchor=W, pady=(0, 5))
        ttk_bs.Entry(
            form_frame,
            textvariable=self.item_name,
            width=25
        ).pack(fill=X, pady=(0, 10))

        # الموقع
        ttk_bs.Label(form_frame, text="الموقع:").pack(anchor=W, pady=(0, 5))
        ttk_bs.Entry(
            form_frame,
            textvariable=self.location,
            width=25
        ).pack(fill=X, pady=(0, 10))

        # المساحة
        ttk_bs.Label(form_frame, text="المساحة (م²):").pack(anchor=W, pady=(0, 5))
        ttk_bs.Entry(
            form_frame,
            textvariable=self.area_sqm,
            width=25
        ).pack(fill=X, pady=(0, 10))

        # الحالة
        ttk_bs.Label(form_frame, text="الحالة:").pack(anchor=W, pady=(0, 5))
        status_combo = ttk_bs.Combobox(
            form_frame,
            textvariable=self.status,
            values=["متاح", "مؤجر", "صيانة", "غير متاح"],
            state="readonly",
            width=25
        )
        status_combo.pack(fill=X, pady=(0, 10))

        # الوصف
        ttk_bs.Label(form_frame, text="الوصف:").pack(anchor=W, pady=(0, 5))
        ttk_bs.Entry(
            form_frame,
            textvariable=self.description,
            width=25
        ).pack(fill=X, pady=(0, 20))

        # أزرار النموذج
        ttk_bs.Button(
            form_frame,
            text="حفظ",
            command=self.save_item,
            bootstyle=SUCCESS
        ).pack(fill=X, pady=(0, 5))

        ttk_bs.Button(
            form_frame,
            text="جديد",
            command=self.clear_item_form,
            bootstyle=INFO
        ).pack(fill=X, pady=(0, 5))

        ttk_bs.Button(
            form_frame,
            text="إلغاء",
            command=self.cancel_item_form,
            bootstyle=SECONDARY
        ).pack(fill=X)

    def create_activity_types_tab(self, notebook):
        """إنشاء تبويب أنواع الأنشطة"""
        activity_frame = ttk_bs.Frame(notebook, padding=10)
        notebook.add(activity_frame, text="أنواع الأنشطة")

        # إطار المحتوى
        content_frame = ttk_bs.Frame(activity_frame)
        content_frame.pack(fill=BOTH, expand=True)

        # الجانب الأيسر - الجدول
        left_frame = ttk_bs.Frame(content_frame)
        left_frame.pack(side=LEFT, fill=BOTH, expand=True, padx=(0, 10))

        # جدول أنواع الأنشطة
        self.create_activity_types_table(left_frame)

        # أزرار العمليات
        buttons_frame = ttk_bs.Frame(left_frame)
        buttons_frame.pack(fill=X, pady=(10, 0))

        ttk_bs.Button(
            buttons_frame,
            text="تعديل",
            command=self.edit_activity_type,
            bootstyle=WARNING
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bs.Button(
            buttons_frame,
            text="حذف",
            command=self.delete_activity_type,
            bootstyle=DANGER
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bs.Button(
            buttons_frame,
            text="إحصائيات",
            command=self.view_activity_stats,
            bootstyle=INFO
        ).pack(side=LEFT)

        # الجانب الأيمن - النموذج
        right_frame = ttk_bs.Frame(content_frame)
        right_frame.pack(side=RIGHT, fill=Y, padx=(10, 0))

        self.create_activity_type_form(right_frame)

    def create_activity_types_table(self, parent):
        """إنشاء جدول أنواع الأنشطة"""
        # إطار الجدول
        table_frame = ttk_bs.Frame(parent)
        table_frame.pack(fill=BOTH, expand=True)

        # إنشاء Treeview
        columns = ('ID', 'اسم النشاط', 'النوع', 'السعر الأساسي (م²)', 'الوصف')
        self.activity_types_tree = ttk_bs.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=20
        )

        # تعيين عناوين الأعمدة
        for col in columns:
            self.activity_types_tree.heading(col, text=col)
            if col == 'ID':
                self.activity_types_tree.column(col, width=50, anchor=CENTER)
            elif col == 'اسم النشاط':
                self.activity_types_tree.column(col, width=150, anchor=W)
            elif col == 'النوع':
                self.activity_types_tree.column(col, width=100, anchor=CENTER)
            elif col == 'السعر الأساسي (م²)':
                self.activity_types_tree.column(col, width=120, anchor=E)
            elif col == 'الوصف':
                self.activity_types_tree.column(col, width=200, anchor=W)

        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.activity_types_tree.yview)
        self.activity_types_tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.activity_types_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)

        # ربط الأحداث
        self.activity_types_tree.bind('<Double-1>', self.on_activity_type_double_click)
        self.activity_types_tree.bind('<<TreeviewSelect>>', self.on_activity_type_select)

    def create_activity_type_form(self, parent):
        """إنشاء نموذج نوع النشاط"""
        # إطار النموذج
        form_frame = ttk_bs.LabelFrame(parent, text="إضافة/تعديل نوع نشاط", padding=10)
        form_frame.pack(fill=BOTH, expand=True)

        # اسم النشاط
        ttk_bs.Label(form_frame, text="اسم النشاط *:").pack(anchor=W, pady=(0, 5))
        ttk_bs.Entry(
            form_frame,
            textvariable=self.activity_name,
            width=25
        ).pack(fill=X, pady=(0, 10))

        # نوع النشاط
        ttk_bs.Label(form_frame, text="نوع النشاط *:").pack(anchor=W, pady=(0, 5))
        activity_type_combo = ttk_bs.Combobox(
            form_frame,
            textvariable=self.activity_type_category,
            values=["استهلاكي", "غذائي"],
            state="readonly",
            width=25
        )
        activity_type_combo.pack(fill=X, pady=(0, 10))

        # السعر الأساسي
        ttk_bs.Label(form_frame, text="السعر الأساسي (م²):").pack(anchor=W, pady=(0, 5))
        ttk_bs.Entry(
            form_frame,
            textvariable=self.base_price_per_sqm,
            width=25
        ).pack(fill=X, pady=(0, 10))

        # الوصف
        ttk_bs.Label(form_frame, text="الوصف:").pack(anchor=W, pady=(0, 5))
        ttk_bs.Entry(
            form_frame,
            textvariable=self.activity_description,
            width=25
        ).pack(fill=X, pady=(0, 20))

        # أزرار النموذج
        ttk_bs.Button(
            form_frame,
            text="حفظ",
            command=self.save_activity_type,
            bootstyle=SUCCESS
        ).pack(fill=X, pady=(0, 5))

        ttk_bs.Button(
            form_frame,
            text="جديد",
            command=self.clear_activity_type_form,
            bootstyle=INFO
        ).pack(fill=X, pady=(0, 5))

        ttk_bs.Button(
            form_frame,
            text="إلغاء",
            command=self.cancel_activity_type_form,
            bootstyle=SECONDARY
        ).pack(fill=X)

    def create_statistics_tab(self, notebook):
        """إنشاء تبويب الإحصائيات"""
        stats_frame = ttk_bs.Frame(notebook, padding=20)
        notebook.add(stats_frame, text="الإحصائيات")

        # عنوان
        title_label = ttk_bs.Label(
            stats_frame,
            text="إحصائيات الطبليات والعناصر",
            font=("Arial", 14, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 20))

        # إطار الإحصائيات
        stats_container = ttk_bs.Frame(stats_frame)
        stats_container.pack(fill=BOTH, expand=True)

        # إحصائيات العناصر
        items_stats_frame = ttk_bs.LabelFrame(stats_container, text="إحصائيات العناصر", padding=10)
        items_stats_frame.pack(fill=X, pady=(0, 20))

        self.items_stats_label = ttk_bs.Label(
            items_stats_frame,
            text="سيتم تحميل الإحصائيات...",
            font=("Arial", 12)
        )
        self.items_stats_label.pack()

        # إحصائيات أنواع الأنشطة
        activities_stats_frame = ttk_bs.LabelFrame(stats_container, text="إحصائيات أنواع الأنشطة", padding=10)
        activities_stats_frame.pack(fill=X, pady=(0, 20))

        self.activities_stats_label = ttk_bs.Label(
            activities_stats_frame,
            text="سيتم تحميل الإحصائيات...",
            font=("Arial", 12)
        )
        self.activities_stats_label.pack()

        # زر تحديث الإحصائيات
        ttk_bs.Button(
            stats_frame,
            text="تحديث الإحصائيات",
            command=self.update_statistics,
            bootstyle=INFO
        ).pack(pady=20)

        # تحميل الإحصائيات
        self.update_statistics()

    def load_items(self):
        """تحميل قائمة العناصر"""
        try:
            # مسح البيانات الحالية
            for item in self.items_tree.get_children():
                self.items_tree.delete(item)

            # تحميل العناصر
            items = self.rental_item_model.get_all_items()

            for item in items:
                self.items_tree.insert('', 'end', values=(
                    item['item_id'],
                    item['item_type'],
                    item['item_name'],
                    item['location'] or '',
                    f"{item['area_sqm']:.2f}" if item['area_sqm'] else "0.00",
                    item['status']
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل العناصر:\n{str(e)}")

    def load_activity_types(self):
        """تحميل قائمة أنواع الأنشطة"""
        try:
            # مسح البيانات الحالية
            for item in self.activity_types_tree.get_children():
                self.activity_types_tree.delete(item)

            # تحميل أنواع الأنشطة
            self.activity_types_list = self.activity_type_model.get_all_activity_types()

            for activity_type in self.activity_types_list:
                self.activity_types_tree.insert('', 'end', values=(
                    activity_type['activity_type_id'],
                    activity_type['activity_name'],
                    activity_type['activity_type'],
                    f"{activity_type['base_price_per_sqm']:.2f}" if activity_type['base_price_per_sqm'] else "0.00",
                    activity_type['description'] or ''
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل أنواع الأنشطة:\n{str(e)}")

    def search_items(self):
        """البحث في العناصر"""
        search_term = self.search_term.get().strip()

        try:
            # مسح البيانات الحالية
            for item in self.items_tree.get_children():
                self.items_tree.delete(item)

            if search_term:
                items = self.rental_item_model.search_items(search_term)
            else:
                items = self.rental_item_model.get_all_items()

            for item in items:
                self.items_tree.insert('', 'end', values=(
                    item['item_id'],
                    item['item_type'],
                    item['item_name'],
                    item['location'] or '',
                    f"{item['area_sqm']:.2f}" if item['area_sqm'] else "0.00",
                    item['status']
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث:\n{str(e)}")

    def on_search(self, event):
        """البحث عند الكتابة"""
        self.window.after(500, self.search_items)

    def on_type_filter_change(self, event):
        """تغيير فلتر النوع"""
        item_type = self.type_filter.get()

        try:
            # مسح البيانات الحالية
            for item in self.items_tree.get_children():
                self.items_tree.delete(item)

            if item_type == "الكل":
                items = self.rental_item_model.get_all_items()
            else:
                items = self.rental_item_model.get_all_items(item_type)

            for item in items:
                self.items_tree.insert('', 'end', values=(
                    item['item_id'],
                    item['item_type'],
                    item['item_name'],
                    item['location'] or '',
                    f"{item['area_sqm']:.2f}" if item['area_sqm'] else "0.00",
                    item['status']
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تطبيق الفلتر:\n{str(e)}")

    def on_status_filter_change(self, event):
        """تغيير فلتر الحالة"""
        status = self.status_filter.get()

        try:
            # مسح البيانات الحالية
            for item in self.items_tree.get_children():
                self.items_tree.delete(item)

            if status == "الكل":
                items = self.rental_item_model.get_all_items()
            else:
                items = self.rental_item_model.get_all_items(status=status)

            for item in items:
                self.items_tree.insert('', 'end', values=(
                    item['item_id'],
                    item['item_type'],
                    item['item_name'],
                    item['location'] or '',
                    f"{item['area_sqm']:.2f}" if item['area_sqm'] else "0.00",
                    item['status']
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تطبيق الفلتر:\n{str(e)}")

    def save_item(self):
        """حفظ بيانات العنصر"""
        try:
            # جمع البيانات
            item_data = {
                'item_type': self.item_type.get(),
                'item_name': self.item_name.get().strip(),
                'location': self.location.get().strip(),
                'area_sqm': self.area_sqm.get(),
                'status': self.status.get(),
                'description': self.description.get().strip()
            }

            # التحقق من البيانات الأساسية
            if not item_data['item_name']:
                messagebox.showerror("خطأ", "اسم العنصر مطلوب")
                return

            # حفظ أو تحديث
            if self.item_id.get() == 0:
                # إضافة عنصر جديد
                item_id = self.rental_item_model.create_item(item_data)
                if item_id:
                    messagebox.showinfo("نجح", "تم إضافة العنصر بنجاح")
                    self.clear_item_form()
                    self.load_items()
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة العنصر")
            else:
                # تحديث عنصر موجود
                if self.rental_item_model.update_item(self.item_id.get(), item_data):
                    messagebox.showinfo("نجح", "تم تحديث العنصر بنجاح")
                    self.clear_item_form()
                    self.load_items()
                else:
                    messagebox.showerror("خطأ", "فشل في تحديث العنصر")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ العنصر:\n{str(e)}")

    def clear_item_form(self):
        """مسح نموذج العنصر"""
        self.item_id.set(0)
        self.item_type.set("طبلية")
        self.item_name.set("")
        self.location.set("")
        self.area_sqm.set(0.0)
        self.status.set("متاح")
        self.description.set("")

    def cancel_item_form(self):
        """إلغاء نموذج العنصر"""
        self.clear_item_form()

    def edit_item(self):
        """تعديل العنصر المحدد"""
        selected = self.items_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عنصر للتعديل")
            return

        # الحصول على معرف العنصر
        item = self.items_tree.item(selected[0])
        item_id = item['values'][0]

        # تحميل بيانات العنصر
        item_data = self.rental_item_model.get_item_by_id(item_id)
        if item_data:
            self.load_item_to_form(item_data)
        else:
            messagebox.showerror("خطأ", "فشل في تحميل بيانات العنصر")

    def load_item_to_form(self, item_data):
        """تحميل بيانات العنصر في النموذج"""
        self.item_id.set(item_data['item_id'])
        self.item_type.set(item_data['item_type'] or "طبلية")
        self.item_name.set(item_data['item_name'] or "")
        self.location.set(item_data['location'] or "")
        self.area_sqm.set(item_data['area_sqm'] or 0.0)
        self.status.set(item_data['status'] or "متاح")
        self.description.set(item_data['description'] or "")

    def delete_item(self):
        """حذف العنصر المحدد"""
        selected = self.items_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عنصر للحذف")
            return

        # تأكيد الحذف
        if not messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا العنصر؟"):
            return

        # الحصول على معرف العنصر
        item = self.items_tree.item(selected[0])
        item_id = item['values'][0]

        try:
            if self.rental_item_model.delete_item(item_id):
                messagebox.showinfo("نجح", "تم حذف العنصر بنجاح")
                self.load_items()
            else:
                messagebox.showerror("خطأ", "فشل في حذف العنصر")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف العنصر:\n{str(e)}")

    def view_item_history(self):
        """عرض تاريخ العنصر"""
        selected = self.items_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عنصر لعرض تاريخه")
            return

        # الحصول على معرف العنصر
        item = self.items_tree.item(selected[0])
        item_id = item['values'][0]

        try:
            history = self.rental_item_model.get_item_history(item_id)
            if history:
                message = f"تاريخ العنصر رقم {item_id}:\n\n"
                for record in history:
                    message += f"• العقد {record['contract_number']} - {record['supplier_name']}\n"
                    message += f"  من {record['start_date']} إلى {record['end_date']}\n\n"
                messagebox.showinfo("تاريخ العنصر", message)
            else:
                messagebox.showinfo("تاريخ العنصر", "لا يوجد تاريخ لهذا العنصر")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل تاريخ العنصر:\n{str(e)}")

    def save_activity_type(self):
        """حفظ بيانات نوع النشاط"""
        try:
            # جمع البيانات
            activity_data = {
                'activity_name': self.activity_name.get().strip(),
                'activity_type': self.activity_type_category.get(),
                'base_price_per_sqm': self.base_price_per_sqm.get(),
                'description': self.activity_description.get().strip()
            }

            # التحقق من صحة البيانات
            errors = self.activity_type_model.validate_activity_data(activity_data)
            if errors:
                messagebox.showerror("خطأ في البيانات", "\n".join(errors))
                return

            # حفظ أو تحديث
            if self.activity_type_id.get() == 0:
                # إضافة نوع نشاط جديد
                activity_id = self.activity_type_model.create_activity_type(activity_data)
                if activity_id:
                    messagebox.showinfo("نجح", "تم إضافة نوع النشاط بنجاح")
                    self.clear_activity_type_form()
                    self.load_activity_types()
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة نوع النشاط")
            else:
                # تحديث نوع نشاط موجود
                if self.activity_type_model.update_activity_type(self.activity_type_id.get(), activity_data):
                    messagebox.showinfo("نجح", "تم تحديث نوع النشاط بنجاح")
                    self.clear_activity_type_form()
                    self.load_activity_types()
                else:
                    messagebox.showerror("خطأ", "فشل في تحديث نوع النشاط")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ نوع النشاط:\n{str(e)}")

    def clear_activity_type_form(self):
        """مسح نموذج نوع النشاط"""
        self.activity_type_id.set(0)
        self.activity_name.set("")
        self.activity_type_category.set("استهلاكي")
        self.base_price_per_sqm.set(0.0)
        self.activity_description.set("")

    def cancel_activity_type_form(self):
        """إلغاء نموذج نوع النشاط"""
        self.clear_activity_type_form()

    def edit_activity_type(self):
        """تعديل نوع النشاط المحدد"""
        selected = self.activity_types_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار نوع نشاط للتعديل")
            return

        # الحصول على معرف نوع النشاط
        item = self.activity_types_tree.item(selected[0])
        activity_type_id = item['values'][0]

        # تحميل بيانات نوع النشاط
        activity_data = self.activity_type_model.get_activity_type_by_id(activity_type_id)
        if activity_data:
            self.load_activity_type_to_form(activity_data)
        else:
            messagebox.showerror("خطأ", "فشل في تحميل بيانات نوع النشاط")

    def load_activity_type_to_form(self, activity_data):
        """تحميل بيانات نوع النشاط في النموذج"""
        self.activity_type_id.set(activity_data['activity_type_id'])
        self.activity_name.set(activity_data['activity_name'] or "")
        self.activity_type_category.set(activity_data['activity_type'] or "استهلاكي")
        self.base_price_per_sqm.set(activity_data['base_price_per_sqm'] or 0.0)
        self.activity_description.set(activity_data['description'] or "")

    def delete_activity_type(self):
        """حذف نوع النشاط المحدد"""
        selected = self.activity_types_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار نوع نشاط للحذف")
            return

        # تأكيد الحذف
        if not messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف نوع النشاط هذا؟"):
            return

        # الحصول على معرف نوع النشاط
        item = self.activity_types_tree.item(selected[0])
        activity_type_id = item['values'][0]

        try:
            if self.activity_type_model.delete_activity_type(activity_type_id):
                messagebox.showinfo("نجح", "تم حذف نوع النشاط بنجاح")
                self.load_activity_types()
            else:
                messagebox.showerror("خطأ", "لا يمكن حذف نوع النشاط لأنه مرتبط بعقود")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف نوع النشاط:\n{str(e)}")

    def view_activity_stats(self):
        """عرض إحصائيات نوع النشاط"""
        selected = self.activity_types_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار نوع نشاط لعرض إحصائياته")
            return

        # الحصول على معرف نوع النشاط
        item = self.activity_types_tree.item(selected[0])
        activity_type_id = item['values'][0]

        try:
            stats = self.activity_type_model.get_activity_usage_stats(activity_type_id)
            activity_name = item['values'][1]

            message = f"إحصائيات {activity_name}:\n\n"
            message += f"• العقود النشطة: {stats['active_contracts']}\n"
            message += f"• إجمالي المساحة المؤجرة: {stats['total_area']:.2f} م²\n"
            message += f"• إجمالي الإيرادات: {stats['total_revenue']:.2f} د.ك\n"

            messagebox.showinfo("إحصائيات نوع النشاط", message)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الإحصائيات:\n{str(e)}")

    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            # إحصائيات العناصر
            items_summary = self.rental_item_model.get_items_summary()

            items_text = f"إجمالي العناصر: {items_summary['total']}\n\n"
            items_text += "حسب النوع:\n"
            for item_type, count in items_summary['by_type'].items():
                items_text += f"• {item_type}: {count}\n"

            items_text += "\nحسب الحالة:\n"
            for status, count in items_summary['by_status'].items():
                items_text += f"• {status}: {count}\n"

            self.items_stats_label.config(text=items_text)

            # إحصائيات أنواع الأنشطة
            activities_text = f"إجمالي أنواع الأنشطة: {len(self.activity_types_list)}\n\n"

            food_count = len([a for a in self.activity_types_list if a['activity_type'] == 'غذائي'])
            consumer_count = len([a for a in self.activity_types_list if a['activity_type'] == 'استهلاكي'])

            activities_text += f"• الأنشطة الغذائية: {food_count}\n"
            activities_text += f"• الأنشطة الاستهلاكية: {consumer_count}\n"

            self.activities_stats_label.config(text=activities_text)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث الإحصائيات:\n{str(e)}")

    def on_item_double_click(self, event):
        """عند النقر المزدوج على عنصر"""
        self.edit_item()

    def on_item_select(self, event):
        """عند اختيار عنصر"""
        selected = self.items_tree.selection()
        if selected:
            # يمكن إضافة منطق إضافي هنا
            pass

    def on_activity_type_double_click(self, event):
        """عند النقر المزدوج على نوع نشاط"""
        self.edit_activity_type()

    def on_activity_type_select(self, event):
        """عند اختيار نوع نشاط"""
        selected = self.activity_types_tree.selection()
        if selected:
            # يمكن إضافة منطق إضافي هنا
            pass
