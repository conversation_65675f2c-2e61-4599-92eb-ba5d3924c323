# دليل البدء السريع - نظام إدارة عقود الموردين والإيجارات
## جمعية المنقف التعاونية

---

## 🚀 التشغيل السريع (خطوة واحدة)

```bash
node start-system.js
```

هذا الأمر سيقوم بـ:
- ✅ التحقق من التبعيات وتثبيتها
- ✅ إعداد قاعدة البيانات تلقائياً
- ✅ تشغيل اختبارات النظام
- ✅ بدء تشغيل الخادم
- ✅ فتح المتصفح تلقائياً

---

## 📋 المتطلبات الأساسية

### البرامج المطلوبة
- **Node.js** (الإصدار 16 أو أحدث) - [تحميل](https://nodejs.org/)
- **MySQL** (الإصدار 8.0 أو أحدث) - [تحميل](https://dev.mysql.com/downloads/)
- **Git** (اختياري) - [تحميل](https://git-scm.com/)

### التحقق من التثبيت
```bash
node --version    # يجب أن يكون 16+ 
npm --version     # يجب أن يكون 8+
mysql --version   # يجب أن يكون 8.0+
```

---

## ⚡ التثبيت السريع

### الطريقة الأولى: التشغيل التلقائي
```bash
# تحميل المشروع (إذا كان من Git)
git clone [repository-url]
cd supplier-contracts-system

# تشغيل النظام تلقائياً
node start-system.js
```

### الطريقة الثانية: التثبيت اليدوي
```bash
# 1. تثبيت التبعيات
npm install

# 2. إعداد قاعدة البيانات
node scripts/setup-database.js

# 3. اختبار النظام
node scripts/test-system.js

# 4. تشغيل الخادم
npm start
```

---

## 🌐 الوصول للنظام

بعد التشغيل الناجح، يمكنك الوصول إلى:

### الواجهات الرئيسية
- **🏠 الصفحة الرئيسية**: http://localhost:3000
- **📊 لوحة التحكم**: http://localhost:3000/frontend/
- **📋 إدارة العقود المتقدمة**: http://localhost:3000/advanced_contracts_system.html
- **🏢 إدارة الموردين**: http://localhost:3000/enhanced_web_interface.html
- **📄 واجهة العقود البسيطة**: http://localhost:3000/web_interface.html

### API والخدمات
- **🔗 API الرئيسي**: http://localhost:3000/api/v1
- **🩺 فحص الصحة**: http://localhost:3000/api/health
- **📊 إحصائيات**: http://localhost:3000/api/v1/reports/dashboard

---

## 👤 الحسابات الافتراضية

### المدير العام
- **اسم المستخدم**: `admin`
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `password` (يُنصح بتغييرها فوراً)

### قاعدة البيانات
- **اسم قاعدة البيانات**: `supplier_contracts`
- **مستخدم التطبيق**: `contracts_app`
- **كلمة المرور**: `SecurePassword123!`

---

## 🎯 الميزات الأساسية

### 1. 🏢 إدارة الموردين
- ✅ إضافة وتعديل الموردين
- ✅ تصنيف الموردين (بهارات، استهلاكي، أجبان)
- ✅ البحث والتصفية المتقدمة
- ✅ تتبع معلومات الاتصال والوثائق

### 2. 📋 إدارة العقود
- ✅ إنشاء عقود توريد وإيجار
- ✅ تتبع حالة العقود (نشط، منتهي، ملغي)
- ✅ تنبيهات انتهاء العقود
- ✅ إدارة شروط الدفع والأحكام

### 3. 👁️ نظام العيون (الطبليات)
- ✅ تأجير العيون (60×60 سم)
- ✅ تتبع العيون المتاحة والمؤجرة
- ✅ حساب الإيجارات (60 دينار/عين/شهر)
- ✅ إدارة 4 عيون لكل طبلية

### 4. 📊 التقارير والإحصائيات
- ✅ تقارير الموردين (Excel/PDF)
- ✅ تقارير العقود والإيجارات
- ✅ إحصائيات مالية ولوحة تحكم
- ✅ تقارير العيون حسب الأقسام

### 5. 📁 إدارة الملفات
- ✅ رفع وثائق العقود
- ✅ صور الموردين
- ✅ مرفقات متنوعة

---

## 🛠️ أوامر مفيدة

### تشغيل النظام
```bash
npm start              # تشغيل الإنتاج
npm run dev           # تشغيل التطوير مع المراقبة
node start-system.js  # تشغيل تفاعلي شامل
```

### إدارة قاعدة البيانات
```bash
npm run setup:db      # إعداد قاعدة البيانات
npm run migrate       # تشغيل التحديثات
npm run seed          # إدراج بيانات تجريبية
npm run backup        # إنشاء نسخة احتياطية
```

### الاختبار والتطوير
```bash
npm test              # تشغيل الاختبارات
npm run test:watch    # مراقبة الاختبارات
npm run lint          # فحص الكود
npm run lint:fix      # إصلاح مشاكل الكود
```

### البناء والنشر
```bash
npm run build         # بناء المشروع
npm run docker:build  # بناء صورة Docker
npm run docker:run    # تشغيل في Docker
```

---

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في الاتصال بقاعدة البيانات
```bash
# تأكد من تشغيل MySQL
sudo systemctl start mysql  # Linux
brew services start mysql   # macOS

# تحقق من الإعدادات في .env
DB_HOST=localhost
DB_PORT=3306
DB_USER=contracts_app
DB_PASSWORD=SecurePassword123!
```

#### 2. خطأ في التبعيات
```bash
# حذف وإعادة تثبيت
rm -rf node_modules package-lock.json
npm install
```

#### 3. خطأ في الصلاحيات
```bash
# إعطاء صلاحيات للمجلدات
chmod -R 755 backend/uploads
chmod -R 755 logs
chmod -R 755 backups
```

#### 4. المنفذ مستخدم
```bash
# تغيير المنفذ في .env
PORT=3001

# أو إيقاف العملية المستخدمة للمنفذ
lsof -ti:3000 | xargs kill -9
```

---

## 📚 الوثائق الإضافية

- **📖 الدليل الكامل**: `README.md`
- **🏗️ هيكل المشروع**: `project_structure.md`
- **🔧 دليل التطوير**: `docs/development.md`
- **🚀 دليل النشر**: `docs/deployment.md`
- **🔒 دليل الأمان**: `docs/security.md`

---

## 📞 الدعم الفني

### للحصول على المساعدة:
1. **📋 راجع الوثائق** في مجلد `docs/`
2. **🔍 تحقق من السجلات** في مجلد `logs/`
3. **🧪 شغل الاختبارات** باستخدام `npm test`
4. **📧 اتصل بفريق الدعم**: `<EMAIL>`

### معلومات النظام:
- **الإصدار**: 1.0.0
- **آخر تحديث**: 2025
- **المطور**: جمعية المنقف التعاونية
- **الترخيص**: MIT

---

## ✅ قائمة التحقق السريع

- [ ] تم تثبيت Node.js (16+)
- [ ] تم تثبيت MySQL (8.0+)
- [ ] تم تشغيل `npm install`
- [ ] تم إعداد ملف `.env`
- [ ] تم إنشاء قاعدة البيانات
- [ ] تم تشغيل الخادم بنجاح
- [ ] يمكن الوصول للواجهة الرئيسية
- [ ] تم اختبار تسجيل الدخول

🎉 **مبروك! النظام جاهز للاستخدام**
