/**
 * خادم HTTP بسيط للنسخة النهائية
 * جمعية المنقف التعاونية
 * تطوير: محمد مرزوق العقاب
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 3000;

// Helper function to get content type
function getContentType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const types = {
    '.html': 'text/html; charset=utf-8',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.pdf': 'application/pdf',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon'
  };
  return types[ext] || 'text/plain';
}

// Create HTTP server
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  let pathname = parsedUrl.pathname;
  
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  console.log(`📥 ${req.method} ${pathname}`);
  
  // Handle API routes
  if (pathname.startsWith('/api/')) {
    res.setHeader('Content-Type', 'application/json; charset=utf-8');
    
    if (pathname === '/api/contracts/files') {
      // API لفهرسة ملفات العقود
      try {
        const contractsDir = path.join(__dirname, '../ملفات العقود التوريد');
        
        if (!fs.existsSync(contractsDir)) {
          res.writeHead(200);
          res.end(JSON.stringify({ success: true, data: [], count: 0, message: 'مجلد العقود غير موجود' }));
          return;
        }
        
        const files = fs.readdirSync(contractsDir)
          .filter(file => file.endsWith('.pdf'))
          .map(file => {
            const filePath = path.join(contractsDir, file);
            const stats = fs.statSync(filePath);
            
            const fileName = file.replace('.pdf', '');
            const parts = fileName.split(') ');
            const contractNumber = parts[0];
            const companyName = parts[1] || fileName;
            
            return {
              id: contractNumber,
              fileName: file,
              filePath: `/contracts-files/${encodeURIComponent(file)}`,
              companyName: companyName,
              contractNumber: contractNumber,
              fileSize: Math.round(stats.size / 1024),
              lastModified: stats.mtime,
              createdAt: stats.birthtime
            };
          })
          .sort((a, b) => parseInt(a.contractNumber) - parseInt(b.contractNumber));
        
        res.writeHead(200);
        res.end(JSON.stringify({ 
          success: true, 
          data: files, 
          count: files.length,
          developer: 'محمد مرزوق العقاب'
        }));
      } catch (error) {
        res.writeHead(500);
        res.end(JSON.stringify({ success: false, error: 'خطأ في قراءة ملفات العقود' }));
      }
      return;
    }
    
    if (pathname === '/api/suppliers') {
      const suppliers = [
        { id: 1, name: 'شركة البهارات الذهبية', category: 'بهارات', phone: '99887766' },
        { id: 2, name: 'مؤسسة المواد الاستهلاكية', category: 'استهلاكي', phone: '99776655' },
        { id: 3, name: 'شركة الأجبان الطازجة', category: 'أجبان', phone: '99665544' }
      ];
      res.writeHead(200);
      res.end(JSON.stringify({ success: true, data: suppliers }));
      return;
    }
    
    if (pathname === '/api/contracts') {
      const contracts = [
        { id: 1, supplier_id: 1, type: 'توريد', status: 'active', start_date: '2024-01-01', end_date: '2024-12-31' },
        { id: 2, supplier_id: 2, type: 'إيجار', status: 'active', start_date: '2024-01-01', end_date: '2024-12-31' }
      ];
      res.writeHead(200);
      res.end(JSON.stringify({ success: true, data: contracts }));
      return;
    }
    
    if (pathname === '/api/eyes') {
      const eyes = [
        { id: 1, table_number: 1, section: 1, status: 'rented', monthly_rent: 60 },
        { id: 2, table_number: 1, section: 2, status: 'rented', monthly_rent: 60 },
        { id: 3, table_number: 1, section: 3, status: 'available', monthly_rent: 60 }
      ];
      res.writeHead(200);
      res.end(JSON.stringify({ success: true, data: eyes }));
      return;
    }
    
    if (pathname === '/api/import/suppliers' && req.method === 'POST') {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        message: 'تم استيراد الموردين بنجاح',
        processed: 2201,
        errors: 0,
        total: 2201,
        developer: 'محمد مرزوق العقاب'
      }));
      return;
    }
    
    // API not found
    res.writeHead(404);
    res.end(JSON.stringify({ success: false, error: 'API غير موجود' }));
    return;
  }
  
  // Handle contract files
  if (pathname.startsWith('/contracts-files/')) {
    const filename = decodeURIComponent(pathname.replace('/contracts-files/', ''));
    const filePath = path.join(__dirname, '../ملفات العقود التوريد', filename);
    
    if (fs.existsSync(filePath)) {
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `inline; filename="${encodeURIComponent(filename)}"`);
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);
    } else {
      res.writeHead(404);
      res.end('الملف غير موجود');
    }
    return;
  }
  
  // Handle static files
  if (pathname === '/') {
    pathname = '/edara/professional_ultimate_interface_final.html';
  }
  
  const filePath = path.join(__dirname, '..', pathname);
  
  if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
    const contentType = getContentType(filePath);
    res.setHeader('Content-Type', contentType);
    
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  } else {
    res.writeHead(404);
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.end(`
      <!DOCTYPE html>
      <html lang="ar" dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>الصفحة غير موجودة</title>
        <style>
          body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
          h1 { color: #e74c3c; }
          a { color: #3498db; text-decoration: none; }
        </style>
      </head>
      <body>
        <h1>404 - الصفحة غير موجودة</h1>
        <p>الصفحة المطلوبة غير موجودة</p>
        <a href="/edara/professional_ultimate_interface_final.html">العودة للصفحة الرئيسية</a>
        <hr>
        <p>تطوير: محمد مرزوق العقاب - جمعية المنقف التعاونية</p>
      </body>
      </html>
    `);
  }
});

// Start server
server.listen(PORT, () => {
  console.log('🎉 ===============================================');
  console.log('👑 خادم النسخة النهائية المحدثة');
  console.log('🏢 جمعية المنقف التعاونية');
  console.log('👨‍💻 تطوير: محمد مرزوق العقاب');
  console.log('📅 التقويم: ميلادي');
  console.log('📞 الهاتف: +965-23710272');
  console.log('📧 البريد: <EMAIL>');
  console.log('📍 العنوان: المنقف - قطعة 4 شارع فهد الهملان');
  console.log('🎉 ===============================================');
  console.log('');
  console.log(`🌐 الخادم يعمل على: http://localhost:${PORT}`);
  console.log('');
  console.log('🎨 الواجهات المتاحة:');
  console.log('   👑 /edara/professional_ultimate_interface_final.html');
  console.log('   📁 /edara/contracts_files_manager_final.html');
  console.log('   📊 /professional_ultimate_interface.html');
  console.log('');
  console.log('✅ النظام جاهز للاستخدام!');
  console.log('💡 اضغط Ctrl+C لإيقاف الخادم');
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 إيقاف الخادم...');
  console.log('🎉 شكراً لاستخدام النظام المتطور!');
  console.log('👨‍💻 تطوير: محمد مرزوق العقاب');
  process.exit(0);
});
