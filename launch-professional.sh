#!/bin/bash

# نظام إدارة عقود الموردين والإيجارات المتطور
# جمعية المنقف التعاونية
# تطوير وتنفيذ: محمد مرزوق العقاب

# ألوان للطباعة
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# دالة طباعة ملونة
print_color() {
    echo -e "${1}${2}${NC}"
}

# دالة عرض الرأس
show_header() {
    clear
    print_color $CYAN "👑 نظام إدارة عقود الموردين والإيجارات المتطور"
    print_color $CYAN "جمعية المنقف التعاونية"
    echo
    print_color $PURPLE "👨‍💻 تطوير وتنفيذ: محمد مرزوق العقاب"
    print_color $PURPLE "مطور نظم معلومات محترف ومتخصص"
    print_color $CYAN "================================================"
    echo
}

# دالة عرض القائمة
show_menu() {
    print_color $YELLOW "🎨 اختر الواجهة المطلوبة:"
    echo
    echo "1. 👑 الواجهة الاحترافية المتطورة (موصى بها)"
    echo "2. 🔧 واجهة الإدارة المتقدمة (للمديرين)"
    echo "3. 📁 نظام رفع ملفات الموردين"
    echo "4. 🧪 صفحة الاختبار السريع"
    echo "5. 🚀 صفحة التشغيل (جميع الواجهات)"
    echo
    print_color $BLUE "💡 واجهات إضافية:"
    echo "6. 👑 الواجهة المتطورة"
    echo "7. 👥 إدارة الموردين المحسنة"
    echo "8. 📋 إدارة العقود المتقدمة"
    echo "9. 🖥️  الواجهة البسيطة"
    echo
    print_color $GREEN "🔧 أدوات النظام:"
    echo "A. تشغيل الخادم فقط"
    echo "B. اختبار النظام"
    echo "C. عرض معلومات النظام"
    echo
    echo "0. خروج"
    echo
    read -p "اختر رقم أو حرف (0-9, A-C): " choice
}

# دالة بدء الخادم
start_server() {
    print_color $YELLOW "🔄 بدء تشغيل الخادم..."
    
    if [ ! -d "node_modules" ]; then
        print_color $BLUE "📦 تثبيت التبعيات..."
        npm install
    fi
    
    sleep 2
    
    # تشغيل الخادم في الخلفية
    nohup npm start > /dev/null 2>&1 &
    SERVER_PID=$!
    
    print_color $YELLOW "⏳ انتظار تشغيل الخادم..."
    sleep 5
    
    print_color $GREEN "✅ تم تشغيل الخادم بنجاح"
}

# دالة فتح المتصفح
open_browser() {
    local url=$1
    
    if command -v xdg-open > /dev/null; then
        xdg-open "$url" 2>/dev/null
    elif command -v open > /dev/null; then
        open "$url" 2>/dev/null
    else
        print_color $YELLOW "💡 افتح المتصفح يدوياً: $url"
    fi
}

# الواجهة الاحترافية المتطورة
professional_ultimate() {
    echo
    print_color $GREEN "👑 تشغيل الواجهة الاحترافية المتطورة..."
    print_color $BLUE "🎨 تصميم Glass Morphism مع ألوان عالية التباين"
    echo
    
    start_server
    open_browser "http://localhost:3000/professional_ultimate_interface.html"
}

# واجهة الإدارة المتقدمة
advanced_management() {
    echo
    print_color $GREEN "🔧 تشغيل واجهة الإدارة المتقدمة..."
    print_color $BLUE "💼 مخصصة للمديرين مع ألوان عالية التباين"
    echo
    
    start_server
    open_browser "http://localhost:3000/advanced_management_interface.html"
}

# نظام رفع الملفات
upload_system() {
    echo
    print_color $GREEN "📁 تشغيل نظام رفع ملفات الموردين..."
    print_color $BLUE "📊 رفع ملفات Excel و CSV مع التحقق من البيانات"
    echo
    
    start_server
    open_browser "http://localhost:3000/upload_suppliers.html"
}

# صفحة الاختبار السريع
quick_test() {
    echo
    print_color $GREEN "🧪 تشغيل صفحة الاختبار السريع..."
    print_color $BLUE "🔍 اختبار جميع مكونات النظام"
    echo
    
    start_server
    open_browser "http://localhost:3000/quick-upload-test.html"
}

# صفحة التشغيل
launch_page() {
    echo
    print_color $GREEN "🚀 تشغيل صفحة التشغيل..."
    print_color $BLUE "🎯 عرض جميع الواجهات المتاحة"
    echo
    
    start_server
    open_browser "http://localhost:3000/launch-interface.html"
}

# الواجهة المتطورة
ultimate_interface() {
    echo
    print_color $GREEN "👑 تشغيل الواجهة المتطورة..."
    echo
    
    start_server
    open_browser "http://localhost:3000/ultimate_interface.html"
}

# إدارة الموردين المحسنة
enhanced_interface() {
    echo
    print_color $GREEN "👥 تشغيل إدارة الموردين المحسنة..."
    echo
    
    start_server
    open_browser "http://localhost:3000/enhanced_web_interface.html"
}

# إدارة العقود المتقدمة
advanced_contracts() {
    echo
    print_color $GREEN "📋 تشغيل إدارة العقود المتقدمة..."
    echo
    
    start_server
    open_browser "http://localhost:3000/advanced_contracts_system.html"
}

# الواجهة البسيطة
simple_interface() {
    echo
    print_color $GREEN "🖥️ تشغيل الواجهة البسيطة..."
    echo
    
    start_server
    open_browser "http://localhost:3000/web_interface.html"
}

# تشغيل الخادم فقط
server_only() {
    echo
    print_color $GREEN "🔧 تشغيل الخادم فقط..."
    print_color $BLUE "💡 يمكنك فتح أي واجهة يدوياً من المتصفح"
    echo
    
    if [ ! -d "node_modules" ]; then
        print_color $BLUE "📦 تثبيت التبعيات..."
        npm install
    fi
    
    echo
    print_color $CYAN "🌐 الخادم سيعمل على: http://localhost:3000"
    echo
    print_color $YELLOW "🎨 الواجهات المتاحة:"
    echo "   👑 /professional_ultimate_interface.html"
    echo "   🔧 /advanced_management_interface.html"
    echo "   📁 /upload_suppliers.html"
    echo "   🧪 /quick-upload-test.html"
    echo "   🚀 /launch-interface.html"
    echo
    print_color $BLUE "💡 اضغط Ctrl+C لإيقاف الخادم"
    
    npm start
}

# اختبار النظام
test_system() {
    echo
    print_color $GREEN "🧪 تشغيل اختبار النظام..."
    echo
    
    if [ -f "quick-test.sh" ]; then
        ./quick-test.sh
    elif [ -f "quick-test.bat" ]; then
        print_color $YELLOW "💡 قم بتشغيل: ./quick-test.bat"
    else
        print_color $RED "❌ ملف الاختبار غير موجود"
        print_color $BLUE "💡 قم بتشغيل: npm run test:interactive"
    fi
    
    read -p "اضغط Enter للمتابعة..."
}

# عرض معلومات النظام
system_info() {
    echo
    print_color $CYAN "📊 معلومات النظام:"
    echo
    print_color $WHITE "🏢 الشركة: جمعية المنقف التعاونية"
    print_color $WHITE "👨‍💻 المطور: محمد مرزوق العقاب"
    print_color $WHITE "🌐 الخادم: http://localhost:3000"
    print_color $WHITE "📁 المجلد: $(pwd)"
    print_color $WHITE "🕒 التاريخ: $(date)"
    echo
    print_color $YELLOW "🎨 الواجهات الاحترافية الجديدة:"
    echo "   👑 professional_ultimate_interface.html"
    echo "   🔧 advanced_management_interface.html"
    echo "   📁 upload_suppliers.html"
    echo "   🧪 quick-upload-test.html"
    echo
    
    read -p "اضغط Enter للمتابعة..."
}

# الحلقة الرئيسية
main() {
    while true; do
        show_header
        show_menu
        
        case $choice in
            1)
                professional_ultimate
                ;;
            2)
                advanced_management
                ;;
            3)
                upload_system
                ;;
            4)
                quick_test
                ;;
            5)
                launch_page
                ;;
            6)
                ultimate_interface
                ;;
            7)
                enhanced_interface
                ;;
            8)
                advanced_contracts
                ;;
            9)
                simple_interface
                ;;
            [Aa])
                server_only
                ;;
            [Bb])
                test_system
                ;;
            [Cc])
                system_info
                ;;
            0)
                echo
                print_color $CYAN "👋 شكراً لاستخدام نظام إدارة العقود المتطور"
                print_color $PURPLE "👨‍💻 تطوير: محمد مرزوق العقاب"
                print_color $CYAN "🏢 جمعية المنقف التعاونية"
                echo
                exit 0
                ;;
            *)
                echo
                print_color $RED "❌ اختيار غير صحيح"
                print_color $BLUE "💡 يرجى اختيار رقم أو حرف صحيح"
                sleep 2
                ;;
        esac
        
        if [ "$choice" != "A" ] && [ "$choice" != "a" ]; then
            echo
            print_color $GREEN "🎉 تم تشغيل النظام بنجاح!"
            print_color $BLUE "💡 اضغط Ctrl+C في نافذة الخادم لإيقافه"
            echo
            read -p "اضغط Enter للعودة للقائمة الرئيسية..."
        fi
    done
}

# تشغيل البرنامج
main
