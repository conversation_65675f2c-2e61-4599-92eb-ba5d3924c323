#!/usr/bin/env node

/**
 * سكريبت إعداد قاعدة البيانات
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const readline = require('readline');
require('dotenv').config();

// إعدادات قاعدة البيانات
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: 'root', // نستخدم root لإنشاء قاعدة البيانات
  password: '', // سيتم طلبها من المستخدم
  charset: 'utf8mb4',
  multipleStatements: true
};

// إنشاء واجهة للقراءة من المستخدم
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * طرح سؤال للمستخدم
 */
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

/**
 * عرض رسالة ملونة
 */
function colorLog(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * التحقق من وجود MySQL
 */
async function checkMySQLConnection() {
  try {
    colorLog('🔍 التحقق من اتصال MySQL...', 'blue');
    
    // طلب كلمة مرور root
    const rootPassword = await askQuestion('أدخل كلمة مرور MySQL root (اتركها فارغة إذا لم تكن محددة): ');
    dbConfig.password = rootPassword;
    
    const connection = await mysql.createConnection({
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.user,
      password: dbConfig.password
    });
    
    await connection.ping();
    await connection.end();
    
    colorLog('✅ تم الاتصال بـ MySQL بنجاح', 'green');
    return true;
    
  } catch (error) {
    colorLog('❌ فشل الاتصال بـ MySQL:', 'red');
    colorLog(error.message, 'red');
    return false;
  }
}

/**
 * إنشاء قاعدة البيانات والمستخدمين
 */
async function createDatabase() {
  try {
    colorLog('🚀 بدء إنشاء قاعدة البيانات...', 'blue');
    
    const connection = await mysql.createConnection(dbConfig);
    
    // إنشاء قاعدة البيانات
    await connection.query(`
      CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME || 'supplier_contracts'} 
      CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
    `);
    
    colorLog(`✅ تم إنشاء قاعدة البيانات: ${process.env.DB_NAME || 'supplier_contracts'}`, 'green');
    
    // إنشاء مستخدم التطبيق
    const appUser = process.env.DB_USER || 'contracts_app';
    const appPassword = process.env.DB_PASSWORD || 'SecurePassword123!';
    
    await connection.query(`DROP USER IF EXISTS '${appUser}'@'localhost'`);
    await connection.query(`CREATE USER '${appUser}'@'localhost' IDENTIFIED BY '${appPassword}'`);
    await connection.query(`GRANT ALL PRIVILEGES ON ${process.env.DB_NAME || 'supplier_contracts'}.* TO '${appUser}'@'localhost'`);
    
    colorLog(`✅ تم إنشاء مستخدم التطبيق: ${appUser}`, 'green');
    
    // إنشاء مستخدم القراءة فقط
    await connection.query(`DROP USER IF EXISTS 'contracts_readonly'@'localhost'`);
    await connection.query(`CREATE USER 'contracts_readonly'@'localhost' IDENTIFIED BY 'ReadOnlyPass123!'`);
    await connection.query(`GRANT SELECT ON ${process.env.DB_NAME || 'supplier_contracts'}.* TO 'contracts_readonly'@'localhost'`);
    
    colorLog('✅ تم إنشاء مستخدم القراءة فقط', 'green');
    
    // تطبيق الصلاحيات
    await connection.query('FLUSH PRIVILEGES');
    
    await connection.end();
    
  } catch (error) {
    colorLog('❌ فشل في إنشاء قاعدة البيانات:', 'red');
    colorLog(error.message, 'red');
    throw error;
  }
}

/**
 * تنفيذ ملف schema.sql
 */
async function executeSchema() {
  try {
    colorLog('📋 تنفيذ ملف schema.sql...', 'blue');
    
    // قراءة ملف schema.sql
    const schemaPath = path.join(__dirname, '../backend/database/schema.sql');
    
    if (!fs.existsSync(schemaPath)) {
      throw new Error(`ملف schema.sql غير موجود في: ${schemaPath}`);
    }
    
    const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
    
    // الاتصال بقاعدة البيانات الجديدة
    const connection = await mysql.createConnection({
      ...dbConfig,
      database: process.env.DB_NAME || 'supplier_contracts'
    });
    
    // تنفيذ الـ schema
    await connection.query(schemaSQL);
    
    colorLog('✅ تم تنفيذ ملف schema.sql بنجاح', 'green');
    
    await connection.end();
    
  } catch (error) {
    colorLog('❌ فشل في تنفيذ ملف schema.sql:', 'red');
    colorLog(error.message, 'red');
    throw error;
  }
}

/**
 * إدراج البيانات التجريبية
 */
async function insertSampleData() {
  try {
    const insertSample = await askQuestion('هل تريد إدراج بيانات تجريبية؟ (y/n): ');
    
    if (insertSample.toLowerCase() !== 'y') {
      return;
    }
    
    colorLog('📊 إدراج البيانات التجريبية...', 'blue');
    
    const connection = await mysql.createConnection({
      ...dbConfig,
      database: process.env.DB_NAME || 'supplier_contracts'
    });
    
    // إدراج موردين تجريبيين
    await connection.query(`
      INSERT INTO suppliers (supplier_code, name, category_id, phone, email, address, contact_person) VALUES
      ('SUP001', 'شركة البهارات الذهبية', 1, '+965-12345678', '<EMAIL>', 'الكويت - حولي', 'أحمد محمد'),
      ('SUP002', 'مؤسسة المواد الاستهلاكية', 2, '+965-87654321', '<EMAIL>', 'الكويت - الفروانية', 'فاطمة علي'),
      ('SUP003', 'شركة الأجبان الطازجة', 3, '+965-11223344', '<EMAIL>', 'الكويت - الأحمدي', 'محمد خالد')
    `);
    
    // إدراج عقود تجريبية
    await connection.query(`
      INSERT INTO contracts (contract_number, contract_name, supplier_id, contract_type_id, start_date, end_date, total_amount, status) VALUES
      ('CNT-2024-001', 'عقد توريد البهارات', 1, 1, '2024-01-01', '2024-12-31', 5000.000, 'active'),
      ('CNT-2024-002', 'عقد إيجار العيون - قسم الاستهلاكي', 2, 2, '2024-02-01', '2025-01-31', 7200.000, 'active'),
      ('CNT-2024-003', 'عقد توريد الأجبان', 3, 1, '2024-03-01', '2024-12-31', 3500.000, 'active')
    `);
    
    colorLog('✅ تم إدراج البيانات التجريبية بنجاح', 'green');
    
    await connection.end();
    
  } catch (error) {
    colorLog('❌ فشل في إدراج البيانات التجريبية:', 'red');
    colorLog(error.message, 'red');
  }
}

/**
 * اختبار الاتصال النهائي
 */
async function testFinalConnection() {
  try {
    colorLog('🧪 اختبار الاتصال النهائي...', 'blue');
    
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'contracts_app',
      password: process.env.DB_PASSWORD || 'SecurePassword123!',
      database: process.env.DB_NAME || 'supplier_contracts'
    });
    
    // اختبار بعض الاستعلامات
    const [suppliers] = await connection.query('SELECT COUNT(*) as count FROM suppliers');
    const [contracts] = await connection.query('SELECT COUNT(*) as count FROM contracts');
    const [categories] = await connection.query('SELECT COUNT(*) as count FROM supplier_categories');
    
    colorLog('📊 إحصائيات قاعدة البيانات:', 'cyan');
    colorLog(`   - فئات الموردين: ${categories[0].count}`, 'white');
    colorLog(`   - الموردين: ${suppliers[0].count}`, 'white');
    colorLog(`   - العقود: ${contracts[0].count}`, 'white');
    
    await connection.end();
    
    colorLog('✅ تم اختبار الاتصال بنجاح', 'green');
    
  } catch (error) {
    colorLog('❌ فشل في اختبار الاتصال النهائي:', 'red');
    colorLog(error.message, 'red');
    throw error;
  }
}

/**
 * إنشاء المجلدات المطلوبة
 */
function createDirectories() {
  const directories = [
    './backend/uploads',
    './backend/uploads/contracts',
    './backend/uploads/documents',
    './backend/uploads/images',
    './backups',
    './logs'
  ];
  
  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      colorLog(`✅ تم إنشاء المجلد: ${dir}`, 'green');
    }
  });
}

/**
 * الدالة الرئيسية
 */
async function main() {
  try {
    colorLog('🚀 مرحباً بك في معالج إعداد قاعدة البيانات', 'cyan');
    colorLog('نظام إدارة عقود الموردين والإيجارات - جمعية المنقف', 'cyan');
    colorLog('================================================', 'cyan');
    
    // التحقق من اتصال MySQL
    const mysqlConnected = await checkMySQLConnection();
    if (!mysqlConnected) {
      colorLog('❌ يرجى التأكد من تشغيل MySQL والمحاولة مرة أخرى', 'red');
      process.exit(1);
    }
    
    // إنشاء المجلدات
    colorLog('📁 إنشاء المجلدات المطلوبة...', 'blue');
    createDirectories();
    
    // إنشاء قاعدة البيانات
    await createDatabase();
    
    // تنفيذ schema.sql
    await executeSchema();
    
    // إدراج البيانات التجريبية
    await insertSampleData();
    
    // اختبار الاتصال النهائي
    await testFinalConnection();
    
    colorLog('🎉 تم إعداد قاعدة البيانات بنجاح!', 'green');
    colorLog('يمكنك الآن تشغيل التطبيق باستخدام: npm start', 'cyan');
    
  } catch (error) {
    colorLog('❌ فشل في إعداد قاعدة البيانات:', 'red');
    colorLog(error.message, 'red');
    process.exit(1);
  } finally {
    rl.close();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = { main };
