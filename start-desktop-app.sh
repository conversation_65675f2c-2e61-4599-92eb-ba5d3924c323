#!/bin/bash

# تطبيق نظام إدارة العقود - سطح المكتب
# جمعية المنقف التعاونية
# تطوير وتنفيذ: محمد مرزوق العقاب

# ألوان للطباعة
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# دالة طباعة ملونة
print_color() {
    echo -e "${1}${2}${NC}"
}

# عرض الرأس
clear
print_color $CYAN "🎉 ==============================================="
print_color $CYAN "💻 تطبيق نظام إدارة العقود - سطح المكتب"
print_color $CYAN "🏢 جمعية المنقف التعاونية"
echo
print_color $PURPLE "👨‍💻 تطوير وتنفيذ: محمد مرزوق العقاب"
print_color $PURPLE "🎯 مطور نظم معلومات محترف ومتخصص"
print_color $CYAN "🎉 ==============================================="
echo

print_color $BLUE "📦 التحقق من التبعيات..."
if [ ! -d "node_modules" ]; then
    print_color $YELLOW "🔄 تثبيت التبعيات..."
    npm install
    echo
fi

print_color $BLUE "🎨 إنشاء الأيقونات..."
mkdir -p assets
node assets/create-icons.js

print_color $BLUE "📊 إنشاء البيانات النموذجية..."
if [ ! -d "sample_data" ]; then
    print_color $YELLOW "📄 إنشاء ملفات البيانات النموذجية..."
    node backend/utils/create-excel-files.js
    echo
fi

echo
print_color $CYAN "🎉 ==============================================="
print_color $GREEN "🚀 بدء تشغيل تطبيق سطح المكتب"
print_color $BLUE "💻 تطبيق Desktop App متطور"
print_color $GREEN "🌐 يعمل بدون إنترنت"
print_color $BLUE "📊 قاعدة بيانات محلية SQLite"
print_color $PURPLE "👨‍💻 المطور: محمد مرزوق العقاب"
print_color $GREEN "✅ جميع الوظائف نشطة ومفعلة"
print_color $CYAN "🎉 ==============================================="
echo

print_color $YELLOW "🔗 مميزات التطبيق:"
echo "   💻 واجهة Desktop App احترافية"
echo "   🎮 قوائم وأزرار تفاعلية"
echo "   ⌨️ اختصارات لوحة المفاتيح"
echo "   📁 نوافذ حوار لاختيار الملفات"
echo "   🔔 إشعارات النظام"
echo "   🖼️ أيقونة مخصصة في شريط المهام"
echo "   📊 نوافذ متعددة ومرنة"
echo "   🎨 تصميم متجاوب ومتطور"
echo

print_color $BLUE "🎯 الواجهات المتاحة في التطبيق:"
echo "   🏠 الصفحة الرئيسية (Ctrl+H)"
echo "   👑 الواجهة الاحترافية (Ctrl+P)"
echo "   👥 إدارة الموردين (Ctrl+1)"
echo "   📋 إدارة العقود (Ctrl+2)"
echo "   👁️ إدارة العيون (Ctrl+3)"
echo "   📊 التقارير (Ctrl+4)"
echo "   📁 رفع الملفات (Ctrl+U)"
echo

print_color $GREEN "⌨️ اختصارات لوحة المفاتيح:"
echo "   F5 / Ctrl+R - إعادة تحميل"
echo "   F11 - ملء الشاشة"
echo "   F12 - أدوات المطور"
echo "   Ctrl+Plus - تكبير"
echo "   Ctrl+Minus - تصغير"
echo "   Ctrl+0 - الحجم الطبيعي"
echo "   Ctrl+Q - خروج"
echo

print_color $CYAN "📊 البيانات النموذجية المتاحة:"
echo "   👥 25 مورد (بهارات، استهلاكي، أجبان)"
echo "   📋 30 عقد (توريد، إيجار، خدمات)"
echo "   👁️ 45 إيجار عين نشط"
echo "   🏢 20 طبلية × 4 أقسام = 80 عين"
echo "   💰 إيرادات شهرية: 2,700 د.ك"
echo

print_color $WHITE "🔐 بيانات تسجيل الدخول:"
echo "   👤 اسم المستخدم: admin"
echo "   🔑 كلمة المرور: admin123"
echo

print_color $YELLOW "💡 نصائح الاستخدام:"
echo "   • استخدم القوائم العلوية للتنقل السريع"
echo "   • اضغط F11 للشاشة الكاملة"
echo "   • استخدم Ctrl+أرقام للانتقال السريع"
echo "   • ارفع الملفات من قائمة \"أدوات\""
echo "   • تحقق من \"مساعدة\" للمزيد من المعلومات"
echo

sleep 3

print_color $GREEN "🚀 تشغيل تطبيق سطح المكتب..."
echo

npm run electron
