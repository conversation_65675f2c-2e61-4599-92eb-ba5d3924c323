#!/usr/bin/env node

/**
 * سكريبت تشغيل اختبار نظام رفع الملفات
 * جمعية المنقف التعاونية
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// ألوان للطباعة
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function showHeader() {
  console.clear();
  colorLog('🧪 اختبار نظام رفع ملفات الموردين', 'cyan');
  colorLog('جمعية المنقف التعاونية', 'cyan');
  colorLog('='.repeat(60), 'cyan');
  colorLog('', 'white');
}

function showMenu() {
  colorLog('اختر نوع الاختبار:', 'yellow');
  colorLog('', 'white');
  colorLog('1️⃣  اختبار شامل للنظام', 'white');
  colorLog('2️⃣  اختبار سريع للملفات', 'white');
  colorLog('3️⃣  تشغيل الخادم واختبار يدوي', 'white');
  colorLog('4️⃣  إنشاء ملفات اختبار فقط', 'white');
  colorLog('5️⃣  فتح صفحة الاختبار في المتصفح', 'white');
  colorLog('6️⃣  عرض حالة النظام', 'white');
  colorLog('0️⃣  خروج', 'white');
  colorLog('', 'white');
  process.stdout.write('اختر رقم (0-6): ');
}

async function runFullTest() {
  colorLog('\n🔄 تشغيل الاختبار الشامل...', 'cyan');
  
  return new Promise((resolve) => {
    const testProcess = spawn('node', ['test-upload-system.js'], {
      stdio: 'inherit'
    });
    
    testProcess.on('close', (code) => {
      if (code === 0) {
        colorLog('\n✅ الاختبار الشامل اكتمل بنجاح', 'green');
      } else {
        colorLog('\n❌ الاختبار الشامل فشل', 'red');
      }
      resolve();
    });
  });
}

async function runQuickTest() {
  colorLog('\n⚡ تشغيل الاختبار السريع...', 'cyan');
  
  const filesToCheck = [
    'upload_suppliers.html',
    'quick-upload-test.html',
    'backend/routes/upload.js',
    'package.json'
  ];
  
  let allGood = true;
  
  for (const file of filesToCheck) {
    if (fs.existsSync(file)) {
      colorLog(`   ✅ ${file}`, 'green');
    } else {
      colorLog(`   ❌ ${file} - غير موجود`, 'red');
      allGood = false;
    }
  }
  
  if (allGood) {
    colorLog('\n🎉 جميع الملفات الأساسية موجودة!', 'green');
  } else {
    colorLog('\n⚠️ بعض الملفات مفقودة', 'yellow');
  }
  
  // اختبار package.json
  try {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const requiredDeps = ['multer', 'xlsx', 'csv-parser'];
    
    colorLog('\n📦 التحقق من التبعيات:', 'cyan');
    for (const dep of requiredDeps) {
      if (pkg.dependencies && pkg.dependencies[dep]) {
        colorLog(`   ✅ ${dep}`, 'green');
      } else {
        colorLog(`   ❌ ${dep} - مفقود`, 'red');
        allGood = false;
      }
    }
  } catch (error) {
    colorLog('\n❌ خطأ في قراءة package.json', 'red');
    allGood = false;
  }
  
  return allGood;
}

async function startServerAndTest() {
  colorLog('\n🚀 تشغيل الخادم...', 'cyan');
  
  // التحقق من وجود node_modules
  if (!fs.existsSync('node_modules')) {
    colorLog('❌ node_modules غير موجود. قم بتشغيل: npm install', 'red');
    return;
  }
  
  // تشغيل الخادم
  const serverProcess = spawn('npm', ['start'], {
    stdio: 'pipe'
  });
  
  let serverStarted = false;
  
  serverProcess.stdout.on('data', (data) => {
    const output = data.toString();
    console.log(output);
    
    if (output.includes('Server running') || output.includes('listening')) {
      if (!serverStarted) {
        serverStarted = true;
        setTimeout(() => {
          colorLog('\n🌐 فتح صفحات الاختبار...', 'cyan');
          
          // فتح صفحات الاختبار
          const urls = [
            'http://localhost:3000/quick-upload-test.html',
            'http://localhost:3000/upload_suppliers.html'
          ];
          
          urls.forEach((url, index) => {
            setTimeout(() => {
              exec(`start ${url}`, (error) => {
                if (error) {
                  colorLog(`💡 افتح يدوياً: ${url}`, 'yellow');
                }
              });
            }, index * 1000);
          });
          
        }, 2000);
      }
    }
  });
  
  serverProcess.stderr.on('data', (data) => {
    console.error(data.toString());
  });
  
  // إيقاف الخادم عند الضغط على Ctrl+C
  process.on('SIGINT', () => {
    colorLog('\n🛑 إيقاف الخادم...', 'yellow');
    serverProcess.kill();
    process.exit(0);
  });
  
  colorLog('\n💡 اضغط Ctrl+C لإيقاف الخادم', 'yellow');
}

async function createTestFiles() {
  colorLog('\n📄 إنشاء ملفات الاختبار...', 'cyan');
  
  const testData = [
    {
      'اسم المورد': 'شركة الاختبار الأولى',
      'الفئة': 'بهارات',
      'رقم الهاتف': '+965-11111111',
      'البريد الإلكتروني': '<EMAIL>',
      'العنوان': 'الكويت - منطقة الاختبار',
      'الشخص المسؤول': 'مدير الاختبار'
    },
    {
      'اسم المورد': 'مؤسسة الاختبار الثانية',
      'الفئة': 'استهلاكي',
      'رقم الهاتف': '+965-22222222',
      'البريد الإلكتروني': '<EMAIL>',
      'العنوان': 'الكويت - منطقة الاختبار 2',
      'الشخص المسؤول': 'مدير الاختبار 2'
    },
    {
      'اسم المورد': 'شركة الاختبار الثالثة',
      'الفئة': 'أجبان',
      'رقم الهاتف': '+965-33333333',
      'البريد الإلكتروني': '<EMAIL>',
      'العنوان': 'الكويت - منطقة الاختبار 3',
      'الشخص المسؤول': 'مدير الاختبار 3'
    },
    {
      'اسم المورد': 'مورد خاطئ للاختبار',
      'الفئة': 'فئة خاطئة',
      'رقم الهاتف': 'رقم خاطئ',
      'البريد الإلكتروني': 'بريد خاطئ',
      'العنوان': '',
      'الشخص المسؤول': ''
    }
  ];
  
  // إنشاء ملف CSV
  const csvHeader = Object.keys(testData[0]).join(',');
  const csvRows = testData.map(row => Object.values(row).join(','));
  const csvContent = [csvHeader, ...csvRows].join('\n');
  
  fs.writeFileSync('test-suppliers.csv', csvContent, 'utf8');
  colorLog('   ✅ تم إنشاء test-suppliers.csv', 'green');
  
  // إنشاء ملف Excel إذا كان XLSX متاح
  try {
    const XLSX = require('xlsx');
    const ws = XLSX.utils.json_to_sheet(testData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'الموردين');
    
    ws['!cols'] = [
      { width: 25 }, { width: 15 }, { width: 20 },
      { width: 30 }, { width: 25 }, { width: 20 }
    ];
    
    XLSX.writeFile(wb, 'test-suppliers.xlsx');
    colorLog('   ✅ تم إنشاء test-suppliers.xlsx', 'green');
  } catch (error) {
    colorLog('   ⚠️ لم يتم إنشاء ملف Excel (XLSX غير متاح)', 'yellow');
  }
  
  colorLog('\n📁 ملفات الاختبار جاهزة للاستخدام!', 'green');
}

async function openTestPage() {
  colorLog('\n🌐 فتح صفحة الاختبار...', 'cyan');
  
  const url = 'http://localhost:3000/quick-upload-test.html';
  
  exec(`start ${url}`, (error) => {
    if (error) {
      colorLog(`💡 افتح يدوياً: ${url}`, 'yellow');
      colorLog('💡 تأكد من تشغيل الخادم أولاً: npm start', 'yellow');
    } else {
      colorLog('✅ تم فتح صفحة الاختبار في المتصفح', 'green');
    }
  });
}

async function showSystemStatus() {
  colorLog('\n📊 حالة النظام:', 'cyan');
  
  // التحقق من الملفات
  const files = [
    'upload_suppliers.html',
    'quick-upload-test.html',
    'backend/routes/upload.js',
    'backend/api/server.js',
    'package.json'
  ];
  
  colorLog('\n📁 الملفات:', 'yellow');
  files.forEach(file => {
    if (fs.existsSync(file)) {
      colorLog(`   ✅ ${file}`, 'green');
    } else {
      colorLog(`   ❌ ${file}`, 'red');
    }
  });
  
  // التحقق من التبعيات
  colorLog('\n📦 التبعيات:', 'yellow');
  if (fs.existsSync('node_modules')) {
    colorLog('   ✅ node_modules موجود', 'green');
    
    const deps = ['multer', 'xlsx', 'csv-parser'];
    deps.forEach(dep => {
      if (fs.existsSync(`node_modules/${dep}`)) {
        colorLog(`   ✅ ${dep}`, 'green');
      } else {
        colorLog(`   ❌ ${dep}`, 'red');
      }
    });
  } else {
    colorLog('   ❌ node_modules غير موجود', 'red');
    colorLog('   💡 قم بتشغيل: npm install', 'yellow');
  }
  
  // التحقق من ملفات الاختبار
  colorLog('\n🧪 ملفات الاختبار:', 'yellow');
  const testFiles = ['test-suppliers.csv', 'test-suppliers.xlsx'];
  testFiles.forEach(file => {
    if (fs.existsSync(file)) {
      colorLog(`   ✅ ${file}`, 'green');
    } else {
      colorLog(`   ❌ ${file}`, 'red');
    }
  });
}

async function main() {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  while (true) {
    showHeader();
    showMenu();
    
    const choice = await new Promise(resolve => {
      rl.question('', resolve);
    });
    
    switch (choice.trim()) {
      case '1':
        await runFullTest();
        break;
      case '2':
        await runQuickTest();
        break;
      case '3':
        await startServerAndTest();
        break;
      case '4':
        await createTestFiles();
        break;
      case '5':
        await openTestPage();
        break;
      case '6':
        await showSystemStatus();
        break;
      case '0':
        colorLog('\n👋 وداعاً!', 'cyan');
        rl.close();
        process.exit(0);
        break;
      default:
        colorLog('\n❌ اختيار غير صحيح', 'red');
    }
    
    colorLog('\n⏸️ اضغط Enter للمتابعة...', 'yellow');
    await new Promise(resolve => {
      rl.question('', resolve);
    });
  }
}

// تشغيل البرنامج
main().catch(error => {
  colorLog(`❌ خطأ: ${error.message}`, 'red');
  process.exit(1);
});
