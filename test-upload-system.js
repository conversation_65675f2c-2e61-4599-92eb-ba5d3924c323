/**
 * اختبار شامل لنظام رفع ملفات الموردين
 * جمعية المنقف التعاونية
 */

const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

// ألوان للطباعة
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function colorLog(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * اختبار وجود الملفات المطلوبة
 */
async function testRequiredFiles() {
  colorLog('\n🔍 اختبار وجود الملفات المطلوبة...', 'cyan');
  
  const requiredFiles = [
    'upload_suppliers.html',
    'quick-upload-test.html',
    'backend/routes/upload.js',
    'backend/api/server.js',
    'package.json'
  ];
  
  let allFilesExist = true;
  
  for (const file of requiredFiles) {
    try {
      await fs.access(file);
      colorLog(`   ✅ ${file}`, 'green');
    } catch (error) {
      colorLog(`   ❌ ${file} - غير موجود`, 'red');
      allFilesExist = false;
    }
  }
  
  return allFilesExist;
}

/**
 * اختبار التبعيات المطلوبة
 */
async function testDependencies() {
  colorLog('\n📦 اختبار التبعيات المطلوبة...', 'cyan');
  
  try {
    const packageJson = JSON.parse(await fs.readFile('package.json', 'utf8'));
    const requiredDeps = ['multer', 'xlsx', 'csv-parser', 'express'];
    
    let allDepsExist = true;
    
    for (const dep of requiredDeps) {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        colorLog(`   ✅ ${dep} - ${packageJson.dependencies[dep]}`, 'green');
      } else {
        colorLog(`   ❌ ${dep} - غير موجود في package.json`, 'red');
        allDepsExist = false;
      }
    }
    
    return allDepsExist;
  } catch (error) {
    colorLog(`   ❌ خطأ في قراءة package.json: ${error.message}`, 'red');
    return false;
  }
}

/**
 * اختبار تثبيت التبعيات
 */
async function testNodeModules() {
  colorLog('\n📁 اختبار تثبيت التبعيات...', 'cyan');
  
  try {
    await fs.access('node_modules');
    colorLog('   ✅ مجلد node_modules موجود', 'green');
    
    // اختبار وجود التبعيات المطلوبة
    const requiredModules = ['multer', 'xlsx', 'csv-parser'];
    let allModulesExist = true;
    
    for (const module of requiredModules) {
      try {
        await fs.access(path.join('node_modules', module));
        colorLog(`   ✅ ${module} مثبت`, 'green');
      } catch (error) {
        colorLog(`   ❌ ${module} غير مثبت`, 'red');
        allModulesExist = false;
      }
    }
    
    return allModulesExist;
  } catch (error) {
    colorLog('   ❌ مجلد node_modules غير موجود', 'red');
    colorLog('   💡 قم بتشغيل: npm install', 'yellow');
    return false;
  }
}

/**
 * اختبار بنية المجلدات
 */
async function testDirectoryStructure() {
  colorLog('\n📂 اختبار بنية المجلدات...', 'cyan');
  
  const requiredDirs = [
    'backend',
    'backend/routes',
    'backend/api',
    'backend/uploads',
    'frontend',
    'frontend/assets'
  ];
  
  let allDirsExist = true;
  
  for (const dir of requiredDirs) {
    try {
      const stats = await fs.stat(dir);
      if (stats.isDirectory()) {
        colorLog(`   ✅ ${dir}/`, 'green');
      } else {
        colorLog(`   ❌ ${dir} ليس مجلد`, 'red');
        allDirsExist = false;
      }
    } catch (error) {
      colorLog(`   ❌ ${dir}/ - غير موجود`, 'red');
      allDirsExist = false;
      
      // إنشاء المجلد إذا لم يكن موجود
      try {
        await fs.mkdir(dir, { recursive: true });
        colorLog(`   ✨ تم إنشاء ${dir}/`, 'yellow');
      } catch (createError) {
        colorLog(`   ❌ فشل في إنشاء ${dir}/: ${createError.message}`, 'red');
      }
    }
  }
  
  return allDirsExist;
}

/**
 * اختبار ملف الخادم
 */
async function testServerFile() {
  colorLog('\n🖥️ اختبار ملف الخادم...', 'cyan');
  
  try {
    const serverContent = await fs.readFile('backend/api/server.js', 'utf8');
    
    // اختبار وجود المسارات المطلوبة
    const requiredRoutes = [
      "require('../routes/upload')",
      "app.use(`/api/${API_VERSION}/uploads`",
      "uploadRoutes"
    ];
    
    let allRoutesExist = true;
    
    for (const route of requiredRoutes) {
      if (serverContent.includes(route)) {
        colorLog(`   ✅ ${route}`, 'green');
      } else {
        colorLog(`   ❌ ${route} - غير موجود`, 'red');
        allRoutesExist = false;
      }
    }
    
    return allRoutesExist;
  } catch (error) {
    colorLog(`   ❌ خطأ في قراءة ملف الخادم: ${error.message}`, 'red');
    return false;
  }
}

/**
 * إنشاء ملف اختبار
 */
async function createTestFile() {
  colorLog('\n📄 إنشاء ملف اختبار...', 'cyan');
  
  try {
    // إنشاء بيانات تجريبية
    const testData = [
      {
        'اسم المورد': 'شركة الاختبار الأولى',
        'الفئة': 'بهارات',
        'رقم الهاتف': '+965-11111111',
        'البريد الإلكتروني': '<EMAIL>',
        'العنوان': 'الكويت - منطقة الاختبار',
        'الشخص المسؤول': 'مدير الاختبار'
      },
      {
        'اسم المورد': 'مؤسسة الاختبار الثانية',
        'الفئة': 'استهلاكي',
        'رقم الهاتف': '+965-22222222',
        'البريد الإلكتروني': '<EMAIL>',
        'العنوان': 'الكويت - منطقة الاختبار 2',
        'الشخص المسؤول': 'مدير الاختبار 2'
      },
      {
        'اسم المورد': 'شركة الاختبار الثالثة',
        'الفئة': 'أجبان',
        'رقم الهاتف': '+965-33333333',
        'البريد الإلكتروني': '<EMAIL>',
        'العنوان': 'الكويت - منطقة الاختبار 3',
        'الشخص المسؤول': 'مدير الاختبار 3'
      },
      {
        'اسم المورد': 'مورد خاطئ للاختبار',
        'الفئة': 'فئة خاطئة',
        'رقم الهاتف': 'رقم خاطئ',
        'البريد الإلكتروني': 'بريد خاطئ',
        'العنوان': '',
        'الشخص المسؤول': ''
      }
    ];
    
    // تحويل إلى CSV
    const csvHeader = Object.keys(testData[0]).join(',');
    const csvRows = testData.map(row => Object.values(row).join(','));
    const csvContent = [csvHeader, ...csvRows].join('\n');
    
    // حفظ ملف CSV
    await fs.writeFile('test-suppliers.csv', csvContent, 'utf8');
    colorLog('   ✅ تم إنشاء test-suppliers.csv', 'green');
    
    // إنشاء ملف Excel إذا كان XLSX متاح
    try {
      const XLSX = require('xlsx');
      const ws = XLSX.utils.json_to_sheet(testData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'الموردين');
      
      // تعيين عرض الأعمدة
      ws['!cols'] = [
        { width: 25 },
        { width: 15 },
        { width: 20 },
        { width: 30 },
        { width: 25 },
        { width: 20 }
      ];
      
      XLSX.writeFile(wb, 'test-suppliers.xlsx');
      colorLog('   ✅ تم إنشاء test-suppliers.xlsx', 'green');
    } catch (error) {
      colorLog('   ⚠️ لم يتم إنشاء ملف Excel (XLSX غير متاح)', 'yellow');
    }
    
    return true;
  } catch (error) {
    colorLog(`   ❌ فشل في إنشاء ملف الاختبار: ${error.message}`, 'red');
    return false;
  }
}

/**
 * اختبار تشغيل الخادم
 */
async function testServerStart() {
  colorLog('\n🚀 اختبار تشغيل الخادم...', 'cyan');
  
  try {
    // محاولة تشغيل الخادم في الخلفية
    const { stdout, stderr } = await execAsync('node -e "console.log(\'Server test\')"');
    colorLog('   ✅ Node.js يعمل بشكل صحيح', 'green');
    
    // اختبار تحميل ملف الخادم
    try {
      require('./backend/api/server.js');
      colorLog('   ✅ ملف الخادم يمكن تحميله', 'green');
    } catch (error) {
      colorLog(`   ❌ خطأ في تحميل ملف الخادم: ${error.message}`, 'red');
      return false;
    }
    
    return true;
  } catch (error) {
    colorLog(`   ❌ خطأ في اختبار الخادم: ${error.message}`, 'red');
    return false;
  }
}

/**
 * عرض تعليمات الاختبار اليدوي
 */
function showManualTestInstructions() {
  colorLog('\n📋 تعليمات الاختبار اليدوي:', 'cyan');
  colorLog('', 'white');
  
  colorLog('1️⃣ تشغيل النظام:', 'yellow');
  colorLog('   npm start', 'white');
  colorLog('', 'white');
  
  colorLog('2️⃣ فتح صفحة الاختبار السريع:', 'yellow');
  colorLog('   http://localhost:3000/quick-upload-test.html', 'white');
  colorLog('', 'white');
  
  colorLog('3️⃣ اختبار المكونات:', 'yellow');
  colorLog('   • انقر على "اختبار الخادم"', 'white');
  colorLog('   • انقر على "اختبار التحميل"', 'white');
  colorLog('   • انقر على "إنشاء ملف تجريبي"', 'white');
  colorLog('', 'white');
  
  colorLog('4️⃣ اختبار الرفع:', 'yellow');
  colorLog('   • افتح صفحة الرفع', 'white');
  colorLog('   • ارفع الملف التجريبي', 'white');
  colorLog('   • راجع النتائج', 'white');
  colorLog('', 'white');
  
  colorLog('5️⃣ التحقق من النتائج:', 'yellow');
  colorLog('   • افتح قائمة الموردين', 'white');
  colorLog('   • تأكد من إضافة الموردين الجدد', 'white');
  colorLog('', 'white');
}

/**
 * الدالة الرئيسية للاختبار
 */
async function runTests() {
  colorLog('🧪 بدء اختبار نظام رفع ملفات الموردين', 'cyan');
  colorLog('جمعية المنقف التعاونية', 'cyan');
  colorLog('='.repeat(50), 'cyan');
  
  const tests = [
    { name: 'وجود الملفات المطلوبة', func: testRequiredFiles },
    { name: 'التبعيات في package.json', func: testDependencies },
    { name: 'تثبيت التبعيات', func: testNodeModules },
    { name: 'بنية المجلدات', func: testDirectoryStructure },
    { name: 'ملف الخادم', func: testServerFile },
    { name: 'إنشاء ملف اختبار', func: createTestFile },
    { name: 'تشغيل الخادم', func: testServerStart }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    try {
      const result = await test.func();
      if (result) {
        passedTests++;
        colorLog(`✅ ${test.name} - نجح`, 'green');
      } else {
        colorLog(`❌ ${test.name} - فشل`, 'red');
      }
    } catch (error) {
      colorLog(`❌ ${test.name} - خطأ: ${error.message}`, 'red');
    }
  }
  
  // عرض النتائج النهائية
  colorLog('\n' + '='.repeat(50), 'cyan');
  colorLog('📊 نتائج الاختبار:', 'cyan');
  colorLog(`   ✅ نجح: ${passedTests}`, 'green');
  colorLog(`   ❌ فشل: ${totalTests - passedTests}`, 'red');
  colorLog(`   📈 معدل النجاح: ${Math.round((passedTests / totalTests) * 100)}%`, 'blue');
  
  if (passedTests === totalTests) {
    colorLog('\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام', 'green');
    colorLog('🚀 يمكنك الآن تشغيل النظام باستخدام: npm start', 'green');
  } else {
    colorLog('\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه', 'yellow');
    
    if (passedTests >= totalTests * 0.7) {
      colorLog('💡 النظام قد يعمل جزئياً. جرب الاختبار اليدوي', 'yellow');
    }
  }
  
  // عرض تعليمات الاختبار اليدوي
  showManualTestInstructions();
  
  // إنشاء تقرير
  const report = {
    timestamp: new Date().toISOString(),
    totalTests: totalTests,
    passedTests: passedTests,
    failedTests: totalTests - passedTests,
    successRate: Math.round((passedTests / totalTests) * 100),
    tests: tests.map(test => ({ name: test.name, status: 'completed' }))
  };
  
  try {
    await fs.writeFile('test-report.json', JSON.stringify(report, null, 2));
    colorLog('\n📄 تم حفظ تقرير الاختبار في: test-report.json', 'blue');
  } catch (error) {
    colorLog('\n⚠️ فشل في حفظ تقرير الاختبار', 'yellow');
  }
}

// تشغيل الاختبارات
if (require.main === module) {
  runTests().catch(error => {
    colorLog(`❌ خطأ في تشغيل الاختبارات: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = {
  runTests,
  testRequiredFiles,
  testDependencies,
  testNodeModules,
  testDirectoryStructure,
  createTestFile
};
