#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة الموردين
Suppliers Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from models.supplier import Supplier

class SuppliersWindow:
    """نافذة إدارة الموردين"""
    
    def __init__(self, parent, db_manager):
        """
        تهيئة نافذة الموردين
        
        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
        """
        self.parent = parent
        self.db_manager = db_manager
        self.supplier_model = Supplier(db_manager)
        
        # إنشاء النافذة
        self.window = ttk_bs.Toplevel(parent)
        self.window.title("إدارة الموردين - نظام الأقسام")
        self.window.geometry("1200x800")
        self.window.resizable(True, True)
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_suppliers()
        
        # تعيين النافذة في المقدمة
        self.window.transient(parent)
        self.window.grab_set()
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.supplier_id = tk.IntVar()
        self.supplier_name = tk.StringVar()
        self.supplier_category = tk.StringVar(value="بهارات")
        self.contact_person = tk.StringVar()
        self.phone = tk.StringVar()
        self.email = tk.StringVar()
        self.address = tk.StringVar()
        self.national_id = tk.StringVar()
        self.commercial_registration = tk.StringVar()
        self.status = tk.StringVar(value="نشط")
        self.notes = tk.StringVar()
        self.search_term = tk.StringVar()
        self.category_filter = tk.StringVar(value="الكل")
    
    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=10)
        main_frame.pack(fill=BOTH, expand=True)
        
        # عنوان النافذة
        title_label = ttk_bs.Label(
            main_frame,
            text="إدارة الموردين حسب الأقسام",
            font=("Arial", 16, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 10))

        # وصف النافذة
        desc_label = ttk_bs.Label(
            main_frame,
            text="إدارة موردين البهارات والمواد الاستهلاكية والأجبان",
            font=("Arial", 11),
            bootstyle=SECONDARY
        )
        desc_label.pack(pady=(0, 20))
        
        # إنشاء التبويبات
        notebook = ttk_bs.Notebook(main_frame)
        notebook.pack(fill=BOTH, expand=True)
        
        # تبويب قائمة الموردين
        self.create_suppliers_list_tab(notebook)
        
        # تبويب إضافة/تعديل مورد
        self.create_supplier_form_tab(notebook)
    
    def create_suppliers_list_tab(self, notebook):
        """إنشاء تبويب قائمة الموردين"""
        list_frame = ttk_bs.Frame(notebook, padding=10)
        notebook.add(list_frame, text="قائمة الموردين")
        
        # إطار البحث
        search_frame = ttk_bs.Frame(list_frame)
        search_frame.pack(fill=X, pady=(0, 10))
        
        ttk_bs.Label(search_frame, text="🔍 بحث:").pack(side=LEFT, padx=(0, 5))
        search_entry = ttk_bs.Entry(
            search_frame,
            textvariable=self.search_term,
            width=40
        )
        search_entry.pack(side=LEFT, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.on_dynamic_search)
        search_entry.insert(0, "ابحث باسم المورد أو الهاتف أو البريد...")
        search_entry.bind('<FocusIn>', self.on_search_focus_in)
        search_entry.bind('<FocusOut>', self.on_search_focus_out)

        # حفظ مرجع لحقل البحث
        self.search_entry = search_entry

        ttk_bs.Button(
            search_frame,
            text="🔄 تحديث",
            command=self.load_suppliers,
            bootstyle=INFO
        ).pack(side=LEFT, padx=(0, 10))
        
        # فلتر القسم
        ttk_bs.Label(search_frame, text="📂 القسم:").pack(side=LEFT, padx=(20, 5))
        self.category_filter_combo = ttk_bs.Combobox(
            search_frame,
            textvariable=self.category_filter,
            values=["الكل", "بهارات", "استهلاكي", "أجبان"],
            state="readonly",
            width=15
        )
        self.category_filter_combo.pack(side=LEFT, padx=(0, 10))
        self.category_filter_combo.bind('<<ComboboxSelected>>', self.on_category_filter_change)

        # عداد النتائج
        self.results_count_label = ttk_bs.Label(
            search_frame,
            text="",
            font=("Arial", 10),
            bootstyle=SECONDARY
        )
        self.results_count_label.pack(side=RIGHT, padx=(10, 0))
        
        # جدول الموردين
        self.create_suppliers_table(list_frame)
        
        # أزرار العمليات
        buttons_frame = ttk_bs.Frame(list_frame)
        buttons_frame.pack(fill=X, pady=(10, 0))
        
        ttk_bs.Button(
            buttons_frame,
            text="تعديل",
            command=self.edit_supplier,
            bootstyle=WARNING
        ).pack(side=LEFT, padx=(0, 10))
        
        ttk_bs.Button(
            buttons_frame,
            text="حذف",
            command=self.delete_supplier,
            bootstyle=DANGER
        ).pack(side=LEFT, padx=(0, 10))
        
        ttk_bs.Button(
            buttons_frame,
            text="عرض العقود",
            command=self.view_supplier_contracts,
            bootstyle=INFO
        ).pack(side=LEFT)
    
    def create_suppliers_table(self, parent):
        """إنشاء جدول الموردين"""
        # إطار الجدول
        table_frame = ttk_bs.Frame(parent)
        table_frame.pack(fill=BOTH, expand=True)
        
        # إنشاء Treeview
        columns = ('ID', 'اسم المورد', 'القسم', 'الشخص المسؤول', 'الهاتف', 'البريد الإلكتروني', 'الحالة')
        self.suppliers_tree = ttk_bs.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=15
        )

        # تعيين عناوين الأعمدة
        for col in columns:
            self.suppliers_tree.heading(col, text=col)
            if col == 'ID':
                self.suppliers_tree.column(col, width=50, anchor=CENTER)
            elif col == 'اسم المورد':
                self.suppliers_tree.column(col, width=180, anchor=W)
            elif col == 'القسم':
                self.suppliers_tree.column(col, width=100, anchor=CENTER)
            elif col == 'الشخص المسؤول':
                self.suppliers_tree.column(col, width=130, anchor=W)
            elif col == 'الهاتف':
                self.suppliers_tree.column(col, width=100, anchor=CENTER)
            elif col == 'البريد الإلكتروني':
                self.suppliers_tree.column(col, width=180, anchor=W)
            elif col == 'الحالة':
                self.suppliers_tree.column(col, width=80, anchor=CENTER)
        
        # شريط التمرير
        scrollbar = ttk_bs.Scrollbar(table_frame, orient=VERTICAL, command=self.suppliers_tree.yview)
        self.suppliers_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.suppliers_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # ربط الأحداث
        self.suppliers_tree.bind('<Double-1>', self.on_supplier_double_click)
    
    def create_supplier_form_tab(self, notebook):
        """إنشاء تبويب نموذج المورد"""
        form_frame = ttk_bs.Frame(notebook, padding=20)
        notebook.add(form_frame, text="إضافة/تعديل مورد")
        
        # إطار النموذج
        form_container = ttk_bs.Frame(form_frame)
        form_container.pack(fill=BOTH, expand=True)
        
        # الصف الأول
        row1 = ttk_bs.Frame(form_container)
        row1.pack(fill=X, pady=(0, 10))
        
        # اسم المورد
        ttk_bs.Label(row1, text="اسم المورد *:").pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            row1,
            textvariable=self.supplier_name,
            width=25
        ).pack(side=LEFT, padx=(0, 20))

        # قسم المورد
        ttk_bs.Label(row1, text="القسم *:").pack(side=LEFT, padx=(0, 5))
        category_combo = ttk_bs.Combobox(
            row1,
            textvariable=self.supplier_category,
            values=["بهارات", "استهلاكي", "أجبان"],
            state="readonly",
            width=15
        )
        category_combo.pack(side=LEFT)

        # الصف الثاني
        row1_5 = ttk_bs.Frame(form_container)
        row1_5.pack(fill=X, pady=(0, 10))

        # الشخص المسؤول
        ttk_bs.Label(row1_5, text="الشخص المسؤول:").pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            row1_5,
            textvariable=self.contact_person,
            width=30
        ).pack(side=LEFT)
        
        # الصف الثاني
        row2 = ttk_bs.Frame(form_container)
        row2.pack(fill=X, pady=(0, 10))
        
        # رقم الهاتف
        ttk_bs.Label(row2, text="رقم الهاتف *:").pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            row2,
            textvariable=self.phone,
            width=30
        ).pack(side=LEFT, padx=(0, 20))
        
        # البريد الإلكتروني
        ttk_bs.Label(row2, text="البريد الإلكتروني:").pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            row2,
            textvariable=self.email,
            width=30
        ).pack(side=LEFT)

        # الصف الثالث
        row3 = ttk_bs.Frame(form_container)
        row3.pack(fill=X, pady=(0, 10))

        # الهوية الوطنية
        ttk_bs.Label(row3, text="الهوية الوطنية:").pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            row3,
            textvariable=self.national_id,
            width=30
        ).pack(side=LEFT, padx=(0, 20))

        # السجل التجاري
        ttk_bs.Label(row3, text="السجل التجاري:").pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            row3,
            textvariable=self.commercial_registration,
            width=30
        ).pack(side=LEFT)

        # الصف الرابع
        row4 = ttk_bs.Frame(form_container)
        row4.pack(fill=X, pady=(0, 10))

        # الحالة
        ttk_bs.Label(row4, text="الحالة:").pack(side=LEFT, padx=(0, 5))
        status_combo = ttk_bs.Combobox(
            row4,
            textvariable=self.status,
            values=["نشط", "غير نشط", "معلق"],
            state="readonly",
            width=27
        )
        status_combo.pack(side=LEFT)

        # الصف الخامس - العنوان
        row5 = ttk_bs.Frame(form_container)
        row5.pack(fill=X, pady=(0, 10))

        ttk_bs.Label(row5, text="العنوان:").pack(side=TOP, anchor=W)
        address_entry = ttk_bs.Entry(
            row5,
            textvariable=self.address,
            width=80
        )
        address_entry.pack(fill=X, pady=(5, 0))

        # الصف السادس - الملاحظات
        row6 = ttk_bs.Frame(form_container)
        row6.pack(fill=X, pady=(0, 20))

        ttk_bs.Label(row6, text="الملاحظات:").pack(side=TOP, anchor=W)
        notes_text = tk.Text(
            row6,
            height=4,
            width=80,
            wrap=tk.WORD
        )
        notes_text.pack(fill=X, pady=(5, 0))

        # ربط النص بالمتغير
        def update_notes(*args):
            self.notes.set(notes_text.get("1.0", tk.END).strip())

        notes_text.bind('<KeyRelease>', update_notes)
        self.notes_text = notes_text

        # أزرار النموذج
        buttons_frame = ttk_bs.Frame(form_container)
        buttons_frame.pack(fill=X, pady=(10, 0))

        ttk_bs.Button(
            buttons_frame,
            text="حفظ",
            command=self.save_supplier,
            bootstyle=SUCCESS
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bs.Button(
            buttons_frame,
            text="جديد",
            command=self.clear_form,
            bootstyle=INFO
        ).pack(side=LEFT, padx=(0, 10))

        ttk_bs.Button(
            buttons_frame,
            text="إلغاء",
            command=self.cancel_form,
            bootstyle=SECONDARY
        ).pack(side=LEFT)

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            # مسح البيانات الحالية
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)

            # تحميل الموردين
            suppliers = self.supplier_model.get_all_suppliers()

            for supplier in suppliers:
                self.suppliers_tree.insert('', 'end', values=(
                    supplier['supplier_id'],
                    supplier['supplier_name'],
                    supplier['supplier_category'] or '',
                    supplier['contact_person'] or '',
                    supplier['phone'] or '',
                    supplier['email'] or '',
                    supplier['status']
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الموردين:\n{str(e)}")

    def search_suppliers(self):
        """البحث في الموردين"""
        search_term = self.search_term.get().strip()

        try:
            # مسح البيانات الحالية
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)

            if search_term:
                suppliers = self.supplier_model.search_suppliers(search_term)
            else:
                suppliers = self.supplier_model.get_all_suppliers()

            for supplier in suppliers:
                self.suppliers_tree.insert('', 'end', values=(
                    supplier['supplier_id'],
                    supplier['supplier_name'],
                    supplier['supplier_category'] or '',
                    supplier['contact_person'] or '',
                    supplier['phone'] or '',
                    supplier['email'] or '',
                    supplier['status']
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث:\n{str(e)}")

    def on_search(self, event):
        """البحث عند الكتابة"""
        # البحث بعد توقف قصير
        self.window.after(500, self.search_suppliers)

    def on_category_filter_change(self, event):
        """تغيير فلتر القسم"""
        category = self.category_filter.get()

        try:
            # مسح البيانات الحالية
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)

            if category == "الكل":
                suppliers = self.supplier_model.get_all_suppliers()
            else:
                suppliers = self.supplier_model.get_suppliers_by_category(category)

            for supplier in suppliers:
                self.suppliers_tree.insert('', 'end', values=(
                    supplier['supplier_id'],
                    supplier['supplier_name'],
                    supplier['supplier_category'] or '',
                    supplier['contact_person'] or '',
                    supplier['phone'] or '',
                    supplier['email'] or '',
                    supplier['status']
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تطبيق فلتر القسم:\n{str(e)}")

    def save_supplier(self):
        """حفظ بيانات المورد"""
        try:
            # جمع البيانات
            supplier_data = {
                'supplier_name': self.supplier_name.get().strip(),
                'supplier_category': self.supplier_category.get(),
                'contact_person': self.contact_person.get().strip(),
                'phone': self.phone.get().strip(),
                'email': self.email.get().strip(),
                'address': self.address.get().strip(),
                'national_id': self.national_id.get().strip(),
                'commercial_registration': self.commercial_registration.get().strip(),
                'status': self.status.get(),
                'notes': self.notes_text.get("1.0", tk.END).strip()
            }

            # التحقق من صحة البيانات
            errors = self.supplier_model.validate_supplier_data(supplier_data)
            if errors:
                messagebox.showerror("خطأ في البيانات", "\n".join(errors))
                return

            # حفظ أو تحديث
            if self.supplier_id.get() == 0:
                # إضافة مورد جديد
                supplier_id = self.supplier_model.create_supplier(supplier_data)
                if supplier_id:
                    messagebox.showinfo("نجح", "تم إضافة المورد بنجاح")
                    self.clear_form()
                    self.load_suppliers()
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة المورد")
            else:
                # تحديث مورد موجود
                if self.supplier_model.update_supplier(self.supplier_id.get(), supplier_data):
                    messagebox.showinfo("نجح", "تم تحديث المورد بنجاح")
                    self.clear_form()
                    self.load_suppliers()
                else:
                    messagebox.showerror("خطأ", "فشل في تحديث المورد")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ المورد:\n{str(e)}")

    def clear_form(self):
        """مسح النموذج"""
        self.supplier_id.set(0)
        self.supplier_name.set("")
        self.supplier_category.set("بهارات")
        self.contact_person.set("")
        self.phone.set("")
        self.email.set("")
        self.address.set("")
        self.national_id.set("")
        self.commercial_registration.set("")
        self.status.set("نشط")
        self.notes_text.delete("1.0", tk.END)

    def cancel_form(self):
        """إلغاء النموذج"""
        self.clear_form()

    def edit_supplier(self):
        """تعديل المورد المحدد"""
        selected = self.suppliers_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد للتعديل")
            return

        # الحصول على معرف المورد
        item = self.suppliers_tree.item(selected[0])
        supplier_id = item['values'][0]

        # تحميل بيانات المورد
        supplier = self.supplier_model.get_supplier_by_id(supplier_id)
        if supplier:
            self.load_supplier_to_form(supplier)
            # التبديل إلى تبويب النموذج
            # (سيتم تطوير هذا لاحقاً)
        else:
            messagebox.showerror("خطأ", "فشل في تحميل بيانات المورد")

    def load_supplier_to_form(self, supplier):
        """تحميل بيانات المورد في النموذج"""
        self.supplier_id.set(supplier['supplier_id'])
        self.supplier_name.set(supplier['supplier_name'] or "")
        self.supplier_category.set(supplier['supplier_category'] or "بهارات")
        self.contact_person.set(supplier['contact_person'] or "")
        self.phone.set(supplier['phone'] or "")
        self.email.set(supplier['email'] or "")
        self.address.set(supplier['address'] or "")
        self.national_id.set(supplier['national_id'] or "")
        self.commercial_registration.set(supplier['commercial_registration'] or "")
        self.status.set(supplier['status'] or "نشط")

        # تحميل الملاحظات
        self.notes_text.delete("1.0", tk.END)
        if supplier['notes']:
            self.notes_text.insert("1.0", supplier['notes'])

    def delete_supplier(self):
        """حذف المورد المحدد"""
        selected = self.suppliers_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد للحذف")
            return

        # تأكيد الحذف
        if not messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المورد؟"):
            return

        # الحصول على معرف المورد
        item = self.suppliers_tree.item(selected[0])
        supplier_id = item['values'][0]

        try:
            if self.supplier_model.delete_supplier(supplier_id):
                messagebox.showinfo("نجح", "تم حذف المورد بنجاح")
                self.load_suppliers()
            else:
                messagebox.showerror("خطأ", "فشل في حذف المورد")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف المورد:\n{str(e)}")

    def view_supplier_contracts(self):
        """عرض عقود المورد"""
        selected = self.suppliers_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد لعرض عقوده")
            return

        # الحصول على معرف المورد
        item = self.suppliers_tree.item(selected[0])
        supplier_id = item['values'][0]

        # عرض العقود (سيتم تطوير هذا لاحقاً)
        messagebox.showinfo("عقود المورد", f"سيتم عرض عقود المورد رقم {supplier_id}")

    def on_supplier_double_click(self, event):
        """عند النقر المزدوج على مورد"""
        self.edit_supplier()

    # ==================== دوال البحث الديناميكي ====================

    def on_search_focus_in(self, event):
        """عند التركيز على حقل البحث"""
        if self.search_entry.get() == "ابحث باسم المورد أو الهاتف أو البريد...":
            self.search_entry.delete(0, tk.END)
            self.search_entry.config(foreground='black')

    def on_search_focus_out(self, event):
        """عند فقدان التركيز من حقل البحث"""
        if not self.search_entry.get():
            self.search_entry.insert(0, "ابحث باسم المورد أو الهاتف أو البريد...")
            self.search_entry.config(foreground='gray')

    def on_dynamic_search(self, event):
        """البحث الديناميكي أثناء الكتابة"""
        # تأخير قصير لتجنب البحث مع كل حرف
        if hasattr(self, '_search_timer'):
            self.window.after_cancel(self._search_timer)

        self._search_timer = self.window.after(300, self.perform_dynamic_search)

    def perform_dynamic_search(self):
        """تنفيذ البحث الديناميكي"""
        try:
            search_term = self.search_term.get().strip().lower()
            category_filter = self.category_filter.get()

            # إذا كان النص هو النص التوضيحي، تجاهله
            if search_term == "ابحث باسم المورد أو الهاتف أو البريد...":
                search_term = ""

            # مسح النتائج الحالية
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)

            # الحصول على جميع الموردين
            if category_filter == "الكل":
                all_suppliers = self.supplier_model.get_all_suppliers()
            else:
                all_suppliers = self.supplier_model.get_suppliers_by_category(category_filter)

            filtered_suppliers = []

            for supplier in all_suppliers:
                # البحث النصي
                if search_term:
                    # البحث في اسم المورد
                    if search_term in supplier['supplier_name'].lower():
                        filtered_suppliers.append(supplier)
                        continue

                    # البحث في الهاتف
                    phone = supplier.get('phone', '').lower()
                    if search_term in phone:
                        filtered_suppliers.append(supplier)
                        continue

                    # البحث في البريد الإلكتروني
                    email = supplier.get('email', '').lower()
                    if search_term in email:
                        filtered_suppliers.append(supplier)
                        continue

                    # البحث في الشخص المسؤول
                    contact_person = supplier.get('contact_person', '').lower()
                    if search_term in contact_person:
                        filtered_suppliers.append(supplier)
                        continue

                    # البحث في العنوان
                    address = supplier.get('address', '').lower()
                    if search_term in address:
                        filtered_suppliers.append(supplier)
                        continue
                else:
                    # إذا لم يكن هناك نص بحث، أضف جميع الموردين المفلترين حسب القسم
                    filtered_suppliers.append(supplier)

            # عرض النتائج
            for supplier in filtered_suppliers:
                self.suppliers_tree.insert('', 'end', values=(
                    supplier['supplier_id'],
                    supplier['supplier_name'],
                    supplier['supplier_category'] or '',
                    supplier['contact_person'] or '',
                    supplier['phone'] or '',
                    supplier['email'] or '',
                    supplier['status']
                ))

            # تحديث عداد النتائج
            self.update_results_count(len(filtered_suppliers), len(all_suppliers))

        except Exception as e:
            print(f"خطأ في البحث الديناميكي: {e}")

    def update_results_count(self, filtered_count, total_count):
        """تحديث عداد النتائج"""
        if filtered_count == total_count:
            count_text = f"👥 إجمالي: {total_count} مورد"
        else:
            count_text = f"👥 النتائج: {filtered_count} من {total_count} مورد"

        self.results_count_label.config(text=count_text)

    def on_category_filter_change(self, event):
        """عند تغيير فلتر القسم"""
        self.perform_dynamic_search()

    def search_suppliers(self):
        """البحث في الموردين (الدالة القديمة - محدثة)"""
        self.perform_dynamic_search()
