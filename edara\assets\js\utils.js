/**
 * دوال مساعدة عامة - النسخة المحدثة
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 * تطوير: محمد مرزوق العقاب
 * التقويم: ميلادي (محدث)
 */

// ==================== دوال التاريخ والوقت ====================

/**
 * تنسيق التاريخ للعرض (التقويم الميلادي)
 * @param {string|Date} date - التاريخ
 * @param {string} format - تنسيق العرض
 * @returns {string} التاريخ المنسق
 */
function formatDate(date, format = 'full') {
  if (!date) return '';
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';
  
  const options = {
    full: {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    },
    short: {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    },
    numeric: {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    },
    time: {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    },
    datetime: {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }
  };
  
  // استخدام التقويم الميلادي (en-GB)
  return dateObj.toLocaleDateString('en-GB', options[format] || options.full);
}

/**
 * تحديث التاريخ والوقت الحالي (ميلادي)
 */
function updateCurrentDateTime() {
  const now = new Date();
  
  // تنسيق التاريخ الميلادي
  const dateOptions = { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric',
    weekday: 'long'
  };
  
  const timeOptions = {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  };
  
  const currentDate = now.toLocaleDateString('en-GB', dateOptions);
  const currentTime = now.toLocaleTimeString('en-GB', timeOptions);
  
  // تحديث العناصر في الصفحة
  const dateElement = document.getElementById('currentDate');
  const timeElement = document.getElementById('currentTime');
  
  if (dateElement) dateElement.textContent = currentDate;
  if (timeElement) timeElement.textContent = currentTime;
  
  return { date: currentDate, time: currentTime };
}

/**
 * حساب الفرق بين تاريخين
 * @param {string|Date} startDate - تاريخ البداية
 * @param {string|Date} endDate - تاريخ النهاية
 * @returns {Object} الفرق بالأيام والساعات والدقائق
 */
function dateDifference(startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return { days: 0, hours: 0, minutes: 0 };
  }
  
  const diffMs = end.getTime() - start.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
  
  return {
    days: diffDays,
    hours: diffHours,
    minutes: diffMinutes,
    totalMs: diffMs
  };
}

/**
 * التحقق من انتهاء صلاحية التاريخ
 * @param {string|Date} date - التاريخ
 * @param {number} warningDays - عدد أيام التحذير
 * @returns {Object} حالة انتهاء الصلاحية
 */
function checkExpiry(date, warningDays = 30) {
  const targetDate = new Date(date);
  const today = new Date();
  const diffDays = Math.ceil((targetDate - today) / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) {
    return { status: 'expired', days: Math.abs(diffDays), message: `منتهي منذ ${Math.abs(diffDays)} يوم` };
  } else if (diffDays <= warningDays) {
    return { status: 'expiring', days: diffDays, message: `ينتهي خلال ${diffDays} يوم` };
  } else {
    return { status: 'active', days: diffDays, message: `ينتهي خلال ${diffDays} يوم` };
  }
}

// ==================== دوال الأرقام والعملة ====================

/**
 * تنسيق الأرقام للعرض
 * @param {number} number - الرقم
 * @param {number} decimals - عدد الخانات العشرية
 * @returns {string} الرقم المنسق
 */
function formatNumber(number, decimals = 2) {
  if (isNaN(number)) return '0';
  
  return new Intl.NumberFormat('en-GB', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(number);
}

/**
 * تنسيق العملة للعرض (دينار كويتي)
 * @param {number} amount - المبلغ
 * @param {string} currency - رمز العملة
 * @returns {string} المبلغ المنسق
 */
function formatCurrency(amount, currency = 'KWD') {
  if (isNaN(amount)) return '0.000 د.ك';
  
  const currencyMap = {
    'KWD': { symbol: 'د.ك', decimals: 3 },
    'USD': { symbol: '$', decimals: 2 },
    'EUR': { symbol: '€', decimals: 2 },
    'SAR': { symbol: 'ر.س', decimals: 2 }
  };
  
  const currencyInfo = currencyMap[currency] || currencyMap['KWD'];
  
  return new Intl.NumberFormat('en-GB', {
    minimumFractionDigits: currencyInfo.decimals,
    maximumFractionDigits: currencyInfo.decimals
  }).format(amount) + ' ' + currencyInfo.symbol;
}

// ==================== دوال النصوص ====================

/**
 * اقتطاع النص مع إضافة نقاط
 * @param {string} text - النص
 * @param {number} maxLength - الطول الأقصى
 * @returns {string} النص المقتطع
 */
function truncateText(text, maxLength = 100) {
  if (!text || text.length <= maxLength) return text || '';
  return text.substring(0, maxLength) + '...';
}

/**
 * تنظيف النص من الرموز الخاصة
 * @param {string} text - النص
 * @returns {string} النص المنظف
 */
function sanitizeText(text) {
  if (!text) return '';
  return text.replace(/[<>\"'&]/g, '');
}

/**
 * البحث في النص مع تمييز النتائج
 * @param {string} text - النص
 * @param {string} searchTerm - مصطلح البحث
 * @returns {string} النص مع التمييز
 */
function highlightSearchTerm(text, searchTerm) {
  if (!text || !searchTerm) return text;
  
  const regex = new RegExp(`(${searchTerm})`, 'gi');
  return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
}

// ==================== دوال التحقق من صحة البيانات ====================

/**
 * التحقق من صحة البريد الإلكتروني
 * @param {string} email - البريد الإلكتروني
 * @returns {boolean} صحة البريد الإلكتروني
 */
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * التحقق من صحة رقم الهاتف الكويتي
 * @param {string} phone - رقم الهاتف
 * @returns {boolean} صحة رقم الهاتف
 */
function validateKuwaitPhone(phone) {
  const phoneRegex = /^(\+965|965)?[2569]\d{7}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
}

// ==================== دوال الملفات ====================

/**
 * تنسيق حجم الملف
 * @param {number} bytes - حجم الملف بالبايت
 * @returns {string} حجم الملف المنسق
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 بايت';
  
  const k = 1024;
  const sizes = ['بايت', 'ك.ب', 'م.ب', 'ج.ب', 'ت.ب'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * الحصول على أيقونة الملف حسب النوع
 * @param {string} filename - اسم الملف
 * @returns {string} فئة الأيقونة
 */
function getFileIcon(filename) {
  if (!filename) return 'fas fa-file';
  
  const extension = filename.split('.').pop().toLowerCase();
  const iconMap = {
    pdf: 'fas fa-file-pdf text-red-600',
    doc: 'fas fa-file-word text-blue-600',
    docx: 'fas fa-file-word text-blue-600',
    xls: 'fas fa-file-excel text-green-600',
    xlsx: 'fas fa-file-excel text-green-600',
    jpg: 'fas fa-file-image text-purple-600',
    jpeg: 'fas fa-file-image text-purple-600',
    png: 'fas fa-file-image text-purple-600',
    zip: 'fas fa-file-archive text-yellow-600',
    txt: 'fas fa-file-alt text-gray-600'
  };
  
  return iconMap[extension] || 'fas fa-file text-gray-600';
}

// ==================== دوال التخزين المحلي ====================

/**
 * حفظ البيانات في التخزين المحلي
 * @param {string} key - المفتاح
 * @param {*} data - البيانات
 */
function saveToLocalStorage(key, data) {
  try {
    localStorage.setItem(key, JSON.stringify(data));
    return true;
  } catch (error) {
    console.error('خطأ في حفظ البيانات:', error);
    return false;
  }
}

/**
 * جلب البيانات من التخزين المحلي
 * @param {string} key - المفتاح
 * @param {*} defaultValue - القيمة الافتراضية
 * @returns {*} البيانات المحفوظة
 */
function getFromLocalStorage(key, defaultValue = null) {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error('خطأ في جلب البيانات:', error);
    return defaultValue;
  }
}

// ==================== دوال متنوعة ====================

/**
 * إنشاء معرف فريد
 * @returns {string} معرف فريد
 */
function generateUniqueId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * تأخير التنفيذ (Debounce)
 * @param {Function} func - الدالة
 * @param {number} wait - وقت التأخير بالميلي ثانية
 * @returns {Function} الدالة المؤخرة
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * نسخ النص إلى الحافظة
 * @param {string} text - النص المراد نسخه
 * @returns {Promise<boolean>} نجح النسخ أم لا
 */
async function copyToClipboard(text) {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error('خطأ في نسخ النص:', error);
    return false;
  }
}

// تصدير الدوال للاستخدام العام
window.Utils = {
  formatDate,
  updateCurrentDateTime,
  dateDifference,
  checkExpiry,
  formatNumber,
  formatCurrency,
  truncateText,
  sanitizeText,
  highlightSearchTerm,
  validateEmail,
  validateKuwaitPhone,
  formatFileSize,
  getFileIcon,
  saveToLocalStorage,
  getFromLocalStorage,
  generateUniqueId,
  debounce,
  copyToClipboard
};
