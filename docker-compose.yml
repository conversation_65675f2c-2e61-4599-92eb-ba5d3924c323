# =====================================================
# Docker Compose لنظام إدارة عقود الموردين والإيجارات
# جمعية المنقف التعاونية
# =====================================================

version: '3.8'

services:
  # =====================================================
  # خدمة التطبيق الرئيسي
  # =====================================================
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    container_name: supplier-contracts-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DB_HOST=database
      - DB_PORT=3306
      - DB_NAME=supplier_contracts
      - DB_USER=contracts_app
      - DB_PASSWORD=${DB_PASSWORD:-SecurePassword123!}
      - JWT_SECRET=${JWT_SECRET:-your-very-secure-jwt-secret-key-here}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET:-your-very-secure-refresh-secret-key-here}
      - JWT_EXPIRES_IN=24h
      - LOG_LEVEL=info
      - TZ=Asia/Kuwait
    volumes:
      - app_uploads:/app/backend/uploads
      - app_logs:/app/backend/logs
    depends_on:
      database:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - supplier-contracts-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # =====================================================
  # خدمة قاعدة البيانات MySQL
  # =====================================================
  database:
    image: mysql:8.0
    container_name: supplier-contracts-db
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-RootPassword123!}
      - MYSQL_DATABASE=supplier_contracts
      - MYSQL_USER=contracts_app
      - MYSQL_PASSWORD=${DB_PASSWORD:-SecurePassword123!}
      - TZ=Asia/Kuwait
    volumes:
      - db_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
      - ./database/config/my.cnf:/etc/mysql/conf.d/custom.cnf:ro
    ports:
      - "3306:3306"
    networks:
      - supplier-contracts-network
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=256M
      --max-connections=200
      --sql-mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-RootPassword123!}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # =====================================================
  # خدمة Redis للتخزين المؤقت والجلسات
  # =====================================================
  redis:
    image: redis:7-alpine
    container_name: supplier-contracts-redis
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --requirepass ${REDIS_PASSWORD:-RedisPassword123!}
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - supplier-contracts-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =====================================================
  # خدمة Nginx كخادم ويب عكسي
  # =====================================================
  nginx:
    image: nginx:alpine
    container_name: supplier-contracts-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - app_uploads:/var/www/uploads:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - supplier-contracts-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =====================================================
  # خدمة النسخ الاحتياطي
  # =====================================================
  backup:
    image: mysql:8.0
    container_name: supplier-contracts-backup
    restart: "no"
    environment:
      - MYSQL_HOST=database
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=supplier_contracts
      - MYSQL_USER=contracts_app
      - MYSQL_PASSWORD=${DB_PASSWORD:-SecurePassword123!}
      - BACKUP_SCHEDULE=${BACKUP_SCHEDULE:-0 2 * * *}
    volumes:
      - ./scripts/backup.sh:/backup.sh:ro
      - backup_data:/backups
    depends_on:
      database:
        condition: service_healthy
    networks:
      - supplier-contracts-network
    command: >
      sh -c "
        chmod +x /backup.sh &&
        echo '${BACKUP_SCHEDULE:-0 2 * * *} /backup.sh' | crontab - &&
        crond -f
      "

  # =====================================================
  # خدمة مراقبة الأداء (اختيارية)
  # =====================================================
  monitoring:
    image: prom/prometheus:latest
    container_name: supplier-contracts-monitoring
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - supplier-contracts-network
    profiles:
      - monitoring

  # =====================================================
  # خدمة Grafana للوحات المراقبة (اختيارية)
  # =====================================================
  grafana:
    image: grafana/grafana:latest
    container_name: supplier-contracts-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - monitoring
    networks:
      - supplier-contracts-network
    profiles:
      - monitoring

# =====================================================
# الشبكات
# =====================================================
networks:
  supplier-contracts-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =====================================================
# وحدات التخزين
# =====================================================
volumes:
  # بيانات قاعدة البيانات
  db_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/mysql

  # بيانات Redis
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/redis

  # ملفات التطبيق المرفوعة
  app_uploads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/uploads

  # سجلات التطبيق
  app_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/logs

  # سجلات Nginx
  nginx_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/nginx-logs

  # النسخ الاحتياطية
  backup_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/backups

  # بيانات Prometheus (للمراقبة)
  prometheus_data:
    driver: local

  # بيانات Grafana (للمراقبة)
  grafana_data:
    driver: local
