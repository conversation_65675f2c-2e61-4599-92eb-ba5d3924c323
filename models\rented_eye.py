#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج العيون المؤجرة
Rented Eye Model for Contract Management System
"""

from datetime import datetime
from typing import Optional, List, Dict

class RentedEye:
    """نموذج العيون المؤجرة"""
    
    def __init__(self, db_manager):
        """
        تهيئة نموذج العيون المؤجرة
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db_manager = db_manager
    
    def rent_eye(self, eye_data: Dict) -> Optional[int]:
        """
        تأجير عين في طبلية
        
        Args:
            eye_data: بيانات العين المؤجرة
            
        Returns:
            معرف العين المؤجرة أو None في حالة الفشل
        """
        # التحقق من توفر العين
        if not self.is_eye_available(eye_data['item_id'], eye_data['eye_number'], 
                                    eye_data['start_date'], eye_data['end_date']):
            return None
        
        query = """
        INSERT INTO rented_eyes (
            item_id, contract_id, supplier_id, eye_number, rental_price,
            start_date, end_date, status, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params = (
            eye_data.get('item_id'),
            eye_data.get('contract_id'),
            eye_data.get('supplier_id'),
            eye_data.get('eye_number'),
            eye_data.get('rental_price'),
            eye_data.get('start_date'),
            eye_data.get('end_date'),
            eye_data.get('status', 'نشط'),
            eye_data.get('notes')
        )
        
        if self.db_manager.execute_query(query, params):
            result = self.db_manager.fetch_one("SELECT last_insert_rowid() as id")
            return result['id'] if result else None
        return None
    
    def is_eye_available(self, item_id: int, eye_number: int, start_date: str, end_date: str) -> bool:
        """
        التحقق من توفر العين في الفترة المحددة
        
        Args:
            item_id: معرف الطبلية
            eye_number: رقم العين
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            
        Returns:
            True إذا كانت العين متاحة، False إذا كانت مؤجرة
        """
        query = """
        SELECT COUNT(*) as count FROM rented_eyes 
        WHERE item_id = ? AND eye_number = ? AND status = 'نشط'
        AND (
            (start_date <= ? AND end_date >= ?) OR
            (start_date <= ? AND end_date >= ?) OR
            (start_date >= ? AND end_date <= ?)
        )
        """
        
        params = (item_id, eye_number, start_date, start_date, end_date, end_date, start_date, end_date)
        result = self.db_manager.fetch_one(query, params)
        return result['count'] == 0 if result else False
    
    def get_available_eyes(self, item_id: int, start_date: str, end_date: str) -> List[int]:
        """
        الحصول على العيون المتاحة في طبلية خلال فترة معينة
        
        Args:
            item_id: معرف الطبلية
            start_date: تاريخ البداية
            end_date: تاريخ النهاية
            
        Returns:
            قائمة بأرقام العيون المتاحة
        """
        # الحصول على إجمالي عدد العيون في الطبلية
        item_query = "SELECT total_eyes FROM rental_items WHERE item_id = ?"
        item_result = self.db_manager.fetch_one(item_query, (item_id,))
        
        if not item_result:
            return []
        
        total_eyes = item_result['total_eyes']
        all_eyes = list(range(1, total_eyes + 1))
        
        # الحصول على العيون المؤجرة في الفترة المحددة
        rented_query = """
        SELECT DISTINCT eye_number FROM rented_eyes 
        WHERE item_id = ? AND status = 'نشط'
        AND (
            (start_date <= ? AND end_date >= ?) OR
            (start_date <= ? AND end_date >= ?) OR
            (start_date >= ? AND end_date <= ?)
        )
        """
        
        params = (item_id, start_date, start_date, end_date, end_date, start_date, end_date)
        rented_eyes = self.db_manager.fetch_query(rented_query, params)
        rented_eye_numbers = [eye['eye_number'] for eye in rented_eyes]
        
        # إرجاع العيون المتاحة
        available_eyes = [eye for eye in all_eyes if eye not in rented_eye_numbers]
        return available_eyes
    
    def get_rented_eyes_by_item(self, item_id: int) -> List[Dict]:
        """
        الحصول على العيون المؤجرة في طبلية معينة
        
        Args:
            item_id: معرف الطبلية
            
        Returns:
            قائمة بالعيون المؤجرة
        """
        query = """
        SELECT re.*, s.supplier_name, s.supplier_category, c.contract_number,
               ri.item_name, ri.location
        FROM rented_eyes re
        JOIN suppliers s ON re.supplier_id = s.supplier_id
        JOIN contracts c ON re.contract_id = c.contract_id
        JOIN rental_items ri ON re.item_id = ri.item_id
        WHERE re.item_id = ?
        ORDER BY re.eye_number, re.start_date
        """
        return self.db_manager.fetch_query(query, (item_id,))
    
    def get_rented_eyes_by_supplier(self, supplier_id: int) -> List[Dict]:
        """
        الحصول على العيون المؤجرة لمورد معين
        
        Args:
            supplier_id: معرف المورد
            
        Returns:
            قائمة بالعيون المؤجرة للمورد
        """
        query = """
        SELECT re.*, s.supplier_name, s.supplier_category, c.contract_number,
               ri.item_name, ri.location, ri.price_per_eye
        FROM rented_eyes re
        JOIN suppliers s ON re.supplier_id = s.supplier_id
        JOIN contracts c ON re.contract_id = c.contract_id
        JOIN rental_items ri ON re.item_id = ri.item_id
        WHERE re.supplier_id = ? AND re.status = 'نشط'
        ORDER BY ri.item_name, re.eye_number
        """
        return self.db_manager.fetch_query(query, (supplier_id,))
    
    def get_all_active_rented_eyes(self) -> List[Dict]:
        """
        الحصول على جميع العيون المؤجرة النشطة
        
        Returns:
            قائمة بجميع العيون المؤجرة النشطة
        """
        query = """
        SELECT re.*, s.supplier_name, s.supplier_category, c.contract_number,
               ri.item_name, ri.location, ri.price_per_eye
        FROM rented_eyes re
        JOIN suppliers s ON re.supplier_id = s.supplier_id
        JOIN contracts c ON re.contract_id = c.contract_id
        JOIN rental_items ri ON re.item_id = ri.item_id
        WHERE re.status = 'نشط'
        ORDER BY s.supplier_category, ri.item_name, re.eye_number
        """
        return self.db_manager.fetch_query(query)
    
    def end_eye_rental(self, rented_eye_id: int) -> bool:
        """
        إنهاء تأجير عين
        
        Args:
            rented_eye_id: معرف العين المؤجرة
            
        Returns:
            True إذا نجح الإنهاء، False إذا فشل
        """
        query = """
        UPDATE rented_eyes SET 
            status = 'منتهي', 
            updated_at = CURRENT_TIMESTAMP 
        WHERE rented_eye_id = ?
        """
        return self.db_manager.execute_query(query, (rented_eye_id,))
    
    def get_eye_rental_history(self, item_id: int, eye_number: int) -> List[Dict]:
        """
        الحصول على تاريخ تأجير عين معينة
        
        Args:
            item_id: معرف الطبلية
            eye_number: رقم العين
            
        Returns:
            قائمة بتاريخ تأجير العين
        """
        query = """
        SELECT re.*, s.supplier_name, s.supplier_category, c.contract_number
        FROM rented_eyes re
        JOIN suppliers s ON re.supplier_id = s.supplier_id
        JOIN contracts c ON re.contract_id = c.contract_id
        WHERE re.item_id = ? AND re.eye_number = ?
        ORDER BY re.start_date DESC
        """
        return self.db_manager.fetch_query(query, (item_id, eye_number))
    
    def get_rental_summary_by_category(self) -> Dict:
        """
        الحصول على ملخص الإيجارات حسب الأقسام
        
        Returns:
            ملخص الإيجارات حسب الأقسام
        """
        query = """
        SELECT s.supplier_category,
               COUNT(re.rented_eye_id) as total_rented_eyes,
               SUM(re.rental_price) as total_revenue,
               COUNT(DISTINCT re.supplier_id) as active_suppliers
        FROM rented_eyes re
        JOIN suppliers s ON re.supplier_id = s.supplier_id
        WHERE re.status = 'نشط'
        GROUP BY s.supplier_category
        """
        
        results = self.db_manager.fetch_query(query)
        summary = {}
        
        for result in results:
            summary[result['supplier_category']] = {
                'total_rented_eyes': result['total_rented_eyes'],
                'total_revenue': result['total_revenue'],
                'active_suppliers': result['active_suppliers']
            }
        
        return summary
