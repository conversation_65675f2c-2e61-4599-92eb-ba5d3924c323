# نظام إدارة عقود الموردين والإيجارات - جمعية المنقف التعاونية

<div align="center">

![النظام](https://img.shields.io/badge/النظام-إدارة_العقود-blue)
![الإصدار](https://img.shields.io/badge/الإصدار-1.0.0-green)
![Node.js](https://img.shields.io/badge/Node.js-16+-brightgreen)
![MySQL](https://img.shields.io/badge/MySQL-8.0+-orange)
![الترخيص](https://img.shields.io/badge/الترخيص-MIT-blue)

**نظام متكامل لإدارة عقود الموردين وإيجار العيون في السوق المركزي**

[🚀 البدء السريع](#-البدء-السريع) • 
[📖 الوثائق](#-الوثائق) • 
[🎯 الميزات](#-الميزات) • 
[🛠️ التطوير](#️-التطوير) • 
[📞 الدعم](#-الدعم)

</div>

---

## 📋 نظرة عامة

نظام إدارة عقود الموردين والإيجارات هو حل متكامل مطور خصيصاً لجمعية المنقف التعاونية لإدارة:

- **🏢 الموردين** حسب الأقسام (بهارات، استهلاكي، أجبان)
- **📋 العقود** بأنواعها (توريد، إيجار، خدمات)
- **👁️ نظام العيون** لتأجير مساحات الطبليات
- **📊 التقارير** والإحصائيات المالية
- **📁 إدارة الملفات** والوثائق

---

## 🚀 البدء السريع

### التشغيل بخطوة واحدة
```bash
node start-system.js
```

### التثبيت اليدوي
```bash
# 1. تثبيت التبعيات
npm install

# 2. إعداد قاعدة البيانات
node scripts/setup-database.js

# 3. تشغيل النظام
npm start
```

**🌐 الوصول للنظام**: http://localhost:3000

📚 **للمزيد**: راجع [دليل البدء السريع](QUICK_START_GUIDE.md)

---

## 🎯 الميزات الرئيسية

### 🏢 إدارة الموردين المتقدمة
- ✅ تصنيف الموردين حسب الأقسام
- ✅ إدارة معلومات الاتصال والوثائق
- ✅ تتبع حالة الموردين وتقييمهم
- ✅ البحث والتصفية المتقدمة

### 📋 نظام العقود الشامل
- ✅ عقود التوريد والإيجار والخدمات
- ✅ تتبع حالة العقود ومواعيد الانتهاء
- ✅ إدارة شروط الدفع والأحكام
- ✅ تنبيهات انتهاء العقود التلقائية

### 👁️ نظام العيون المبتكر
- ✅ تقسيم كل طبلية إلى 4 عيون (60×60 سم)
- ✅ تأجير العيون بشكل منفرد أو مجمع
- ✅ حساب الإيجارات تلقائياً (60 د.ك/عين/شهر)
- ✅ تتبع العيون المتاحة والمؤجرة

### 📊 التقارير والإحصائيات
- ✅ تقارير شاملة للموردين والعقود
- ✅ إحصائيات مالية مفصلة
- ✅ تقارير العيون حسب الأقسام
- ✅ تصدير Excel و PDF

### 🔒 الأمان والصلاحيات
- ✅ نظام مصادقة متقدم
- ✅ إدارة صلاحيات المستخدمين
- ✅ تشفير البيانات الحساسة
- ✅ سجل العمليات والأنشطة

---

## 🏗️ التقنيات المستخدمة

### Backend
- **Node.js** - بيئة تشغيل JavaScript
- **Express.js** - إطار عمل الخادم
- **MySQL** - قاعدة البيانات
- **JWT** - المصادقة والتوكن
- **Multer** - رفع الملفات

### Frontend
- **HTML5/CSS3** - هيكل وتصميم الواجهة
- **JavaScript ES6+** - منطق الواجهة
- **Tailwind CSS** - إطار عمل التصميم
- **Chart.js** - الرسوم البيانية
- **Flatpickr** - أدوات التاريخ

### أدوات إضافية
- **ExcelJS** - تصدير Excel
- **jsPDF** - تصدير PDF
- **Sharp** - معالجة الصور
- **Winston** - نظام السجلات

---

## 📁 هيكل المشروع

```
mohamed/
├── 📁 backend/                 # الخادم الخلفي
│   ├── 📁 api/                 # خادم API
│   ├── 📁 routes/              # مسارات API
│   ├── 📁 utils/               # أدوات مساعدة
│   ├── 📁 database/            # قاعدة البيانات
│   └── 📁 uploads/             # الملفات المرفوعة
├── 📁 frontend/                # الواجهة الأمامية
│   ├── 📁 assets/              # الموارد (CSS/JS)
│   └── 📄 index.html           # الصفحة الرئيسية
├── 📁 scripts/                 # سكريبتات الإعداد
├── 📁 gui/                     # واجهة سطح المكتب
├── 📁 models/                  # نماذج البيانات
├── 📁 reports/                 # نظام التقارير
├── 📄 .env                     # متغيرات البيئة
├── 📄 package.json             # تبعيات المشروع
└── 📄 start-system.js          # سكريبت التشغيل
```

---

## 🛠️ التطوير

### متطلبات التطوير
- Node.js 16+
- MySQL 8.0+
- Git
- محرر نصوص (VS Code مُنصح به)

### إعداد بيئة التطوير
```bash
# استنساخ المشروع
git clone [repository-url]
cd supplier-contracts-system

# تثبيت التبعيات
npm install

# إعداد متغيرات البيئة
cp .env.example .env

# تشغيل وضع التطوير
npm run dev
```

### أوامر التطوير
```bash
npm run dev          # تطوير مع إعادة التحميل
npm run lint         # فحص الكود
npm run test         # تشغيل الاختبارات
npm run build        # بناء للإنتاج
```

---

## 🚀 النشر

### النشر التقليدي
```bash
# بناء المشروع
npm run build

# تشغيل الإنتاج
npm start
```

### النشر باستخدام Docker
```bash
# بناء الصورة
npm run docker:build

# تشغيل الحاوية
npm run docker:run
```

### النشر باستخدام PM2
```bash
# تثبيت PM2
npm install -g pm2

# تشغيل التطبيق
npm run deploy
```

---

## 📊 لوحة التحكم

### الواجهات المتاحة

| الواجهة | الرابط | الوصف |
|---------|--------|-------|
| 🏠 الرئيسية | `/` | الصفحة الرئيسية |
| 📊 لوحة التحكم | `/frontend/` | إحصائيات شاملة |
| 📋 العقود المتقدمة | `/advanced_contracts_system.html` | إدارة العقود |
| 🏢 الموردين | `/enhanced_web_interface.html` | إدارة الموردين |
| 📄 العقود البسيطة | `/web_interface.html` | واجهة مبسطة |

### API Endpoints

| المسار | الطريقة | الوصف |
|--------|---------|-------|
| `/api/v1/suppliers` | GET/POST | إدارة الموردين |
| `/api/v1/contracts` | GET/POST | إدارة العقود |
| `/api/v1/reports` | GET | التقارير |
| `/api/v1/uploads` | POST | رفع الملفات |

---

## 🔧 الإعدادات

### متغيرات البيئة الأساسية
```env
# الخادم
PORT=3000
NODE_ENV=development

# قاعدة البيانات
DB_HOST=localhost
DB_PORT=3306
DB_NAME=supplier_contracts
DB_USER=contracts_app
DB_PASSWORD=SecurePassword123!

# الأمان
JWT_SECRET=your-secret-key
ENCRYPTION_KEY=your-encryption-key
```

### إعدادات النظام
```env
# الشركة
COMPANY_NAME=جمعية المنقف التعاونية
CURRENCY=KWD
TIMEZONE=Asia/Kuwait

# العيون
EYE_RENTAL_PRICE=60
EYE_SIZE=60x60
TABLE_EYES_COUNT=4
```

---

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# جميع الاختبارات
npm test

# اختبارات محددة
npm run test:unit
npm run test:integration
npm run test:api

# تغطية الكود
npm run test:coverage
```

### اختبار النظام
```bash
# اختبار شامل للنظام
node scripts/test-system.js

# اختبار قاعدة البيانات
node scripts/test-database.js
```

---

## 📞 الدعم

### الحصول على المساعدة
- 📧 **البريد الإلكتروني**: <EMAIL>
- 📱 **الهاتف**: +965-1234567
- 🌐 **الموقع**: https://manqaf-coop.com

### الإبلاغ عن المشاكل
1. تحقق من [الأسئلة الشائعة](docs/FAQ.md)
2. راجع [دليل استكشاف الأخطاء](docs/troubleshooting.md)
3. أنشئ [تقرير مشكلة](issues/new)

### المساهمة
نرحب بمساهماتكم! راجع [دليل المساهمة](CONTRIBUTING.md)

---

## 📄 الترخيص

هذا المشروع مرخص تحت [رخصة MIT](LICENSE) - راجع ملف الترخيص للتفاصيل.

---

## 🙏 شكر وتقدير

- **جمعية المنقف التعاونية** - العميل والراعي
- **فريق التطوير** - التصميم والتطوير
- **المجتمع المفتوح** - الأدوات والمكتبات

---

<div align="center">

**صُنع بـ ❤️ لجمعية المنقف التعاونية**

[⬆️ العودة للأعلى](#نظام-إدارة-عقود-الموردين-والإيجارات---جمعية-المنقف-التعاونية)

</div>
