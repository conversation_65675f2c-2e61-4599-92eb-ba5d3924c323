# =====================================================
# .gitignore لنظام إدارة عقود الموردين والإيجارات
# جمعية المنقف التعاونية
# =====================================================

# =====================================================
# Node.js
# =====================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# =====================================================
# متغيرات البيئة والإعدادات الحساسة
# =====================================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# ملفات الإعداد المحلية
config/local.js
config/local.json
config/production.js
config/production.json

# مفاتيح API والشهادات
*.key
*.pem
*.crt
*.p12
*.pfx
secrets/
certificates/

# =====================================================
# قواعد البيانات والبيانات الحساسة
# =====================================================
*.db
*.sqlite
*.sqlite3
*.db-journal

# ملفات قاعدة البيانات المحلية
database/local/
database/backups/
database/dumps/

# ملفات SQL المؤقتة
*.sql.tmp
*.sql.bak

# =====================================================
# السجلات والملفات المؤقتة
# =====================================================
logs/
*.log
log/

# ملفات السجلات المحددة
backend/logs/
frontend/logs/
application.log
error.log
access.log
debug.log
combined.log

# ملفات PID
*.pid
*.pid.lock

# =====================================================
# الملفات المرفوعة والمحتوى المُنشأ
# =====================================================
uploads/
backend/uploads/
frontend/uploads/
public/uploads/

# الصور والملفات المرفوعة
*.jpg
*.jpeg
*.png
*.gif
*.pdf
*.doc
*.docx
*.xls
*.xlsx
*.zip
*.rar

# استثناءات للصور الأساسية
!assets/images/logo.png
!assets/images/favicon.ico
!frontend/assets/images/logo.png
!frontend/assets/images/favicon.ico

# =====================================================
# التقارير والملفات المُنشأة
# =====================================================
reports/
reports_output/
backend/reports/
exports/
downloads/

# ملفات التقارير
*.pdf
*.xlsx
*.csv

# ملفات النسخ الاحتياطي
backups/
backup/
*.backup
*.bak

# =====================================================
# ملفات IDE والمحررات
# =====================================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr

# WebStorm
.webstorm/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =====================================================
# أنظمة التشغيل
# =====================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =====================================================
# Docker
# =====================================================
.dockerignore
docker-compose.override.yml
docker-compose.local.yml

# ملفات Docker المؤقتة
.docker/

# =====================================================
# أدوات البناء والنشر
# =====================================================

# Webpack
webpack-stats.json

# Rollup
rollup.config.local.js

# Parcel
.parcel-cache/

# PM2
.pm2/
ecosystem.config.local.js

# =====================================================
# اختبارات ومراقبة الجودة
# =====================================================

# Jest
coverage/
.nyc_output/

# Cypress
cypress/videos/
cypress/screenshots/

# ملفات الاختبار المؤقتة
test-results/
test-output/

# =====================================================
# ملفات مخصصة للمشروع
# =====================================================

# ملفات التطوير المحلية
local/
dev/
development/

# ملفات التوثيق المُنشأة
docs/generated/
documentation/build/

# ملفات الترجمة المؤقتة
locales/temp/
i18n/temp/

# ملفات الإحصائيات
stats/
analytics/
metrics/

# ملفات التكامل المؤقتة
integration/temp/
api/temp/

# =====================================================
# ملفات خاصة بالمشروع
# =====================================================

# ملفات إعداد المطورين
.developer/
.local/

# ملفات الاختبار المحلية
test.js
test.html
playground/

# ملفات TODO والملاحظات
TODO.md
NOTES.md
CHANGELOG.local.md

# ملفات النماذج الأولية
prototype/
mockup/
wireframe/

# =====================================================
# ملفات الأمان
# =====================================================

# مفاتيح SSH
id_rsa
id_rsa.pub
*.pem

# ملفات الشهادات
ssl/
certs/

# ملفات التشفير
*.gpg
*.asc

# =====================================================
# ملفات مؤقتة أخرى
# =====================================================

# ملفات النظام المؤقتة
*.tmp
*.temp
*.cache

# ملفات الضغط
*.7z
*.dmg
*.gz
*.iso
*.jar
*.tar
*.zip

# ملفات الفيديو والصوت (إذا لم تكن مطلوبة)
*.mp4
*.avi
*.mov
*.mp3
*.wav

# =====================================================
# استثناءات مهمة (ملفات يجب تضمينها)
# =====================================================

# ملفات الإعداد الأساسية
!.env.example
!.gitkeep
!README.md
!LICENSE
!package.json
!package-lock.json

# ملفات Docker الأساسية
!Dockerfile
!docker-compose.yml
!.dockerignore

# ملفات الإعداد الأساسية
!config/default.js
!config/default.json

# ملفات قاعدة البيانات الأساسية
!database/schema.sql
!database/migrations/
!database/seeds/

# الصور الأساسية للنظام
!assets/images/logo.png
!assets/images/favicon.ico
