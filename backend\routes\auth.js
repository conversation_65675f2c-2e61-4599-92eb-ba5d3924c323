/**
 * مسارات المصادقة والتفويض
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 */

const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');
const database = require('../utils/database');
const logger = require('../utils/logger');

const router = express.Router();

// إعداد تحديد معدل تسجيل الدخول
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 5, // 5 محاولات كحد أقصى
  message: {
    error: 'تم تجاوز عدد محاولات تسجيل الدخول المسموحة',
    message: 'يرجى المحاولة بعد 15 دقيقة'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true
});

// قواعد التحقق من صحة البيانات
const loginValidationRules = [
  body('username')
    .notEmpty()
    .withMessage('اسم المستخدم مطلوب')
    .isLength({ min: 3, max: 50 })
    .withMessage('اسم المستخدم يجب أن يكون بين 3 و 50 حرف'),
  
  body('password')
    .notEmpty()
    .withMessage('كلمة المرور مطلوبة')
    .isLength({ min: 6 })
    .withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
];

const registerValidationRules = [
  body('username')
    .notEmpty()
    .withMessage('اسم المستخدم مطلوب')
    .isLength({ min: 3, max: 50 })
    .withMessage('اسم المستخدم يجب أن يكون بين 3 و 50 حرف')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط'),
  
  body('email')
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح')
    .normalizeEmail(),
  
  body('password')
    .isLength({ min: 8 })
    .withMessage('كلمة المرور يجب أن تكون 8 أحرف على الأقل')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص'),
  
  body('full_name')
    .notEmpty()
    .withMessage('الاسم الكامل مطلوب')
    .isLength({ min: 2, max: 100 })
    .withMessage('الاسم الكامل يجب أن يكون بين 2 و 100 حرف'),
  
  body('role')
    .optional()
    .isIn(['admin', 'manager', 'employee', 'viewer'])
    .withMessage('الدور غير صحيح'),
  
  body('department')
    .optional()
    .isLength({ max: 50 })
    .withMessage('القسم لا يجب أن يتجاوز 50 حرف')
];

// معالجة أخطاء التحقق من صحة البيانات
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    logger.logValidationError(
      'auth_validation',
      req.body,
      errors.array(),
      null,
      req.ip
    );
    
    return res.status(400).json({
      success: false,
      message: 'خطأ في البيانات المدخلة',
      errors: errors.array().map(error => ({
        field: error.param,
        message: error.msg
      }))
    });
  }
  next();
};

// دالة إنشاء JWT Token
const generateToken = (user) => {
  const payload = {
    id: user.id,
    username: user.username,
    email: user.email,
    role: user.role,
    department: user.department
  };
  
  return jwt.sign(payload, process.env.JWT_SECRET || 'default_secret_key', {
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    issuer: 'supplier-contracts-system',
    audience: 'manqaf-coop'
  });
};

// دالة إنشاء Refresh Token
const generateRefreshToken = (userId) => {
  return jwt.sign(
    { userId, type: 'refresh' },
    process.env.JWT_REFRESH_SECRET || 'default_refresh_secret',
    { expiresIn: '7d' }
  );
};

/**
 * POST /api/v1/auth/login
 * تسجيل الدخول
 */
router.post('/login', [
  loginLimiter,
  ...loginValidationRules,
  handleValidationErrors
], async (req, res) => {
  try {
    const { username, password, remember_me = false } = req.body;
    
    // البحث عن المستخدم
    const user = await database.findOne('users', {
      username: username
    });
    
    if (!user) {
      logger.logSecurityEvent(
        'login_attempt_invalid_user',
        { username },
        req.ip,
        req.get('User-Agent')
      );
      
      return res.status(401).json({
        success: false,
        message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      });
    }
    
    // التحقق من حالة المستخدم
    if (!user.is_active) {
      logger.logSecurityEvent(
        'login_attempt_inactive_user',
        { username, userId: user.id },
        req.ip,
        req.get('User-Agent')
      );
      
      return res.status(401).json({
        success: false,
        message: 'الحساب غير نشط، يرجى التواصل مع المدير'
      });
    }
    
    // التحقق من كلمة المرور
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    
    if (!isPasswordValid) {
      logger.logSecurityEvent(
        'login_attempt_invalid_password',
        { username, userId: user.id },
        req.ip,
        req.get('User-Agent')
      );
      
      return res.status(401).json({
        success: false,
        message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      });
    }
    
    // تحديث آخر تسجيل دخول
    await database.update('users', {
      last_login: new Date()
    }, { id: user.id });
    
    // إنشاء الرموز المميزة
    const token = generateToken(user);
    const refreshToken = remember_me ? generateRefreshToken(user.id) : null;
    
    // إعداد الكوكيز
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: remember_me ? 7 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000 // 7 أيام أو 24 ساعة
    };
    
    res.cookie('token', token, cookieOptions);
    if (refreshToken) {
      res.cookie('refreshToken', refreshToken, {
        ...cookieOptions,
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7 أيام
      });
    }
    
    // تسجيل نجاح تسجيل الدخول
    logger.logUserLogin(user.id, user.username, req.ip, req.get('User-Agent'));
    
    // إرسال الاستجابة (بدون كلمة المرور)
    const { password_hash, ...userWithoutPassword } = user;
    
    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      data: {
        user: userWithoutPassword,
        token,
        expires_in: remember_me ? '7d' : '24h'
      }
    });
    
  } catch (error) {
    logger.error('خطأ في تسجيل الدخول:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم'
    });
  }
});

/**
 * POST /api/v1/auth/register
 * تسجيل مستخدم جديد (للمديرين فقط)
 */
router.post('/register', [
  ...registerValidationRules,
  handleValidationErrors
], async (req, res) => {
  try {
    const { username, email, password, full_name, role = 'employee', department, phone } = req.body;
    
    // التحقق من عدم وجود المستخدم مسبقاً
    const existingUser = await database.query(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [username, email]
    );
    
    if (existingUser.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'اسم المستخدم أو البريد الإلكتروني موجود مسبقاً'
      });
    }
    
    // تشفير كلمة المرور
    const saltRounds = 12;
    const password_hash = await bcrypt.hash(password, saltRounds);
    
    // إنشاء المستخدم
    const userId = await database.insert('users', {
      username,
      email,
      password_hash,
      full_name,
      role,
      department,
      phone,
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    });
    
    // تسجيل العملية
    logger.logDatabaseOperation(
      'CREATE',
      'users',
      userId,
      req.user?.id || null,
      { username, email, role }
    );
    
    res.status(201).json({
      success: true,
      message: 'تم إنشاء المستخدم بنجاح',
      data: { id: userId }
    });
    
  } catch (error) {
    logger.error('خطأ في تسجيل المستخدم:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إنشاء المستخدم'
    });
  }
});

/**
 * POST /api/v1/auth/logout
 * تسجيل الخروج
 */
router.post('/logout', async (req, res) => {
  try {
    // مسح الكوكيز
    res.clearCookie('token');
    res.clearCookie('refreshToken');
    
    // تسجيل العملية إذا كان المستخدم مسجل الدخول
    if (req.user) {
      logger.logUserLogout(req.user.id, req.user.username, req.ip);
    }
    
    res.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    });
    
  } catch (error) {
    logger.error('خطأ في تسجيل الخروج:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تسجيل الخروج'
    });
  }
});

/**
 * POST /api/v1/auth/refresh
 * تجديد الرمز المميز
 */
router.post('/refresh', async (req, res) => {
  try {
    const refreshToken = req.cookies.refreshToken || req.body.refresh_token;
    
    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        message: 'رمز التجديد مطلوب'
      });
    }
    
    // التحقق من صحة رمز التجديد
    const decoded = jwt.verify(
      refreshToken,
      process.env.JWT_REFRESH_SECRET || 'default_refresh_secret'
    );
    
    if (decoded.type !== 'refresh') {
      return res.status(401).json({
        success: false,
        message: 'رمز التجديد غير صحيح'
      });
    }
    
    // جلب بيانات المستخدم
    const user = await database.findOne('users', {
      id: decoded.userId,
      is_active: true
    });
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'المستخدم غير موجود أو غير نشط'
      });
    }
    
    // إنشاء رمز جديد
    const newToken = generateToken(user);
    
    // إعداد الكوكيز
    res.cookie('token', newToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000 // 24 ساعة
    });
    
    res.json({
      success: true,
      message: 'تم تجديد الرمز المميز بنجاح',
      data: {
        token: newToken,
        expires_in: '24h'
      }
    });
    
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'رمز التجديد غير صحيح أو منتهي الصلاحية'
      });
    }
    
    logger.error('خطأ في تجديد الرمز المميز:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تجديد الرمز المميز'
    });
  }
});

/**
 * GET /api/v1/auth/me
 * الحصول على بيانات المستخدم الحالي
 */
router.get('/me', async (req, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'غير مصرح لك بالوصول'
      });
    }
    
    // جلب بيانات المستخدم المحدثة
    const user = await database.findOne('users', { id: req.user.id });
    
    if (!user || !user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'المستخدم غير موجود أو غير نشط'
      });
    }
    
    // إرسال البيانات بدون كلمة المرور
    const { password_hash, ...userWithoutPassword } = user;
    
    res.json({
      success: true,
      data: userWithoutPassword
    });
    
  } catch (error) {
    logger.error('خطأ في جلب بيانات المستخدم:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب بيانات المستخدم'
    });
  }
});

module.exports = router;
