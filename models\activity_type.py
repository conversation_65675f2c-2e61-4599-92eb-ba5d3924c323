#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج أنواع الأنشطة
Activity Type Model for Contract Management System
"""

from typing import Optional, List, Dict

class ActivityType:
    """نموذج أنواع الأنشطة"""
    
    def __init__(self, db_manager):
        """
        تهيئة نموذج أنواع الأنشطة
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db_manager = db_manager
    
    def create_activity_type(self, activity_data: Dict) -> Optional[int]:
        """
        إنشاء نوع نشاط جديد
        
        Args:
            activity_data: بيانات نوع النشاط
            
        Returns:
            معرف نوع النشاط الجديد أو None في حالة الفشل
        """
        query = """
        INSERT INTO activity_types (
            activity_name, activity_type, base_price_per_sqm, description
        ) VALUES (?, ?, ?, ?)
        """
        
        params = (
            activity_data.get('activity_name'),
            activity_data.get('activity_type'),
            activity_data.get('base_price_per_sqm'),
            activity_data.get('description')
        )
        
        if self.db_manager.execute_query(query, params):
            result = self.db_manager.fetch_one("SELECT last_insert_rowid() as id")
            return result['id'] if result else None
        return None
    
    def get_activity_type_by_id(self, activity_type_id: int) -> Optional[Dict]:
        """
        الحصول على نوع نشاط بالمعرف
        
        Args:
            activity_type_id: معرف نوع النشاط
            
        Returns:
            بيانات نوع النشاط أو None
        """
        query = "SELECT * FROM activity_types WHERE activity_type_id = ?"
        return self.db_manager.fetch_one(query, (activity_type_id,))
    
    def get_all_activity_types(self, activity_type: Optional[str] = None) -> List[Dict]:
        """
        الحصول على جميع أنواع الأنشطة
        
        Args:
            activity_type: نوع النشاط (استهلاكي/غذائي) - اختياري
            
        Returns:
            قائمة بأنواع الأنشطة
        """
        if activity_type:
            query = """
            SELECT * FROM activity_types 
            WHERE activity_type = ? 
            ORDER BY activity_name
            """
            return self.db_manager.fetch_query(query, (activity_type,))
        else:
            query = "SELECT * FROM activity_types ORDER BY activity_type, activity_name"
            return self.db_manager.fetch_query(query)
    
    def update_activity_type(self, activity_type_id: int, activity_data: Dict) -> bool:
        """
        تحديث بيانات نوع النشاط
        
        Args:
            activity_type_id: معرف نوع النشاط
            activity_data: البيانات الجديدة
            
        Returns:
            True إذا نجح التحديث، False إذا فشل
        """
        query = """
        UPDATE activity_types SET
            activity_name = ?, activity_type = ?, base_price_per_sqm = ?,
            description = ?, updated_at = CURRENT_TIMESTAMP
        WHERE activity_type_id = ?
        """
        
        params = (
            activity_data.get('activity_name'),
            activity_data.get('activity_type'),
            activity_data.get('base_price_per_sqm'),
            activity_data.get('description'),
            activity_type_id
        )
        
        return self.db_manager.execute_query(query, params)
    
    def delete_activity_type(self, activity_type_id: int) -> bool:
        """
        حذف نوع نشاط
        
        Args:
            activity_type_id: معرف نوع النشاط
            
        Returns:
            True إذا نجح الحذف، False إذا فشل
        """
        # التحقق من وجود عقود مرتبطة بنوع النشاط
        contracts = self.db_manager.fetch_query(
            "SELECT COUNT(*) as count FROM contract_items WHERE activity_type_id = ?",
            (activity_type_id,)
        )
        
        if contracts and contracts[0]['count'] > 0:
            return False  # لا يمكن حذف نوع نشاط مرتبط بعقود
        else:
            query = "DELETE FROM activity_types WHERE activity_type_id = ?"
            return self.db_manager.execute_query(query, (activity_type_id,))
    
    def search_activity_types(self, search_term: str) -> List[Dict]:
        """
        البحث في أنواع الأنشطة
        
        Args:
            search_term: مصطلح البحث
            
        Returns:
            قائمة بأنواع الأنشطة المطابقة
        """
        query = """
        SELECT * FROM activity_types 
        WHERE activity_name LIKE ? OR description LIKE ?
        ORDER BY activity_type, activity_name
        """
        search_pattern = f"%{search_term}%"
        params = (search_pattern, search_pattern)
        return self.db_manager.fetch_query(query, params)
    
    def get_food_activities(self) -> List[Dict]:
        """
        الحصول على الأنشطة الغذائية
        
        Returns:
            قائمة بالأنشطة الغذائية
        """
        return self.get_all_activity_types('غذائي')
    
    def get_consumer_activities(self) -> List[Dict]:
        """
        الحصول على الأنشطة الاستهلاكية
        
        Returns:
            قائمة بالأنشطة الاستهلاكية
        """
        return self.get_all_activity_types('استهلاكي')
    
    def get_activity_usage_stats(self, activity_type_id: int) -> Dict:
        """
        الحصول على إحصائيات استخدام نوع النشاط
        
        Args:
            activity_type_id: معرف نوع النشاط
            
        Returns:
            إحصائيات الاستخدام
        """
        # عدد العقود النشطة
        active_contracts_query = """
        SELECT COUNT(*) as count 
        FROM contract_items ci
        JOIN contracts c ON ci.contract_id = c.contract_id
        WHERE ci.activity_type_id = ? AND ci.status = 'نشط'
        """
        active_result = self.db_manager.fetch_one(active_contracts_query, (activity_type_id,))
        active_contracts = active_result['count'] if active_result else 0
        
        # إجمالي المساحة المؤجرة
        total_area_query = """
        SELECT SUM(ci.area_sqm) as total_area
        FROM contract_items ci
        JOIN contracts c ON ci.contract_id = c.contract_id
        WHERE ci.activity_type_id = ? AND ci.status = 'نشط'
        """
        area_result = self.db_manager.fetch_one(total_area_query, (activity_type_id,))
        total_area = area_result['total_area'] if area_result and area_result['total_area'] else 0
        
        # إجمالي الإيرادات
        total_revenue_query = """
        SELECT SUM(ci.total_price) as total_revenue
        FROM contract_items ci
        JOIN contracts c ON ci.contract_id = c.contract_id
        WHERE ci.activity_type_id = ? AND ci.status = 'نشط'
        """
        revenue_result = self.db_manager.fetch_one(total_revenue_query, (activity_type_id,))
        total_revenue = revenue_result['total_revenue'] if revenue_result and revenue_result['total_revenue'] else 0
        
        return {
            'active_contracts': active_contracts,
            'total_area': float(total_area),
            'total_revenue': float(total_revenue)
        }
    
    def get_price_comparison(self) -> List[Dict]:
        """
        الحصول على مقارنة الأسعار بين أنواع الأنشطة
        
        Returns:
            قائمة بأنواع الأنشطة مع الأسعار
        """
        query = """
        SELECT activity_name, activity_type, base_price_per_sqm,
               (SELECT AVG(price_per_sqm) 
                FROM contract_items ci 
                WHERE ci.activity_type_id = at.activity_type_id) as avg_actual_price
        FROM activity_types at
        ORDER BY activity_type, base_price_per_sqm DESC
        """
        return self.db_manager.fetch_query(query)
    
    def validate_activity_data(self, activity_data: Dict) -> List[str]:
        """
        التحقق من صحة بيانات نوع النشاط
        
        Args:
            activity_data: بيانات نوع النشاط
            
        Returns:
            قائمة بالأخطاء (فارغة إذا كانت البيانات صحيحة)
        """
        errors = []
        
        # التحقق من الحقول المطلوبة
        if not activity_data.get('activity_name'):
            errors.append("اسم النشاط مطلوب")
        
        if not activity_data.get('activity_type'):
            errors.append("نوع النشاط مطلوب")
        elif activity_data.get('activity_type') not in ['استهلاكي', 'غذائي']:
            errors.append("نوع النشاط يجب أن يكون 'استهلاكي' أو 'غذائي'")
        
        # التحقق من السعر
        price = activity_data.get('base_price_per_sqm')
        if price is not None:
            try:
                price_float = float(price)
                if price_float < 0:
                    errors.append("السعر يجب أن يكون أكبر من أو يساوي صفر")
            except (ValueError, TypeError):
                errors.append("السعر يجب أن يكون رقماً صحيحاً")
        
        # التحقق من تفرد اسم النشاط
        activity_name = activity_data.get('activity_name')
        if activity_name:
            existing = self.db_manager.fetch_one(
                "SELECT activity_type_id FROM activity_types WHERE activity_name = ?",
                (activity_name,)
            )
            if existing:
                errors.append("اسم النشاط موجود مسبقاً")
        
        return errors
