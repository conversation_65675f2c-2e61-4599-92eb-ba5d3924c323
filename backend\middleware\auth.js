/**
 * وسيط المصادقة والتفويض
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 */

const jwt = require('jsonwebtoken');
const database = require('../utils/database');
const logger = require('../utils/logger');

/**
 * وسيط التحقق من المصادقة
 */
const authenticateToken = async (req, res, next) => {
  try {
    // الحصول على الرمز المميز من الرأس أو الكوكيز
    let token = null;
    
    // التحقق من رأس Authorization
    const authHeader = req.headers['authorization'];
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    }
    
    // التحقق من الكوكيز إذا لم يوجد في الرأس
    if (!token && req.cookies && req.cookies.token) {
      token = req.cookies.token;
    }
    
    // إذا لم يوجد رمز مميز
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'الوصول مرفوض - رمز المصادقة مطلوب',
        code: 'NO_TOKEN'
      });
    }
    
    // التحقق من صحة الرمز المميز
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default_secret_key');
    
    // التحقق من نوع الرمز المميز
    if (decoded.type === 'refresh') {
      return res.status(401).json({
        success: false,
        message: 'رمز التجديد غير صالح للمصادقة',
        code: 'INVALID_TOKEN_TYPE'
      });
    }
    
    // جلب بيانات المستخدم من قاعدة البيانات
    const user = await database.findOne('users', {
      id: decoded.id,
      is_active: true
    });
    
    if (!user) {
      logger.logSecurityEvent(
        'invalid_token_user_not_found',
        { userId: decoded.id },
        req.ip,
        req.get('User-Agent')
      );
      
      return res.status(401).json({
        success: false,
        message: 'المستخدم غير موجود أو غير نشط',
        code: 'USER_NOT_FOUND'
      });
    }
    
    // إضافة بيانات المستخدم إلى الطلب
    req.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      full_name: user.full_name,
      role: user.role,
      department: user.department,
      is_active: user.is_active
    };
    
    // إضافة وقت بداية الطلب لقياس الأداء
    req.startTime = Date.now();
    
    next();
    
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      logger.logSecurityEvent(
        'invalid_token_format',
        { error: error.message },
        req.ip,
        req.get('User-Agent')
      );
      
      return res.status(401).json({
        success: false,
        message: 'رمز المصادقة غير صحيح',
        code: 'INVALID_TOKEN'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      logger.logSecurityEvent(
        'expired_token',
        { expiredAt: error.expiredAt },
        req.ip,
        req.get('User-Agent')
      );
      
      return res.status(401).json({
        success: false,
        message: 'رمز المصادقة منتهي الصلاحية',
        code: 'TOKEN_EXPIRED'
      });
    }
    
    logger.error('خطأ في التحقق من المصادقة:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في التحقق من المصادقة',
      code: 'AUTH_ERROR'
    });
  }
};

/**
 * وسيط التحقق من الصلاحيات
 * @param {string|Array} roles - الأدوار المسموحة
 */
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'المصادقة مطلوبة',
        code: 'AUTHENTICATION_REQUIRED'
      });
    }
    
    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!allowedRoles.includes(req.user.role)) {
      logger.logSecurityEvent(
        'unauthorized_access_attempt',
        {
          userId: req.user.id,
          userRole: req.user.role,
          requiredRoles: allowedRoles,
          path: req.path,
          method: req.method
        },
        req.ip,
        req.get('User-Agent')
      );
      
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية للوصول إلى هذا المورد',
        code: 'INSUFFICIENT_PERMISSIONS'
      });
    }
    
    next();
  };
};

/**
 * وسيط التحقق من ملكية المورد
 * يتحقق من أن المستخدم يملك المورد أو لديه صلاحية إدارية
 */
const requireOwnership = (resourceTable, resourceIdParam = 'id', ownerField = 'created_by') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'المصادقة مطلوبة',
          code: 'AUTHENTICATION_REQUIRED'
        });
      }
      
      // المديرون لديهم صلاحية الوصول لجميع الموارد
      if (req.user.role === 'admin') {
        return next();
      }
      
      const resourceId = req.params[resourceIdParam];
      
      if (!resourceId) {
        return res.status(400).json({
          success: false,
          message: 'معرف المورد مطلوب',
          code: 'RESOURCE_ID_REQUIRED'
        });
      }
      
      // جلب المورد من قاعدة البيانات
      const resource = await database.findOne(resourceTable, { id: resourceId });
      
      if (!resource) {
        return res.status(404).json({
          success: false,
          message: 'المورد غير موجود',
          code: 'RESOURCE_NOT_FOUND'
        });
      }
      
      // التحقق من الملكية
      if (resource[ownerField] !== req.user.id) {
        logger.logSecurityEvent(
          'unauthorized_resource_access',
          {
            userId: req.user.id,
            resourceTable,
            resourceId,
            ownerId: resource[ownerField]
          },
          req.ip,
          req.get('User-Agent')
        );
        
        return res.status(403).json({
          success: false,
          message: 'ليس لديك صلاحية للوصول إلى هذا المورد',
          code: 'RESOURCE_ACCESS_DENIED'
        });
      }
      
      // إضافة المورد إلى الطلب للاستخدام في المعالجات
      req.resource = resource;
      
      next();
      
    } catch (error) {
      logger.error('خطأ في التحقق من ملكية المورد:', error);
      res.status(500).json({
        success: false,
        message: 'خطأ في التحقق من الصلاحيات',
        code: 'OWNERSHIP_CHECK_ERROR'
      });
    }
  };
};

/**
 * وسيط اختياري للمصادقة
 * يحاول المصادقة ولكن لا يرفض الطلب إذا فشلت
 */
const optionalAuth = async (req, res, next) => {
  try {
    // محاولة الحصول على الرمز المميز
    let token = null;
    
    const authHeader = req.headers['authorization'];
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    }
    
    if (!token && req.cookies && req.cookies.token) {
      token = req.cookies.token;
    }
    
    // إذا لم يوجد رمز مميز، المتابعة بدون مصادقة
    if (!token) {
      return next();
    }
    
    // محاولة التحقق من الرمز المميز
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default_secret_key');
    
    // جلب بيانات المستخدم
    const user = await database.findOne('users', {
      id: decoded.id,
      is_active: true
    });
    
    if (user) {
      req.user = {
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        department: user.department,
        is_active: user.is_active
      };
    }
    
    next();
    
  } catch (error) {
    // في حالة الخطأ، المتابعة بدون مصادقة
    next();
  }
};

/**
 * وسيط تسجيل العمليات
 */
const logActivity = (action, resourceType) => {
  return async (req, res, next) => {
    // تنفيذ المعالج الأصلي أولاً
    const originalSend = res.send;
    
    res.send = function(data) {
      // تسجيل العملية بعد نجاح الطلب
      if (res.statusCode >= 200 && res.statusCode < 300) {
        logger.logDatabaseOperation(
          action,
          resourceType,
          req.params.id || 'unknown',
          req.user?.id,
          {
            method: req.method,
            path: req.path,
            statusCode: res.statusCode
          }
        );
      }
      
      // استدعاء الدالة الأصلية
      originalSend.call(this, data);
    };
    
    next();
  };
};

/**
 * وسيط قياس الأداء
 */
const performanceMonitor = (req, res, next) => {
  const startTime = process.hrtime.bigint();
  const startMemory = process.memoryUsage();
  
  // تنفيذ المعالج الأصلي
  const originalSend = res.send;
  
  res.send = function(data) {
    const endTime = process.hrtime.bigint();
    const endMemory = process.memoryUsage();
    
    const duration = Number(endTime - startTime) / 1000000; // تحويل إلى ميلي ثانية
    const memoryDiff = endMemory.heapUsed - startMemory.heapUsed;
    
    // تسجيل معلومات الأداء للطلبات البطيئة
    if (duration > 1000) { // أكثر من ثانية واحدة
      logger.logPerformance(
        `${req.method} ${req.path}`,
        duration,
        memoryDiff,
        process.cpuUsage()
      );
    }
    
    // إضافة معلومات الأداء إلى الرأس
    res.set('X-Response-Time', `${duration}ms`);
    
    originalSend.call(this, data);
  };
  
  next();
};

module.exports = {
  authenticateToken,
  requireRole,
  requireOwnership,
  optionalAuth,
  logActivity,
  performanceMonitor
};
