# نظام إدارة عقود الموردين - جمعية المنقف

## نظرة عامة

نظام إدارة عقود الموردين هو تطبيق سطح مكتب مطور خصيصاً لجمعية المنقف لإدارة عقود إيجار الطبليات والقواطع والجندولات والاستنادات في السوق المركزي.

## الميزات الرئيسية

### 1. إدارة الموردين حسب الأقسام
- **أقسام الموردين**: بهارات، استهلاكي، أجبان
- تسجيل بيانات الموردين بشكل منظم حسب القسم
- إدارة معلومات الاتصال والوثائق
- تتبع حالة الموردين (نشط/غير نشط)
- البحث والتصفية المتقدمة حسب القسم

### 2. إدارة العقود
- إنشاء وتعديل عقود الإيجار
- تتبع تواريخ بداية وانتهاء العقود
- إدارة شروط الدفع والأحكام
- تنبيهات للعقود المنتهية قريباً

### 3. إدارة الطبليات ونظام العيون
- **نظام العيون الجديد**: كل طبلية تحتوي على 4 عيون (60 سم × 60 سم)
- **تسعير العيون**: كل عين بـ 60 دينار شهرياً
- تسجيل الطبليات والقواطع والجندولات والاستنادات
- تحديد المساحات والمواقع حسب الأقسام
- تتبع حالة العناصر (متاح/مؤجر/صيانة)
- إدارة أنواع الأنشطة (استهلاكي/غذائي)
- **إدارة العيون**: تأجير عيون منفردة، تتبع العيون المتاحة والمؤجرة

### 4. نظام التسعير
- تسجيل أسعار الطبليات حسب نوع النشاط
- حساب المبالغ تلقائياً بناءً على المساحة والسعر
- مرونة في تحديد أسعار مختلفة لكل عقد

### 5. البحث والتصفية
- بحث متقدم في جميع البيانات
- تصفية حسب المورد ونوع النشاط والفترة الزمنية
- عرض النتائج بشكل منظم وسهل القراءة

### 6. التقارير والإحصائيات
- تقارير شاملة للموردين والعقود والإيجارات
- **تقارير العيون**: تقارير مفصلة عن العيون المؤجرة حسب الأقسام
- تقارير مالية دورية مع إحصائيات الأقسام
- إمكانية التصدير إلى Excel و PDF
- إحصائيات سريعة في الواجهة الرئيسية
- **إحصائيات العيون**: عدد العيون المؤجرة والمتاحة، الإيرادات حسب القسم

## الميزات الجديدة - نظام العيون والأقسام

### نظام العيون المتطور
- **تقسيم الطبليات إلى عيون**: كل طبلية مقسمة إلى 4 عيون
- **مقاس العين الواحدة**: 60 سم × 60 سم
- **سعر العين**: 60 دينار كويتي شهرياً
- **تأجير مرن**: يمكن تأجير عين واحدة أو أكثر من نفس الطبلية
- **تتبع دقيق**: معرفة العيون المتاحة والمؤجرة في كل طبلية

### أقسام الموردين
- **قسم البهارات**: موردين متخصصين في البهارات والتوابل
- **قسم الاستهلاكي**: موردين المواد الاستهلاكية والمنزلية
- **قسم الأجبان**: موردين متخصصين في الأجبان ومنتجات الألبان

### إدارة متقدمة للعيون
- **واجهة مخصصة**: نافذة منفصلة لإدارة العيون
- **فلترة حسب القسم**: عرض الطبليات حسب القسم المطلوب
- **تحديد العيون المتاحة**: عرض العيون المتاحة في كل طبلية
- **ربط بالعقود**: ربط كل عين مؤجرة بعقد ومورد محدد
- **إحصائيات مفصلة**: إحصائيات الإيجارات والإيرادات حسب كل قسم

## متطلبات النظام

- نظام التشغيل: Windows 10 أو أحدث
- Python 3.8 أو أحدث
- ذاكرة: 4 جيجابايت RAM على الأقل
- مساحة القرص: 500 ميجابايت للتطبيق والبيانات

## التثبيت والتشغيل

### 1. تثبيت Python
تأكد من تثبيت Python 3.8 أو أحدث على النظام.

### 2. تثبيت المكتبات المطلوبة
```bash
pip install ttkbootstrap pillow reportlab openpyxl
```

### 3. تشغيل التطبيق
```bash
python main.py
```

## دليل الاستخدام

### البدء السريع

1. **تشغيل التطبيق**: قم بتشغيل ملف `main.py`
2. **إضافة مورد جديد**: انتقل إلى تبويب "الموردين" واضغط "إدارة الموردين"
3. **إنشاء عقد**: انتقل إلى تبويب "العقود" واضغط "إدارة العقود"
4. **إضافة طبليات**: انتقل إلى تبويب "الطبليات والعناصر"
5. **توليد التقارير**: انتقل إلى تبويب "التقارير"

### إدارة الموردين

#### إضافة مورد جديد:
1. اضغط على "إدارة الموردين" في التبويب الرئيسي
2. انتقل إلى تبويب "إضافة/تعديل مورد"
3. املأ البيانات المطلوبة (اسم المورد ورقم الهاتف مطلوبان)
4. اضغط "حفظ"

#### البحث عن مورد:
1. في نافذة إدارة الموردين، استخدم حقل البحث
2. يمكن البحث بالاسم أو رقم الهاتف أو البريد الإلكتروني

### إدارة العقود

#### إنشاء عقد جديد:
1. اضغط على "إدارة العقود" في التبويب الرئيسي
2. انتقل إلى تبويب "إضافة/تعديل عقد"
3. اضغط "توليد رقم" لإنشاء رقم عقد تلقائي
4. اختر المورد من القائمة المنسدلة
5. حدد تواريخ البداية والنهاية
6. املأ باقي البيانات واضغط "حفظ"

#### تتبع العقود المنتهية:
- اضغط "العقود المنتهية قريباً" في تبويب العقود للحصول على تنبيه

### إدارة الطبليات والعناصر

#### إضافة طبلية جديدة:
1. اضغط على "إدارة الطبليات والعناصر"
2. في الجانب الأيمن، املأ نموذج العنصر
3. حدد نوع العنصر (طبلية/قاطع/جندولة/استناد)
4. أدخل الاسم والموقع والمساحة
5. اضغط "حفظ"

#### إدارة أنواع الأنشطة:
1. انتقل إلى تبويب "أنواع الأنشطة"
2. يمكن إضافة أنشطة جديدة أو تعديل الأسعار الأساسية

### إدارة العيون (النظام الجديد)

#### تأجير عين في طبلية:
1. اضغط على "إدارة العيون (نظام الطبليات)" في التبويب الرئيسي
2. في تبويب "تأجير العيون":
   - اختر القسم المطلوب (بهارات/استهلاكي/أجبان)
   - اختر الطبلية من القائمة
   - اختر المورد من القائمة المنسدلة
   - اختر العقد المرتبط بالمورد
   - اختر رقم العين المتاحة (1-4)
   - أدخل تواريخ البداية والنهاية
   - تأكد من السعر (60 دينار افتراضياً)
   - اضغط "تأجير العين"

#### عرض العيون المؤجرة:
1. انتقل إلى تبويب "العيون المؤجرة"
2. يمكن فلترة العرض حسب القسم
3. لإنهاء إيجار عين، اختر العين واضغط "إنهاء الإيجار"

#### إحصائيات العيون:
1. انتقل إلى تبويب "الإحصائيات"
2. عرض إحصائيات شاملة عن:
   - إجمالي الطبليات والعيون
   - العيون المؤجرة والمتاحة
   - الإيرادات الشهرية
   - إحصائيات كل قسم على حدة

### توليد التقارير

#### تقرير الموردين:
1. انتقل إلى تبويب "التقارير"
2. اضغط "مركز التقارير"
3. في تبويب "تقارير الموردين"، اختر التنسيق (Excel/PDF)
4. اضغط "توليد تقرير الموردين"

#### تقرير العقود:
1. في مركز التقارير، انتقل إلى تبويب "تقارير العقود"
2. حدد الفترة الزمنية والحالة (اختياري)
3. اختر التنسيق واضغط "توليد تقرير العقود"

## هيكل المشروع

```
mohamed/
├── main.py                 # الملف الرئيسي
├── database/              # مجلد قاعدة البيانات
│   ├── __init__.py
│   └── db_manager.py      # مدير قاعدة البيانات
├── models/                # نماذج البيانات
│   ├── __init__.py
│   ├── supplier.py        # نموذج المورد (محدث للأقسام)
│   ├── contract.py        # نموذج العقد
│   ├── rental_item.py     # نموذج العناصر (محدث للعيون)
│   ├── activity_type.py   # نموذج أنواع الأنشطة
│   └── rented_eye.py      # نموذج العيون المؤجرة (جديد)
├── gui/                   # واجهة المستخدم
│   ├── __init__.py
│   ├── main_window.py     # النافذة الرئيسية (محدثة)
│   ├── suppliers_window.py # نافذة الموردين (محدثة للأقسام)
│   ├── contracts_window.py # نافذة العقود
│   ├── items_window.py    # نافذة الطبليات (محدثة للعيون)
│   ├── reports_window.py  # نافذة التقارير
│   └── eyes_management_window.py # نافذة إدارة العيون (جديدة)
├── reports/               # نظام التقارير
│   ├── __init__.py
│   └── report_generator.py # مولد التقارير
├── reports_output/        # مجلد التقارير المُنشأة
├── supplier_contracts.db  # قاعدة البيانات
└── README.md             # هذا الملف
```

## قاعدة البيانات

يستخدم التطبيق قاعدة بيانات SQLite مع الجداول التالية:

- **suppliers**: بيانات الموردين (محدث ليشمل أقسام الموردين)
- **contracts**: العقود
- **contract_items**: عناصر العقود
- **rental_items**: الطبليات والعناصر (محدث ليشمل نظام العيون)
- **rented_eyes**: العيون المؤجرة (جدول جديد)
- **activity_types**: أنواع الأنشطة
- **payments**: المدفوعات

### تفاصيل الجداول الجديدة:

#### جدول العيون المؤجرة (rented_eyes):
- معرف العين المؤجرة
- معرف الطبلية
- معرف العقد والمورد
- رقم العين (1-4)
- سعر الإيجار
- تواريخ البداية والنهاية
- حالة الإيجار
- ملاحظات

## الدعم والصيانة

### النسخ الاحتياطي
- يُنصح بعمل نسخة احتياطية من ملف `supplier_contracts.db` بانتظام
- يمكن نسخ الملف إلى مكان آمن أو خدمة تخزين سحابية

### استكشاف الأخطاء
- تأكد من تثبيت جميع المكتبات المطلوبة
- تحقق من صلاحيات الكتابة في مجلد التطبيق
- في حالة مشاكل قاعدة البيانات، تأكد من عدم فتح الملف في برنامج آخر

## الترخيص

هذا التطبيق مطور خصيصاً لجمعية المنقف. جميع الحقوق محفوظة.

## معلومات التطوير

- **الإصدار**: 1.0
- **تاريخ الإصدار**: 2025
- **المطور**: تم تطويره باستخدام Python و Tkinter
- **قاعدة البيانات**: SQLite
- **التقارير**: Excel (openpyxl) و PDF (reportlab)
