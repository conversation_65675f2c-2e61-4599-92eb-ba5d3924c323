# =====================================================
# ملف متغيرات البيئة - نظام إدارة عقود الموردين
# جمعية المنقف التعاونية
# =====================================================

# =====================================================
# إعدادات الخادم
# =====================================================
NODE_ENV=development
PORT=3000
HOST=localhost
TZ=Asia/Kuwait

# =====================================================
# إعدادات قاعدة البيانات MySQL
# =====================================================
DB_HOST=localhost
DB_PORT=3306
DB_NAME=supplier_contracts
DB_USER=contracts_app
DB_PASSWORD=SecurePassword123!
DB_CHARSET=utf8mb4
DB_TIMEZONE=+03:00

# إعدادات اتصال قاعدة البيانات
DB_CONNECTION_LIMIT=10
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000
DB_RECONNECT=true

# SSL لقاعدة البيانات (للإنتاج)
DB_SSL=false
DB_SSL_CA_PATH=
DB_SSL_CERT_PATH=
DB_SSL_KEY_PATH=

# =====================================================
# إعدادات المصادقة والأمان
# =====================================================

# JWT Secrets - يجب تغييرها في الإنتاج
JWT_SECRET=your-very-secure-jwt-secret-key-here-change-in-production
JWT_REFRESH_SECRET=your-very-secure-refresh-secret-key-here-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# إعدادات كلمات المرور
BCRYPT_SALT_ROUNDS=12
PASSWORD_MIN_LENGTH=8

# إعدادات الجلسات
SESSION_SECRET=your-session-secret-key-here
SESSION_MAX_AGE=86400000

# =====================================================
# إعدادات Redis (للتخزين المؤقت والجلسات)
# =====================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=RedisPassword123!
REDIS_DB=0
REDIS_MAX_MEMORY=256mb
REDIS_MAX_MEMORY_POLICY=allkeys-lru

# =====================================================
# إعدادات البريد الإلكتروني
# =====================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM_NAME=جمعية المنقف التعاونية
SMTP_FROM_EMAIL=<EMAIL>

# إعدادات البريد الإلكتروني للتنبيهات
ALERT_EMAIL_ENABLED=true
ALERT_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>

# =====================================================
# إعدادات رفع الملفات
# =====================================================
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif
UPLOAD_PATH=backend/uploads
UPLOAD_URL_PREFIX=/uploads

# إعدادات الصور
IMAGE_MAX_WIDTH=1920
IMAGE_MAX_HEIGHT=1080
IMAGE_QUALITY=85

# =====================================================
# إعدادات التسجيل (Logging)
# =====================================================
LOG_LEVEL=info
LOG_FILE_PATH=backend/logs
LOG_MAX_SIZE=10485760
LOG_MAX_FILES=10
LOG_DATE_PATTERN=YYYY-MM-DD

# تسجيل قاعدة البيانات
DB_LOG_ENABLED=false
DB_LOG_LEVEL=error

# =====================================================
# إعدادات الأمان
# =====================================================

# CORS
CORS_ORIGIN=http://localhost:3000,https://contracts.manqaf-coop.com
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_LOGIN_MAX=5

# Helmet Security Headers
HELMET_ENABLED=true
HELMET_CSP_ENABLED=true

# =====================================================
# إعدادات التطبيق
# =====================================================

# معلومات الشركة
COMPANY_NAME=جمعية المنقف التعاونية
COMPANY_NAME_EN=Manqaf Cooperative Society
COMPANY_ADDRESS=المنقف، دولة الكويت
COMPANY_PHONE=+965 1234 5678
COMPANY_EMAIL=<EMAIL>
COMPANY_WEBSITE=https://manqaf-coop.com

# إعدادات النظام
SYSTEM_NAME=نظام إدارة عقود الموردين والإيجارات
SYSTEM_VERSION=1.0.0
SYSTEM_TIMEZONE=Asia/Kuwait
SYSTEM_LANGUAGE=ar
SYSTEM_CURRENCY=KWD

# إعدادات الطاولات والإيجار
TABLE_SECTION_SIZE=60
TABLE_SECTION_PRICE=60
TABLE_SECTIONS_PER_TABLE=4
DEFAULT_RENTAL_CURRENCY=KWD

# =====================================================
# إعدادات التقارير
# =====================================================
REPORTS_PATH=backend/reports
REPORTS_TEMP_PATH=backend/temp
REPORTS_MAX_RECORDS=10000

# إعدادات PDF
PDF_FONT_PATH=assets/fonts/Cairo-Regular.ttf
PDF_LOGO_PATH=assets/images/logo.png

# إعدادات Excel
EXCEL_TEMPLATE_PATH=assets/templates

# =====================================================
# إعدادات النسخ الاحتياطي
# =====================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_PATH=backups
BACKUP_RETENTION_DAYS=30
BACKUP_COMPRESSION=true

# إعدادات النسخ الاحتياطي السحابي
CLOUD_BACKUP_ENABLED=false
CLOUD_BACKUP_PROVIDER=aws
CLOUD_BACKUP_BUCKET=manqaf-contracts-backup
CLOUD_BACKUP_REGION=me-south-1

# =====================================================
# إعدادات المراقبة والتنبيهات
# =====================================================
MONITORING_ENABLED=true
HEALTH_CHECK_INTERVAL=30000
PERFORMANCE_MONITORING=true

# إعدادات Prometheus (اختياري)
PROMETHEUS_ENABLED=false
PROMETHEUS_PORT=9090

# إعدادات التنبيهات
ALERTS_ENABLED=true
ALERT_CONTRACT_EXPIRY_DAYS=30
ALERT_PAYMENT_DUE_DAYS=7

# =====================================================
# إعدادات التطوير
# =====================================================
DEBUG=false
VERBOSE_LOGGING=false
API_DOCS_ENABLED=true
SWAGGER_ENABLED=true

# إعدادات Hot Reload
HOT_RELOAD=true
WATCH_FILES=true

# =====================================================
# إعدادات الإنتاج
# =====================================================

# إعدادات SSL/HTTPS
SSL_ENABLED=false
SSL_CERT_PATH=
SSL_KEY_PATH=
SSL_CA_PATH=

# إعدادات CDN
CDN_ENABLED=false
CDN_URL=https://cdn.manqaf-coop.com

# إعدادات Load Balancer
LOAD_BALANCER_ENABLED=false
CLUSTER_MODE=false
CLUSTER_WORKERS=auto

# =====================================================
# إعدادات خارجية (APIs)
# =====================================================

# إعدادات SMS (للتنبيهات)
SMS_ENABLED=false
SMS_PROVIDER=twilio
SMS_API_KEY=
SMS_API_SECRET=
SMS_FROM_NUMBER=

# إعدادات الدفع الإلكتروني
PAYMENT_ENABLED=false
PAYMENT_PROVIDER=knet
PAYMENT_MERCHANT_ID=
PAYMENT_API_KEY=

# إعدادات التكامل مع الأنظمة الأخرى
ERP_INTEGRATION=false
ERP_API_URL=
ERP_API_KEY=

# =====================================================
# إعدادات Docker
# =====================================================
DOCKER_REGISTRY=
DOCKER_IMAGE_TAG=latest
DOCKER_NETWORK=supplier-contracts-network

# =====================================================
# إعدادات متقدمة
# =====================================================

# إعدادات الذاكرة
NODE_OPTIONS=--max-old-space-size=2048
UV_THREADPOOL_SIZE=4

# إعدادات الشبكة
KEEP_ALIVE_TIMEOUT=5000
HEADERS_TIMEOUT=60000

# إعدادات التخزين المؤقت
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=100

# إعدادات الضغط
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6
COMPRESSION_THRESHOLD=1024

# =====================================================
# ملاحظات مهمة
# =====================================================
# 1. يجب تغيير جميع كلمات المرور والمفاتيح السرية في الإنتاج
# 2. تأكد من تعيين NODE_ENV=production في بيئة الإنتاج
# 3. قم بتفعيل SSL في بيئة الإنتاج
# 4. راجع إعدادات الأمان قبل النشر
# 5. تأكد من صحة إعدادات قاعدة البيانات
# 6. قم بتفعيل النسخ الاحتياطي في الإنتاج
