@echo off
chcp 65001 >nul
title نظام إدارة العقود المتطور - محمد مرزوق العقاب

echo.
echo 👑 نظام إدارة عقود الموردين والإيجارات المتطور
echo جمعية المنقف التعاونية
echo.
echo 👨‍💻 تطوير وتنفيذ: محمد مرزوق العقاب
echo مطور نظم معلومات محترف ومتخصص
echo ================================================
echo.

:menu
echo 🎨 اختر الواجهة المطلوبة:
echo.
echo 1. 👑 الواجهة الاحترافية المتطورة (موصى بها)
echo 2. 🔧 واجهة الإدارة المتقدمة (للمديرين)
echo 3. 📁 نظام رفع ملفات الموردين
echo 4. 🧪 صفحة الاختبار السريع
echo 5. 🚀 صفحة التشغيل (جميع الواجهات)
echo.
echo 💡 واجهات إضافية:
echo 6. 👑 الواجهة المتطورة
echo 7. 👥 إدارة الموردين المحسنة
echo 8. 📋 إدارة العقود المتقدمة
echo 9. 🖥️  الواجهة البسيطة
echo.
echo 🔧 أدوات النظام:
echo A. تشغيل الخادم فقط
echo B. اختبار النظام
echo C. عرض معلومات النظام
echo.
echo 0. خروج
echo.
set /p choice="اختر رقم أو حرف (0-9, A-C): "

if "%choice%"=="1" goto professional_ultimate
if "%choice%"=="2" goto advanced_management
if "%choice%"=="3" goto upload_system
if "%choice%"=="4" goto quick_test
if "%choice%"=="5" goto launch_page
if "%choice%"=="6" goto ultimate_interface
if "%choice%"=="7" goto enhanced_interface
if "%choice%"=="8" goto advanced_contracts
if "%choice%"=="9" goto simple_interface
if /i "%choice%"=="A" goto server_only
if /i "%choice%"=="B" goto test_system
if /i "%choice%"=="C" goto system_info
if "%choice%"=="0" goto exit
goto invalid

:professional_ultimate
echo.
echo 👑 تشغيل الواجهة الاحترافية المتطورة...
echo 🎨 تصميم Glass Morphism مع ألوان عالية التباين
echo.
call :start_server
start "" http://localhost:3000/professional_ultimate_interface.html
goto end

:advanced_management
echo.
echo 🔧 تشغيل واجهة الإدارة المتقدمة...
echo 💼 مخصصة للمديرين مع ألوان عالية التباين
echo.
call :start_server
start "" http://localhost:3000/advanced_management_interface.html
goto end

:upload_system
echo.
echo 📁 تشغيل نظام رفع ملفات الموردين...
echo 📊 رفع ملفات Excel و CSV مع التحقق من البيانات
echo.
call :start_server
start "" http://localhost:3000/upload_suppliers.html
goto end

:quick_test
echo.
echo 🧪 تشغيل صفحة الاختبار السريع...
echo 🔍 اختبار جميع مكونات النظام
echo.
call :start_server
start "" http://localhost:3000/quick-upload-test.html
goto end

:launch_page
echo.
echo 🚀 تشغيل صفحة التشغيل...
echo 🎯 عرض جميع الواجهات المتاحة
echo.
call :start_server
start "" http://localhost:3000/launch-interface.html
goto end

:ultimate_interface
echo.
echo 👑 تشغيل الواجهة المتطورة...
echo.
call :start_server
start "" http://localhost:3000/ultimate_interface.html
goto end

:enhanced_interface
echo.
echo 👥 تشغيل إدارة الموردين المحسنة...
echo.
call :start_server
start "" http://localhost:3000/enhanced_web_interface.html
goto end

:advanced_contracts
echo.
echo 📋 تشغيل إدارة العقود المتقدمة...
echo.
call :start_server
start "" http://localhost:3000/advanced_contracts_system.html
goto end

:simple_interface
echo.
echo 🖥️ تشغيل الواجهة البسيطة...
echo.
call :start_server
start "" http://localhost:3000/web_interface.html
goto end

:server_only
echo.
echo 🔧 تشغيل الخادم فقط...
echo 💡 يمكنك فتح أي واجهة يدوياً من المتصفح
echo.
call :start_server_only
goto end

:test_system
echo.
echo 🧪 تشغيل اختبار النظام...
echo.
if exist "quick-test.bat" (
    call quick-test.bat
) else (
    echo ❌ ملف الاختبار غير موجود
    echo 💡 قم بتشغيل: npm run test:interactive
    pause
)
goto menu

:system_info
echo.
echo 📊 معلومات النظام:
echo.
echo 🏢 الشركة: جمعية المنقف التعاونية
echo 👨‍💻 المطور: محمد مرزوق العقاب
echo 🌐 الخادم: http://localhost:3000
echo 📁 المجلد: %CD%
echo 🕒 التاريخ: %DATE% %TIME%
echo.
echo 🎨 الواجهات الاحترافية الجديدة:
echo    👑 professional_ultimate_interface.html
echo    🔧 advanced_management_interface.html
echo    📁 upload_suppliers.html
echo    🧪 quick-upload-test.html
echo.
pause
goto menu

:start_server
echo 🔄 بدء تشغيل الخادم...
if not exist "node_modules" (
    echo 📦 تثبيت التبعيات...
    npm install
)
timeout /t 2 /nobreak >nul
start /min cmd /c "npm start"
echo ⏳ انتظار تشغيل الخادم...
timeout /t 5 /nobreak >nul
echo ✅ تم تشغيل الخادم بنجاح
goto :eof

:start_server_only
echo 🔄 بدء تشغيل الخادم...
if not exist "node_modules" (
    echo 📦 تثبيت التبعيات...
    npm install
)
echo.
echo 🌐 الخادم سيعمل على: http://localhost:3000
echo.
echo 🎨 الواجهات المتاحة:
echo    👑 /professional_ultimate_interface.html
echo    🔧 /advanced_management_interface.html
echo    📁 /upload_suppliers.html
echo    🧪 /quick-upload-test.html
echo    🚀 /launch-interface.html
echo.
echo 💡 اضغط Ctrl+C لإيقاف الخادم
npm start
goto end

:invalid
echo.
echo ❌ اختيار غير صحيح
echo 💡 يرجى اختيار رقم أو حرف صحيح
timeout /t 2 /nobreak >nul
goto menu

:end
echo.
echo 🎉 تم تشغيل النظام بنجاح!
echo 💡 اضغط Ctrl+C في نافذة الخادم لإيقافه
echo.
pause
goto menu

:exit
echo.
echo 👋 شكراً لاستخدام نظام إدارة العقود المتطور
echo 👨‍💻 تطوير: محمد مرزوق العقاب
echo 🏢 جمعية المنقف التعاونية
echo.
timeout /t 3 /nobreak >nul
exit
