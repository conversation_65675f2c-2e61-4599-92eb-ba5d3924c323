/**
 * دوال مساعدة عامة
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 */

// ==================== دوال التاريخ والوقت ====================

/**
 * تنسيق التاريخ للعرض
 * @param {string|Date} date - التاريخ
 * @param {string} format - تنسيق العرض
 * @returns {string} التاريخ المنسق
 */
function formatDate(date, format = 'full') {
  if (!date) return '';
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';
  
  const options = {
    full: {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    },
    short: {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    },
    numeric: {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    },
    time: {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    },
    datetime: {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }
  };
  
  return dateObj.toLocaleDateString('en-GB', options[format] || options.full);
}

/**
 * حساب الفرق بين تاريخين
 * @param {string|Date} startDate - تاريخ البداية
 * @param {string|Date} endDate - تاريخ النهاية
 * @returns {Object} الفرق بالأيام والساعات والدقائق
 */
function dateDifference(startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return { days: 0, hours: 0, minutes: 0 };
  }
  
  const diffMs = end.getTime() - start.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
  
  return {
    days: diffDays,
    hours: diffHours,
    minutes: diffMinutes,
    totalMs: diffMs
  };
}

/**
 * التحقق من انتهاء صلاحية التاريخ
 * @param {string|Date} date - التاريخ
 * @param {number} warningDays - عدد أيام التحذير
 * @returns {Object} حالة انتهاء الصلاحية
 */
function checkExpiry(date, warningDays = 30) {
  const targetDate = new Date(date);
  const today = new Date();
  const diffDays = Math.ceil((targetDate - today) / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) {
    return { status: 'expired', days: Math.abs(diffDays), message: `منتهي منذ ${Math.abs(diffDays)} يوم` };
  } else if (diffDays <= warningDays) {
    return { status: 'expiring', days: diffDays, message: `ينتهي خلال ${diffDays} يوم` };
  } else {
    return { status: 'active', days: diffDays, message: `ينتهي خلال ${diffDays} يوم` };
  }
}

// ==================== دوال الأرقام والعملة ====================

/**
 * تنسيق الأرقام للعرض
 * @param {number} number - الرقم
 * @param {number} decimals - عدد الخانات العشرية
 * @returns {string} الرقم المنسق
 */
function formatNumber(number, decimals = 2) {
  if (isNaN(number)) return '0';
  
  return new Intl.NumberFormat('ar-SA', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(number);
}

/**
 * تنسيق العملة للعرض
 * @param {number} amount - المبلغ
 * @param {string} currency - رمز العملة
 * @returns {string} المبلغ المنسق
 */
function formatCurrency(amount, currency = 'KWD') {
  if (isNaN(amount)) return '0.000 د.ك';
  
  const currencyMap = {
    'KWD': { symbol: 'د.ك', decimals: 3 },
    'USD': { symbol: '$', decimals: 2 },
    'EUR': { symbol: '€', decimals: 2 },
    'SAR': { symbol: 'ر.س', decimals: 2 }
  };
  
  const currencyInfo = currencyMap[currency] || currencyMap['KWD'];
  
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: currencyInfo.decimals,
    maximumFractionDigits: currencyInfo.decimals
  }).format(amount);
}

/**
 * تحويل الرقم إلى كلمات عربية
 * @param {number} number - الرقم
 * @returns {string} الرقم بالكلمات
 */
function numberToWords(number) {
  // تنفيذ مبسط - يمكن توسيعه
  const ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
  const tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
  const teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];
  
  if (number === 0) return 'صفر';
  if (number < 10) return ones[number];
  if (number >= 10 && number < 20) return teens[number - 10];
  if (number < 100) return tens[Math.floor(number / 10)] + (number % 10 ? ' ' + ones[number % 10] : '');
  
  // للأرقام الأكبر، إرجاع الرقم كما هو
  return number.toString();
}

// ==================== دوال النصوص ====================

/**
 * اقتطاع النص مع إضافة نقاط
 * @param {string} text - النص
 * @param {number} maxLength - الطول الأقصى
 * @returns {string} النص المقتطع
 */
function truncateText(text, maxLength = 100) {
  if (!text || text.length <= maxLength) return text || '';
  return text.substring(0, maxLength) + '...';
}

/**
 * تنظيف النص من الرموز الخاصة
 * @param {string} text - النص
 * @returns {string} النص المنظف
 */
function sanitizeText(text) {
  if (!text) return '';
  return text.replace(/[<>\"'&]/g, '');
}

/**
 * تحويل النص إلى slug
 * @param {string} text - النص
 * @returns {string} الـ slug
 */
function textToSlug(text) {
  if (!text) return '';
  return text
    .toLowerCase()
    .replace(/[^\u0600-\u06FFa-z0-9\s-]/g, '') // الاحتفاظ بالعربية والإنجليزية والأرقام
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

/**
 * البحث في النص مع تمييز النتائج
 * @param {string} text - النص
 * @param {string} searchTerm - مصطلح البحث
 * @returns {string} النص مع التمييز
 */
function highlightSearchTerm(text, searchTerm) {
  if (!text || !searchTerm) return text;
  
  const regex = new RegExp(`(${searchTerm})`, 'gi');
  return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
}

// ==================== دوال التحقق من صحة البيانات ====================

/**
 * التحقق من صحة البريد الإلكتروني
 * @param {string} email - البريد الإلكتروني
 * @returns {boolean} صحة البريد الإلكتروني
 */
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * التحقق من صحة رقم الهاتف الكويتي
 * @param {string} phone - رقم الهاتف
 * @returns {boolean} صحة رقم الهاتف
 */
function validateKuwaitPhone(phone) {
  const phoneRegex = /^(\+965|965)?[2569]\d{7}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
}

/**
 * التحقق من قوة كلمة المرور
 * @param {string} password - كلمة المرور
 * @returns {Object} تقييم قوة كلمة المرور
 */
function validatePasswordStrength(password) {
  const checks = {
    length: password.length >= 8,
    uppercase: /[A-Z]/.test(password),
    lowercase: /[a-z]/.test(password),
    numbers: /\d/.test(password),
    special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
  };
  
  const score = Object.values(checks).filter(Boolean).length;
  
  let strength = 'ضعيف';
  let color = 'red';
  
  if (score >= 4) {
    strength = 'قوي';
    color = 'green';
  } else if (score >= 3) {
    strength = 'متوسط';
    color = 'yellow';
  }
  
  return {
    score,
    strength,
    color,
    checks,
    isValid: score >= 3
  };
}

// ==================== دوال الملفات ====================

/**
 * تنسيق حجم الملف
 * @param {number} bytes - حجم الملف بالبايت
 * @returns {string} حجم الملف المنسق
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 بايت';
  
  const k = 1024;
  const sizes = ['بايت', 'ك.ب', 'م.ب', 'ج.ب', 'ت.ب'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * التحقق من نوع الملف
 * @param {string} filename - اسم الملف
 * @param {Array} allowedTypes - الأنواع المسموحة
 * @returns {boolean} صحة نوع الملف
 */
function validateFileType(filename, allowedTypes = []) {
  if (!filename || allowedTypes.length === 0) return true;
  
  const extension = filename.split('.').pop().toLowerCase();
  return allowedTypes.includes(extension);
}

/**
 * الحصول على أيقونة الملف حسب النوع
 * @param {string} filename - اسم الملف
 * @returns {string} فئة الأيقونة
 */
function getFileIcon(filename) {
  if (!filename) return 'fas fa-file';
  
  const extension = filename.split('.').pop().toLowerCase();
  const iconMap = {
    pdf: 'fas fa-file-pdf text-red-600',
    doc: 'fas fa-file-word text-blue-600',
    docx: 'fas fa-file-word text-blue-600',
    xls: 'fas fa-file-excel text-green-600',
    xlsx: 'fas fa-file-excel text-green-600',
    ppt: 'fas fa-file-powerpoint text-orange-600',
    pptx: 'fas fa-file-powerpoint text-orange-600',
    jpg: 'fas fa-file-image text-purple-600',
    jpeg: 'fas fa-file-image text-purple-600',
    png: 'fas fa-file-image text-purple-600',
    gif: 'fas fa-file-image text-purple-600',
    zip: 'fas fa-file-archive text-yellow-600',
    rar: 'fas fa-file-archive text-yellow-600',
    txt: 'fas fa-file-alt text-gray-600'
  };
  
  return iconMap[extension] || 'fas fa-file text-gray-600';
}

// ==================== دوال الألوان والتصميم ====================

/**
 * تحويل HEX إلى RGB
 * @param {string} hex - لون HEX
 * @returns {Object} قيم RGB
 */
function hexToRgb(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

/**
 * تحديد لون النص المناسب للخلفية
 * @param {string} backgroundColor - لون الخلفية
 * @returns {string} لون النص (أبيض أو أسود)
 */
function getContrastTextColor(backgroundColor) {
  const rgb = hexToRgb(backgroundColor);
  if (!rgb) return '#000000';
  
  // حساب السطوع
  const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
  return brightness > 128 ? '#000000' : '#ffffff';
}

// ==================== دوال التخزين المحلي ====================

/**
 * حفظ البيانات في التخزين المحلي
 * @param {string} key - المفتاح
 * @param {*} data - البيانات
 */
function saveToLocalStorage(key, data) {
  try {
    localStorage.setItem(key, JSON.stringify(data));
    return true;
  } catch (error) {
    console.error('خطأ في حفظ البيانات:', error);
    return false;
  }
}

/**
 * جلب البيانات من التخزين المحلي
 * @param {string} key - المفتاح
 * @param {*} defaultValue - القيمة الافتراضية
 * @returns {*} البيانات المحفوظة
 */
function getFromLocalStorage(key, defaultValue = null) {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error('خطأ في جلب البيانات:', error);
    return defaultValue;
  }
}

/**
 * حذف البيانات من التخزين المحلي
 * @param {string} key - المفتاح
 */
function removeFromLocalStorage(key) {
  try {
    localStorage.removeItem(key);
    return true;
  } catch (error) {
    console.error('خطأ في حذف البيانات:', error);
    return false;
  }
}

// ==================== دوال متنوعة ====================

/**
 * إنشاء معرف فريد
 * @returns {string} معرف فريد
 */
function generateUniqueId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * تأخير التنفيذ (Debounce)
 * @param {Function} func - الدالة
 * @param {number} wait - وقت التأخير بالميلي ثانية
 * @returns {Function} الدالة المؤخرة
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * نسخ النص إلى الحافظة
 * @param {string} text - النص المراد نسخه
 * @returns {Promise<boolean>} نجح النسخ أم لا
 */
async function copyToClipboard(text) {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error('خطأ في نسخ النص:', error);
    return false;
  }
}

/**
 * طباعة عنصر HTML محدد
 * @param {string} elementId - معرف العنصر
 */
function printElement(elementId) {
  const element = document.getElementById(elementId);
  if (!element) return;
  
  const printWindow = window.open('', '_blank');
  printWindow.document.write(`
    <html>
      <head>
        <title>طباعة</title>
        <style>
          body { font-family: 'Cairo', sans-serif; direction: rtl; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        ${element.innerHTML}
      </body>
    </html>
  `);
  printWindow.document.close();
  printWindow.print();
}

// تصدير الدوال للاستخدام العام
window.Utils = {
  formatDate,
  dateDifference,
  checkExpiry,
  formatNumber,
  formatCurrency,
  numberToWords,
  truncateText,
  sanitizeText,
  textToSlug,
  highlightSearchTerm,
  validateEmail,
  validateKuwaitPhone,
  validatePasswordStrength,
  formatFileSize,
  validateFileType,
  getFileIcon,
  hexToRgb,
  getContrastTextColor,
  saveToLocalStorage,
  getFromLocalStorage,
  removeFromLocalStorage,
  generateUniqueId,
  debounce,
  copyToClipboard,
  printElement
};
