<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>إدارة عقود الموردين والإيجارات - جمعية المنقف</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://unpkg.com/just-validate@latest/dist/just-validate.production.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/axios@1.6.0/dist/axios.min.js"></script>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
    body { font-family: 'Cairo', sans-serif; }
    .toast-success { background-color: #10b981; color: white; }
    .toast-error { background-color: #ef4444; color: white; }
    .toast-warning { background-color: #f59e0b; color: white; }
    .contract-card { transition: all 0.3s ease; }
    .contract-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
    .loading { opacity: 0.6; pointer-events: none; }
    .highlight { background-color: #fef3c7; }
    .pagination-btn { transition: all 0.2s ease; }
    .pagination-btn:hover { transform: scale(1.05); }
    .filter-active { background-color: #3b82f6; color: white; }
  </style>
</head>
<body class="bg-gray-100 min-h-screen">

  <!-- شريط التنقل العلوي -->
  <nav class="bg-blue-800 text-white p-4 shadow-lg">
    <div class="max-w-7xl mx-auto flex items-center justify-between">
      <h1 class="text-2xl font-bold">🏢 نظام إدارة عقود الموردين - جمعية المنقف</h1>
      <div class="flex items-center space-x-4">
        <span id="currentTime" class="text-sm opacity-75"></span>
        <span id="contractsCount" class="bg-blue-600 px-3 py-1 rounded-full text-sm">0 عقد</span>
      </div>
    </div>
  </nav>

  <!-- حاوية رئيسية -->
  <div class="max-w-7xl mx-auto p-6 space-y-8">

    <!-- شريط أدوات أعلى الصفحة -->
    <div class="bg-white p-4 rounded-lg shadow-md">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <!-- أزرار التحكم الرئيسية -->
        <div class="flex flex-wrap gap-3">
          <button id="btnNewContract" class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors">
            ➕ عقد جديد
          </button>
          <button id="btnLoadDrafts" class="bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg transition-colors">
            📂 المسودات
          </button>
          <button id="btnExportData" class="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors">
            📊 تصدير البيانات
          </button>
          <button id="btnPrint" class="bg-gray-700 hover:bg-gray-800 text-white py-2 px-4 rounded-lg transition-colors">
            🖨️ طباعة
          </button>
          <button id="btnSync" class="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-lg transition-colors">
            🔄 مزامنة
          </button>
        </div>
        
        <!-- معلومات الحالة -->
        <div class="flex items-center space-x-4 text-sm">
          <span id="lastSaved" class="text-gray-600">آخر حفظ: لم يتم الحفظ بعد</span>
          <div id="connectionStatus" class="flex items-center">
            <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
            <span class="text-green-600">متصل</span>
          </div>
        </div>
      </div>
    </div>

    <!-- نموذج إضافة/تعديل عقد -->
    <div id="contractForm" class="bg-white p-6 rounded-lg shadow-md">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-bold text-gray-800">📝 إضافة/تعديل عقد</h2>
        <button id="btnToggleForm" class="text-gray-500 hover:text-gray-700">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <form id="contract-form" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- رقم العقد -->
          <label class="block">
            <span class="text-gray-700 font-medium">رقم العقد *</span>
            <div class="flex mt-1">
              <input type="text" name="contractNumber" id="contractNumber" 
                     class="flex-1 border border-gray-300 rounded-r-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                     placeholder="2025-001" required />
              <button type="button" id="btnGenerateNumber" 
                      class="bg-blue-600 hover:bg-blue-700 text-white px-3 rounded-l-lg transition-colors">
                🔄
              </button>
            </div>
          </label>

          <!-- اسم المورد -->
          <label class="block">
            <span class="text-gray-700 font-medium">اسم المورد *</span>
            <input type="text" name="supplierName" id="supplierName" 
                   class="w-full mt-1 border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                   placeholder="شركة البهارات الذهبية" required />
          </label>

          <!-- نوع العقد -->
          <label class="block">
            <span class="text-gray-700 font-medium">نوع العقد *</span>
            <select name="contractType" id="contractType" 
                    class="w-full mt-1 border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
              <option value="">اختر نوع العقد</option>
              <option value="توريد">📦 توريد</option>
              <option value="إيجار">🏠 إيجار</option>
              <option value="خدمات">🔧 خدمات</option>
              <option value="صيانة">⚙️ صيانة</option>
            </select>
          </label>

          <!-- قسم المورد -->
          <label class="block">
            <span class="text-gray-700 font-medium">قسم المورد</span>
            <select name="supplierCategory" id="supplierCategory" 
                    class="w-full mt-1 border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">اختر القسم</option>
              <option value="بهارات">🌶️ بهارات</option>
              <option value="استهلاكي">🧽 مواد استهلاكية</option>
              <option value="أجبان">🧀 أجبان</option>
            </select>
          </label>

          <!-- تاريخ البداية -->
          <label class="block">
            <span class="text-gray-700 font-medium">تاريخ البداية *</span>
            <input type="date" name="startDate" id="startDate" 
                   class="w-full mt-1 border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required />
          </label>

          <!-- تاريخ النهاية -->
          <label class="block">
            <span class="text-gray-700 font-medium">تاريخ النهاية *</span>
            <input type="date" name="endDate" id="endDate" 
                   class="w-full mt-1 border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required />
          </label>

          <!-- المبلغ الإجمالي -->
          <label class="block">
            <span class="text-gray-700 font-medium">المبلغ الإجمالي (د.ك)</span>
            <input type="number" name="totalAmount" id="totalAmount" step="0.01" min="0"
                   class="w-full mt-1 border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                   placeholder="1000.00" />
          </label>

          <!-- حالة العقد -->
          <label class="block">
            <span class="text-gray-700 font-medium">حالة العقد</span>
            <select name="contractStatus" id="contractStatus" 
                    class="w-full mt-1 border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="نشط">✅ نشط</option>
              <option value="منتهي">⏰ منتهي</option>
              <option value="ملغي">❌ ملغي</option>
              <option value="معلق">⏸️ معلق</option>
            </select>
          </label>

          <!-- لون العقد -->
          <label class="block">
            <span class="text-gray-700 font-medium">لون العقد</span>
            <div class="flex items-center mt-1 space-x-2">
              <input type="color" name="contractColor" id="contractColor" value="#3b82f6"
                     class="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer" />
              <span class="text-sm text-gray-500">للتمييز البصري</span>
            </div>
          </label>
        </div>

        <!-- العناصر المتعددة -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <label class="block">
            <span class="text-gray-700 font-medium">عناصر العقد *</span>
            <select name="contractItems" id="contractItems" multiple
                    class="w-full mt-1 h-32 border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required>
              <option value="بهارات_حارة">🌶️ بهارات حارة</option>
              <option value="بهارات_حلوة">🍯 بهارات حلوة</option>
              <option value="توابل_شرقية">🥘 توابل شرقية</option>
              <option value="مواد_تنظيف">🧽 مواد تنظيف</option>
              <option value="مستلزمات_مطبخ">🍴 مستلزمات مطبخ</option>
              <option value="أدوات_شخصية">🧴 أدوات شخصية</option>
              <option value="أجبان_طازجة">🧀 أجبان طازجة</option>
              <option value="منتجات_ألبان">🥛 منتجات ألبان</option>
              <option value="أجبان_مستوردة">🧀 أجبان مستوردة</option>
              <option value="خدمات_صيانة">🔧 خدمات صيانة</option>
              <option value="خدمات_تنظيف">🧹 خدمات تنظيف</option>
              <option value="خدمات_أمن">🛡️ خدمات أمن</option>
            </select>
            <div class="text-xs text-gray-500 mt-1">
              اضغط Ctrl (أو Cmd) + النقر لاختيار عدة عناصر
            </div>
          </label>

          <!-- الأولوية -->
          <label class="block">
            <span class="text-gray-700 font-medium">أولوية العقد</span>
            <select name="contractPriority" id="contractPriority"
                    class="w-full mt-1 border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="منخفضة">🟢 منخفضة</option>
              <option value="متوسطة" selected>🟡 متوسطة</option>
              <option value="عالية">🟠 عالية</option>
              <option value="عاجلة">🔴 عاجلة</option>
            </select>
          </label>
        </div>

        <!-- شروط الدفع -->
        <label class="block">
          <span class="text-gray-700 font-medium">شروط الدفع</span>
          <input type="text" name="paymentTerms" id="paymentTerms" 
                 class="w-full mt-1 border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                 placeholder="دفع شهري مقدم" />
        </label>

        <!-- الملاحظات -->
        <label class="block">
          <span class="text-gray-700 font-medium">الملاحظات</span>
          <textarea name="notes" id="notes" rows="3"
                    class="w-full mt-1 border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    placeholder="أي ملاحظات إضافية..."></textarea>
        </label>

        <!-- أزرار النموذج -->
        <div class="flex flex-wrap gap-3 pt-4 border-t">
          <button type="button" id="btnSaveLocal" 
                  class="bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg transition-colors">
            💾 حفظ مؤقت
          </button>
          <button type="submit" id="btnSaveFinal"
                  class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors">
            ✅ حفظ نهائي
          </button>
          <button type="button" id="btnClearForm"
                  class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors">
            🗑️ مسح النموذج
          </button>
          <button type="button" id="btnCancelEdit"
                  class="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-lg transition-colors hidden">
            ❌ إلغاء التعديل
          </button>
        </div>
      </form>
    </div>

    <!-- بحث وفلاتر متقدمة -->
    <div class="bg-white p-6 rounded-lg shadow-md">
      <h3 class="text-lg font-bold text-gray-800 mb-4">🔍 البحث والفلاتر</h3>

      <!-- الصف الأول - البحث الأساسي -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
        <div class="relative">
          <input type="text" id="searchInput" placeholder="ابحث باسم المورد أو رقم العقد..."
                 class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>

        <select id="filterType" class="border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
          <option value="">كل الأنواع</option>
          <option value="توريد">📦 توريد</option>
          <option value="إيجار">🏠 إيجار</option>
          <option value="خدمات">🔧 خدمات</option>
          <option value="صيانة">⚙️ صيانة</option>
        </select>

        <select id="filterStatus" class="border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
          <option value="">كل الحالات</option>
          <option value="نشط">✅ نشط</option>
          <option value="منتهي">⏰ منتهي</option>
          <option value="ملغي">❌ ملغي</option>
          <option value="معلق">⏸️ معلق</option>
        </select>

        <select id="filterCategory" class="border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
          <option value="">كل الأقسام</option>
          <option value="بهارات">🌶️ بهارات</option>
          <option value="استهلاكي">🧽 مواد استهلاكية</option>
          <option value="أجبان">🧀 أجبان</option>
        </select>
      </div>

      <!-- الصف الثاني - فلاتر متقدمة -->
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
        <div>
          <label class="block text-sm text-gray-600 mb-1">من تاريخ</label>
          <input type="date" id="filterStartDate" class="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
        </div>

        <div>
          <label class="block text-sm text-gray-600 mb-1">إلى تاريخ</label>
          <input type="date" id="filterEndDate" class="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
        </div>

        <div>
          <label class="block text-sm text-gray-600 mb-1">المبلغ من</label>
          <input type="number" id="filterMinAmount" step="0.01" min="0" placeholder="0.00"
                 class="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
        </div>

        <div>
          <label class="block text-sm text-gray-600 mb-1">المبلغ إلى</label>
          <input type="number" id="filterMaxAmount" step="0.01" min="0" placeholder="∞"
                 class="w-full border border-gray-300 rounded-lg p-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
        </div>

        <div class="flex items-end">
          <button id="btnClearFilters" class="w-full bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors">
            🗑️ مسح الفلاتر
          </button>
        </div>
      </div>

      <!-- معلومات النتائج -->
      <div class="flex items-center justify-between text-sm text-gray-600">
        <span id="resultsInfo">عرض 0 من 0 عقد</span>
        <span id="activeFilters" class="text-blue-600"></span>
      </div>
    </div>

    <!-- جدول العقود -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <div class="p-4 bg-gray-50 border-b flex items-center justify-between">
        <h3 class="text-lg font-bold text-gray-800">📋 قائمة العقود</h3>
        <div class="flex items-center space-x-2">
          <select id="itemsPerPage" class="border border-gray-300 rounded p-1 text-sm">
            <option value="10">10 عقود</option>
            <option value="25">25 عقد</option>
            <option value="50">50 عقد</option>
            <option value="100">100 عقد</option>
          </select>
          <button id="btnRefresh" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors">
            🔄 تحديث
          </button>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full">
          <thead class="bg-gray-100">
            <tr>
              <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200" data-sort="contractNumber">
                رقم العقد ↕️
              </th>
              <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200" data-sort="supplierName">
                المورد ↕️
              </th>
              <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200" data-sort="contractType">
                النوع ↕️
              </th>
              <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200" data-sort="startDate">
                تاريخ البداية ↕️
              </th>
              <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200" data-sort="totalAmount">
                المبلغ ↕️
              </th>
              <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200" data-sort="contractStatus">
                الحالة ↕️
              </th>
              <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                الإجراءات
              </th>
            </tr>
          </thead>
          <tbody id="contractsTableBody" class="bg-white divide-y divide-gray-200">
            <!-- سيتم ملء البيانات ديناميكياً -->
          </tbody>
        </table>
      </div>

      <!-- رسالة عدم وجود بيانات -->
      <div id="noDataMessage" class="hidden p-8 text-center text-gray-500">
        <div class="text-6xl mb-4">📄</div>
        <h3 class="text-lg font-medium mb-2">لا توجد عقود</h3>
        <p class="text-sm">ابدأ بإضافة عقد جديد أو قم بتعديل الفلاتر</p>
      </div>
    </div>

    <!-- ترقيم الصفحات -->
    <div class="flex items-center justify-between bg-white px-4 py-3 rounded-lg shadow-md">
      <div class="flex items-center text-sm text-gray-700">
        <span>عرض</span>
        <span id="pageInfo" class="mx-1 font-medium">1-10 من 0</span>
        <span>نتيجة</span>
      </div>

      <div class="flex items-center space-x-2">
        <button id="btnFirstPage" class="pagination-btn px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
          ⏮️ الأولى
        </button>
        <button id="btnPrevPage" class="pagination-btn px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
          ⏪ السابقة
        </button>

        <div id="pageNumbers" class="flex space-x-1">
          <!-- أرقام الصفحات ستُضاف ديناميكياً -->
        </div>

        <button id="btnNextPage" class="pagination-btn px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
          التالية ⏩
        </button>
        <button id="btnLastPage" class="pagination-btn px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
          الأخيرة ⏭️
        </button>
      </div>
    </div>

  </div>

  <!-- صندوق الرسائل (Toast) -->
  <div id="toast" class="fixed top-5 left-5 hidden p-4 rounded-lg shadow-lg z-50 max-w-sm">
    <div class="flex items-center">
      <div id="toastIcon" class="flex-shrink-0 w-6 h-6 mr-3"></div>
      <div id="toastMessage" class="text-sm font-medium"></div>
      <button id="toastClose" class="ml-auto text-white hover:text-gray-200">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- نافذة المسودات -->
  <div id="draftsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div class="p-6 border-b">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-bold text-gray-800">📂 المسودات المحفوظة</h3>
            <button id="closeDraftsModal" class="text-gray-500 hover:text-gray-700">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>

        <div class="p-6 overflow-y-auto max-h-[70vh]">
          <div id="draftsList" class="space-y-3">
            <!-- قائمة المسودات ستُضاف ديناميكياً -->
          </div>

          <div id="noDraftsMessage" class="hidden text-center text-gray-500 py-8">
            <div class="text-4xl mb-4">📝</div>
            <h4 class="text-lg font-medium mb-2">لا توجد مسودات محفوظة</h4>
            <p class="text-sm">ابدأ بإنشاء عقد جديد واحفظه كمسودة</p>
          </div>
        </div>

        <div class="p-6 border-t bg-gray-50">
          <div class="flex justify-end space-x-3">
            <button id="btnClearAllDrafts" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors">
              🗑️ حذف جميع المسودات
            </button>
            <button id="btnCloseDrafts" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors">
              إغلاق
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- السكريبت الرئيسي -->
  <script>
    // ==================== المتغيرات العامة ====================
    let contracts = JSON.parse(localStorage.getItem('contracts') || '[]');
    let drafts = JSON.parse(localStorage.getItem('drafts') || '[]');
    let currentPage = 1;
    let itemsPerPage = 10;
    let sortField = 'contractNumber';
    let sortDirection = 'asc';
    let editingContractId = null;
    let filteredContracts = [];
    let validator = null;

    // ==================== إعدادات API ====================
    const API_CONFIG = {
      baseURL: 'http://localhost:3000/api', // يمكن تغييرها حسب الخادم
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };

    // إعداد Axios
    const apiClient = axios.create(API_CONFIG);

    // معالج الأخطاء العام
    apiClient.interceptors.response.use(
      response => response,
      error => {
        console.error('API Error:', error);

        if (error.code === 'ECONNABORTED') {
          showToast('انتهت مهلة الاتصال بالخادم', 'error');
        } else if (error.response) {
          const status = error.response.status;
          const message = error.response.data?.message || 'خطأ في الخادم';

          switch (status) {
            case 400:
              showToast(`خطأ في البيانات: ${message}`, 'error');
              break;
            case 401:
              showToast('غير مصرح لك بالوصول', 'error');
              break;
            case 403:
              showToast('ليس لديك صلاحية لهذا الإجراء', 'error');
              break;
            case 404:
              showToast('المورد المطلوب غير موجود', 'error');
              break;
            case 500:
              showToast('خطأ داخلي في الخادم', 'error');
              break;
            default:
              showToast(`خطأ: ${message}`, 'error');
          }
        } else if (error.request) {
          showToast('لا يمكن الاتصال بالخادم. يتم العمل في الوضع المحلي.', 'warning');
          updateConnectionStatus(false);
        }

        return Promise.reject(error);
      }
    );

    // متغير حالة الاتصال
    let isOnline = true;

    // ==================== تهيئة التطبيق ====================
    document.addEventListener('DOMContentLoaded', function() {
      initializeApp();
      setupEventListeners();
      updateCurrentTime();
      setInterval(updateCurrentTime, 1000);
    });

    function initializeApp() {
      // تعيين التاريخ الحالي كافتراضي
      const today = new Date().toISOString().split('T')[0];
      document.getElementById('startDate').value = today;

      // تهيئة التحقق من صحة البيانات
      initializeValidation();

      // فحص الاتصال بالخادم
      checkServerConnection();

      // تحديث العرض
      applyFilters();
      updateContractsCount();
      hideForm();

      // تحميل البيانات من الخادم أو محلياً
      loadContractsFromServer();
    }

    function initializeValidation() {
      validator = new JustValidate('#contract-form', {
        errorFieldCssClass: 'border-red-500',
        errorLabelCssClass: 'text-red-500 text-sm mt-1',
        successFieldCssClass: 'border-green-500',
        focusInvalidField: true,
        lockForm: true,
      });

      // قواعد التحقق
      validator
        .addField('#contractNumber', [
          {
            rule: 'required',
            errorMessage: 'رقم العقد مطلوب'
          },
          {
            rule: 'minLength',
            value: 3,
            errorMessage: 'رقم العقد يجب أن يكون 3 أحرف على الأقل'
          },
          {
            rule: 'customRegexp',
            value: /^[0-9]{4}-[0-9]{3}$/,
            errorMessage: 'تنسيق رقم العقد يجب أن يكون: YYYY-XXX'
          }
        ])
        .addField('#supplierName', [
          {
            rule: 'required',
            errorMessage: 'اسم المورد مطلوب'
          },
          {
            rule: 'minLength',
            value: 2,
            errorMessage: 'اسم المورد يجب أن يكون حرفين على الأقل'
          },
          {
            rule: 'maxLength',
            value: 100,
            errorMessage: 'اسم المورد لا يجب أن يتجاوز 100 حرف'
          }
        ])
        .addField('#contractType', [
          {
            rule: 'required',
            errorMessage: 'نوع العقد مطلوب'
          }
        ])
        .addField('#contractItems', [
          {
            rule: 'required',
            errorMessage: 'يجب اختيار عنصر واحد على الأقل'
          },
          {
            validator: (value, fields) => {
              const selectedOptions = Array.from(fields[0].selectedOptions);
              return selectedOptions.length >= 1;
            },
            errorMessage: 'يجب اختيار عنصر واحد على الأقل من القائمة'
          }
        ])
        .addField('#startDate', [
          {
            rule: 'required',
            errorMessage: 'تاريخ البداية مطلوب'
          },
          {
            validator: (value) => {
              const today = new Date();
              const startDate = new Date(value);
              today.setHours(0, 0, 0, 0);
              return startDate >= today;
            },
            errorMessage: 'تاريخ البداية لا يمكن أن يكون في الماضي'
          }
        ])
        .addField('#endDate', [
          {
            rule: 'required',
            errorMessage: 'تاريخ النهاية مطلوب'
          },
          {
            validator: (value, fields) => {
              const startDate = new Date(document.getElementById('startDate').value);
              const endDate = new Date(value);
              return endDate > startDate;
            },
            errorMessage: 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية'
          }
        ])
        .addField('#totalAmount', [
          {
            rule: 'number',
            errorMessage: 'المبلغ يجب أن يكون رقماً'
          },
          {
            rule: 'minNumber',
            value: 0,
            errorMessage: 'المبلغ لا يمكن أن يكون سالباً'
          },
          {
            rule: 'maxNumber',
            value: 999999.99,
            errorMessage: 'المبلغ كبير جداً'
          }
        ])
        .onSuccess(handleValidFormSubmit);
    }

    function addSampleData() {
      const sampleContracts = [
        {
          id: generateId(),
          contractNumber: '2025-001',
          supplierName: 'شركة البهارات الذهبية',
          contractType: 'توريد',
          supplierCategory: 'بهارات',
          startDate: '2025-01-01',
          endDate: '2025-12-31',
          totalAmount: 1200.00,
          contractStatus: 'نشط',
          contractColor: '#10b981',
          paymentTerms: 'دفع شهري مقدم',
          notes: 'عقد توريد البهارات والتوابل الشرقية',
          createdAt: new Date().toISOString()
        },
        {
          id: generateId(),
          contractNumber: '2025-002',
          supplierName: 'مؤسسة المواد الاستهلاكية',
          contractType: 'توريد',
          supplierCategory: 'استهلاكي',
          startDate: '2025-01-15',
          endDate: '2025-06-15',
          totalAmount: 800.00,
          contractStatus: 'نشط',
          contractColor: '#3b82f6',
          paymentTerms: 'دفع عند التسليم',
          notes: 'توريد مواد التنظيف والمستلزمات',
          createdAt: new Date().toISOString()
        },
        {
          id: generateId(),
          contractNumber: '2025-003',
          supplierName: 'شركة الأجبان الطازجة',
          contractType: 'إيجار',
          supplierCategory: 'أجبان',
          startDate: '2025-02-01',
          endDate: '2025-07-31',
          totalAmount: 360.00,
          contractStatus: 'نشط',
          contractColor: '#f59e0b',
          paymentTerms: 'دفع شهري 60 دينار',
          notes: 'إيجار عين واحدة في قسم الأجبان',
          createdAt: new Date().toISOString()
        }
      ];

      contracts = sampleContracts;
      saveContracts();
      showToast('تم إضافة بيانات تجريبية', 'success');
    }

    // ==================== إدارة الأحداث ====================
    function setupEventListeners() {
      // أزرار التحكم الرئيسية
      document.getElementById('btnNewContract').addEventListener('click', showForm);
      document.getElementById('btnLoadDrafts').addEventListener('click', showDraftsModal);
      document.getElementById('btnExportData').addEventListener('click', exportData);
      document.getElementById('btnPrint').addEventListener('click', () => window.print());
      document.getElementById('btnSync').addEventListener('click', syncWithServer);

      // نموذج العقد
      document.getElementById('contract-form').addEventListener('submit', handleFormSubmit);
      document.getElementById('btnSaveLocal').addEventListener('click', saveDraft);
      document.getElementById('btnClearForm').addEventListener('click', clearForm);
      document.getElementById('btnCancelEdit').addEventListener('click', cancelEdit);
      document.getElementById('btnToggleForm').addEventListener('click', hideForm);
      document.getElementById('btnGenerateNumber').addEventListener('click', generateContractNumber);

      // البحث والفلاتر
      document.getElementById('searchInput').addEventListener('input', debounce(applyFilters, 300));
      document.getElementById('filterType').addEventListener('change', applyFilters);
      document.getElementById('filterStatus').addEventListener('change', applyFilters);
      document.getElementById('filterCategory').addEventListener('change', applyFilters);
      document.getElementById('filterStartDate').addEventListener('change', applyFilters);
      document.getElementById('filterEndDate').addEventListener('change', applyFilters);
      document.getElementById('filterMinAmount').addEventListener('input', debounce(applyFilters, 500));
      document.getElementById('filterMaxAmount').addEventListener('input', debounce(applyFilters, 500));
      document.getElementById('btnClearFilters').addEventListener('click', clearFilters);

      // الجدول والترقيم
      document.getElementById('itemsPerPage').addEventListener('change', changeItemsPerPage);
      document.getElementById('btnRefresh').addEventListener('click', () => {
        applyFilters();
        showToast('تم تحديث البيانات', 'success');
      });

      // ترتيب الجدول
      document.querySelectorAll('[data-sort]').forEach(header => {
        header.addEventListener('click', () => sortTable(header.dataset.sort));
      });

      // الترقيم
      document.getElementById('btnFirstPage').addEventListener('click', () => goToPage(1));
      document.getElementById('btnPrevPage').addEventListener('click', () => goToPage(currentPage - 1));
      document.getElementById('btnNextPage').addEventListener('click', () => goToPage(currentPage + 1));
      document.getElementById('btnLastPage').addEventListener('click', () => goToPage(Math.ceil(filteredContracts.length / itemsPerPage)));

      // نافذة المسودات
      document.getElementById('closeDraftsModal').addEventListener('click', hideDraftsModal);
      document.getElementById('btnCloseDrafts').addEventListener('click', hideDraftsModal);
      document.getElementById('btnClearAllDrafts').addEventListener('click', clearAllDrafts);

      // إغلاق Toast
      document.getElementById('toastClose').addEventListener('click', hideToast);

      // إغلاق النوافذ بالضغط على Escape
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
          hideDraftsModal();
          hideToast();
        }
      });
    }

    // ==================== دوال النموذج ====================
    function showForm() {
      document.getElementById('contractForm').classList.remove('hidden');
      document.getElementById('btnNewContract').textContent = '❌ إلغاء';
      document.getElementById('btnNewContract').onclick = hideForm;
      clearForm();
      generateContractNumber();
    }

    function hideForm() {
      document.getElementById('contractForm').classList.add('hidden');
      document.getElementById('btnNewContract').textContent = '➕ عقد جديد';
      document.getElementById('btnNewContract').onclick = showForm;
      clearForm();
      cancelEdit();
    }

    function clearForm() {
      document.getElementById('contract-form').reset();
      const today = new Date().toISOString().split('T')[0];
      document.getElementById('startDate').value = today;
      document.getElementById('contractStatus').value = 'نشط';
      document.getElementById('contractColor').value = '#3b82f6';
      editingContractId = null;
      document.getElementById('btnCancelEdit').classList.add('hidden');
    }

    function generateContractNumber() {
      const year = new Date().getFullYear();
      const nextNumber = contracts.length + 1;
      const contractNumber = `${year}-${nextNumber.toString().padStart(3, '0')}`;
      document.getElementById('contractNumber').value = contractNumber;
    }

    function handleFormSubmit(e) {
      e.preventDefault();
      // التحقق سيتم عبر JustValidate
      // إذا وصل هنا فالبيانات صحيحة
    }

    async function handleValidFormSubmit(fields, groups) {
      try {
        // إظهار مؤشر التحميل
        setFormLoading(true);

        const formData = new FormData(document.getElementById('contract-form'));
        const contractData = {
          id: editingContractId || generateId(),
          contractNumber: formData.get('contractNumber'),
          supplierName: formData.get('supplierName'),
          contractType: formData.get('contractType'),
          supplierCategory: formData.get('supplierCategory'),
          contractItems: Array.from(formData.getAll('contractItems')),
          contractPriority: formData.get('contractPriority'),
          startDate: formData.get('startDate'),
          endDate: formData.get('endDate'),
          totalAmount: parseFloat(formData.get('totalAmount')) || 0,
          contractStatus: formData.get('contractStatus'),
          contractColor: formData.get('contractColor'),
          paymentTerms: formData.get('paymentTerms'),
          notes: formData.get('notes'),
          createdAt: editingContractId ?
            contracts.find(c => c.id === editingContractId)?.createdAt :
            new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // التحقق من تكرار رقم العقد
        if (await isDuplicateContractNumber(contractData.contractNumber, contractData.id)) {
          showToast('رقم العقد موجود مسبقاً', 'error');
          setFormLoading(false);
          return;
        }

        let result;
        if (editingContractId) {
          // تحديث عقد موجود
          result = await updateContractOnServer(contractData);
          if (result.success) {
            const index = contracts.findIndex(c => c.id === editingContractId);
            if (index !== -1) {
              contracts[index] = contractData;
            }
            showToast('تم تحديث العقد بنجاح', 'success');
          }
        } else {
          // إضافة عقد جديد
          result = await createContractOnServer(contractData);
          if (result.success) {
            contracts.push(contractData);
            showToast('تم إضافة العقد بنجاح', 'success');
          }
        }

        if (result.success) {
          saveContracts();
          applyFilters();
          updateContractsCount();
          hideForm();
          updateLastSaved();
        }

      } catch (error) {
        console.error('Error saving contract:', error);
        showToast('حدث خطأ أثناء حفظ العقد', 'error');
      } finally {
        setFormLoading(false);
      }
    }

    // ==================== دوال API ====================
    async function checkServerConnection() {
      try {
        const response = await apiClient.get('/health');
        updateConnectionStatus(true);
        showToast('تم الاتصال بالخادم بنجاح', 'success');
      } catch (error) {
        updateConnectionStatus(false);
        showToast('العمل في الوضع المحلي - لا يوجد اتصال بالخادم', 'warning');
      }
    }

    async function loadContractsFromServer() {
      if (!isOnline) {
        // تحميل من التخزين المحلي
        applyFilters();
        return;
      }

      try {
        setPageLoading(true);
        const response = await apiClient.get('/contracts');

        if (response.data && response.data.contracts) {
          contracts = response.data.contracts;
          saveContracts(); // حفظ نسخة محلية
          applyFilters();
          updateContractsCount();
          showToast('تم تحميل العقود من الخادم', 'success');
        }
      } catch (error) {
        console.error('Error loading contracts:', error);
        // التراجع للتخزين المحلي
        applyFilters();
        showToast('تم تحميل العقود من التخزين المحلي', 'info');
      } finally {
        setPageLoading(false);
      }
    }

    async function createContractOnServer(contractData) {
      if (!isOnline) {
        return { success: true, data: contractData };
      }

      try {
        const response = await apiClient.post('/contracts', contractData);
        return { success: true, data: response.data };
      } catch (error) {
        console.error('Error creating contract:', error);

        // في حالة فشل الإرسال، احفظ محلياً
        showToast('تم حفظ العقد محلياً - سيتم مزامنته لاحقاً', 'warning');
        return { success: true, data: contractData };
      }
    }

    async function updateContractOnServer(contractData) {
      if (!isOnline) {
        return { success: true, data: contractData };
      }

      try {
        const response = await apiClient.put(`/contracts/${contractData.id}`, contractData);
        return { success: true, data: response.data };
      } catch (error) {
        console.error('Error updating contract:', error);

        // في حالة فشل التحديث، احفظ محلياً
        showToast('تم تحديث العقد محلياً - سيتم مزامنته لاحقاً', 'warning');
        return { success: true, data: contractData };
      }
    }

    async function deleteContractOnServer(contractId) {
      if (!isOnline) {
        return { success: true };
      }

      try {
        await apiClient.delete(`/contracts/${contractId}`);
        return { success: true };
      } catch (error) {
        console.error('Error deleting contract:', error);

        // في حالة فشل الحذف، احذف محلياً
        showToast('تم حذف العقد محلياً - سيتم مزامنته لاحقاً', 'warning');
        return { success: true };
      }
    }

    async function isDuplicateContractNumber(contractNumber, excludeId = null) {
      // فحص محلي أولاً
      const localDuplicate = contracts.find(c =>
        c.contractNumber === contractNumber && c.id !== excludeId
      );

      if (localDuplicate) {
        return true;
      }

      // فحص على الخادم إذا كان متاحاً
      if (isOnline) {
        try {
          const response = await apiClient.get(`/contracts/check-number/${contractNumber}`);
          return response.data.exists && response.data.id !== excludeId;
        } catch (error) {
          console.error('Error checking duplicate:', error);
        }
      }

      return false;
    }

    async function syncWithServer() {
      if (!isOnline) {
        showToast('لا يوجد اتصال بالخادم للمزامنة', 'warning');
        return;
      }

      try {
        setPageLoading(true);

        // مزامنة العقود المحلية مع الخادم
        const localContracts = JSON.parse(localStorage.getItem('pendingSync') || '[]');

        for (const contract of localContracts) {
          if (contract.action === 'create') {
            await createContractOnServer(contract.data);
          } else if (contract.action === 'update') {
            await updateContractOnServer(contract.data);
          } else if (contract.action === 'delete') {
            await deleteContractOnServer(contract.id);
          }
        }

        // مسح قائمة المزامنة المعلقة
        localStorage.removeItem('pendingSync');

        // إعادة تحميل البيانات من الخادم
        await loadContractsFromServer();

        showToast('تم مزامنة البيانات بنجاح', 'success');

      } catch (error) {
        console.error('Error syncing:', error);
        showToast('فشل في مزامنة البيانات', 'error');
      } finally {
        setPageLoading(false);
      }
    }

    function validateContract(contract) {
      if (!contract.contractNumber) {
        showToast('رقم العقد مطلوب', 'error');
        return false;
      }

      if (!contract.supplierName) {
        showToast('اسم المورد مطلوب', 'error');
        return false;
      }

      if (!contract.contractType) {
        showToast('نوع العقد مطلوب', 'error');
        return false;
      }

      if (!contract.startDate) {
        showToast('تاريخ البداية مطلوب', 'error');
        return false;
      }

      if (!contract.endDate) {
        showToast('تاريخ النهاية مطلوب', 'error');
        return false;
      }

      if (new Date(contract.startDate) >= new Date(contract.endDate)) {
        showToast('تاريخ النهاية يجب أن يكون بعد تاريخ البداية', 'error');
        return false;
      }

      // التحقق من عدم تكرار رقم العقد
      const existingContract = contracts.find(c =>
        c.contractNumber === contract.contractNumber && c.id !== contract.id
      );
      if (existingContract) {
        showToast('رقم العقد موجود مسبقاً', 'error');
        return false;
      }

      return true;
    }

    function editContract(id) {
      const contract = contracts.find(c => c.id === id);
      if (!contract) {
        showToast('العقد غير موجود', 'error');
        return;
      }

      editingContractId = id;

      // ملء النموذج بالبيانات
      document.getElementById('contractNumber').value = contract.contractNumber;
      document.getElementById('supplierName').value = contract.supplierName;
      document.getElementById('contractType').value = contract.contractType;
      document.getElementById('supplierCategory').value = contract.supplierCategory || '';
      document.getElementById('startDate').value = contract.startDate;
      document.getElementById('endDate').value = contract.endDate;
      document.getElementById('totalAmount').value = contract.totalAmount || '';
      document.getElementById('contractStatus').value = contract.contractStatus;
      document.getElementById('contractColor').value = contract.contractColor || '#3b82f6';
      document.getElementById('paymentTerms').value = contract.paymentTerms || '';
      document.getElementById('notes').value = contract.notes || '';

      document.getElementById('btnCancelEdit').classList.remove('hidden');
      showForm();

      showToast('تم تحميل بيانات العقد للتعديل', 'success');
    }

    function cancelEdit() {
      editingContractId = null;
      document.getElementById('btnCancelEdit').classList.add('hidden');
    }

    async function deleteContract(id) {
      if (!confirm('هل أنت متأكد من حذف هذا العقد؟\nلا يمكن التراجع عن هذا الإجراء.')) {
        return;
      }

      try {
        setPageLoading(true);

        const result = await deleteContractOnServer(id);

        if (result.success) {
          const index = contracts.findIndex(c => c.id === id);
          if (index !== -1) {
            contracts.splice(index, 1);
            saveContracts();
            applyFilters();
            updateContractsCount();
            showToast('تم حذف العقد بنجاح', 'success');
            updateLastSaved();
          }
        }
      } catch (error) {
        console.error('Error deleting contract:', error);
        showToast('حدث خطأ أثناء حذف العقد', 'error');
      } finally {
        setPageLoading(false);
      }
    }

    // ==================== دوال واجهة المستخدم ====================
    function setFormLoading(loading) {
      const form = document.getElementById('contract-form');
      const submitBtn = document.getElementById('btnSaveFinal');
      const saveLocalBtn = document.getElementById('btnSaveLocal');

      if (loading) {
        form.classList.add('loading');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '⏳ جاري الحفظ...';
        saveLocalBtn.disabled = true;
      } else {
        form.classList.remove('loading');
        submitBtn.disabled = false;
        submitBtn.innerHTML = '✅ حفظ نهائي';
        saveLocalBtn.disabled = false;
      }
    }

    function setPageLoading(loading) {
      const refreshBtn = document.getElementById('btnRefresh');
      const tableBody = document.getElementById('contractsTableBody');

      if (loading) {
        refreshBtn.innerHTML = '⏳ جاري التحميل...';
        refreshBtn.disabled = true;
        tableBody.style.opacity = '0.5';
      } else {
        refreshBtn.innerHTML = '🔄 تحديث';
        refreshBtn.disabled = false;
        tableBody.style.opacity = '1';
      }
    }

    function updateConnectionStatus(online) {
      isOnline = online;
      const statusElement = document.getElementById('connectionStatus');

      if (online) {
        statusElement.innerHTML = `
          <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
          <span class="text-green-600">متصل</span>
        `;
      } else {
        statusElement.innerHTML = `
          <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
          <span class="text-red-600">غير متصل</span>
        `;
      }
    }

    // ==================== تحسين التحقق من صحة البيانات ====================
    function validateContract(contract) {
      const errors = [];

      // التحقق من الحقول المطلوبة
      if (!contract.contractNumber?.trim()) {
        errors.push('رقم العقد مطلوب');
      }

      if (!contract.supplierName?.trim()) {
        errors.push('اسم المورد مطلوب');
      }

      if (!contract.contractType?.trim()) {
        errors.push('نوع العقد مطلوب');
      }

      if (!contract.contractItems || contract.contractItems.length === 0) {
        errors.push('يجب اختيار عنصر واحد على الأقل');
      }

      if (!contract.startDate) {
        errors.push('تاريخ البداية مطلوب');
      }

      if (!contract.endDate) {
        errors.push('تاريخ النهاية مطلوب');
      }

      // التحقق من التواريخ
      if (contract.startDate && contract.endDate) {
        const startDate = new Date(contract.startDate);
        const endDate = new Date(contract.endDate);

        if (startDate >= endDate) {
          errors.push('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
        }

        // التحقق من أن تاريخ البداية ليس في الماضي (للعقود الجديدة)
        if (!editingContractId) {
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          if (startDate < today) {
            errors.push('تاريخ البداية لا يمكن أن يكون في الماضي');
          }
        }
      }

      // التحقق من المبلغ
      if (contract.totalAmount < 0) {
        errors.push('المبلغ لا يمكن أن يكون سالباً');
      }

      if (contract.totalAmount > 999999.99) {
        errors.push('المبلغ كبير جداً');
      }

      // التحقق من تنسيق رقم العقد
      const contractNumberPattern = /^[0-9]{4}-[0-9]{3}$/;
      if (contract.contractNumber && !contractNumberPattern.test(contract.contractNumber)) {
        errors.push('تنسيق رقم العقد يجب أن يكون: YYYY-XXX');
      }

      // عرض الأخطاء
      if (errors.length > 0) {
        showToast(`أخطاء في البيانات:\n${errors.join('\n')}`, 'error');
        return false;
      }

      return true;
    }

    // ==================== دوال البحث والفلترة ====================
    function applyFilters() {
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();
      const typeFilter = document.getElementById('filterType').value;
      const statusFilter = document.getElementById('filterStatus').value;
      const categoryFilter = document.getElementById('filterCategory').value;
      const startDateFilter = document.getElementById('filterStartDate').value;
      const endDateFilter = document.getElementById('filterEndDate').value;
      const minAmountFilter = parseFloat(document.getElementById('filterMinAmount').value) || 0;
      const maxAmountFilter = parseFloat(document.getElementById('filterMaxAmount').value) || Infinity;

      filteredContracts = contracts.filter(contract => {
        // البحث النصي
        const matchesSearch = !searchTerm ||
          contract.contractNumber.toLowerCase().includes(searchTerm) ||
          contract.supplierName.toLowerCase().includes(searchTerm) ||
          contract.contractType.toLowerCase().includes(searchTerm) ||
          (contract.notes && contract.notes.toLowerCase().includes(searchTerm));

        // فلتر النوع
        const matchesType = !typeFilter || contract.contractType === typeFilter;

        // فلتر الحالة
        const matchesStatus = !statusFilter || contract.contractStatus === statusFilter;

        // فلتر القسم
        const matchesCategory = !categoryFilter || contract.supplierCategory === categoryFilter;

        // فلتر التاريخ
        const matchesStartDate = !startDateFilter || contract.startDate >= startDateFilter;
        const matchesEndDate = !endDateFilter || contract.endDate <= endDateFilter;

        // فلتر المبلغ
        const matchesAmount = contract.totalAmount >= minAmountFilter && contract.totalAmount <= maxAmountFilter;

        return matchesSearch && matchesType && matchesStatus && matchesCategory &&
               matchesStartDate && matchesEndDate && matchesAmount;
      });

      // ترتيب النتائج
      sortContracts();

      // إعادة تعيين الصفحة الحالية
      currentPage = 1;

      // تحديث العرض
      renderTable();
      renderPagination();
      updateResultsInfo();
      updateActiveFilters();
    }

    function clearFilters() {
      document.getElementById('searchInput').value = '';
      document.getElementById('filterType').value = '';
      document.getElementById('filterStatus').value = '';
      document.getElementById('filterCategory').value = '';
      document.getElementById('filterStartDate').value = '';
      document.getElementById('filterEndDate').value = '';
      document.getElementById('filterMinAmount').value = '';
      document.getElementById('filterMaxAmount').value = '';

      applyFilters();
      showToast('تم مسح جميع الفلاتر', 'success');
    }

    function updateActiveFilters() {
      const activeFilters = [];

      const typeFilter = document.getElementById('filterType').value;
      const statusFilter = document.getElementById('filterStatus').value;
      const categoryFilter = document.getElementById('filterCategory').value;
      const startDateFilter = document.getElementById('filterStartDate').value;
      const endDateFilter = document.getElementById('filterEndDate').value;
      const minAmountFilter = document.getElementById('filterMinAmount').value;
      const maxAmountFilter = document.getElementById('filterMaxAmount').value;

      if (typeFilter) activeFilters.push(`النوع: ${typeFilter}`);
      if (statusFilter) activeFilters.push(`الحالة: ${statusFilter}`);
      if (categoryFilter) activeFilters.push(`القسم: ${categoryFilter}`);
      if (startDateFilter) activeFilters.push(`من: ${startDateFilter}`);
      if (endDateFilter) activeFilters.push(`إلى: ${endDateFilter}`);
      if (minAmountFilter) activeFilters.push(`المبلغ ≥ ${minAmountFilter}`);
      if (maxAmountFilter) activeFilters.push(`المبلغ ≤ ${maxAmountFilter}`);

      const activeFiltersElement = document.getElementById('activeFilters');
      if (activeFilters.length > 0) {
        activeFiltersElement.textContent = `الفلاتر النشطة: ${activeFilters.join(' | ')}`;
      } else {
        activeFiltersElement.textContent = '';
      }
    }

    function updateResultsInfo() {
      const total = filteredContracts.length;
      const start = (currentPage - 1) * itemsPerPage + 1;
      const end = Math.min(currentPage * itemsPerPage, total);

      document.getElementById('resultsInfo').textContent =
        total > 0 ? `عرض ${start}-${end} من ${total} عقد` : 'لا توجد نتائج';
    }

    // ==================== دوال الترتيب ====================
    function sortTable(field) {
      if (sortField === field) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        sortField = field;
        sortDirection = 'asc';
      }

      sortContracts();
      renderTable();
      updateSortIndicators();
    }

    function sortContracts() {
      filteredContracts.sort((a, b) => {
        let aValue = a[sortField];
        let bValue = b[sortField];

        // معالجة التواريخ
        if (sortField.includes('Date')) {
          aValue = new Date(aValue);
          bValue = new Date(bValue);
        }

        // معالجة الأرقام
        if (sortField === 'totalAmount') {
          aValue = parseFloat(aValue) || 0;
          bValue = parseFloat(bValue) || 0;
        }

        // معالجة النصوص
        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
      });
    }

    function updateSortIndicators() {
      document.querySelectorAll('[data-sort]').forEach(header => {
        const field = header.dataset.sort;
        const indicator = field === sortField ?
          (sortDirection === 'asc' ? ' ↑' : ' ↓') : ' ↕️';

        const text = header.textContent.replace(/ [↑↓↕️]/, '');
        header.textContent = text + indicator;
      });
    }

    // ==================== دوال عرض الجدول ====================
    function renderTable() {
      const tbody = document.getElementById('contractsTableBody');
      const noDataMessage = document.getElementById('noDataMessage');

      if (filteredContracts.length === 0) {
        tbody.innerHTML = '';
        noDataMessage.classList.remove('hidden');
        return;
      }

      noDataMessage.classList.add('hidden');

      const start = (currentPage - 1) * itemsPerPage;
      const end = start + itemsPerPage;
      const pageContracts = filteredContracts.slice(start, end);

      tbody.innerHTML = pageContracts.map(contract => `
        <tr class="hover:bg-gray-50 transition-colors">
          <td class="px-4 py-3 text-sm">
            <div class="flex items-center">
              <div class="w-3 h-3 rounded-full mr-2" style="background-color: ${contract.contractColor}"></div>
              <span class="font-medium">${contract.contractNumber}</span>
            </div>
          </td>
          <td class="px-4 py-3 text-sm">
            <div>
              <div class="font-medium text-gray-900">${contract.supplierName}</div>
              ${contract.supplierCategory ? `<div class="text-xs text-gray-500">${getEmojiForCategory(contract.supplierCategory)} ${contract.supplierCategory}</div>` : ''}
              ${contract.contractItems && contract.contractItems.length > 0 ?
                `<div class="text-xs text-blue-600 mt-1">${contract.contractItems.length} عنصر</div>` : ''}
            </div>
          </td>
          <td class="px-4 py-3 text-sm">
            <div class="space-y-1">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeClass(contract.contractType)}">
                ${getEmojiForType(contract.contractType)} ${contract.contractType}
              </span>
              ${contract.contractPriority ?
                `<div class="inline-flex items-center px-2 py-0.5 rounded text-xs ${getPriorityClass(contract.contractPriority)}">
                  ${getEmojiForPriority(contract.contractPriority)} ${contract.contractPriority}
                </div>` : ''}
            </div>
          </td>
          <td class="px-4 py-3 text-sm text-gray-900">
            ${formatDate(contract.startDate)}
          </td>
          <td class="px-4 py-3 text-sm text-gray-900">
            <div class="font-medium">${contract.totalAmount.toFixed(2)} د.ك</div>
            ${contract.paymentTerms ? `<div class="text-xs text-gray-500">${contract.paymentTerms}</div>` : ''}
          </td>
          <td class="px-4 py-3 text-sm">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusClass(contract.contractStatus)}">
              ${getEmojiForStatus(contract.contractStatus)} ${contract.contractStatus}
            </span>
          </td>
          <td class="px-4 py-3 text-sm">
            <div class="flex items-center space-x-2">
              <button onclick="editContract('${contract.id}')"
                      class="text-blue-600 hover:text-blue-800 transition-colors"
                      title="تعديل">
                ✏️
              </button>
              <button onclick="viewContract('${contract.id}')"
                      class="text-green-600 hover:text-green-800 transition-colors"
                      title="عرض">
                👁️
              </button>
              <button onclick="deleteContract('${contract.id}')"
                      class="text-red-600 hover:text-red-800 transition-colors"
                      title="حذف">
                🗑️
              </button>
            </div>
          </td>
        </tr>
      `).join('');
    }

    function getTypeClass(type) {
      const classes = {
        'توريد': 'bg-blue-100 text-blue-800',
        'إيجار': 'bg-green-100 text-green-800',
        'خدمات': 'bg-purple-100 text-purple-800',
        'صيانة': 'bg-orange-100 text-orange-800'
      };
      return classes[type] || 'bg-gray-100 text-gray-800';
    }

    function getStatusClass(status) {
      const classes = {
        'نشط': 'bg-green-100 text-green-800',
        'منتهي': 'bg-gray-100 text-gray-800',
        'ملغي': 'bg-red-100 text-red-800',
        'معلق': 'bg-yellow-100 text-yellow-800'
      };
      return classes[status] || 'bg-gray-100 text-gray-800';
    }

    function getEmojiForType(type) {
      const emojis = {
        'توريد': '📦',
        'إيجار': '🏠',
        'خدمات': '🔧',
        'صيانة': '⚙️'
      };
      return emojis[type] || '📄';
    }

    function getEmojiForStatus(status) {
      const emojis = {
        'نشط': '✅',
        'منتهي': '⏰',
        'ملغي': '❌',
        'معلق': '⏸️'
      };
      return emojis[status] || '❓';
    }

    function getEmojiForCategory(category) {
      const emojis = {
        'بهارات': '🌶️',
        'استهلاكي': '🧽',
        'أجبان': '🧀'
      };
      return emojis[category] || '📦';
    }

    function getPriorityClass(priority) {
      const classes = {
        'منخفضة': 'bg-green-100 text-green-800',
        'متوسطة': 'bg-yellow-100 text-yellow-800',
        'عالية': 'bg-orange-100 text-orange-800',
        'عاجلة': 'bg-red-100 text-red-800'
      };
      return classes[priority] || 'bg-gray-100 text-gray-800';
    }

    function getEmojiForPriority(priority) {
      const emojis = {
        'منخفضة': '🟢',
        'متوسطة': '🟡',
        'عالية': '🟠',
        'عاجلة': '🔴'
      };
      return emojis[priority] || '⚪';
    }

    function viewContract(id) {
      const contract = contracts.find(c => c.id === id);
      if (!contract) {
        showToast('العقد غير موجود', 'error');
        return;
      }

      const contractItems = contract.contractItems && contract.contractItems.length > 0
        ? contract.contractItems.join('، ')
        : 'غير محدد';

      const details = `
        📋 تفاصيل العقد
        ═══════════════════

        🔢 رقم العقد: ${contract.contractNumber}
        🏢 المورد: ${contract.supplierName}
        📦 النوع: ${contract.contractType}
        📂 القسم: ${contract.supplierCategory || 'غير محدد'}
        🎯 الأولوية: ${contract.contractPriority || 'غير محدد'}

        📋 عناصر العقد:
        ${contractItems}

        📅 تاريخ البداية: ${formatDate(contract.startDate)}
        📅 تاريخ النهاية: ${formatDate(contract.endDate)}
        💰 المبلغ الإجمالي: ${contract.totalAmount.toFixed(2)} د.ك

        📊 الحالة: ${contract.contractStatus}
        💳 شروط الدفع: ${contract.paymentTerms || 'غير محدد'}

        📝 الملاحظات:
        ${contract.notes || 'لا توجد ملاحظات'}

        ⏰ تاريخ الإنشاء: ${formatDateTime(contract.createdAt)}
        ${contract.updatedAt ? `🔄 آخر تحديث: ${formatDateTime(contract.updatedAt)}` : ''}
      `;

      alert(details);
    }

    // ==================== دوال الترقيم ====================
    function renderPagination() {
      const totalPages = Math.ceil(filteredContracts.length / itemsPerPage);

      // تحديث معلومات الصفحة
      const start = (currentPage - 1) * itemsPerPage + 1;
      const end = Math.min(currentPage * itemsPerPage, filteredContracts.length);
      document.getElementById('pageInfo').textContent =
        filteredContracts.length > 0 ? `${start}-${end} من ${filteredContracts.length}` : '0-0 من 0';

      // تحديث أزرار التنقل
      document.getElementById('btnFirstPage').disabled = currentPage === 1;
      document.getElementById('btnPrevPage').disabled = currentPage === 1;
      document.getElementById('btnNextPage').disabled = currentPage === totalPages || totalPages === 0;
      document.getElementById('btnLastPage').disabled = currentPage === totalPages || totalPages === 0;

      // إنشاء أرقام الصفحات
      const pageNumbers = document.getElementById('pageNumbers');
      pageNumbers.innerHTML = '';

      if (totalPages <= 7) {
        // عرض جميع الصفحات
        for (let i = 1; i <= totalPages; i++) {
          pageNumbers.appendChild(createPageButton(i));
        }
      } else {
        // عرض مختصر للصفحات
        pageNumbers.appendChild(createPageButton(1));

        if (currentPage > 4) {
          pageNumbers.appendChild(createEllipsis());
        }

        const start = Math.max(2, currentPage - 2);
        const end = Math.min(totalPages - 1, currentPage + 2);

        for (let i = start; i <= end; i++) {
          pageNumbers.appendChild(createPageButton(i));
        }

        if (currentPage < totalPages - 3) {
          pageNumbers.appendChild(createEllipsis());
        }

        if (totalPages > 1) {
          pageNumbers.appendChild(createPageButton(totalPages));
        }
      }
    }

    function createPageButton(pageNum) {
      const button = document.createElement('button');
      button.textContent = pageNum;
      button.className = `pagination-btn px-3 py-1 text-sm border rounded ${
        pageNum === currentPage
          ? 'bg-blue-600 text-white border-blue-600'
          : 'border-gray-300 hover:bg-gray-50'
      }`;
      button.onclick = () => goToPage(pageNum);
      return button;
    }

    function createEllipsis() {
      const span = document.createElement('span');
      span.textContent = '...';
      span.className = 'px-2 py-1 text-sm text-gray-500';
      return span;
    }

    function goToPage(page) {
      const totalPages = Math.ceil(filteredContracts.length / itemsPerPage);
      if (page >= 1 && page <= totalPages) {
        currentPage = page;
        renderTable();
        renderPagination();
      }
    }

    function changeItemsPerPage() {
      itemsPerPage = parseInt(document.getElementById('itemsPerPage').value);
      currentPage = 1;
      renderTable();
      renderPagination();
    }

    // ==================== دوال المسودات ====================
    function saveDraft() {
      const formData = new FormData(document.getElementById('contract-form'));
      const draftData = {
        id: generateId(),
        contractNumber: formData.get('contractNumber'),
        supplierName: formData.get('supplierName'),
        contractType: formData.get('contractType'),
        supplierCategory: formData.get('supplierCategory'),
        contractItems: Array.from(formData.getAll('contractItems')),
        contractPriority: formData.get('contractPriority'),
        startDate: formData.get('startDate'),
        endDate: formData.get('endDate'),
        totalAmount: formData.get('totalAmount'),
        contractStatus: formData.get('contractStatus'),
        contractColor: formData.get('contractColor'),
        paymentTerms: formData.get('paymentTerms'),
        notes: formData.get('notes'),
        savedAt: new Date().toISOString()
      };

      drafts.push(draftData);
      saveDrafts();
      showToast('تم حفظ المسودة بنجاح', 'success');
    }

    function showDraftsModal() {
      document.getElementById('draftsModal').classList.remove('hidden');
      renderDraftsList();
    }

    function hideDraftsModal() {
      document.getElementById('draftsModal').classList.add('hidden');
    }

    function renderDraftsList() {
      const draftsList = document.getElementById('draftsList');
      const noDraftsMessage = document.getElementById('noDraftsMessage');

      if (drafts.length === 0) {
        draftsList.innerHTML = '';
        noDraftsMessage.classList.remove('hidden');
        return;
      }

      noDraftsMessage.classList.add('hidden');

      draftsList.innerHTML = drafts.map(draft => `
        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h4 class="font-medium text-gray-900">
                ${draft.contractNumber || 'رقم غير محدد'} - ${draft.supplierName || 'مورد غير محدد'}
              </h4>
              <p class="text-sm text-gray-600 mt-1">
                ${draft.contractType || 'نوع غير محدد'} | ${draft.totalAmount ? `${draft.totalAmount} د.ك` : 'مبلغ غير محدد'}
              </p>
              <p class="text-xs text-gray-500 mt-2">
                تم الحفظ: ${formatDateTime(draft.savedAt)}
              </p>
            </div>
            <div class="flex items-center space-x-2">
              <button onclick="loadDraft('${draft.id}')"
                      class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors">
                تحميل
              </button>
              <button onclick="deleteDraft('${draft.id}')"
                      class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors">
                حذف
              </button>
            </div>
          </div>
        </div>
      `).join('');
    }

    function loadDraft(id) {
      const draft = drafts.find(d => d.id === id);
      if (!draft) {
        showToast('المسودة غير موجودة', 'error');
        return;
      }

      // ملء النموذج بالبيانات
      document.getElementById('contractNumber').value = draft.contractNumber || '';
      document.getElementById('supplierName').value = draft.supplierName || '';
      document.getElementById('contractType').value = draft.contractType || '';
      document.getElementById('supplierCategory').value = draft.supplierCategory || '';
      document.getElementById('startDate').value = draft.startDate || '';
      document.getElementById('endDate').value = draft.endDate || '';
      document.getElementById('totalAmount').value = draft.totalAmount || '';
      document.getElementById('contractStatus').value = draft.contractStatus || 'نشط';
      document.getElementById('contractColor').value = draft.contractColor || '#3b82f6';
      document.getElementById('paymentTerms').value = draft.paymentTerms || '';
      document.getElementById('notes').value = draft.notes || '';
      document.getElementById('contractPriority').value = draft.contractPriority || 'متوسطة';

      // تحديد العناصر المختارة
      const contractItemsSelect = document.getElementById('contractItems');
      if (draft.contractItems && Array.isArray(draft.contractItems)) {
        Array.from(contractItemsSelect.options).forEach(option => {
          option.selected = draft.contractItems.includes(option.value);
        });
      }

      hideDraftsModal();
      showForm();
      showToast('تم تحميل المسودة بنجاح', 'success');
    }

    function deleteDraft(id) {
      if (!confirm('هل أنت متأكد من حذف هذه المسودة؟')) {
        return;
      }

      const index = drafts.findIndex(d => d.id === id);
      if (index !== -1) {
        drafts.splice(index, 1);
        saveDrafts();
        renderDraftsList();
        showToast('تم حذف المسودة بنجاح', 'success');
      }
    }

    function clearAllDrafts() {
      if (!confirm('هل أنت متأكد من حذف جميع المسودات؟\nلا يمكن التراجع عن هذا الإجراء.')) {
        return;
      }

      drafts = [];
      saveDrafts();
      renderDraftsList();
      showToast('تم حذف جميع المسودات', 'success');
    }

    // ==================== دوال مساعدة ====================
    function generateId() {
      return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    function formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-GB');
    }

    function formatDateTime(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString('en-GB');
    }

    function updateCurrentTime() {
      const now = new Date();
      document.getElementById('currentTime').textContent = now.toLocaleString('en-GB');
    }

    function updateContractsCount() {
      document.getElementById('contractsCount').textContent = `${contracts.length} عقد`;
    }

    function updateLastSaved() {
      const now = new Date();
      document.getElementById('lastSaved').textContent = `آخر حفظ: ${now.toLocaleTimeString('en-GB')}`;
    }

    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

    // ==================== دوال التخزين ====================
    function saveContracts() {
      localStorage.setItem('contracts', JSON.stringify(contracts));
    }

    function saveDrafts() {
      localStorage.setItem('drafts', JSON.stringify(drafts));
    }

    // ==================== دوال التصدير ====================
    function exportData() {
      const dataToExport = {
        contracts: contracts,
        exportDate: new Date().toISOString(),
        totalContracts: contracts.length,
        activeContracts: contracts.filter(c => c.contractStatus === 'نشط').length
      };

      const dataStr = JSON.stringify(dataToExport, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      const link = document.createElement('a');
      link.href = URL.createObjectURL(dataBlob);
      link.download = `contracts_export_${new Date().toISOString().split('T')[0]}.json`;
      link.click();

      showToast('تم تصدير البيانات بنجاح', 'success');
    }

    // ==================== دوال الإشعارات ====================
    function showToast(message, type = 'success') {
      const toast = document.getElementById('toast');
      const toastMessage = document.getElementById('toastMessage');
      const toastIcon = document.getElementById('toastIcon');

      // تعيين الرسالة
      toastMessage.textContent = message;

      // تعيين الأيقونة والنوع
      const icons = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
      };

      toastIcon.textContent = icons[type] || icons.info;

      // تعيين الألوان
      toast.className = `fixed top-5 left-5 p-4 rounded-lg shadow-lg z-50 max-w-sm toast-${type}`;

      // عرض الإشعار
      toast.classList.remove('hidden');

      // إخفاء الإشعار تلقائياً بعد 3 ثوان
      setTimeout(() => {
        hideToast();
      }, 3000);
    }

    function hideToast() {
      document.getElementById('toast').classList.add('hidden');
    }

    // ==================== تهيئة الطباعة ====================
    window.addEventListener('beforeprint', function() {
      // إخفاء العناصر غير المرغوب فيها في الطباعة
      document.querySelectorAll('.no-print, button, .pagination-btn').forEach(el => {
        el.style.display = 'none';
      });
    });

    window.addEventListener('afterprint', function() {
      // إعادة إظهار العناصر بعد الطباعة
      document.querySelectorAll('.no-print, button, .pagination-btn').forEach(el => {
        el.style.display = '';
      });
    });
  </script>

</body>
</html>
