/**
 * الملف الرئيسي لـ JavaScript
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 */

// ==================== المتغيرات العامة ====================
let currentUser = null;
let isOnline = navigator.onLine;
let contracts = [];
let suppliers = [];
let contractTypes = [];
let supplierCategories = [];

// إعدادات التطبيق
const APP_CONFIG = {
  API_BASE_URL: '/api/v1',
  VERSION: '1.0.0',
  COMPANY_NAME: 'جمعية المنقف التعاونية',
  ITEMS_PER_PAGE: 10,
  AUTO_SAVE_INTERVAL: 30000, // 30 ثانية
  CONNECTION_CHECK_INTERVAL: 10000, // 10 ثواني
  TOAST_DURATION: 4000 // 4 ثواني
};

// ==================== تهيئة التطبيق ====================
document.addEventListener('DOMContentLoaded', function() {
  initializeApp();
});

async function initializeApp() {
  try {
    // إظهار شاشة التحميل
    showLoadingScreen();
    
    // تهيئة المكونات الأساسية
    await initializeComponents();
    
    // فحص حالة المصادقة
    await checkAuthStatus();
    
    // تحميل البيانات الأساسية
    await loadInitialData();
    
    // إعداد مستمعي الأحداث
    setupEventListeners();
    
    // بدء المراقبة
    startMonitoring();
    
    // إخفاء شاشة التحميل وإظهار التطبيق
    hideLoadingScreen();
    
    console.log('تم تهيئة التطبيق بنجاح');
    
  } catch (error) {
    console.error('خطأ في تهيئة التطبيق:', error);
    showErrorMessage('حدث خطأ في تحميل التطبيق. يرجى إعادة تحميل الصفحة.');
  }
}

async function initializeComponents() {
  // تهيئة مكونات الواجهة
  initializeUI();
  
  // تهيئة منتقي التواريخ
  initializeDatePickers();
  
  // تهيئة الرسوم البيانية
  initializeCharts();
  
  // تهيئة التخزين المحلي
  initializeLocalStorage();
}

function initializeUI() {
  // تحديث الوقت الحالي
  updateCurrentTime();
  setInterval(updateCurrentTime, 1000);
  
  // إعداد مؤشر حالة الاتصال
  updateConnectionStatus();
  
  // إعداد القوائم المنسدلة
  setupDropdowns();
  
  // إعداد النوافذ المنبثقة
  setupModals();
}

function initializeDatePickers() {
  // إعداد Flatpickr للتواريخ العربية
  const flatpickrConfig = {
    locale: 'ar',
    dateFormat: 'Y-m-d',
    allowInput: true,
    clickOpens: true,
    position: 'auto'
  };
  
  // تطبيق الإعداد على جميع حقول التاريخ
  document.querySelectorAll('.date-picker').forEach(input => {
    flatpickr(input, flatpickrConfig);
  });
}

function initializeCharts() {
  // سيتم تهيئة الرسوم البيانية عند الحاجة
  console.log('تم تهيئة مكونات الرسوم البيانية');
}

function initializeLocalStorage() {
  // التحقق من دعم التخزين المحلي
  if (!localStorage) {
    console.warn('التخزين المحلي غير مدعوم');
    return;
  }
  
  // تحميل البيانات المحفوظة محلياً
  const savedContracts = localStorage.getItem('contracts');
  if (savedContracts) {
    try {
      contracts = JSON.parse(savedContracts);
    } catch (error) {
      console.error('خطأ في تحليل البيانات المحفوظة:', error);
    }
  }
}

// ==================== إدارة المصادقة ====================
async function checkAuthStatus() {
  try {
    const response = await API.get('/auth/me');
    
    if (response.success) {
      currentUser = response.data;
      updateUserInterface(true);
      console.log('المستخدم مسجل الدخول:', currentUser.full_name);
    } else {
      updateUserInterface(false);
    }
  } catch (error) {
    console.log('المستخدم غير مسجل الدخول');
    updateUserInterface(false);
  }
}

function updateUserInterface(isLoggedIn) {
  const loginButton = document.getElementById('loginButton');
  const userMenu = document.getElementById('userMenu');
  const userName = document.getElementById('userName');
  
  if (isLoggedIn && currentUser) {
    // إخفاء زر تسجيل الدخول
    loginButton.classList.add('hidden');
    
    // إظهار قائمة المستخدم
    userMenu.classList.remove('hidden');
    userName.textContent = currentUser.full_name;
    
    // تحديث الصلاحيات
    updatePermissions();
    
  } else {
    // إظهار زر تسجيل الدخول
    loginButton.classList.remove('hidden');
    
    // إخفاء قائمة المستخدم
    userMenu.classList.add('hidden');
    userName.textContent = 'ضيف';
  }
}

function updatePermissions() {
  if (!currentUser) return;
  
  // إظهار/إخفاء العناصر حسب الصلاحيات
  const adminElements = document.querySelectorAll('.admin-only');
  const managerElements = document.querySelectorAll('.manager-only');
  
  if (currentUser.role === 'admin') {
    adminElements.forEach(el => el.classList.remove('hidden'));
    managerElements.forEach(el => el.classList.remove('hidden'));
  } else if (currentUser.role === 'manager') {
    adminElements.forEach(el => el.classList.add('hidden'));
    managerElements.forEach(el => el.classList.remove('hidden'));
  } else {
    adminElements.forEach(el => el.classList.add('hidden'));
    managerElements.forEach(el => el.classList.add('hidden'));
  }
}

// ==================== تحميل البيانات ====================
async function loadInitialData() {
  try {
    // تحميل الإحصائيات السريعة
    await loadQuickStats();
    
    // تحميل أنواع العقود
    await loadContractTypes();
    
    // تحميل فئات الموردين
    await loadSupplierCategories();
    
  } catch (error) {
    console.error('خطأ في تحميل البيانات الأولية:', error);
  }
}

async function loadQuickStats() {
  try {
    const response = await API.get('/contracts/statistics');
    
    if (response.success) {
      const stats = response.data.overview;
      
      // تحديث الإحصائيات في الواجهة
      updateElement('totalContracts', stats.total_contracts || 0);
      updateElement('activeContracts', stats.active_contracts || 0);
      updateElement('totalSuppliers', stats.active_suppliers || 0);
      updateElement('totalValue', formatCurrency(stats.total_active_value || 0));
      
      // إظهار قسم الإحصائيات
      document.getElementById('quickStats').classList.remove('hidden');
    }
  } catch (error) {
    console.error('خطأ في تحميل الإحصائيات:', error);
  }
}

async function loadContractTypes() {
  try {
    const response = await API.get('/contract-types');
    
    if (response.success) {
      contractTypes = response.data;
      console.log('تم تحميل أنواع العقود:', contractTypes.length);
    }
  } catch (error) {
    console.error('خطأ في تحميل أنواع العقود:', error);
  }
}

async function loadSupplierCategories() {
  try {
    const response = await API.get('/supplier-categories');
    
    if (response.success) {
      supplierCategories = response.data;
      console.log('تم تحميل فئات الموردين:', supplierCategories.length);
    }
  } catch (error) {
    console.error('خطأ في تحميل فئات الموردين:', error);
  }
}

// ==================== إعداد مستمعي الأحداث ====================
function setupEventListeners() {
  // أحداث المصادقة
  setupAuthEvents();
  
  // أحداث التنقل
  setupNavigationEvents();
  
  // أحداث النوافذ المنبثقة
  setupModalEvents();
  
  // أحداث الشبكة
  setupNetworkEvents();
  
  // أحداث لوحة المفاتيح
  setupKeyboardEvents();
}

function setupAuthEvents() {
  // زر تسجيل الدخول
  const loginButton = document.getElementById('loginButton');
  if (loginButton) {
    loginButton.addEventListener('click', showLoginModal);
  }
  
  // زر تسجيل الخروج
  const logoutButton = document.getElementById('logoutButton');
  if (logoutButton) {
    logoutButton.addEventListener('click', handleLogout);
  }
  
  // قائمة المستخدم
  const userMenuButton = document.getElementById('userMenuButton');
  const userDropdown = document.getElementById('userDropdown');
  
  if (userMenuButton && userDropdown) {
    userMenuButton.addEventListener('click', (e) => {
      e.stopPropagation();
      userDropdown.classList.toggle('hidden');
    });
    
    // إغلاق القائمة عند النقر خارجها
    document.addEventListener('click', () => {
      userDropdown.classList.add('hidden');
    });
  }
}

function setupNavigationEvents() {
  // أزرار البدء السريع
  const quickStartButton = document.getElementById('quickStartButton');
  if (quickStartButton) {
    quickStartButton.addEventListener('click', handleQuickStart);
  }
  
  // زر العرض التوضيحي
  const viewDemoButton = document.getElementById('viewDemoButton');
  if (viewDemoButton) {
    viewDemoButton.addEventListener('click', showDemo);
  }
  
  // زر تحميل التطبيق
  const downloadAppButton = document.getElementById('downloadAppButton');
  if (downloadAppButton) {
    downloadAppButton.addEventListener('click', downloadApp);
  }
}

function setupModalEvents() {
  // إغلاق النوافذ المنبثقة بالضغط على Escape
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      closeAllModals();
    }
  });
}

function setupNetworkEvents() {
  // مراقبة حالة الاتصال
  window.addEventListener('online', () => {
    isOnline = true;
    updateConnectionStatus();
    showToast('تم استعادة الاتصال بالإنترنت', 'success');
  });
  
  window.addEventListener('offline', () => {
    isOnline = false;
    updateConnectionStatus();
    showToast('انقطع الاتصال بالإنترنت - العمل في الوضع المحلي', 'warning');
  });
}

function setupKeyboardEvents() {
  // اختصارات لوحة المفاتيح
  document.addEventListener('keydown', (e) => {
    // Ctrl+L لتسجيل الدخول
    if (e.ctrlKey && e.key === 'l') {
      e.preventDefault();
      if (!currentUser) {
        showLoginModal();
      }
    }
    
    // Ctrl+H للعودة للصفحة الرئيسية
    if (e.ctrlKey && e.key === 'h') {
      e.preventDefault();
      showHomePage();
    }
  });
}

// ==================== بدء المراقبة ====================
function startMonitoring() {
  // مراقبة حالة الاتصال
  setInterval(checkConnectionStatus, APP_CONFIG.CONNECTION_CHECK_INTERVAL);
  
  // الحفظ التلقائي
  setInterval(autoSave, APP_CONFIG.AUTO_SAVE_INTERVAL);
  
  console.log('تم بدء أنظمة المراقبة');
}

async function checkConnectionStatus() {
  try {
    const response = await fetch('/api/health', {
      method: 'GET',
      cache: 'no-cache'
    });
    
    const wasOnline = isOnline;
    isOnline = response.ok;
    
    if (isOnline !== wasOnline) {
      updateConnectionStatus();
    }
  } catch (error) {
    if (isOnline) {
      isOnline = false;
      updateConnectionStatus();
    }
  }
}

function autoSave() {
  // حفظ البيانات المحلية
  if (contracts.length > 0) {
    localStorage.setItem('contracts', JSON.stringify(contracts));
  }
  
  console.log('تم الحفظ التلقائي');
}

// ==================== دوال مساعدة ====================
function showLoadingScreen() {
  document.getElementById('loadingScreen').classList.remove('hidden');
  document.getElementById('app').classList.add('hidden');
}

function hideLoadingScreen() {
  document.getElementById('loadingScreen').classList.add('hidden');
  document.getElementById('app').classList.remove('hidden');
}

function updateCurrentTime() {
  const now = new Date();
  const timeString = now.toLocaleString('ar-SA', {
    timeZone: 'Asia/Kuwait',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
  
  updateElement('currentTime', timeString);
}

function updateConnectionStatus() {
  const statusElement = document.getElementById('connectionStatus');
  
  if (isOnline) {
    statusElement.innerHTML = `
      <div class="w-3 h-3 bg-green-500 rounded-full ml-2 animate-pulse"></div>
      <span class="text-sm text-green-600 font-medium">متصل</span>
    `;
  } else {
    statusElement.innerHTML = `
      <div class="w-3 h-3 bg-red-500 rounded-full ml-2"></div>
      <span class="text-sm text-red-600 font-medium">غير متصل</span>
    `;
  }
}

function updateElement(id, content) {
  const element = document.getElementById(id);
  if (element) {
    element.textContent = content;
  }
}

function formatCurrency(amount) {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: 'KWD',
    minimumFractionDigits: 2
  }).format(amount);
}

function showErrorMessage(message) {
  showToast(message, 'error');
}

// ==================== معالجات الأحداث ====================
function handleQuickStart() {
  if (currentUser) {
    // الانتقال إلى صفحة إدارة العقود
    window.location.href = '/static/pages/contracts.html';
  } else {
    // عرض نافذة تسجيل الدخول
    showLoginModal();
  }
}

function showDemo() {
  showToast('العرض التوضيحي قيد التطوير', 'info');
}

function downloadApp() {
  // رابط تحميل التطبيق
  const link = document.createElement('a');
  link.href = '/downloads/SupplierContractManager.exe';
  link.download = 'SupplierContractManager.exe';
  link.click();
}

async function handleLogout() {
  try {
    await API.post('/auth/logout');
    currentUser = null;
    updateUserInterface(false);
    showToast('تم تسجيل الخروج بنجاح', 'success');
    
    // إعادة تحميل الصفحة
    setTimeout(() => {
      window.location.reload();
    }, 1000);
    
  } catch (error) {
    console.error('خطأ في تسجيل الخروج:', error);
    showToast('حدث خطأ في تسجيل الخروج', 'error');
  }
}

function showHomePage() {
  // إظهار الصفحة الرئيسية
  document.querySelectorAll('.page').forEach(page => {
    page.classList.add('hidden');
  });
  
  document.getElementById('homePage').classList.remove('hidden');
}

// ==================== دوال النوافذ المنبثقة ====================
function setupDropdowns() {
  // إعداد القوائم المنسدلة
  console.log('تم إعداد القوائم المنسدلة');
}

function setupModals() {
  // إعداد النوافذ المنبثقة
  console.log('تم إعداد النوافذ المنبثقة');
}

function showLoginModal() {
  // إنشاء نافذة تسجيل الدخول ديناميكياً
  const modal = createLoginModal();
  document.body.appendChild(modal);

  // إظهار النافذة مع تأثير
  setTimeout(() => {
    modal.classList.remove('opacity-0');
    modal.querySelector('.modal-content').classList.remove('scale-95');
  }, 10);
}

function createLoginModal() {
  const modal = document.createElement('div');
  modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 opacity-0 transition-opacity duration-300';
  modal.id = 'loginModal';

  modal.innerHTML = `
    <div class="modal-content bg-white rounded-2xl p-8 w-full max-w-md mx-4 transform scale-95 transition-transform duration-300">
      <div class="text-center mb-6">
        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fas fa-user-shield text-blue-600 text-2xl"></i>
        </div>
        <h2 class="text-2xl font-bold text-gray-800">تسجيل الدخول</h2>
        <p class="text-gray-600 mt-2">ادخل بياناتك للوصول إلى النظام</p>
      </div>

      <form id="loginForm" class="space-y-6">
        <div class="relative">
          <i class="fas fa-user absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          <input type="text" id="loginUsername" placeholder="اسم المستخدم" required
                 class="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
        </div>

        <div class="relative">
          <i class="fas fa-lock absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          <input type="password" id="loginPassword" placeholder="كلمة المرور" required
                 class="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
          <button type="button" id="togglePassword" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            <i class="fas fa-eye"></i>
          </button>
        </div>

        <div class="flex items-center justify-between">
          <label class="flex items-center">
            <input type="checkbox" id="rememberMe" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
            <span class="mr-2 text-sm text-gray-600">تذكرني</span>
          </label>
          <a href="#" class="text-sm text-blue-600 hover:text-blue-800">نسيت كلمة المرور؟</a>
        </div>

        <div class="flex gap-3">
          <button type="button" onclick="closeLoginModal()" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 px-4 rounded-lg transition-colors">
            إلغاء
          </button>
          <button type="submit" class="flex-1 btn-primary">
            <i class="fas fa-sign-in-alt ml-2"></i>
            دخول
          </button>
        </div>
      </form>
    </div>
  `;

  // إضافة مستمعي الأحداث
  modal.querySelector('#loginForm').addEventListener('submit', handleLogin);
  modal.querySelector('#togglePassword').addEventListener('click', togglePasswordVisibility);
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      closeLoginModal();
    }
  });

  return modal;
}

function closeLoginModal() {
  const modal = document.getElementById('loginModal');
  if (modal) {
    modal.classList.add('opacity-0');
    modal.querySelector('.modal-content').classList.add('scale-95');

    setTimeout(() => {
      modal.remove();
    }, 300);
  }
}

function closeAllModals() {
  const modals = document.querySelectorAll('.fixed.inset-0');
  modals.forEach(modal => {
    if (modal.id === 'loginModal') {
      closeLoginModal();
    }
  });
}

async function handleLogin(e) {
  e.preventDefault();

  const username = document.getElementById('loginUsername').value;
  const password = document.getElementById('loginPassword').value;
  const rememberMe = document.getElementById('rememberMe').checked;

  try {
    const response = await API.post('/auth/login', {
      username,
      password,
      remember_me: rememberMe
    });

    if (response.success) {
      currentUser = response.data.user;
      updateUserInterface(true);
      closeLoginModal();
      showToast(`مرحباً ${currentUser.full_name}! تم تسجيل الدخول بنجاح`, 'success');

      // إعادة تحميل البيانات
      await loadInitialData();
    } else {
      showToast(response.message || 'خطأ في تسجيل الدخول', 'error');
    }
  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error);
    showToast('حدث خطأ في تسجيل الدخول', 'error');
  }
}

function togglePasswordVisibility() {
  const passwordInput = document.getElementById('loginPassword');
  const toggleBtn = document.getElementById('togglePassword');

  if (passwordInput.type === 'password') {
    passwordInput.type = 'text';
    toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i>';
  } else {
    passwordInput.type = 'password';
    toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
  }
}

// ==================== نظام الإشعارات ====================
function showToast(message, type = 'success', duration = APP_CONFIG.TOAST_DURATION) {
  // إنشاء عنصر الإشعار
  const toast = document.createElement('div');
  toast.className = `fixed top-5 left-5 p-4 rounded-lg shadow-lg z-50 max-w-sm transform translate-x-full transition-transform duration-300`;

  // تحديد اللون حسب النوع
  const colors = {
    success: 'bg-green-600 text-white',
    error: 'bg-red-600 text-white',
    warning: 'bg-yellow-600 text-white',
    info: 'bg-blue-600 text-white'
  };

  const icons = {
    success: 'fas fa-check-circle',
    error: 'fas fa-exclamation-circle',
    warning: 'fas fa-exclamation-triangle',
    info: 'fas fa-info-circle'
  };

  toast.className += ` ${colors[type] || colors.info}`;

  toast.innerHTML = `
    <div class="flex items-center">
      <i class="${icons[type] || icons.info} ml-3"></i>
      <span class="flex-1">${message}</span>
      <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-white hover:text-gray-200">
        <i class="fas fa-times"></i>
      </button>
    </div>
  `;

  document.body.appendChild(toast);

  // إظهار الإشعار
  setTimeout(() => {
    toast.classList.remove('translate-x-full');
  }, 10);

  // إخفاء الإشعار تلقائياً
  setTimeout(() => {
    toast.classList.add('translate-x-full');
    setTimeout(() => {
      if (toast.parentNode) {
        toast.remove();
      }
    }, 300);
  }, duration);
}

// تصدير الدوال للاستخدام في ملفات أخرى
window.APP = {
  currentUser,
  isOnline,
  contracts,
  suppliers,
  contractTypes,
  supplierCategories,
  CONFIG: APP_CONFIG,
  updateUserInterface,
  loadQuickStats,
  showToast,
  formatCurrency,
  showLoginModal,
  closeLoginModal
};
