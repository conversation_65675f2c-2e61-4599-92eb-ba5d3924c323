# دليل التشغيل السريع

## نظام إدارة عقود الموردين والإيجارات - جمعية المنقف التعاونية

---

## 🚀 التشغيل السريع

### للمستخدمين الجدد

#### 1. تحميل المشروع
```bash
git clone https://github.com/manqaf-coop/supplier-contracts-system.git
cd supplier-contracts-system
```

#### 2. التشغيل التلقائي

**على Windows:**
```cmd
# تشغيل ملف Batch
start.bat

# أو تشغيل PowerShell
powershell -ExecutionPolicy Bypass -File start.ps1
```

**على Linux/macOS:**
```bash
# إعطاء صلاحية التنفيذ
chmod +x start.sh

# تشغيل السكريبت
./start.sh
```

#### 3. اختيار طريقة التشغيل
- **الخيار 1**: تشغيل عادي (للإنتاج)
- **الخيار 2**: تشغيل للتطوير (مع إعادة التحميل التلقائي)
- **الخيار 3**: تشغيل باستخدام Docker
- **الخيار 5**: إعداد أولي فقط

---

## 📋 المتطلبات

### الحد الأدنى
- **Node.js**: الإصدار 18.0 أو أحدث
- **MySQL**: الإصدار 8.0 أو أحدث
- **npm**: يأتي مع Node.js
- **الذاكرة**: 4 جيجابايت RAM
- **التخزين**: 10 جيجابايت مساحة فارغة

### اختياري
- **Docker**: للتشغيل المعزول
- **Docker Compose**: لتشغيل جميع الخدمات
- **Redis**: للتخزين المؤقت

---

## ⚙️ الإعداد اليدوي

### 1. تثبيت التبعيات
```bash
npm install
```

### 2. إعداد متغيرات البيئة
```bash
# نسخ ملف القالب
cp .env.example .env

# تحرير الإعدادات
nano .env
```

### 3. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
mysql -u root -p < database/schema.sql
```

### 4. تشغيل النظام
```bash
# للتطوير
npm run dev

# للإنتاج
npm start
```

---

## 🐳 التشغيل باستخدام Docker

### التشغيل السريع
```bash
# تشغيل جميع الخدمات
docker-compose up -d

# عرض السجلات
docker-compose logs -f app

# إيقاف الخدمات
docker-compose down
```

### مع خدمات المراقبة
```bash
# تشغيل مع Prometheus و Grafana
docker-compose --profile monitoring up -d
```

---

## 🌐 الوصول للنظام

بعد التشغيل الناجح، يمكنك الوصول للنظام عبر:

- **النظام الرئيسي**: http://localhost:3000
- **API Health Check**: http://localhost:3000/api/health
- **Prometheus** (إذا كان مفعل): http://localhost:9090
- **Grafana** (إذا كان مفعل): http://localhost:3001

### بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

---

## 🔧 إعدادات قاعدة البيانات

### إعدادات MySQL الافتراضية
```env
DB_HOST=localhost
DB_PORT=3306
DB_NAME=supplier_contracts
DB_USER=contracts_app
DB_PASSWORD=SecurePassword123!
```

### إنشاء المستخدم والقاعدة
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE supplier_contracts CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء المستخدم
CREATE USER 'contracts_app'@'localhost' IDENTIFIED BY 'SecurePassword123!';

-- منح الصلاحيات
GRANT ALL PRIVILEGES ON supplier_contracts.* TO 'contracts_app'@'localhost';
FLUSH PRIVILEGES;
```

---

## 📁 هيكل المجلدات

```
supplier-contracts-system/
├── backend/                 # الخادم الخلفي
│   ├── api/                # نقاط النهاية
│   ├── routes/             # مسارات API
│   ├── middleware/         # الوسطاء
│   ├── utils/              # الأدوات المساعدة
│   ├── uploads/            # الملفات المرفوعة
│   └── logs/               # ملفات السجلات
├── frontend/               # الواجهة الأمامية
│   ├── assets/             # الموارد الثابتة
│   ├── pages/              # صفحات النظام
│   └── index.html          # الصفحة الرئيسية
├── database/               # قاعدة البيانات
│   ├── schema.sql          # هيكل قاعدة البيانات
│   ├── migrations/         # الهجرات
│   └── seeds/              # البيانات الأولية
├── docker-compose.yml      # إعداد Docker
├── Dockerfile              # ملف Docker
├── start.sh               # سكريبت تشغيل Linux/macOS
├── start.bat              # سكريبت تشغيل Windows
├── start.ps1              # سكريبت PowerShell
└── package.json           # تبعيات Node.js
```

---

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في الاتصال بقاعدة البيانات
```bash
# التحقق من تشغيل MySQL
sudo systemctl status mysql

# إعادة تشغيل MySQL
sudo systemctl restart mysql

# التحقق من الاتصال
mysql -u contracts_app -p -e "SELECT 1;"
```

#### 2. المنفذ 3000 مستخدم
```bash
# العثور على العملية
lsof -ti:3000

# إيقاف العملية
kill -9 $(lsof -ti:3000)
```

#### 3. مشاكل الصلاحيات
```bash
# إعطاء صلاحيات للمجلدات
chmod -R 755 backend/uploads
chmod -R 755 backend/logs
```

#### 4. مشاكل npm
```bash
# تنظيف cache
npm cache clean --force

# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

---

## 📊 مراقبة النظام

### فحص حالة النظام
```bash
# فحص صحة API
curl http://localhost:3000/api/health

# عرض السجلات
tail -f backend/logs/combined.log

# مراقبة استخدام الموارد
htop
```

### السجلات المهمة
- **combined.log**: جميع السجلات
- **error.log**: الأخطاء فقط
- **http.log**: طلبات HTTP
- **exceptions.log**: الاستثناءات غير المعالجة

---

## 🔐 الأمان

### تغيير كلمات المرور الافتراضية
1. قم بتحديث ملف `.env`:
   ```env
   JWT_SECRET=your-new-secret-key
   DB_PASSWORD=your-new-db-password
   ```

2. قم بتغيير كلمة مرور المدير:
   ```sql
   UPDATE users SET password_hash = '$2a$12$new_hash_here' WHERE username = 'admin';
   ```

### إعدادات الأمان للإنتاج
- تفعيل HTTPS
- تحديث جميع كلمات المرور
- تفعيل جدار الحماية
- تحديث النظام بانتظام

---

## 📞 الدعم

### في حالة وجود مشاكل:

1. **تحقق من السجلات**:
   ```bash
   # سجلات التطبيق
   tail -f backend/logs/error.log
   
   # سجلات Docker
   docker-compose logs -f app
   ```

2. **تحقق من حالة الخدمات**:
   ```bash
   # حالة قاعدة البيانات
   systemctl status mysql
   
   # حالة Docker
   docker-compose ps
   ```

3. **إعادة تشغيل النظام**:
   ```bash
   # إعادة تشغيل عادي
   npm restart
   
   # إعادة تشغيل Docker
   docker-compose restart
   ```

### التواصل
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +965 1234 5678
- **GitHub Issues**: [رابط المشاكل](https://github.com/manqaf-coop/supplier-contracts-system/issues)

---

## 🎯 الخطوات التالية

بعد التشغيل الناجح:

1. **قم بتسجيل الدخول** باستخدام بيانات المدير
2. **أضف فئات الموردين** الخاصة بك
3. **أدخل بيانات الموردين**
4. **ابدأ في إنشاء العقود**
5. **استكشف التقارير والإحصائيات**

---

**جمعية المنقف التعاونية** - نحو مستقبل رقمي أفضل 🚀
