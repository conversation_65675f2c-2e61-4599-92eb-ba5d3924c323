#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة التقارير
Reports Window
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
import os
import subprocess
from datetime import datetime, date
from reports.report_generator import ReportGenerator

class ReportsWindow:
    """نافذة التقارير"""
    
    def __init__(self, parent, db_manager):
        """
        تهيئة نافذة التقارير
        
        Args:
            parent: النافذة الأب
            db_manager: مدير قاعدة البيانات
        """
        self.parent = parent
        self.db_manager = db_manager
        self.report_generator = ReportGenerator(db_manager)
        
        # إنشاء النافذة
        self.window = ttk_bs.Toplevel(parent)
        self.window.title("التقارير")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        
        # متغيرات النموذج
        self.setup_variables()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تعيين النافذة في المقدمة
        self.window.transient(parent)
        self.window.grab_set()
    
    def setup_variables(self):
        """إعداد متغيرات النموذج"""
        self.start_date = tk.StringVar()
        self.end_date = tk.StringVar()
        self.contract_status = tk.StringVar()
        self.report_year = tk.StringVar(value=str(datetime.now().year))
        self.output_format = tk.StringVar(value="excel")
    
    def create_interface(self):
        """إنشاء واجهة النافذة"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # عنوان النافذة
        title_label = ttk_bs.Label(
            main_frame,
            text="التقارير والإحصائيات",
            font=("Arial", 16, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 30))
        
        # إنشاء التبويبات
        notebook = ttk_bs.Notebook(main_frame)
        notebook.pack(fill=BOTH, expand=True)
        
        # تبويب تقارير الموردين
        self.create_suppliers_reports_tab(notebook)
        
        # تبويب تقارير العقود
        self.create_contracts_reports_tab(notebook)
        
        # تبويب تقارير الإيجارات
        self.create_rentals_reports_tab(notebook)
        
        # تبويب التقارير المالية
        self.create_financial_reports_tab(notebook)
    
    def create_suppliers_reports_tab(self, notebook):
        """إنشاء تبويب تقارير الموردين"""
        suppliers_frame = ttk_bs.Frame(notebook, padding=20)
        notebook.add(suppliers_frame, text="تقارير الموردين")
        
        # عنوان
        title_label = ttk_bs.Label(
            suppliers_frame,
            text="تقارير الموردين",
            font=("Arial", 14, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 20))
        
        # وصف
        desc_label = ttk_bs.Label(
            suppliers_frame,
            text="تقرير شامل عن جميع الموردين مع إحصائيات العقود والإيرادات",
            font=("Arial", 11)
        )
        desc_label.pack(pady=(0, 30))
        
        # تنسيق الإخراج
        format_frame = ttk_bs.Frame(suppliers_frame)
        format_frame.pack(pady=(0, 20))
        
        ttk_bs.Label(format_frame, text="تنسيق التقرير:").pack(side=LEFT, padx=(0, 10))
        
        excel_radio = ttk_bs.Radiobutton(
            format_frame,
            text="Excel",
            variable=self.output_format,
            value="excel"
        )
        excel_radio.pack(side=LEFT, padx=(0, 20))
        
        pdf_radio = ttk_bs.Radiobutton(
            format_frame,
            text="PDF",
            variable=self.output_format,
            value="pdf"
        )
        pdf_radio.pack(side=LEFT)
        
        # أزرار
        buttons_frame = ttk_bs.Frame(suppliers_frame)
        buttons_frame.pack(pady=30)
        
        ttk_bs.Button(
            buttons_frame,
            text="توليد تقرير الموردين",
            command=self.generate_suppliers_report,
            bootstyle=SUCCESS,
            width=25
        ).pack(pady=10)
        
        ttk_bs.Button(
            buttons_frame,
            text="فتح مجلد التقارير",
            command=self.open_reports_folder,
            bootstyle=INFO,
            width=25
        ).pack()
    
    def create_contracts_reports_tab(self, notebook):
        """إنشاء تبويب تقارير العقود"""
        contracts_frame = ttk_bs.Frame(notebook, padding=20)
        notebook.add(contracts_frame, text="تقارير العقود")
        
        # عنوان
        title_label = ttk_bs.Label(
            contracts_frame,
            text="تقارير العقود",
            font=("Arial", 14, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 20))
        
        # خيارات التصفية
        filter_frame = ttk_bs.LabelFrame(contracts_frame, text="خيارات التصفية", padding=15)
        filter_frame.pack(fill=X, pady=(0, 20))
        
        # الفترة الزمنية
        date_frame = ttk_bs.Frame(filter_frame)
        date_frame.pack(fill=X, pady=(0, 10))
        
        ttk_bs.Label(date_frame, text="من تاريخ:").pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            date_frame,
            textvariable=self.start_date,
            width=15
        ).pack(side=LEFT, padx=(0, 20))
        
        ttk_bs.Label(date_frame, text="إلى تاريخ:").pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            date_frame,
            textvariable=self.end_date,
            width=15
        ).pack(side=LEFT)
        
        # حالة العقد
        status_frame = ttk_bs.Frame(filter_frame)
        status_frame.pack(fill=X, pady=(0, 10))
        
        ttk_bs.Label(status_frame, text="حالة العقد:").pack(side=LEFT, padx=(0, 5))
        status_combo = ttk_bs.Combobox(
            status_frame,
            textvariable=self.contract_status,
            values=["", "نشط", "منتهي", "ملغي", "معلق"],
            state="readonly",
            width=15
        )
        status_combo.pack(side=LEFT)
        
        # تنسيق الإخراج
        format_frame = ttk_bs.Frame(contracts_frame)
        format_frame.pack(pady=(0, 20))
        
        ttk_bs.Label(format_frame, text="تنسيق التقرير:").pack(side=LEFT, padx=(0, 10))
        
        excel_radio = ttk_bs.Radiobutton(
            format_frame,
            text="Excel",
            variable=self.output_format,
            value="excel"
        )
        excel_radio.pack(side=LEFT, padx=(0, 20))
        
        pdf_radio = ttk_bs.Radiobutton(
            format_frame,
            text="PDF",
            variable=self.output_format,
            value="pdf"
        )
        pdf_radio.pack(side=LEFT)
        
        # أزرار
        buttons_frame = ttk_bs.Frame(contracts_frame)
        buttons_frame.pack(pady=20)
        
        ttk_bs.Button(
            buttons_frame,
            text="توليد تقرير العقود",
            command=self.generate_contracts_report,
            bootstyle=SUCCESS,
            width=25
        ).pack(pady=10)
        
        ttk_bs.Button(
            buttons_frame,
            text="مسح الفلاتر",
            command=self.clear_filters,
            bootstyle=SECONDARY,
            width=25
        ).pack()
    
    def create_rentals_reports_tab(self, notebook):
        """إنشاء تبويب تقارير الإيجارات"""
        rentals_frame = ttk_bs.Frame(notebook, padding=20)
        notebook.add(rentals_frame, text="تقارير الإيجارات")
        
        # عنوان
        title_label = ttk_bs.Label(
            rentals_frame,
            text="تقارير الإيجارات",
            font=("Arial", 14, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 20))
        
        # وصف
        desc_label = ttk_bs.Label(
            rentals_frame,
            text="تقرير مفصل عن جميع الإيجارات النشطة مع تفاصيل الأسعار والمساحات",
            font=("Arial", 11)
        )
        desc_label.pack(pady=(0, 30))
        
        # تنسيق الإخراج
        format_frame = ttk_bs.Frame(rentals_frame)
        format_frame.pack(pady=(0, 20))
        
        ttk_bs.Label(format_frame, text="تنسيق التقرير:").pack(side=LEFT, padx=(0, 10))
        
        excel_radio = ttk_bs.Radiobutton(
            format_frame,
            text="Excel",
            variable=self.output_format,
            value="excel"
        )
        excel_radio.pack(side=LEFT, padx=(0, 20))
        
        pdf_radio = ttk_bs.Radiobutton(
            format_frame,
            text="PDF",
            variable=self.output_format,
            value="pdf"
        )
        pdf_radio.pack(side=LEFT)
        
        # أزرار
        buttons_frame = ttk_bs.Frame(rentals_frame)
        buttons_frame.pack(pady=30)
        
        ttk_bs.Button(
            buttons_frame,
            text="توليد تقرير الإيجارات",
            command=self.generate_rentals_report,
            bootstyle=SUCCESS,
            width=25
        ).pack()
    
    def create_financial_reports_tab(self, notebook):
        """إنشاء تبويب التقارير المالية"""
        financial_frame = ttk_bs.Frame(notebook, padding=20)
        notebook.add(financial_frame, text="التقارير المالية")
        
        # عنوان
        title_label = ttk_bs.Label(
            financial_frame,
            text="التقارير المالية",
            font=("Arial", 14, "bold"),
            bootstyle=PRIMARY
        )
        title_label.pack(pady=(0, 20))
        
        # اختيار السنة
        year_frame = ttk_bs.Frame(financial_frame)
        year_frame.pack(pady=(0, 20))
        
        ttk_bs.Label(year_frame, text="السنة:").pack(side=LEFT, padx=(0, 10))
        ttk_bs.Entry(
            year_frame,
            textvariable=self.report_year,
            width=10
        ).pack(side=LEFT)
        
        # تنسيق الإخراج
        format_frame = ttk_bs.Frame(financial_frame)
        format_frame.pack(pady=(0, 20))
        
        ttk_bs.Label(format_frame, text="تنسيق التقرير:").pack(side=LEFT, padx=(0, 10))
        
        excel_radio = ttk_bs.Radiobutton(
            format_frame,
            text="Excel",
            variable=self.output_format,
            value="excel"
        )
        excel_radio.pack(side=LEFT, padx=(0, 20))
        
        pdf_radio = ttk_bs.Radiobutton(
            format_frame,
            text="PDF",
            variable=self.output_format,
            value="pdf"
        )
        pdf_radio.pack(side=LEFT)
        
        # أزرار
        buttons_frame = ttk_bs.Frame(financial_frame)
        buttons_frame.pack(pady=30)
        
        ttk_bs.Button(
            buttons_frame,
            text="توليد التقرير المالي",
            command=self.generate_financial_report,
            bootstyle=SUCCESS,
            width=25
        ).pack()

    def generate_suppliers_report(self):
        """توليد تقرير الموردين"""
        try:
            output_format = self.output_format.get()

            # إظهار رسالة التحميل
            self.show_loading_message("جاري توليد تقرير الموردين...")

            # توليد التقرير
            filepath = self.report_generator.generate_suppliers_report(output_format)

            # إخفاء رسالة التحميل
            self.hide_loading_message()

            # إظهار رسالة النجاح
            result = messagebox.askyesno(
                "تم توليد التقرير",
                f"تم توليد تقرير الموردين بنجاح!\n\nالملف: {os.path.basename(filepath)}\n\nهل تريد فتح التقرير؟"
            )

            if result:
                self.open_file(filepath)

        except Exception as e:
            self.hide_loading_message()
            messagebox.showerror("خطأ", f"فشل في توليد تقرير الموردين:\n{str(e)}")

    def generate_contracts_report(self):
        """توليد تقرير العقود"""
        try:
            output_format = self.output_format.get()
            start_date = self.start_date.get().strip() or None
            end_date = self.end_date.get().strip() or None
            status = self.contract_status.get().strip() or None

            # إظهار رسالة التحميل
            self.show_loading_message("جاري توليد تقرير العقود...")

            # توليد التقرير
            filepath = self.report_generator.generate_contracts_report(
                start_date, end_date, status, output_format
            )

            # إخفاء رسالة التحميل
            self.hide_loading_message()

            # إظهار رسالة النجاح
            result = messagebox.askyesno(
                "تم توليد التقرير",
                f"تم توليد تقرير العقود بنجاح!\n\nالملف: {os.path.basename(filepath)}\n\nهل تريد فتح التقرير؟"
            )

            if result:
                self.open_file(filepath)

        except Exception as e:
            self.hide_loading_message()
            messagebox.showerror("خطأ", f"فشل في توليد تقرير العقود:\n{str(e)}")

    def generate_rentals_report(self):
        """توليد تقرير الإيجارات"""
        try:
            output_format = self.output_format.get()

            # إظهار رسالة التحميل
            self.show_loading_message("جاري توليد تقرير الإيجارات...")

            # توليد التقرير
            filepath = self.report_generator.generate_rentals_report(output_format)

            # إخفاء رسالة التحميل
            self.hide_loading_message()

            # إظهار رسالة النجاح
            result = messagebox.askyesno(
                "تم توليد التقرير",
                f"تم توليد تقرير الإيجارات بنجاح!\n\nالملف: {os.path.basename(filepath)}\n\nهل تريد فتح التقرير؟"
            )

            if result:
                self.open_file(filepath)

        except Exception as e:
            self.hide_loading_message()
            messagebox.showerror("خطأ", f"فشل في توليد تقرير الإيجارات:\n{str(e)}")

    def generate_financial_report(self):
        """توليد التقرير المالي"""
        try:
            output_format = self.output_format.get()
            year = self.report_year.get().strip()

            # التحقق من صحة السنة
            try:
                year = int(year)
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال سنة صحيحة")
                return

            # إظهار رسالة التحميل
            self.show_loading_message("جاري توليد التقرير المالي...")

            # توليد التقرير
            filepath = self.report_generator.generate_financial_summary_report(year, output_format)

            # إخفاء رسالة التحميل
            self.hide_loading_message()

            # إظهار رسالة النجاح
            result = messagebox.askyesno(
                "تم توليد التقرير",
                f"تم توليد التقرير المالي بنجاح!\n\nالملف: {os.path.basename(filepath)}\n\nهل تريد فتح التقرير؟"
            )

            if result:
                self.open_file(filepath)

        except Exception as e:
            self.hide_loading_message()
            messagebox.showerror("خطأ", f"فشل في توليد التقرير المالي:\n{str(e)}")

    def clear_filters(self):
        """مسح الفلاتر"""
        self.start_date.set("")
        self.end_date.set("")
        self.contract_status.set("")

    def open_reports_folder(self):
        """فتح مجلد التقارير"""
        try:
            reports_dir = self.report_generator.reports_dir
            if os.path.exists(reports_dir):
                if os.name == 'nt':  # Windows
                    os.startfile(reports_dir)
                elif os.name == 'posix':  # macOS and Linux
                    subprocess.call(['open', reports_dir])
            else:
                messagebox.showinfo("معلومات", "مجلد التقارير غير موجود بعد")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح مجلد التقارير:\n{str(e)}")

    def open_file(self, filepath):
        """فتح ملف التقرير"""
        try:
            if os.name == 'nt':  # Windows
                os.startfile(filepath)
            elif os.name == 'posix':  # macOS and Linux
                subprocess.call(['open', filepath])
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح الملف:\n{str(e)}")

    def show_loading_message(self, message):
        """إظهار رسالة التحميل"""
        # يمكن تطوير هذا لإظهار نافذة تحميل
        self.window.config(cursor="wait")
        self.window.update()

    def hide_loading_message(self):
        """إخفاء رسالة التحميل"""
        self.window.config(cursor="")
        self.window.update()
