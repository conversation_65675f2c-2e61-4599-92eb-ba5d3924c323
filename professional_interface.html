<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>نظام إدارة عقود الموردين والإيجارات - جمعية المنقف</title>
  
  <!-- CSS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
  
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');
    
    :root {
      --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      --dark-gradient: linear-gradient(135deg, #434343 0%, #000000 100%);
      
      --glass-bg: rgba(255, 255, 255, 0.1);
      --glass-border: rgba(255, 255, 255, 0.2);
      --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
      
      --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Cairo', sans-serif;
      background: var(--primary-gradient);
      min-height: 100vh;
      overflow-x: hidden;
    }
    
    /* Glass Morphism Effects */
    .glass {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      box-shadow: var(--shadow-glass);
    }
    
    .glass-dark {
      background: rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    /* Animated Background */
    .animated-bg {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      background: var(--primary-gradient);
    }
    
    .animated-bg::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      animation: float 20s ease-in-out infinite;
    }
    
    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-20px) rotate(180deg); }
    }
    
    /* Navigation Styles */
    .navbar {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid var(--glass-border);
      transition: var(--transition-smooth);
    }
    
    .navbar.scrolled {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(30px);
    }
    
    .nav-item {
      position: relative;
      transition: var(--transition-smooth);
    }
    
    .nav-item::before {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 50%;
      width: 0;
      height: 3px;
      background: var(--secondary-gradient);
      transition: var(--transition-bounce);
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    .nav-item:hover::before,
    .nav-item.active::before {
      width: 100%;
    }
    
    .nav-item:hover {
      transform: translateY(-2px);
    }
    
    /* Dropdown Styles */
    .dropdown {
      position: relative;
    }
    
    .dropdown-menu {
      position: absolute;
      top: 100%;
      right: 0;
      min-width: 250px;
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 15px;
      box-shadow: var(--shadow-glass);
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px) scale(0.95);
      transition: var(--transition-smooth);
      z-index: 1000;
    }
    
    .dropdown:hover .dropdown-menu,
    .dropdown.active .dropdown-menu {
      opacity: 1;
      visibility: visible;
      transform: translateY(0) scale(1);
    }
    
    .dropdown-item {
      padding: 12px 20px;
      color: white;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 10px;
      transition: var(--transition-smooth);
      border-radius: 10px;
      margin: 5px;
    }
    
    .dropdown-item:hover {
      background: rgba(255, 255, 255, 0.1);
      transform: translateX(5px);
    }
    
    /* Card Styles */
    .card {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 20px;
      box-shadow: var(--shadow-glass);
      transition: var(--transition-smooth);
      overflow: hidden;
    }
    
    .card:hover {
      transform: translateY(-10px) scale(1.02);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }
    
    .card-gradient {
      position: relative;
      overflow: hidden;
    }
    
    .card-gradient::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--secondary-gradient);
    }
    
    /* Button Styles */
    .btn {
      position: relative;
      padding: 12px 30px;
      border: none;
      border-radius: 50px;
      font-weight: 600;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: var(--transition-smooth);
      overflow: hidden;
      cursor: pointer;
    }
    
    .btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: var(--transition-smooth);
    }
    
    .btn:hover::before {
      left: 100%;
    }
    
    .btn-primary {
      background: var(--primary-gradient);
      color: white;
    }
    
    .btn-secondary {
      background: var(--secondary-gradient);
      color: white;
    }
    
    .btn-success {
      background: var(--success-gradient);
      color: white;
    }
    
    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }
    
    /* Sidebar Styles */
    .sidebar {
      position: fixed;
      top: 0;
      right: -350px;
      width: 350px;
      height: 100vh;
      background: var(--glass-dark);
      backdrop-filter: blur(20px);
      border-left: 1px solid var(--glass-border);
      transition: var(--transition-smooth);
      z-index: 1000;
      overflow-y: auto;
    }
    
    .sidebar.active {
      right: 0;
    }
    
    .sidebar-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      opacity: 0;
      visibility: hidden;
      transition: var(--transition-smooth);
      z-index: 999;
    }
    
    .sidebar-overlay.active {
      opacity: 1;
      visibility: visible;
    }
    
    /* Stats Cards */
    .stats-card {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 20px;
      padding: 25px;
      text-align: center;
      transition: var(--transition-smooth);
      position: relative;
      overflow: hidden;
    }
    
    .stats-card::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: conic-gradient(from 0deg, transparent, rgba(255,255,255,0.1), transparent);
      animation: rotate 4s linear infinite;
      opacity: 0;
      transition: var(--transition-smooth);
    }
    
    .stats-card:hover::before {
      opacity: 1;
    }
    
    @keyframes rotate {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .stats-icon {
      width: 60px;
      height: 60px;
      margin: 0 auto 15px;
      background: var(--secondary-gradient);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
    }
    
    /* Form Styles */
    .form-group {
      position: relative;
      margin-bottom: 25px;
    }
    
    .form-input {
      width: 100%;
      padding: 15px 20px;
      border: 2px solid var(--glass-border);
      border-radius: 15px;
      background: var(--glass-bg);
      backdrop-filter: blur(10px);
      color: white;
      font-size: 16px;
      transition: var(--transition-smooth);
    }
    
    .form-input:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    }
    
    .form-input::placeholder {
      color: rgba(255, 255, 255, 0.7);
    }
    
    .form-label {
      position: absolute;
      top: -10px;
      right: 15px;
      background: var(--primary-gradient);
      padding: 5px 15px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 600;
      color: white;
    }
    
    /* Animation Classes */
    .fade-in {
      animation: fadeIn 0.6s ease-out;
    }
    
    .slide-up {
      animation: slideUp 0.6s ease-out;
    }
    
    .scale-in {
      animation: scaleIn 0.6s ease-out;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    @keyframes slideUp {
      from { transform: translateY(30px); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
    
    @keyframes scaleIn {
      from { transform: scale(0.9); opacity: 0; }
      to { transform: scale(1); opacity: 1; }
    }
    
    /* Responsive */
    @media (max-width: 768px) {
      .sidebar {
        width: 100%;
        right: -100%;
      }
      
      .dropdown-menu {
        position: fixed;
        top: 70px;
        right: 10px;
        left: 10px;
        width: auto;
      }
      
      .stats-card {
        margin-bottom: 20px;
      }
    }
    
    /* Loading Animation */
    .loading {
      position: relative;
      overflow: hidden;
    }
    
    .loading::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      animation: loading 1.5s infinite;
    }
    
    @keyframes loading {
      0% { left: -100%; }
      100% { left: 100%; }
    }
    
    /* Scroll Animations */
    .scroll-reveal {
      opacity: 0;
      transform: translateY(50px);
      transition: var(--transition-smooth);
    }
    
    .scroll-reveal.revealed {
      opacity: 1;
      transform: translateY(0);
    }
  </style>
</head>
<body>
  <!-- Animated Background -->
  <div class="animated-bg"></div>
  
  <!-- Navigation -->
  <nav class="navbar fixed top-0 left-0 right-0 z-50 px-6 py-4">
    <div class="max-w-7xl mx-auto flex items-center justify-between">
      <!-- Logo -->
      <div class="flex items-center gap-3">
        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
          <i class="fas fa-building text-white text-xl"></i>
        </div>
        <div>
          <h1 class="text-white font-bold text-xl">جمعية المنقف</h1>
          <p class="text-white/70 text-sm">نظام إدارة العقود</p>
        </div>
      </div>
      
      <!-- Navigation Menu -->
      <div class="hidden md:flex items-center gap-8">
        <a href="#dashboard" class="nav-item text-white font-medium px-4 py-2 active">
          <i class="fas fa-home ml-2"></i>
          الرئيسية
        </a>
        
        <!-- Suppliers Dropdown -->
        <div class="dropdown">
          <a href="#" class="nav-item text-white font-medium px-4 py-2 flex items-center">
            <i class="fas fa-users ml-2"></i>
            الموردين
            <i class="fas fa-chevron-down mr-2 text-sm"></i>
          </a>
          <div class="dropdown-menu">
            <a href="#suppliers-list" class="dropdown-item">
              <i class="fas fa-list"></i>
              قائمة الموردين
            </a>
            <a href="#add-supplier" class="dropdown-item">
              <i class="fas fa-plus"></i>
              إضافة مورد جديد
            </a>
            <a href="#supplier-categories" class="dropdown-item">
              <i class="fas fa-tags"></i>
              فئات الموردين
            </a>
            <a href="#supplier-reports" class="dropdown-item">
              <i class="fas fa-chart-bar"></i>
              تقارير الموردين
            </a>
          </div>
        </div>
        
        <!-- Contracts Dropdown -->
        <div class="dropdown">
          <a href="#" class="nav-item text-white font-medium px-4 py-2 flex items-center">
            <i class="fas fa-file-contract ml-2"></i>
            العقود
            <i class="fas fa-chevron-down mr-2 text-sm"></i>
          </a>
          <div class="dropdown-menu">
            <a href="#contracts-list" class="dropdown-item">
              <i class="fas fa-list"></i>
              قائمة العقود
            </a>
            <a href="#new-contract" class="dropdown-item">
              <i class="fas fa-plus"></i>
              عقد جديد
            </a>
            <a href="#contract-types" class="dropdown-item">
              <i class="fas fa-layer-group"></i>
              أنواع العقود
            </a>
            <a href="#expiring-contracts" class="dropdown-item">
              <i class="fas fa-exclamation-triangle"></i>
              العقود المنتهية قريباً
            </a>
          </div>
        </div>
        
        <!-- Eyes System Dropdown -->
        <div class="dropdown">
          <a href="#" class="nav-item text-white font-medium px-4 py-2 flex items-center">
            <i class="fas fa-eye ml-2"></i>
            نظام العيون
            <i class="fas fa-chevron-down mr-2 text-sm"></i>
          </a>
          <div class="dropdown-menu">
            <a href="#eyes-management" class="dropdown-item">
              <i class="fas fa-cog"></i>
              إدارة العيون
            </a>
            <a href="#rent-eye" class="dropdown-item">
              <i class="fas fa-plus-circle"></i>
              تأجير عين
            </a>
            <a href="#available-eyes" class="dropdown-item">
              <i class="fas fa-check-circle"></i>
              العيون المتاحة
            </a>
            <a href="#rented-eyes" class="dropdown-item">
              <i class="fas fa-clock"></i>
              العيون المؤجرة
            </a>
          </div>
        </div>
        
        <!-- Reports Dropdown -->
        <div class="dropdown">
          <a href="#" class="nav-item text-white font-medium px-4 py-2 flex items-center">
            <i class="fas fa-chart-line ml-2"></i>
            التقارير
            <i class="fas fa-chevron-down mr-2 text-sm"></i>
          </a>
          <div class="dropdown-menu">
            <a href="#financial-reports" class="dropdown-item">
              <i class="fas fa-dollar-sign"></i>
              التقارير المالية
            </a>
            <a href="#supplier-reports" class="dropdown-item">
              <i class="fas fa-users"></i>
              تقارير الموردين
            </a>
            <a href="#contract-reports" class="dropdown-item">
              <i class="fas fa-file-alt"></i>
              تقارير العقود
            </a>
            <a href="#eyes-reports" class="dropdown-item">
              <i class="fas fa-eye"></i>
              تقارير العيون
            </a>
          </div>
        </div>
      </div>
      
      <!-- Mobile Menu & User Actions -->
      <div class="flex items-center gap-4">
        <!-- Notifications -->
        <div class="relative">
          <button class="text-white p-2 rounded-full hover:bg-white/10 transition-all">
            <i class="fas fa-bell text-xl"></i>
            <span class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full text-xs flex items-center justify-center">3</span>
          </button>
        </div>
        
        <!-- User Menu -->
        <div class="dropdown">
          <button class="flex items-center gap-2 text-white p-2 rounded-full hover:bg-white/10 transition-all">
            <img src="https://via.placeholder.com/40" alt="User" class="w-8 h-8 rounded-full">
            <i class="fas fa-chevron-down text-sm"></i>
          </button>
          <div class="dropdown-menu">
            <a href="#profile" class="dropdown-item">
              <i class="fas fa-user"></i>
              الملف الشخصي
            </a>
            <a href="#settings" class="dropdown-item">
              <i class="fas fa-cog"></i>
              الإعدادات
            </a>
            <a href="#logout" class="dropdown-item">
              <i class="fas fa-sign-out-alt"></i>
              تسجيل الخروج
            </a>
          </div>
        </div>
        
        <!-- Mobile Menu Toggle -->
        <button class="md:hidden text-white p-2" onclick="toggleSidebar()">
          <i class="fas fa-bars text-xl"></i>
        </button>
      </div>
    </div>
  </nav>
  
  <!-- Mobile Sidebar -->
  <div class="sidebar-overlay" onclick="toggleSidebar()"></div>
  <div class="sidebar" id="mobileSidebar">
    <div class="p-6">
      <div class="flex items-center justify-between mb-8">
        <h2 class="text-white text-xl font-bold">القائمة الرئيسية</h2>
        <button onclick="toggleSidebar()" class="text-white p-2">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>
      
      <!-- Mobile Navigation Items -->
      <div class="space-y-4">
        <a href="#dashboard" class="block text-white p-3 rounded-lg hover:bg-white/10 transition-all">
          <i class="fas fa-home ml-3"></i>
          الرئيسية
        </a>
        <a href="#suppliers" class="block text-white p-3 rounded-lg hover:bg-white/10 transition-all">
          <i class="fas fa-users ml-3"></i>
          الموردين
        </a>
        <a href="#contracts" class="block text-white p-3 rounded-lg hover:bg-white/10 transition-all">
          <i class="fas fa-file-contract ml-3"></i>
          العقود
        </a>
        <a href="#eyes" class="block text-white p-3 rounded-lg hover:bg-white/10 transition-all">
          <i class="fas fa-eye ml-3"></i>
          نظام العيون
        </a>
        <a href="#reports" class="block text-white p-3 rounded-lg hover:bg-white/10 transition-all">
          <i class="fas fa-chart-line ml-3"></i>
          التقارير
        </a>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <main class="pt-24 px-6 pb-12">
    <div class="max-w-7xl mx-auto">

      <!-- Dashboard Section -->
      <section id="dashboard" class="section-content active">
        <!-- Welcome Header -->
        <div class="text-center mb-12 fade-in">
          <h1 class="text-4xl md:text-6xl font-bold text-white mb-4">
            مرحباً بك في نظام إدارة العقود
          </h1>
          <p class="text-xl text-white/80 max-w-2xl mx-auto">
            نظام متكامل لإدارة عقود الموردين وإيجار العيون في السوق المركزي
          </p>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <div class="stats-card scroll-reveal">
            <div class="stats-icon">
              <i class="fas fa-users"></i>
            </div>
            <h3 class="text-2xl font-bold text-white mb-2">156</h3>
            <p class="text-white/70">إجمالي الموردين</p>
            <div class="mt-3">
              <span class="text-green-400 text-sm">
                <i class="fas fa-arrow-up"></i> +12% هذا الشهر
              </span>
            </div>
          </div>

          <div class="stats-card scroll-reveal" style="animation-delay: 0.1s">
            <div class="stats-icon">
              <i class="fas fa-file-contract"></i>
            </div>
            <h3 class="text-2xl font-bold text-white mb-2">89</h3>
            <p class="text-white/70">العقود النشطة</p>
            <div class="mt-3">
              <span class="text-blue-400 text-sm">
                <i class="fas fa-arrow-up"></i> +8% هذا الشهر
              </span>
            </div>
          </div>

          <div class="stats-card scroll-reveal" style="animation-delay: 0.2s">
            <div class="stats-icon">
              <i class="fas fa-eye"></i>
            </div>
            <h3 class="text-2xl font-bold text-white mb-2">324</h3>
            <p class="text-white/70">العيون المؤجرة</p>
            <div class="mt-3">
              <span class="text-yellow-400 text-sm">
                <i class="fas fa-arrow-up"></i> +15% هذا الشهر
              </span>
            </div>
          </div>

          <div class="stats-card scroll-reveal" style="animation-delay: 0.3s">
            <div class="stats-icon">
              <i class="fas fa-dollar-sign"></i>
            </div>
            <h3 class="text-2xl font-bold text-white mb-2">19,440</h3>
            <p class="text-white/70">الإيرادات الشهرية (د.ك)</p>
            <div class="mt-3">
              <span class="text-green-400 text-sm">
                <i class="fas fa-arrow-up"></i> +22% هذا الشهر
              </span>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          <div class="card card-gradient scroll-reveal">
            <div class="p-8 text-center">
              <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-plus text-white text-2xl"></i>
              </div>
              <h3 class="text-xl font-bold text-white mb-3">إضافة مورد جديد</h3>
              <p class="text-white/70 mb-6">إضافة مورد جديد إلى النظام مع جميع البيانات المطلوبة</p>
              <button class="btn btn-primary w-full" onclick="showSection('add-supplier')">
                <i class="fas fa-plus"></i>
                إضافة الآن
              </button>
            </div>
          </div>

          <div class="card card-gradient scroll-reveal" style="animation-delay: 0.1s">
            <div class="p-8 text-center">
              <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-file-contract text-white text-2xl"></i>
              </div>
              <h3 class="text-xl font-bold text-white mb-3">إنشاء عقد جديد</h3>
              <p class="text-white/70 mb-6">إنشاء عقد توريد أو إيجار جديد مع تحديد جميع الشروط</p>
              <button class="btn btn-success w-full" onclick="showSection('new-contract')">
                <i class="fas fa-file-plus"></i>
                إنشاء عقد
              </button>
            </div>
          </div>

          <div class="card card-gradient scroll-reveal" style="animation-delay: 0.2s">
            <div class="p-8 text-center">
              <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-eye text-white text-2xl"></i>
              </div>
              <h3 class="text-xl font-bold text-white mb-3">تأجير عين جديدة</h3>
              <p class="text-white/70 mb-6">تأجير عين في إحدى الطبليات المتاحة في الأقسام</p>
              <button class="btn btn-secondary w-full" onclick="showSection('rent-eye')">
                <i class="fas fa-plus-circle"></i>
                تأجير عين
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Add Supplier Section -->
      <section id="add-supplier" class="section-content">
        <div class="card">
          <div class="p-8">
            <div class="flex items-center justify-between mb-8">
              <h2 class="text-3xl font-bold text-white">إضافة مورد جديد</h2>
              <button class="btn btn-secondary" onclick="showSection('suppliers-list')">
                <i class="fas fa-arrow-right"></i>
                العودة للقائمة
              </button>
            </div>

            <form class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="form-group">
                <label class="form-label">اسم المورد *</label>
                <input type="text" class="form-input" placeholder="أدخل اسم المورد" required>
              </div>

              <div class="form-group">
                <label class="form-label">فئة المورد *</label>
                <select class="form-input" required>
                  <option value="">اختر الفئة</option>
                  <option value="spices">بهارات</option>
                  <option value="consumer">استهلاكي</option>
                  <option value="cheese">أجبان</option>
                </select>
              </div>

              <div class="form-group">
                <label class="form-label">رقم الهاتف *</label>
                <input type="tel" class="form-input" placeholder="+965-12345678" required>
              </div>

              <div class="form-group">
                <label class="form-label">البريد الإلكتروني</label>
                <input type="email" class="form-input" placeholder="<EMAIL>">
              </div>

              <div class="form-group md:col-span-2">
                <label class="form-label">العنوان</label>
                <input type="text" class="form-input" placeholder="عنوان المورد">
              </div>

              <div class="form-group md:col-span-2">
                <label class="form-label">ملاحظات</label>
                <textarea class="form-input" rows="4" placeholder="ملاحظات إضافية"></textarea>
              </div>

              <div class="md:col-span-2 flex gap-4">
                <button type="submit" class="btn btn-primary flex-1">
                  <i class="fas fa-save"></i>
                  حفظ المورد
                </button>
                <button type="reset" class="btn btn-secondary">
                  <i class="fas fa-undo"></i>
                  إعادة تعيين
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>

      <!-- New Contract Section -->
      <section id="new-contract" class="section-content">
        <div class="card">
          <div class="p-8">
            <div class="flex items-center justify-between mb-8">
              <h2 class="text-3xl font-bold text-white">إنشاء عقد جديد</h2>
              <button class="btn btn-secondary" onclick="showSection('contracts-list')">
                <i class="fas fa-arrow-right"></i>
                العودة للقائمة
              </button>
            </div>

            <form class="space-y-8">
              <!-- Contract Basic Info -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                  <label class="form-label">رقم العقد *</label>
                  <input type="text" class="form-input" placeholder="CNT-2024-001" required>
                </div>

                <div class="form-group">
                  <label class="form-label">نوع العقد *</label>
                  <select class="form-input" required>
                    <option value="">اختر نوع العقد</option>
                    <option value="supply">توريد</option>
                    <option value="rental">إيجار</option>
                    <option value="service">خدمات</option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="form-label">المورد *</label>
                  <select class="form-input" required>
                    <option value="">اختر المورد</option>
                    <option value="1">شركة البهارات الذهبية</option>
                    <option value="2">مؤسسة المواد الاستهلاكية</option>
                    <option value="3">شركة الأجبان الطازجة</option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="form-label">المبلغ الإجمالي (د.ك) *</label>
                  <input type="number" class="form-input" placeholder="0.000" step="0.001" required>
                </div>

                <div class="form-group">
                  <label class="form-label">تاريخ البداية *</label>
                  <input type="date" class="form-input date-picker" required>
                </div>

                <div class="form-group">
                  <label class="form-label">تاريخ النهاية *</label>
                  <input type="date" class="form-input date-picker" required>
                </div>
              </div>

              <!-- Contract Details -->
              <div class="form-group">
                <label class="form-label">وصف العقد</label>
                <textarea class="form-input" rows="4" placeholder="تفاصيل العقد والشروط"></textarea>
              </div>

              <!-- Eyes Rental Section (if rental type) -->
              <div id="eyes-section" class="hidden">
                <h3 class="text-xl font-bold text-white mb-4">تفاصيل إيجار العيون</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div class="form-group">
                    <label class="form-label">القسم</label>
                    <select class="form-input">
                      <option value="">اختر القسم</option>
                      <option value="spices">قسم البهارات</option>
                      <option value="consumer">قسم الاستهلاكي</option>
                      <option value="cheese">قسم الأجبان</option>
                    </select>
                  </div>

                  <div class="form-group">
                    <label class="form-label">عدد العيون</label>
                    <input type="number" class="form-input" placeholder="1" min="1" max="4">
                  </div>

                  <div class="form-group">
                    <label class="form-label">الإيجار الشهري (د.ك)</label>
                    <input type="number" class="form-input" placeholder="60.000" step="0.001" readonly>
                  </div>
                </div>
              </div>

              <div class="flex gap-4">
                <button type="submit" class="btn btn-primary flex-1">
                  <i class="fas fa-save"></i>
                  إنشاء العقد
                </button>
                <button type="reset" class="btn btn-secondary">
                  <i class="fas fa-undo"></i>
                  إعادة تعيين
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>

      <!-- Rent Eye Section -->
      <section id="rent-eye" class="section-content">
        <div class="card">
          <div class="p-8">
            <div class="flex items-center justify-between mb-8">
              <h2 class="text-3xl font-bold text-white">تأجير عين جديدة</h2>
              <button class="btn btn-secondary" onclick="showSection('eyes-management')">
                <i class="fas fa-arrow-right"></i>
                العودة لإدارة العيون
              </button>
            </div>

            <!-- Available Eyes Grid -->
            <div class="mb-8">
              <h3 class="text-xl font-bold text-white mb-4">العيون المتاحة</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Spices Section -->
                <div class="card">
                  <div class="p-6">
                    <div class="flex items-center gap-3 mb-4">
                      <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-pepper-hot text-white"></i>
                      </div>
                      <div>
                        <h4 class="text-white font-bold">قسم البهارات</h4>
                        <p class="text-white/60 text-sm">12 عين متاحة</p>
                      </div>
                    </div>

                    <div class="grid grid-cols-4 gap-2 mb-4">
                      <div class="w-12 h-12 bg-green-500/20 border-2 border-green-500 rounded-lg flex items-center justify-center cursor-pointer hover:bg-green-500/30 transition-all">
                        <span class="text-green-400 text-xs font-bold">1</span>
                      </div>
                      <div class="w-12 h-12 bg-green-500/20 border-2 border-green-500 rounded-lg flex items-center justify-center cursor-pointer hover:bg-green-500/30 transition-all">
                        <span class="text-green-400 text-xs font-bold">2</span>
                      </div>
                      <div class="w-12 h-12 bg-red-500/20 border-2 border-red-500 rounded-lg flex items-center justify-center">
                        <span class="text-red-400 text-xs font-bold">3</span>
                      </div>
                      <div class="w-12 h-12 bg-green-500/20 border-2 border-green-500 rounded-lg flex items-center justify-center cursor-pointer hover:bg-green-500/30 transition-all">
                        <span class="text-green-400 text-xs font-bold">4</span>
                      </div>
                    </div>

                    <p class="text-white/70 text-sm">60 د.ك / عين / شهر</p>
                  </div>
                </div>

                <!-- Consumer Section -->
                <div class="card">
                  <div class="p-6">
                    <div class="flex items-center gap-3 mb-4">
                      <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-shopping-cart text-white"></i>
                      </div>
                      <div>
                        <h4 class="text-white font-bold">قسم الاستهلاكي</h4>
                        <p class="text-white/60 text-sm">8 عين متاحة</p>
                      </div>
                    </div>

                    <div class="grid grid-cols-4 gap-2 mb-4">
                      <div class="w-12 h-12 bg-red-500/20 border-2 border-red-500 rounded-lg flex items-center justify-center">
                        <span class="text-red-400 text-xs font-bold">1</span>
                      </div>
                      <div class="w-12 h-12 bg-green-500/20 border-2 border-green-500 rounded-lg flex items-center justify-center cursor-pointer hover:bg-green-500/30 transition-all">
                        <span class="text-green-400 text-xs font-bold">2</span>
                      </div>
                      <div class="w-12 h-12 bg-green-500/20 border-2 border-green-500 rounded-lg flex items-center justify-center cursor-pointer hover:bg-green-500/30 transition-all">
                        <span class="text-green-400 text-xs font-bold">3</span>
                      </div>
                      <div class="w-12 h-12 bg-green-500/20 border-2 border-green-500 rounded-lg flex items-center justify-center cursor-pointer hover:bg-green-500/30 transition-all">
                        <span class="text-green-400 text-xs font-bold">4</span>
                      </div>
                    </div>

                    <p class="text-white/70 text-sm">60 د.ك / عين / شهر</p>
                  </div>
                </div>

                <!-- Cheese Section -->
                <div class="card">
                  <div class="p-6">
                    <div class="flex items-center gap-3 mb-4">
                      <div class="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-cheese text-white"></i>
                      </div>
                      <div>
                        <h4 class="text-white font-bold">قسم الأجبان</h4>
                        <p class="text-white/60 text-sm">16 عين متاحة</p>
                      </div>
                    </div>

                    <div class="grid grid-cols-4 gap-2 mb-4">
                      <div class="w-12 h-12 bg-green-500/20 border-2 border-green-500 rounded-lg flex items-center justify-center cursor-pointer hover:bg-green-500/30 transition-all">
                        <span class="text-green-400 text-xs font-bold">1</span>
                      </div>
                      <div class="w-12 h-12 bg-green-500/20 border-2 border-green-500 rounded-lg flex items-center justify-center cursor-pointer hover:bg-green-500/30 transition-all">
                        <span class="text-green-400 text-xs font-bold">2</span>
                      </div>
                      <div class="w-12 h-12 bg-green-500/20 border-2 border-green-500 rounded-lg flex items-center justify-center cursor-pointer hover:bg-green-500/30 transition-all">
                        <span class="text-green-400 text-xs font-bold">3</span>
                      </div>
                      <div class="w-12 h-12 bg-green-500/20 border-2 border-green-500 rounded-lg flex items-center justify-center cursor-pointer hover:bg-green-500/30 transition-all">
                        <span class="text-green-400 text-xs font-bold">4</span>
                      </div>
                    </div>

                    <p class="text-white/70 text-sm">60 د.ك / عين / شهر</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </main>

  <!-- JavaScript -->
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
  <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
  <script>
    // Navigation functionality
    function showSection(sectionId) {
      // Hide all sections
      document.querySelectorAll('.section-content').forEach(section => {
        section.classList.remove('active');
        section.style.display = 'none';
      });

      // Show target section
      const targetSection = document.getElementById(sectionId);
      if (targetSection) {
        targetSection.style.display = 'block';
        setTimeout(() => {
          targetSection.classList.add('active');
        }, 10);
      }

      // Update navigation
      document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
      });

      // Close mobile sidebar
      toggleSidebar(false);

      // Scroll to top
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    // Sidebar toggle
    function toggleSidebar(force = null) {
      const sidebar = document.getElementById('mobileSidebar');
      const overlay = document.querySelector('.sidebar-overlay');

      if (force === false) {
        sidebar.classList.remove('active');
        overlay.classList.remove('active');
      } else if (force === true) {
        sidebar.classList.add('active');
        overlay.classList.add('active');
      } else {
        sidebar.classList.toggle('active');
        overlay.classList.toggle('active');
      }
    }

    // Scroll animations
    function revealOnScroll() {
      const reveals = document.querySelectorAll('.scroll-reveal');

      reveals.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;

        if (elementTop < window.innerHeight - elementVisible) {
          element.classList.add('revealed');
        }
      });
    }

    // Navbar scroll effect
    function handleNavbarScroll() {
      const navbar = document.querySelector('.navbar');
      if (window.scrollY > 50) {
        navbar.classList.add('scrolled');
      } else {
        navbar.classList.remove('scrolled');
      }
    }

    // Eye selection functionality
    function selectEye(element, section, eyeNumber) {
      // Remove selection from other eyes in the same section
      const sectionElement = element.closest('.card');
      sectionElement.querySelectorAll('.w-12.h-12').forEach(eye => {
        if (eye.classList.contains('bg-green-500/20')) {
          eye.classList.remove('ring-4', 'ring-blue-500', 'bg-blue-500/30');
        }
      });

      // Add selection to clicked eye
      if (element.classList.contains('bg-green-500/20')) {
        element.classList.add('ring-4', 'ring-blue-500', 'bg-blue-500/30');

        // Show rental form
        showRentalForm(section, eyeNumber);
      }
    }

    // Show rental form
    function showRentalForm(section, eyeNumber) {
      const formHtml = `
        <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50" id="rentalModal">
          <div class="bg-white/10 backdrop-blur-20 border border-white/20 rounded-2xl p-8 max-w-md w-full mx-4">
            <h3 class="text-xl font-bold text-white mb-6">تأجير عين - ${section} (العين ${eyeNumber})</h3>

            <form class="space-y-4">
              <div class="form-group">
                <label class="form-label">اسم المستأجر *</label>
                <input type="text" class="form-input" placeholder="أدخل اسم المستأجر" required>
              </div>

              <div class="form-group">
                <label class="form-label">رقم الهاتف *</label>
                <input type="tel" class="form-input" placeholder="+965-12345678" required>
              </div>

              <div class="form-group">
                <label class="form-label">مدة الإيجار (شهر) *</label>
                <input type="number" class="form-input" placeholder="12" min="1" required>
              </div>

              <div class="form-group">
                <label class="form-label">تاريخ البداية *</label>
                <input type="date" class="form-input" required>
              </div>

              <div class="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4 mb-4">
                <div class="flex justify-between items-center">
                  <span class="text-white">الإيجار الشهري:</span>
                  <span class="text-blue-400 font-bold">60.000 د.ك</span>
                </div>
              </div>

              <div class="flex gap-3">
                <button type="submit" class="btn btn-primary flex-1">
                  <i class="fas fa-check"></i>
                  تأكيد الإيجار
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeRentalModal()">
                  <i class="fas fa-times"></i>
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      `;

      document.body.insertAdjacentHTML('beforeend', formHtml);
    }

    // Close rental modal
    function closeRentalModal() {
      const modal = document.getElementById('rentalModal');
      if (modal) {
        modal.remove();
      }
    }

    // Contract type change handler
    function handleContractTypeChange(select) {
      const eyesSection = document.getElementById('eyes-section');
      if (select.value === 'rental') {
        eyesSection.classList.remove('hidden');
      } else {
        eyesSection.classList.add('hidden');
      }
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
      // Show dashboard by default
      showSection('dashboard');

      // Initialize scroll animations
      revealOnScroll();

      // Initialize date pickers
      flatpickr('.date-picker', {
        locale: 'ar',
        dateFormat: 'Y-m-d'
      });

      // Add event listeners
      window.addEventListener('scroll', () => {
        revealOnScroll();
        handleNavbarScroll();
      });

      // Navigation click handlers
      document.querySelectorAll('a[href^="#"]').forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          const targetId = this.getAttribute('href').substring(1);
          if (targetId) {
            showSection(targetId);
          }
        });
      });

      // Close dropdowns when clicking outside
      document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
          document.querySelectorAll('.dropdown').forEach(dropdown => {
            dropdown.classList.remove('active');
          });
        }
      });

      // Dropdown hover/click handlers
      document.querySelectorAll('.dropdown').forEach(dropdown => {
        dropdown.addEventListener('mouseenter', function() {
          this.classList.add('active');
        });

        dropdown.addEventListener('mouseleave', function() {
          this.classList.remove('active');
        });
      });

      // Eye click handlers
      document.addEventListener('click', function(e) {
        if (e.target.closest('.w-12.h-12.bg-green-500\\/20')) {
          const eyeElement = e.target.closest('.w-12.h-12.bg-green-500\\/20');
          const section = eyeElement.closest('.card').querySelector('h4').textContent;
          const eyeNumber = eyeElement.querySelector('span').textContent;
          selectEye(eyeElement, section, eyeNumber);
        }
      });

      // Contract type change handler
      document.addEventListener('change', function(e) {
        if (e.target.matches('select') && e.target.closest('#new-contract')) {
          const contractTypeSelect = document.querySelector('#new-contract select');
          if (e.target === contractTypeSelect) {
            handleContractTypeChange(e.target);
          }
        }
      });

      // Form submissions
      document.addEventListener('submit', function(e) {
        e.preventDefault();

        const form = e.target;
        const formData = new FormData(form);

        // Show loading
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
        submitBtn.disabled = true;

        // Simulate API call
        setTimeout(() => {
          submitBtn.innerHTML = originalText;
          submitBtn.disabled = false;

          // Show success message
          showToast('تم الحفظ بنجاح!', 'success');

          // Reset form
          form.reset();

          // Close modal if exists
          const modal = document.getElementById('rentalModal');
          if (modal) {
            modal.remove();
          }
        }, 2000);
      });
    });

    // Loading states
    function showLoading(element) {
      element.classList.add('loading');
    }

    function hideLoading(element) {
      element.classList.remove('loading');
    }

    // Toast notifications
    function showToast(message, type = 'info') {
      const toast = document.createElement('div');
      toast.className = `fixed top-4 left-4 z-50 p-4 rounded-lg text-white max-w-sm transform translate-x-full transition-transform duration-300`;

      switch(type) {
        case 'success':
          toast.classList.add('bg-green-500');
          break;
        case 'error':
          toast.classList.add('bg-red-500');
          break;
        case 'warning':
          toast.classList.add('bg-yellow-500');
          break;
        default:
          toast.classList.add('bg-blue-500');
      }

      toast.innerHTML = `
        <div class="flex items-center gap-3">
          <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : type === 'warning' ? 'exclamation' : 'info'}-circle"></i>
          <span>${message}</span>
          <button onclick="this.parentElement.parentElement.remove()" class="mr-auto">
            <i class="fas fa-times"></i>
          </button>
        </div>
      `;

      document.body.appendChild(toast);

      setTimeout(() => {
        toast.classList.remove('translate-x-full');
      }, 100);

      setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => toast.remove(), 300);
      }, 5000);
    }

    // Search functionality
    function handleSearch(input) {
      const searchTerm = input.value.toLowerCase();
      const table = input.closest('.card').querySelector('table tbody');

      if (table) {
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => {
          const text = row.textContent.toLowerCase();
          if (text.includes(searchTerm)) {
            row.style.display = '';
          } else {
            row.style.display = 'none';
          }
        });
      }
    }

    // Filter functionality
    function handleFilter(select) {
      const filterValue = select.value.toLowerCase();
      const table = select.closest('.card').querySelector('table tbody');

      if (table) {
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => {
          if (!filterValue || row.textContent.toLowerCase().includes(filterValue)) {
            row.style.display = '';
          } else {
            row.style.display = 'none';
          }
        });
      }
    }
  </script>

  <style>
    .section-content {
      display: none;
      opacity: 0;
      transform: translateY(20px);
      transition: all 0.3s ease;
    }

    .section-content.active {
      opacity: 1;
      transform: translateY(0);
    }

    /* Custom scrollbar */
    ::-webkit-scrollbar {
      width: 8px;
    }

    ::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  </style>
