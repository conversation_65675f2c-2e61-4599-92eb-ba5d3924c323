const fs = require('fs');
const path = require('path');

// إنشاء أيقونة SVG بسيطة
const iconSVG = `
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- خلفية دائرية -->
  <circle cx="128" cy="128" r="120" fill="url(#grad1)" stroke="#ffffff" stroke-width="4"/>
  
  <!-- أيقونة المبنى -->
  <rect x="80" y="100" width="96" height="120" fill="#ffffff" rx="8"/>
  <rect x="88" y="108" width="80" height="104" fill="url(#grad1)" rx="4"/>
  
  <!-- نوافذ المبنى -->
  <rect x="96" y="120" width="16" height="16" fill="#ffffff" rx="2"/>
  <rect x="120" y="120" width="16" height="16" fill="#ffffff" rx="2"/>
  <rect x="144" y="120" width="16" height="16" fill="#ffffff" rx="2"/>
  
  <rect x="96" y="144" width="16" height="16" fill="#ffffff" rx="2"/>
  <rect x="120" y="144" width="16" height="16" fill="#ffffff" rx="2"/>
  <rect x="144" y="144" width="16" height="16" fill="#ffffff" rx="2"/>
  
  <rect x="96" y="168" width="16" height="16" fill="#ffffff" rx="2"/>
  <rect x="120" y="168" width="16" height="16" fill="#ffffff" rx="2"/>
  <rect x="144" y="168" width="16" height="16" fill="#ffffff" rx="2"/>
  
  <!-- باب المدخل -->
  <rect x="116" y="188" width="24" height="32" fill="#ffffff" rx="12"/>
  <circle cx="134" cy="204" r="2" fill="url(#grad1)"/>
  
  <!-- نص عربي -->
  <text x="128" y="50" font-family="Arial" font-size="20" font-weight="bold" text-anchor="middle" fill="#ffffff">المنقف</text>
  <text x="128" y="75" font-family="Arial" font-size="14" text-anchor="middle" fill="#ffffff">التعاونية</text>
</svg>
`;

// حفظ ملف SVG
const assetsDir = path.join(__dirname);
if (!fs.existsSync(assetsDir)) {
    fs.mkdirSync(assetsDir, { recursive: true });
}

fs.writeFileSync(path.join(assetsDir, 'icon.svg'), iconSVG);

// إنشاء ملف PNG بسيط (placeholder)
// في الواقع، ستحتاج لاستخدام مكتبة لتحويل SVG إلى PNG
const iconPNG = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x20,
    0x08, 0x06, 0x00, 0x00, 0x00, 0x73, 0x7A, 0x7A, 0xF4, 0x00, 0x00, 0x00,
    0x19, 0x74, 0x45, 0x58, 0x74, 0x53, 0x6F, 0x66, 0x74, 0x77, 0x61, 0x72,
    0x65, 0x00, 0x41, 0x64, 0x6F, 0x62, 0x65, 0x20, 0x49, 0x6D, 0x61, 0x67,
    0x65, 0x52, 0x65, 0x61, 0x64, 0x79, 0x71, 0xC9, 0x65, 0x3C, 0x00, 0x00,
    0x00, 0x0C, 0x49, 0x44, 0x41, 0x54, 0x78, 0xDA, 0x62, 0x00, 0x02, 0x00,
    0x00, 0x05, 0x00, 0x01, 0xE2, 0x26, 0x05, 0x9B, 0x00, 0x00, 0x00, 0x00,
    0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
]);

fs.writeFileSync(path.join(assetsDir, 'icon.png'), iconPNG);

console.log('✅ تم إنشاء ملفات الأيقونات:');
console.log('   📄 icon.svg');
console.log('   🖼️ icon.png');
console.log('');
console.log('💡 ملاحظة: لإنشاء أيقونات عالية الجودة، استخدم أدوات تصميم متخصصة');
console.log('   مثل Adobe Illustrator أو Figma لإنشاء أيقونات بدقة عالية');
console.log('   وحفظها بصيغ مختلفة (PNG, ICO, ICNS)');
