# 🧪 دليل اختبار نظام رفع ملفات الموردين
## جمعية المنقف التعاونية

---

## 🚀 البدء السريع

### الطريقة الأسرع - اختبار تفاعلي:
```bash
npm run test:interactive
```

### اختبار شامل:
```bash
npm run test:upload
```

### اختبار يدوي:
```bash
npm start
# ثم افتح: http://localhost:3000/quick-upload-test.html
```

---

## 🎯 أنواع الاختبارات

### 1. 🔧 **الاختبار التفاعلي** (`npm run test:interactive`)

واجهة تفاعلية تتيح لك اختيار نوع الاختبار:

- **اختبار شامل للنظام**: فحص جميع المكونات
- **اختبار سريع للملفات**: فحص الملفات الأساسية فقط
- **تشغيل الخادم واختبار يدوي**: تشغيل النظام وفتح صفحات الاختبار
- **إنشاء ملفات اختبار**: إنشاء ملفات Excel و CSV للاختبار
- **فتح صفحة الاختبار**: فتح صفحة الاختبار في المتصفح
- **عرض حالة النظام**: عرض حالة جميع المكونات

### 2. 🔍 **الاختبار الشامل** (`npm run test:upload`)

يفحص:
- ✅ وجود جميع الملفات المطلوبة
- ✅ التبعيات في package.json
- ✅ تثبيت التبعيات في node_modules
- ✅ بنية المجلدات
- ✅ ملف الخادم وإعداداته
- ✅ إنشاء ملفات اختبار
- ✅ تشغيل الخادم

### 3. 🌐 **الاختبار اليدوي**

استخدام الواجهات المرئية:
- **صفحة الاختبار السريع**: `quick-upload-test.html`
- **صفحة رفع الملفات**: `upload_suppliers.html`

---

## 📋 خطوات الاختبار الموصى بها

### المرحلة الأولى - التحقق من النظام:

1. **تشغيل الاختبار التفاعلي:**
   ```bash
   npm run test:interactive
   ```

2. **اختيار "اختبار شامل للنظام"**

3. **مراجعة النتائج:**
   - إذا نجحت جميع الاختبارات ✅ انتقل للمرحلة الثانية
   - إذا فشلت بعض الاختبارات ❌ راجع الأخطاء وأصلحها

### المرحلة الثانية - الاختبار اليدوي:

1. **تشغيل النظام:**
   ```bash
   npm start
   ```

2. **فتح صفحة الاختبار السريع:**
   ```
   http://localhost:3000/quick-upload-test.html
   ```

3. **تنفيذ الاختبارات:**
   - انقر على "اختبار الخادم" ✅
   - انقر على "اختبار التحميل" ✅
   - انقر على "إنشاء ملف تجريبي" ✅

### المرحلة الثالثة - اختبار الرفع:

1. **فتح صفحة الرفع:**
   ```
   http://localhost:3000/upload_suppliers.html
   ```

2. **تحميل النموذج:**
   - انقر على "تحميل النموذج"
   - تأكد من تحميل ملف Excel

3. **رفع ملف تجريبي:**
   - استخدم الملف المُنشأ من الخطوة السابقة
   - اسحب الملف إلى منطقة الرفع
   - انقر على "رفع الملفات"

4. **مراجعة النتائج:**
   - تحقق من الإحصائيات
   - راجع الأخطاء إن وجدت
   - حمل تقرير الأخطاء

### المرحلة الرابعة - التحقق من البيانات:

1. **فتح قائمة الموردين:**
   ```
   http://localhost:3000/enhanced_web_interface.html
   ```

2. **التحقق من الموردين الجدد:**
   - ابحث عن الموردين المضافين
   - تأكد من صحة البيانات
   - راجع الفئات والمعلومات

---

## 🔧 حل المشاكل الشائعة

### ❌ "node_modules غير موجود"
```bash
npm install
```

### ❌ "ملف الخادم لا يعمل"
```bash
# تحقق من وجود الملف
ls backend/api/server.js

# تحقق من صحة الكود
node -c backend/api/server.js
```

### ❌ "التبعيات مفقودة"
```bash
# تثبيت التبعيات المطلوبة
npm install multer xlsx csv-parser
```

### ❌ "الخادم لا يبدأ"
```bash
# تحقق من المنفذ
netstat -an | findstr :3000

# جرب منفذ آخر
PORT=3001 npm start
```

### ❌ "صفحة الاختبار لا تفتح"
```bash
# تأكد من تشغيل الخادم أولاً
npm start

# افتح الصفحة يدوياً
start http://localhost:3000/quick-upload-test.html
```

---

## 📊 فهم نتائج الاختبار

### 🟢 **نجح (✅)**
- المكون يعمل بشكل صحيح
- لا حاجة لإجراءات إضافية

### 🔴 **فشل (❌)**
- المكون لا يعمل أو مفقود
- يحتاج إصلاح فوري

### 🟡 **تحذير (⚠️)**
- المكون يعمل جزئياً
- قد يحتاج تحسين

### 🔵 **معلومات (💡)**
- نصائح وإرشادات
- خطوات إضافية موصى بها

---

## 📁 ملفات الاختبار المُنشأة

### `test-suppliers.csv`
ملف CSV يحتوي على:
- 3 موردين صحيحين
- 1 مورد خاطئ للاختبار

### `test-suppliers.xlsx`
ملف Excel بنفس البيانات مع تنسيق محسن

### `test-report.json`
تقرير مفصل بنتائج الاختبار:
```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "totalTests": 7,
  "passedTests": 6,
  "failedTests": 1,
  "successRate": 86
}
```

---

## 🎯 معايير النجاح

### ✅ **نجاح كامل (100%)**
- جميع الاختبارات نجحت
- النظام جاهز للإنتاج

### ✅ **نجاح جيد (80-99%)**
- معظم الاختبارات نجحت
- النظام يعمل مع تحذيرات بسيطة

### ⚠️ **نجاح جزئي (60-79%)**
- النظام يعمل جزئياً
- يحتاج إصلاحات

### ❌ **فشل (أقل من 60%)**
- النظام لا يعمل بشكل صحيح
- يحتاج مراجعة شاملة

---

## 🚀 اختبارات الأداء

### قياس سرعة الرفع:
- **ملف صغير (< 1 ميجا)**: أقل من 5 ثوان
- **ملف متوسط (1-5 ميجا)**: 5-15 ثانية
- **ملف كبير (5-10 ميجا)**: 15-30 ثانية

### قياس معالجة البيانات:
- **100 صف**: أقل من ثانيتين
- **500 صف**: أقل من 10 ثوان
- **1000 صف**: أقل من 20 ثانية

---

## 🔒 اختبارات الأمان

### التحقق من:
- ✅ فلترة أنواع الملفات
- ✅ حدود حجم الملفات
- ✅ تشفير البيانات الحساسة
- ✅ حذف الملفات المؤقتة
- ✅ التحقق من صحة البيانات

---

## 📱 اختبار التوافق

### المتصفحات المدعومة:
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

### أنظمة التشغيل:
- ✅ Windows 10/11
- ✅ macOS 10.15+
- ✅ Ubuntu 20.04+

---

## 📞 الحصول على المساعدة

### إذا فشلت الاختبارات:

1. **راجع سجل الأخطاء** في وحدة التحكم
2. **تحقق من تقرير الاختبار** في `test-report.json`
3. **جرب الاختبار التفاعلي** لتشخيص دقيق
4. **راجع الوثائق** في `UPLOAD_GUIDE.md`

### للدعم التقني:
- 📧 **البريد الإلكتروني**: <EMAIL>
- 📱 **الهاتف**: +965-1234567
- 🌐 **الموقع**: https://manqaf-coop.com

---

## 🎉 بعد نجاح الاختبارات

### الخطوات التالية:
1. **🚀 تشغيل النظام**: `npm start`
2. **📁 رفع ملفات حقيقية** للموردين
3. **👥 تدريب المستخدمين** على النظام
4. **📊 مراقبة الأداء** والاستخدام
5. **🔄 النسخ الاحتياطي** المنتظم

---

**تم إعداد هذا الدليل بـ ❤️ لجمعية المنقف التعاونية**

**للمزيد من المساعدة، راجع الوثائق الأخرى أو اتصل بفريق الدعم**
