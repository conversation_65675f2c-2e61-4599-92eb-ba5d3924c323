/**
 * وحدة API للتعامل مع الخادم
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 */

class APIClient {
  constructor(baseURL = '/api/v1') {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
  }

  /**
   * إرسال طلب HTTP
   * @param {string} method - نوع الطلب (GET, POST, PUT, DELETE)
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} data - البيانات المرسلة
   * @param {Object} options - خيارات إضافية
   */
  async request(method, endpoint, data = null, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    
    const config = {
      method: method.toUpperCase(),
      headers: {
        ...this.defaultHeaders,
        ...options.headers
      },
      credentials: 'include', // لإرسال الكوكيز
      ...options
    };

    // إضافة البيانات للطلبات التي تدعمها
    if (data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
      if (data instanceof FormData) {
        // إزالة Content-Type للـ FormData (سيتم تعيينه تلقائياً)
        delete config.headers['Content-Type'];
        config.body = data;
      } else {
        config.body = JSON.stringify(data);
      }
    }

    try {
      console.log(`📡 ${method.toUpperCase()} ${url}`, data);
      
      const response = await fetch(url, config);
      
      // التحقق من نوع المحتوى
      const contentType = response.headers.get('content-type');
      let responseData;
      
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      // تسجيل الاستجابة
      console.log(`📨 ${response.status} ${url}`, responseData);

      // التعامل مع أخطاء HTTP
      if (!response.ok) {
        const error = new Error(responseData.message || `HTTP Error: ${response.status}`);
        error.status = response.status;
        error.data = responseData;
        throw error;
      }

      return responseData;

    } catch (error) {
      console.error(`❌ خطأ في API ${method.toUpperCase()} ${url}:`, error);
      
      // التعامل مع أخطاء الشبكة
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new Error('خطأ في الاتصال بالخادم. تحقق من اتصال الإنترنت.');
      }
      
      // التعامل مع أخطاء المصادقة
      if (error.status === 401) {
        this.handleUnauthorized();
      }
      
      throw error;
    }
  }

  /**
   * طلب GET
   */
  async get(endpoint, params = {}, options = {}) {
    // إضافة المعاملات إلى URL
    const url = new URL(endpoint, window.location.origin + this.baseURL);
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined) {
        url.searchParams.append(key, params[key]);
      }
    });
    
    return this.request('GET', url.pathname + url.search, null, options);
  }

  /**
   * طلب POST
   */
  async post(endpoint, data = {}, options = {}) {
    return this.request('POST', endpoint, data, options);
  }

  /**
   * طلب PUT
   */
  async put(endpoint, data = {}, options = {}) {
    return this.request('PUT', endpoint, data, options);
  }

  /**
   * طلب PATCH
   */
  async patch(endpoint, data = {}, options = {}) {
    return this.request('PATCH', endpoint, data, options);
  }

  /**
   * طلب DELETE
   */
  async delete(endpoint, options = {}) {
    return this.request('DELETE', endpoint, null, options);
  }

  /**
   * رفع ملف
   */
  async upload(endpoint, file, additionalData = {}, onProgress = null) {
    const formData = new FormData();
    formData.append('file', file);
    
    // إضافة البيانات الإضافية
    Object.keys(additionalData).forEach(key => {
      formData.append(key, additionalData[key]);
    });

    const options = {};
    
    // إضافة مراقب التقدم إذا كان متوفراً
    if (onProgress && typeof onProgress === 'function') {
      options.onUploadProgress = onProgress;
    }

    return this.request('POST', endpoint, formData, options);
  }

  /**
   * تحميل ملف
   */
  async download(endpoint, filename = null) {
    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'GET',
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP Error: ${response.status}`);
      }

      const blob = await response.blob();
      
      // إنشاء رابط التحميل
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // تحديد اسم الملف
      if (filename) {
        link.download = filename;
      } else {
        // محاولة الحصول على اسم الملف من الرأس
        const contentDisposition = response.headers.get('content-disposition');
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="(.+)"/);
          if (filenameMatch) {
            link.download = filenameMatch[1];
          }
        }
      }
      
      // تنفيذ التحميل
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // تنظيف الذاكرة
      window.URL.revokeObjectURL(url);
      
      return true;

    } catch (error) {
      console.error('خطأ في تحميل الملف:', error);
      throw error;
    }
  }

  /**
   * معالجة عدم التفويض
   */
  handleUnauthorized() {
    console.warn('انتهت صلاحية الجلسة');
    
    // مسح بيانات المستخدم
    if (window.APP) {
      window.APP.currentUser = null;
      window.APP.updateUserInterface(false);
    }
    
    // إظهار رسالة وإعادة توجيه لتسجيل الدخول
    if (window.APP && window.APP.showToast) {
      window.APP.showToast('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.', 'warning');
    }
    
    // إظهار نافذة تسجيل الدخول بعد ثانيتين
    setTimeout(() => {
      if (window.APP && window.APP.showLoginModal) {
        window.APP.showLoginModal();
      }
    }, 2000);
  }

  /**
   * تعيين رأس مخصص
   */
  setHeader(name, value) {
    this.defaultHeaders[name] = value;
  }

  /**
   * إزالة رأس
   */
  removeHeader(name) {
    delete this.defaultHeaders[name];
  }

  /**
   * تعيين رمز المصادقة
   */
  setAuthToken(token) {
    this.setHeader('Authorization', `Bearer ${token}`);
  }

  /**
   * إزالة رمز المصادقة
   */
  removeAuthToken() {
    this.removeHeader('Authorization');
  }
}

// إنشاء مثيل API عام
const API = new APIClient();

// دوال مساعدة للعمليات الشائعة
const APIHelpers = {
  
  /**
   * جلب قائمة العقود مع الفلترة والترقيم
   */
  async getContracts(filters = {}) {
    const params = {
      page: filters.page || 1,
      limit: filters.limit || 10,
      search: filters.search || '',
      status: filters.status || '',
      supplier_id: filters.supplier_id || '',
      start_date: filters.start_date || '',
      end_date: filters.end_date || '',
      sort_by: filters.sort_by || 'created_at',
      sort_order: filters.sort_order || 'DESC'
    };
    
    return API.get('/contracts', params);
  },

  /**
   * جلب تفاصيل عقد محدد
   */
  async getContract(id) {
    return API.get(`/contracts/${id}`);
  },

  /**
   * إنشاء عقد جديد
   */
  async createContract(contractData) {
    return API.post('/contracts', contractData);
  },

  /**
   * تحديث عقد موجود
   */
  async updateContract(id, contractData) {
    return API.put(`/contracts/${id}`, contractData);
  },

  /**
   * حذف عقد
   */
  async deleteContract(id) {
    return API.delete(`/contracts/${id}`);
  },

  /**
   * جلب إحصائيات العقود
   */
  async getContractsStatistics() {
    return API.get('/contracts/statistics');
  },

  /**
   * جلب العقود المنتهية قريباً
   */
  async getExpiringContracts(days = 30) {
    return API.get('/contracts/expiring', { days });
  },

  /**
   * تجديد عقد
   */
  async renewContract(id, renewalData) {
    return API.post(`/contracts/${id}/renew`, renewalData);
  },

  /**
   * اعتماد عقد
   */
  async approveContract(id) {
    return API.post(`/contracts/${id}/approve`);
  },

  /**
   * جلب قائمة الموردين
   */
  async getSuppliers(filters = {}) {
    const params = {
      page: filters.page || 1,
      limit: filters.limit || 10,
      search: filters.search || '',
      category_id: filters.category_id || '',
      is_active: filters.is_active !== undefined ? filters.is_active : ''
    };
    
    return API.get('/suppliers', params);
  },

  /**
   * جلب فئات الموردين
   */
  async getSupplierCategories() {
    return API.get('/supplier-categories');
  },

  /**
   * جلب أنواع العقود
   */
  async getContractTypes() {
    return API.get('/contract-types');
  },

  /**
   * تسجيل الدخول
   */
  async login(credentials) {
    return API.post('/auth/login', credentials);
  },

  /**
   * تسجيل الخروج
   */
  async logout() {
    return API.post('/auth/logout');
  },

  /**
   * جلب بيانات المستخدم الحالي
   */
  async getCurrentUser() {
    return API.get('/auth/me');
  },

  /**
   * تجديد الرمز المميز
   */
  async refreshToken() {
    return API.post('/auth/refresh');
  },

  /**
   * رفع مرفق لعقد
   */
  async uploadContractAttachment(contractId, file, description = '') {
    return API.upload(`/contracts/${contractId}/attachments`, file, { description });
  },

  /**
   * تصدير العقود إلى Excel
   */
  async exportContractsToExcel(filters = {}) {
    const params = new URLSearchParams(filters).toString();
    return API.download(`/reports/contracts/excel?${params}`, 'contracts.xlsx');
  },

  /**
   * تصدير العقود إلى PDF
   */
  async exportContractsToPDF(filters = {}) {
    const params = new URLSearchParams(filters).toString();
    return API.download(`/reports/contracts/pdf?${params}`, 'contracts.pdf');
  }
};

// تصدير للاستخدام العام
window.API = API;
window.APIHelpers = APIHelpers;
