<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>واجهة الإدارة المتقدمة - نظام إدارة العقود | محمد مرزوق العقاب</title>
  
  <!-- Meta Tags -->
  <meta name="description" content="واجهة إدارة متقدمة لنظام عقود الموردين - تطوير محمد مرزوق العقاب">
  <meta name="author" content="محمد مرزوق العقاب">
  
  <!-- CSS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap');
    
    :root {
      /* High Contrast Professional Colors */
      --bg-primary: #0a0f1c;
      --bg-secondary: #1a2332;
      --bg-tertiary: #2a3441;
      --bg-card: #3a4553;
      
      --text-primary: #ffffff;
      --text-secondary: #f1f5f9;
      --text-tertiary: #e2e8f0;
      --text-muted: #94a3b8;
      
      --accent-blue: #3b82f6;
      --accent-green: #10b981;
      --accent-purple: #8b5cf6;
      --accent-orange: #f59e0b;
      --accent-red: #ef4444;
      --accent-cyan: #06b6d4;
      
      --border-primary: #475569;
      --border-accent: #64748b;
      
      --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
      --shadow-card: 0 10px 25px rgba(0, 0, 0, 0.3);
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Cairo', sans-serif;
      background: var(--bg-primary);
      color: var(--text-primary);
      min-height: 100vh;
      line-height: 1.6;
    }
    
    /* High Contrast Background */
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: 
        radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
      z-index: -1;
    }
    
    /* Professional Cards with High Contrast */
    .advanced-card {
      background: var(--bg-secondary);
      border: 2px solid var(--border-primary);
      border-radius: 20px;
      box-shadow: var(--shadow-card);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
    
    .advanced-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-green));
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    .advanced-card:hover {
      transform: translateY(-8px);
      box-shadow: var(--shadow-glow), var(--shadow-card);
      border-color: var(--accent-blue);
    }
    
    .advanced-card:hover::before {
      opacity: 1;
    }
    
    /* High Contrast Buttons */
    .advanced-btn {
      display: inline-flex;
      align-items: center;
      gap: 10px;
      padding: 14px 28px;
      border: 2px solid transparent;
      border-radius: 12px;
      font-weight: 700;
      font-size: 16px;
      text-decoration: none;
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      font-family: 'Cairo', sans-serif;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .advanced-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s;
    }
    
    .advanced-btn:hover::before {
      left: 100%;
    }
    
    .btn-primary {
      background: var(--accent-blue);
      color: var(--text-primary);
      box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    }
    
    .btn-primary:hover {
      background: #2563eb;
      transform: translateY(-3px);
      box-shadow: 0 10px 30px rgba(59, 130, 246, 0.6);
    }
    
    .btn-success {
      background: var(--accent-green);
      color: var(--text-primary);
      box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
    }
    
    .btn-success:hover {
      background: #059669;
      transform: translateY(-3px);
      box-shadow: 0 10px 30px rgba(16, 185, 129, 0.6);
    }
    
    .btn-warning {
      background: var(--accent-orange);
      color: var(--text-primary);
      box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
    }
    
    .btn-warning:hover {
      background: #d97706;
      transform: translateY(-3px);
      box-shadow: 0 10px 30px rgba(245, 158, 11, 0.6);
    }
    
    .btn-danger {
      background: var(--accent-red);
      color: var(--text-primary);
      box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
    }
    
    .btn-danger:hover {
      background: #dc2626;
      transform: translateY(-3px);
      box-shadow: 0 10px 30px rgba(239, 68, 68, 0.6);
    }
    
    .btn-outline {
      background: transparent;
      color: var(--text-secondary);
      border: 2px solid var(--border-accent);
    }
    
    .btn-outline:hover {
      background: var(--bg-tertiary);
      border-color: var(--accent-blue);
      color: var(--text-primary);
      box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
    }
    
    /* Advanced Navigation */
    .advanced-nav {
      background: rgba(26, 35, 50, 0.95);
      backdrop-filter: blur(20px);
      border-bottom: 2px solid var(--border-primary);
      box-shadow: var(--shadow-card);
    }
    
    .nav-item {
      position: relative;
      padding: 16px 24px;
      border-radius: 12px;
      transition: all 0.3s ease;
      color: var(--text-secondary);
      text-decoration: none;
      font-weight: 600;
      font-size: 16px;
    }
    
    .nav-item::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 0;
      height: 4px;
      background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
      transition: all 0.4s ease;
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    .nav-item:hover,
    .nav-item.active {
      color: var(--text-primary);
      background: rgba(59, 130, 246, 0.15);
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
    }
    
    .nav-item:hover::before,
    .nav-item.active::before {
      width: 90%;
    }
    
    /* Advanced Stats Cards */
    .stats-card {
      background: var(--bg-secondary);
      border: 2px solid var(--border-primary);
      border-radius: 24px;
      padding: 32px;
      text-align: center;
      transition: all 0.4s ease;
      position: relative;
      overflow: hidden;
      box-shadow: var(--shadow-card);
    }
    
    .stats-card::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: conic-gradient(from 0deg, transparent, rgba(59, 130, 246, 0.1), transparent);
      animation: rotate 10s linear infinite;
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    .stats-card:hover::before {
      opacity: 1;
    }
    
    @keyframes rotate {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .stats-icon {
      width: 90px;
      height: 90px;
      margin: 0 auto 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36px;
      color: var(--text-primary);
      position: relative;
      z-index: 1;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }
    
    .stats-number {
      font-size: 3.5rem;
      font-weight: 900;
      color: var(--text-primary);
      margin-bottom: 12px;
      position: relative;
      z-index: 1;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .stats-label {
      color: var(--text-secondary);
      font-size: 1.2rem;
      font-weight: 600;
      position: relative;
      z-index: 1;
    }
    
    .stats-trend {
      margin-top: 20px;
      padding: 10px 20px;
      border-radius: 25px;
      font-size: 1rem;
      font-weight: 700;
      position: relative;
      z-index: 1;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    /* High Contrast Badges */
    .badge {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      border-radius: 25px;
      font-size: 14px;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border: 2px solid;
    }
    
    .badge-success {
      background: rgba(16, 185, 129, 0.2);
      color: var(--accent-green);
      border-color: var(--accent-green);
      box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    }
    
    .badge-warning {
      background: rgba(245, 158, 11, 0.2);
      color: var(--accent-orange);
      border-color: var(--accent-orange);
      box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    }
    
    .badge-danger {
      background: rgba(239, 68, 68, 0.2);
      color: var(--accent-red);
      border-color: var(--accent-red);
      box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
    }
    
    .badge-info {
      background: rgba(59, 130, 246, 0.2);
      color: var(--accent-blue);
      border-color: var(--accent-blue);
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    }
    
    /* Advanced Forms */
    .form-group {
      position: relative;
      margin-bottom: 28px;
    }
    
    .form-input {
      width: 100%;
      padding: 18px 24px;
      border: 2px solid var(--border-primary);
      border-radius: 16px;
      background: var(--bg-tertiary);
      color: var(--text-primary);
      font-size: 16px;
      font-family: 'Cairo', sans-serif;
      font-weight: 500;
      transition: all 0.3s ease;
    }
    
    .form-input:focus {
      outline: none;
      border-color: var(--accent-blue);
      box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
      background: var(--bg-secondary);
    }
    
    .form-input::placeholder {
      color: var(--text-muted);
      font-weight: 400;
    }
    
    .form-label {
      position: absolute;
      top: -16px;
      right: 20px;
      background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
      padding: 8px 20px;
      border-radius: 25px;
      font-size: 14px;
      font-weight: 700;
      color: var(--text-primary);
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    /* Advanced Tables */
    .advanced-table {
      background: var(--bg-secondary);
      border: 2px solid var(--border-primary);
      border-radius: 20px;
      overflow: hidden;
      box-shadow: var(--shadow-card);
    }
    
    .advanced-table thead {
      background: var(--bg-tertiary);
    }
    
    .advanced-table th {
      padding: 24px;
      font-weight: 800;
      color: var(--text-primary);
      border: none;
      text-align: right;
      font-size: 16px;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
    
    .advanced-table td {
      padding: 20px 24px;
      border: none;
      border-bottom: 1px solid var(--border-primary);
      color: var(--text-secondary);
      transition: all 0.3s ease;
      font-weight: 500;
    }
    
    .advanced-table tbody tr {
      transition: all 0.3s ease;
    }
    
    .advanced-table tbody tr:hover {
      background: rgba(59, 130, 246, 0.1);
      color: var(--text-primary);
      box-shadow: inset 0 0 0 2px rgba(59, 130, 246, 0.3);
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
      .advanced-card {
        margin-bottom: 20px;
      }
      
      .stats-card {
        padding: 24px;
      }
      
      .stats-number {
        font-size: 2.5rem;
      }
      
      .advanced-btn {
        padding: 12px 24px;
        font-size: 14px;
      }
    }
    
    /* Custom Scrollbar */
    ::-webkit-scrollbar {
      width: 12px;
    }
    
    ::-webkit-scrollbar-track {
      background: var(--bg-secondary);
      border-radius: 6px;
    }
    
    ::-webkit-scrollbar-thumb {
      background: var(--border-accent);
      border-radius: 6px;
      border: 2px solid var(--bg-secondary);
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: var(--accent-blue);
    }
    
    /* Animation Classes */
    .fade-in {
      animation: fadeIn 0.8s ease-out;
    }
    
    .slide-up {
      animation: slideUp 0.8s ease-out;
    }
    
    .scale-in {
      animation: scaleIn 0.8s ease-out;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(30px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes slideUp {
      from { opacity: 0; transform: translateY(50px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes scaleIn {
      from { opacity: 0; transform: scale(0.9); }
      to { opacity: 1; transform: scale(1); }
    }
  </style>
</head>
<body class="font-cairo">
  <!-- Advanced Navigation -->
  <nav class="advanced-nav fixed top-0 left-0 right-0 z-50 px-6 py-4">
    <div class="max-w-7xl mx-auto flex items-center justify-between">
      <!-- Brand -->
      <div class="flex items-center gap-4">
        <div class="w-16 h-16 bg-gradient-to-r from-blue-500 via-purple-500 to-green-500 rounded-2xl flex items-center justify-center shadow-lg">
          <i class="fas fa-building text-white text-2xl"></i>
        </div>
        <div>
          <h1 class="text-white font-bold text-2xl">جمعية المنقف التعاونية</h1>
          <p class="text-slate-300 text-sm font-medium">واجهة الإدارة المتقدمة</p>
        </div>
      </div>

      <!-- Navigation Menu -->
      <div class="hidden lg:flex items-center gap-2">
        <a href="#dashboard" class="nav-item active">
          <i class="fas fa-tachometer-alt ml-2"></i>
          لوحة التحكم
        </a>
        <a href="#suppliers" class="nav-item">
          <i class="fas fa-users ml-2"></i>
          الموردين
        </a>
        <a href="#contracts" class="nav-item">
          <i class="fas fa-file-contract ml-2"></i>
          العقود
        </a>
        <a href="#eyes" class="nav-item">
          <i class="fas fa-eye ml-2"></i>
          العيون
        </a>
        <a href="#reports" class="nav-item">
          <i class="fas fa-chart-line ml-2"></i>
          التقارير
        </a>
      </div>

      <!-- User Actions -->
      <div class="flex items-center gap-4">
        <div class="hidden md:block relative">
          <input type="text" placeholder="بحث متقدم..."
                 class="bg-slate-700/50 border-2 border-slate-600 rounded-full px-6 py-3 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-80 transition-all font-medium">
          <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
        </div>

        <button class="text-slate-300 hover:text-white p-3 rounded-full hover:bg-slate-700/50 transition-all relative">
          <i class="fas fa-bell text-xl"></i>
          <span class="absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full text-xs flex items-center justify-center animate-pulse text-white font-bold">5</span>
        </button>

        <div class="flex items-center gap-3 text-slate-300 hover:text-white p-2 rounded-full hover:bg-slate-700/50 transition-all">
          <img src="https://via.placeholder.com/40" alt="User" class="w-12 h-12 rounded-full border-2 border-slate-600">
          <div class="hidden md:block text-right">
            <p class="text-sm font-bold text-white">أحمد محمد</p>
            <p class="text-xs text-slate-400 font-medium">مدير النظام</p>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <main class="pt-28 px-6 pb-12">
    <div class="max-w-7xl mx-auto">

      <!-- Header Section -->
      <div class="text-center mb-16 fade-in">
        <h1 class="text-6xl md:text-8xl font-black text-white mb-8 leading-tight">
          واجهة الإدارة المتقدمة
        </h1>
        <p class="text-2xl md:text-3xl text-slate-300 max-w-5xl mx-auto leading-relaxed mb-12">
          نظام إدارة احترافي متطور مع ألوان عالية التباين لتجربة مستخدم مثالية
        </p>

        <!-- Developer Signature -->
        <div class="advanced-card max-w-lg mx-auto p-8 mb-12">
          <div class="flex items-center justify-center gap-6">
            <div class="w-16 h-16 bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 rounded-full flex items-center justify-center shadow-lg">
              <i class="fas fa-code text-white text-2xl"></i>
            </div>
            <div class="text-right">
              <p class="text-white font-black text-2xl mb-2">تطوير وتنفيذ</p>
              <p class="text-blue-400 font-bold text-xl mb-1">محمد مرزوق العقاب</p>
              <p class="text-slate-400 font-semibold">مطور نظم معلومات محترف</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Advanced Stats Dashboard -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
        <div class="stats-card slide-up" style="animation-delay: 0.1s">
          <div class="stats-icon" style="background: linear-gradient(135deg, var(--accent-blue), var(--accent-cyan));">
            <i class="fas fa-users"></i>
          </div>
          <div class="stats-number">247</div>
          <div class="stats-label">إجمالي الموردين</div>
          <div class="stats-trend badge-success">
            <i class="fas fa-arrow-up"></i>
            +18% نمو
          </div>
        </div>

        <div class="stats-card slide-up" style="animation-delay: 0.2s">
          <div class="stats-icon" style="background: linear-gradient(135deg, var(--accent-purple), var(--accent-blue));">
            <i class="fas fa-file-contract"></i>
          </div>
          <div class="stats-number">134</div>
          <div class="stats-label">العقود النشطة</div>
          <div class="stats-trend badge-success">
            <i class="fas fa-arrow-up"></i>
            +12% نمو
          </div>
        </div>

        <div class="stats-card slide-up" style="animation-delay: 0.3s">
          <div class="stats-icon" style="background: linear-gradient(135deg, var(--accent-green), var(--accent-cyan));">
            <i class="fas fa-eye"></i>
          </div>
          <div class="stats-number">456</div>
          <div class="stats-label">العيون المؤجرة</div>
          <div class="stats-trend badge-success">
            <i class="fas fa-arrow-up"></i>
            +25% نمو
          </div>
        </div>

        <div class="stats-card slide-up" style="animation-delay: 0.4s">
          <div class="stats-icon" style="background: linear-gradient(135deg, var(--accent-orange), var(--accent-red));">
            <i class="fas fa-dollar-sign"></i>
          </div>
          <div class="stats-number">27,360</div>
          <div class="stats-label">الإيرادات (د.ك)</div>
          <div class="stats-trend badge-success">
            <i class="fas fa-arrow-up"></i>
            +32% نمو
          </div>
        </div>
      </div>

      <!-- Advanced Action Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
        <div class="advanced-card p-10 text-center scale-in" style="animation-delay: 0.1s">
          <div class="w-24 h-24 bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
            <i class="fas fa-plus text-white text-4xl"></i>
          </div>
          <h3 class="text-3xl font-black text-white mb-6">إضافة مورد جديد</h3>
          <p class="text-slate-300 mb-10 leading-relaxed text-lg font-medium">
            إضافة مورد جديد إلى النظام مع جميع البيانات والوثائق المطلوبة بطريقة احترافية ومنظمة
          </p>
          <button class="advanced-btn btn-primary w-full" onclick="showSection('add-supplier')">
            <i class="fas fa-plus"></i>
            إضافة مورد
          </button>
        </div>

        <div class="advanced-card p-10 text-center scale-in" style="animation-delay: 0.2s">
          <div class="w-24 h-24 bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
            <i class="fas fa-file-contract text-white text-4xl"></i>
          </div>
          <h3 class="text-3xl font-black text-white mb-6">إنشاء عقد جديد</h3>
          <p class="text-slate-300 mb-10 leading-relaxed text-lg font-medium">
            إنشاء عقد توريد أو إيجار جديد مع تحديد جميع الشروط والأحكام والمواصفات المطلوبة
          </p>
          <button class="advanced-btn btn-success w-full" onclick="showSection('new-contract')">
            <i class="fas fa-file-plus"></i>
            إنشاء عقد
          </button>
        </div>

        <div class="advanced-card p-10 text-center scale-in" style="animation-delay: 0.3s">
          <div class="w-24 h-24 bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
            <i class="fas fa-eye text-white text-4xl"></i>
          </div>
          <h3 class="text-3xl font-black text-white mb-6">تأجير عين جديدة</h3>
          <p class="text-slate-300 mb-10 leading-relaxed text-lg font-medium">
            تأجير عين في إحدى الطبليات المتاحة في الأقسام المختلفة مع حساب الإيجار تلقائياً
          </p>
          <button class="advanced-btn btn-warning w-full" onclick="showSection('rent-eye')">
            <i class="fas fa-plus-circle"></i>
            تأجير عين
          </button>
        </div>

        <div class="advanced-card p-10 text-center scale-in" style="animation-delay: 0.4s">
          <div class="w-24 h-24 bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
            <i class="fas fa-upload text-white text-4xl"></i>
          </div>
          <h3 class="text-3xl font-black text-white mb-6">رفع ملف الموردين</h3>
          <p class="text-slate-300 mb-10 leading-relaxed text-lg font-medium">
            رفع ملف Excel أو CSV يحتوي على بيانات الموردين لإضافتهم بشكل جماعي إلى النظام
          </p>
          <button class="advanced-btn btn-outline w-full" onclick="window.open('upload_suppliers.html', '_blank')">
            <i class="fas fa-upload"></i>
            رفع الملفات
          </button>
        </div>

        <div class="advanced-card p-10 text-center scale-in" style="animation-delay: 0.5s">
          <div class="w-24 h-24 bg-gradient-to-r from-indigo-500 via-blue-500 to-cyan-500 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
            <i class="fas fa-chart-line text-white text-4xl"></i>
          </div>
          <h3 class="text-3xl font-black text-white mb-6">التقارير المتقدمة</h3>
          <p class="text-slate-300 mb-10 leading-relaxed text-lg font-medium">
            عرض تقارير مفصلة وإحصائيات شاملة عن الموردين والعقود والإيرادات مع رسوم بيانية
          </p>
          <button class="advanced-btn btn-outline w-full" onclick="showSection('reports')">
            <i class="fas fa-chart-bar"></i>
            عرض التقارير
          </button>
        </div>

        <div class="advanced-card p-10 text-center scale-in" style="animation-delay: 0.6s">
          <div class="w-24 h-24 bg-gradient-to-r from-teal-500 via-cyan-500 to-blue-500 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
            <i class="fas fa-cogs text-white text-4xl"></i>
          </div>
          <h3 class="text-3xl font-black text-white mb-6">إعدادات النظام</h3>
          <p class="text-slate-300 mb-10 leading-relaxed text-lg font-medium">
            إدارة إعدادات النظام والمستخدمين والصلاحيات والنسخ الاحتياطي والأمان
          </p>
          <button class="advanced-btn btn-outline w-full" onclick="showSection('settings')">
            <i class="fas fa-cogs"></i>
            الإعدادات
          </button>
        </div>
      </div>

      <!-- Advanced Activity Dashboard -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
        <!-- Recent Activities -->
        <div class="advanced-card p-10 fade-in">
          <div class="flex items-center justify-between mb-8">
            <h3 class="text-3xl font-black text-white flex items-center gap-4">
              <i class="fas fa-clock text-blue-400 text-2xl"></i>
              الأنشطة الحديثة
            </h3>
            <button class="advanced-btn btn-outline">
              <i class="fas fa-arrow-left"></i>
              عرض الكل
            </button>
          </div>

          <div class="space-y-6">
            <div class="flex items-center justify-between p-6 bg-slate-700/40 rounded-2xl border-2 border-slate-600/50 hover:border-blue-500/70 transition-all">
              <div class="flex items-center gap-6">
                <div class="w-16 h-16 bg-blue-500/30 rounded-2xl flex items-center justify-center border-2 border-blue-500/50">
                  <i class="fas fa-file-contract text-blue-400 text-xl"></i>
                </div>
                <div>
                  <h4 class="text-white font-bold text-lg">عقد توريد بهارات جديد</h4>
                  <p class="text-slate-400 font-medium">شركة البهارات الذهبية المتطورة</p>
                </div>
              </div>
              <div class="text-right">
                <span class="badge badge-success">مكتمل</span>
                <p class="text-slate-400 text-sm mt-2 font-medium">منذ ساعتين</p>
              </div>
            </div>

            <div class="flex items-center justify-between p-6 bg-slate-700/40 rounded-2xl border-2 border-slate-600/50 hover:border-purple-500/70 transition-all">
              <div class="flex items-center gap-6">
                <div class="w-16 h-16 bg-purple-500/30 rounded-2xl flex items-center justify-center border-2 border-purple-500/50">
                  <i class="fas fa-eye text-purple-400 text-xl"></i>
                </div>
                <div>
                  <h4 class="text-white font-bold text-lg">تأجير عين رقم 67</h4>
                  <p class="text-slate-400 font-medium">قسم الأجبان - طبلية 5</p>
                </div>
              </div>
              <div class="text-right">
                <span class="badge badge-warning">قيد المراجعة</span>
                <p class="text-slate-400 text-sm mt-2 font-medium">منذ 4 ساعات</p>
              </div>
            </div>

            <div class="flex items-center justify-between p-6 bg-slate-700/40 rounded-2xl border-2 border-slate-600/50 hover:border-green-500/70 transition-all">
              <div class="flex items-center gap-6">
                <div class="w-16 h-16 bg-green-500/30 rounded-2xl flex items-center justify-center border-2 border-green-500/50">
                  <i class="fas fa-user-plus text-green-400 text-xl"></i>
                </div>
                <div>
                  <h4 class="text-white font-bold text-lg">مورد جديد مضاف</h4>
                  <p class="text-slate-400 font-medium">شركة المواد الاستهلاكية الحديثة</p>
                </div>
              </div>
              <div class="text-right">
                <span class="badge badge-success">مكتمل</span>
                <p class="text-slate-400 text-sm mt-2 font-medium">منذ يوم واحد</p>
              </div>
            </div>
          </div>
        </div>

        <!-- System Alerts -->
        <div class="advanced-card p-10 fade-in">
          <div class="flex items-center justify-between mb-8">
            <h3 class="text-3xl font-black text-white flex items-center gap-4">
              <i class="fas fa-exclamation-triangle text-yellow-400 text-2xl"></i>
              تنبيهات النظام
            </h3>
            <button class="advanced-btn btn-outline">
              <i class="fas fa-check-double"></i>
              تحديد الكل
            </button>
          </div>

          <div class="space-y-6">
            <div class="flex items-start gap-6 p-6 bg-red-500/20 border-2 border-red-500/40 rounded-2xl">
              <div class="w-14 h-14 bg-red-500/30 rounded-full flex items-center justify-center flex-shrink-0 border-2 border-red-500/50">
                <i class="fas fa-exclamation-triangle text-red-400 text-lg"></i>
              </div>
              <div class="flex-1">
                <h4 class="text-white font-bold text-lg mb-2">عقد منتهي الصلاحية</h4>
                <p class="text-slate-300 font-medium mb-3">عقد توريد الخضروات مع شركة الخضار الطازج سينتهي خلال يومين فقط</p>
                <p class="text-red-400 text-sm font-bold">عاجل - منذ ساعة واحدة</p>
              </div>
            </div>

            <div class="flex items-start gap-6 p-6 bg-blue-500/20 border-2 border-blue-500/40 rounded-2xl">
              <div class="w-14 h-14 bg-blue-500/30 rounded-full flex items-center justify-center flex-shrink-0 border-2 border-blue-500/50">
                <i class="fas fa-info-circle text-blue-400 text-lg"></i>
              </div>
              <div class="flex-1">
                <h4 class="text-white font-bold text-lg mb-2">طلب انضمام جديد</h4>
                <p class="text-slate-300 font-medium mb-3">تم استلام طلب انضمام جديد من شركة المواد الغذائية المتقدمة</p>
                <p class="text-blue-400 text-sm font-bold">جديد - منذ 3 ساعات</p>
              </div>
            </div>

            <div class="flex items-start gap-6 p-6 bg-green-500/20 border-2 border-green-500/40 rounded-2xl">
              <div class="w-14 h-14 bg-green-500/30 rounded-full flex items-center justify-center flex-shrink-0 border-2 border-green-500/50">
                <i class="fas fa-check-circle text-green-400 text-lg"></i>
              </div>
              <div class="flex-1">
                <h4 class="text-white font-bold text-lg mb-2">دفعة مستلمة بنجاح</h4>
                <p class="text-slate-300 font-medium mb-3">تم استلام دفعة إيجار العين رقم 34 بقيمة 360 د.ك</p>
                <p class="text-green-400 text-sm font-bold">مكتمل - منذ 6 ساعات</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions Bar -->
      <div class="advanced-card p-8 mb-16 fade-in">
        <div class="flex flex-col lg:flex-row items-center justify-between gap-6">
          <div>
            <h3 class="text-2xl font-black text-white mb-2">إجراءات سريعة</h3>
            <p class="text-slate-300 font-medium">الوصول السريع للمهام الأكثر استخداماً</p>
          </div>
          <div class="flex flex-wrap gap-4">
            <button class="advanced-btn btn-primary" onclick="window.open('upload_suppliers.html', '_blank')">
              <i class="fas fa-upload"></i>
              رفع ملفات
            </button>
            <button class="advanced-btn btn-success" onclick="showSection('new-contract')">
              <i class="fas fa-plus"></i>
              عقد جديد
            </button>
            <button class="advanced-btn btn-warning" onclick="showSection('reports')">
              <i class="fas fa-chart-bar"></i>
              التقارير
            </button>
            <button class="advanced-btn btn-outline" onclick="window.open('professional_ultimate_interface.html', '_blank')">
              <i class="fas fa-external-link-alt"></i>
              الواجهة الرئيسية
            </button>
          </div>
        </div>
      </div>

      <!-- Advanced Footer -->
      <footer class="advanced-card p-10 text-center fade-in">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-10 mb-10">
          <!-- Company Info -->
          <div>
            <h4 class="text-2xl font-black text-white mb-6">جمعية المنقف التعاونية</h4>
            <p class="text-slate-300 leading-relaxed mb-6 font-medium">
              نظام متطور ومتكامل لإدارة عقود الموردين وإيجار العيون في السوق المركزي مع تقنيات حديثة
            </p>
            <div class="flex justify-center gap-4">
              <a href="#" class="w-12 h-12 bg-blue-500/30 rounded-full flex items-center justify-center text-blue-400 hover:bg-blue-500/50 transition-all border-2 border-blue-500/50">
                <i class="fab fa-facebook-f"></i>
              </a>
              <a href="#" class="w-12 h-12 bg-blue-400/30 rounded-full flex items-center justify-center text-blue-400 hover:bg-blue-400/50 transition-all border-2 border-blue-400/50">
                <i class="fab fa-twitter"></i>
              </a>
              <a href="#" class="w-12 h-12 bg-green-500/30 rounded-full flex items-center justify-center text-green-400 hover:bg-green-500/50 transition-all border-2 border-green-500/50">
                <i class="fab fa-whatsapp"></i>
              </a>
            </div>
          </div>

          <!-- Quick Links -->
          <div>
            <h4 class="text-2xl font-black text-white mb-6">روابط سريعة</h4>
            <div class="space-y-3">
              <a href="#suppliers" class="block text-slate-300 hover:text-white transition-all font-medium text-lg">قائمة الموردين</a>
              <a href="#contracts" class="block text-slate-300 hover:text-white transition-all font-medium text-lg">إدارة العقود</a>
              <a href="#eyes" class="block text-slate-300 hover:text-white transition-all font-medium text-lg">نظام العيون</a>
              <a href="#reports" class="block text-slate-300 hover:text-white transition-all font-medium text-lg">التقارير</a>
              <a href="upload_suppliers.html" class="block text-slate-300 hover:text-white transition-all font-medium text-lg">رفع الملفات</a>
            </div>
          </div>

          <!-- Contact Info -->
          <div>
            <h4 class="text-2xl font-black text-white mb-6">معلومات الاتصال</h4>
            <div class="space-y-4 text-slate-300">
              <div class="flex items-center justify-center gap-4">
                <i class="fas fa-phone text-green-400 text-xl"></i>
                <span class="font-bold text-lg">+965-1234567</span>
              </div>
              <div class="flex items-center justify-center gap-4">
                <i class="fas fa-envelope text-blue-400 text-xl"></i>
                <span class="font-bold text-lg"><EMAIL></span>
              </div>
              <div class="flex items-center justify-center gap-4">
                <i class="fas fa-map-marker-alt text-red-400 text-xl"></i>
                <span class="font-bold text-lg">الكويت - المنقف</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Developer Credit -->
        <div class="border-t-2 border-slate-600 pt-10">
          <div class="flex flex-col md:flex-row items-center justify-between gap-6">
            <p class="text-slate-400 font-bold text-lg">
              © 2024 جمعية المنقف التعاونية. جميع الحقوق محفوظة.
            </p>
            <div class="flex items-center gap-4">
              <div class="w-12 h-12 bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 rounded-full flex items-center justify-center shadow-lg">
                <i class="fas fa-code text-white text-xl"></i>
              </div>
              <div class="text-right">
                <p class="text-white font-black text-xl">تطوير وتنفيذ: محمد مرزوق العقاب</p>
                <p class="text-slate-400 font-bold">مطور نظم معلومات محترف ومتخصص</p>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </main>

  <!-- Advanced JavaScript -->
  <script>
    // Advanced Application Controller
    class AdvancedSystemController {
      constructor() {
        this.currentSection = 'dashboard';
        this.notifications = [];
        this.init();
      }

      init() {
        console.log('🚀 واجهة الإدارة المتقدمة - تطوير محمد مرزوق العقاب');
        this.initializeAnimations();
        this.initializeNotifications();
        this.initializeSearch();
        this.startRealTimeUpdates();

        // Welcome message
        setTimeout(() => {
          this.showAdvancedNotification('مرحباً بك في واجهة الإدارة المتقدمة! 🎉', 'success', 5000);
        }, 1500);
      }

      initializeAnimations() {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.style.opacity = '1';
              entry.target.style.transform = 'translateY(0) scale(1)';
            }
          });
        }, {
          threshold: 0.1,
          rootMargin: '0px 0px -100px 0px'
        });

        document.querySelectorAll('.fade-in, .slide-up, .scale-in').forEach(el => {
          el.style.opacity = '0';
          el.style.transform = 'translateY(50px) scale(0.95)';
          observer.observe(el);
        });
      }

      initializeNotifications() {
        if (!document.getElementById('advanced-notifications')) {
          const container = document.createElement('div');
          container.id = 'advanced-notifications';
          container.className = 'fixed top-6 left-6 z-50 space-y-4 max-w-md';
          document.body.appendChild(container);
        }
      }

      showAdvancedNotification(message, type = 'info', duration = 4000) {
        const container = document.getElementById('advanced-notifications');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `advanced-card p-6 transform translate-x-full transition-all duration-500 ${this.getNotificationStyle(type)}`;

        notification.innerHTML = `
          <div class="flex items-center gap-4">
            <div class="w-12 h-12 rounded-full flex items-center justify-center ${this.getNotificationIconBg(type)}">
              <i class="fas fa-${this.getNotificationIcon(type)} text-xl ${this.getNotificationIconColor(type)}"></i>
            </div>
            <div class="flex-1">
              <p class="text-white font-bold text-lg">${message}</p>
              <p class="text-slate-400 text-sm font-medium">${new Date().toLocaleTimeString('ar-KW')}</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="text-white/70 hover:text-white p-2 rounded-full hover:bg-white/10 transition-all">
              <i class="fas fa-times"></i>
            </button>
          </div>
        `;

        container.appendChild(notification);

        setTimeout(() => {
          notification.classList.remove('translate-x-full');
        }, 100);

        setTimeout(() => {
          notification.classList.add('translate-x-full');
          setTimeout(() => notification.remove(), 500);
        }, duration);
      }

      getNotificationStyle(type) {
        const styles = {
          success: 'border-green-500/60 bg-green-500/20',
          error: 'border-red-500/60 bg-red-500/20',
          warning: 'border-yellow-500/60 bg-yellow-500/20',
          info: 'border-blue-500/60 bg-blue-500/20'
        };
        return styles[type] || styles.info;
      }

      getNotificationIconBg(type) {
        const bgs = {
          success: 'bg-green-500/30 border-2 border-green-500/50',
          error: 'bg-red-500/30 border-2 border-red-500/50',
          warning: 'bg-yellow-500/30 border-2 border-yellow-500/50',
          info: 'bg-blue-500/30 border-2 border-blue-500/50'
        };
        return bgs[type] || bgs.info;
      }

      getNotificationIconColor(type) {
        const colors = {
          success: 'text-green-400',
          error: 'text-red-400',
          warning: 'text-yellow-400',
          info: 'text-blue-400'
        };
        return colors[type] || colors.info;
      }

      getNotificationIcon(type) {
        const icons = {
          success: 'check-circle',
          error: 'times-circle',
          warning: 'exclamation-triangle',
          info: 'info-circle'
        };
        return icons[type] || icons.info;
      }

      initializeSearch() {
        const searchInput = document.querySelector('input[placeholder="بحث متقدم..."]');
        if (searchInput) {
          searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            if (query.length > 2) {
              this.performAdvancedSearch(query);
            }
          });
        }
      }

      performAdvancedSearch(query) {
        console.log('🔍 البحث المتقدم عن:', query);
        this.showAdvancedNotification(`جاري البحث المتقدم عن: ${query}`, 'info', 3000);
      }

      startRealTimeUpdates() {
        // Update stats every 45 seconds
        setInterval(() => {
          this.updateAdvancedStats();
        }, 45000);

        // Check for new notifications every 60 seconds
        setInterval(() => {
          this.checkForUpdates();
        }, 60000);
      }

      updateAdvancedStats() {
        const statsNumbers = document.querySelectorAll('.stats-number');
        statsNumbers.forEach(stat => {
          const currentValue = parseInt(stat.textContent.replace(/,/g, ''));
          const increment = Math.floor(Math.random() * 8) + 1;
          const newValue = currentValue + increment;

          // Animate number change
          this.animateNumber(stat, currentValue, newValue);
        });
      }

      animateNumber(element, start, end) {
        const duration = 1000;
        const startTime = performance.now();

        const animate = (currentTime) => {
          const elapsed = currentTime - startTime;
          const progress = Math.min(elapsed / duration, 1);

          const current = Math.floor(start + (end - start) * progress);
          element.textContent = current.toLocaleString();

          if (progress < 1) {
            requestAnimationFrame(animate);
          }
        };

        requestAnimationFrame(animate);
      }

      checkForUpdates() {
        // Simulate checking for updates
        if (Math.random() > 0.7) {
          const messages = [
            'تم إضافة مورد جديد إلى النظام',
            'عقد جديد في انتظار المراجعة',
            'تم استلام دفعة إيجار جديدة',
            'تحديث أمني جديد متاح'
          ];

          const randomMessage = messages[Math.floor(Math.random() * messages.length)];
          this.showAdvancedNotification(randomMessage, 'info', 4000);
        }
      }

      showSection(sectionId) {
        this.currentSection = sectionId;
        this.showAdvancedNotification(`تم الانتقال إلى ${this.getSectionName(sectionId)}`, 'success', 2000);

        // Smooth scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }

      getSectionName(sectionId) {
        const names = {
          'dashboard': 'لوحة التحكم',
          'suppliers': 'إدارة الموردين',
          'contracts': 'إدارة العقود',
          'eyes': 'نظام العيون',
          'reports': 'التقارير المتقدمة',
          'settings': 'إعدادات النظام'
        };
        return names[sectionId] || 'القسم المحدد';
      }
    }

    // Initialize Advanced System
    let advancedSystem;

    document.addEventListener('DOMContentLoaded', function() {
      advancedSystem = new AdvancedSystemController();
    });

    // Global Functions
    function showSection(sectionId) {
      if (advancedSystem) {
        advancedSystem.showSection(sectionId);
      }
    }

    // Keyboard Shortcuts
    document.addEventListener('keydown', function(e) {
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.querySelector('input[placeholder="بحث متقدم..."]');
        if (searchInput) {
          searchInput.focus();
        }
      }
    });

    // Performance Monitoring
    if ('performance' in window) {
      window.addEventListener('load', () => {
        const loadTime = performance.now();
        console.log(`⚡ تم تحميل الواجهة المتقدمة في ${Math.round(loadTime)}ms`);

        if (advancedSystem && loadTime < 2000) {
          setTimeout(() => {
            advancedSystem.showAdvancedNotification('تم تحميل الواجهة بسرعة فائقة! ⚡', 'success', 3000);
          }, 2000);
        }
      });
    }
  </script>
</body>
</html>
