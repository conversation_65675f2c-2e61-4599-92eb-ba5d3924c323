#!/bin/bash

# إنشاء ملفات البيانات النموذجية
# جمعية المنقف التعاونية
# تطوير وتنفيذ: محمد مرزوق العقاب

# ألوان للطباعة
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# دالة طباعة ملونة
print_color() {
    echo -e "${1}${2}${NC}"
}

# عرض الرأس
clear
print_color $CYAN "🎉 ==============================================="
print_color $CYAN "📊 إنشاء ملفات البيانات النموذجية"
print_color $CYAN "🏢 جمعية المنقف التعاونية"
echo
print_color $PURPLE "👨‍💻 تطوير وتنفيذ: محمد مرزوق العقاب"
print_color $PURPLE "🎯 مطور نظم معلومات محترف ومتخصص"
print_color $CYAN "🎉 ==============================================="
echo

print_color $BLUE "📦 التحقق من التبعيات..."
if [ ! -d "node_modules" ]; then
    print_color $YELLOW "🔄 تثبيت التبعيات..."
    npm install
    echo
fi

print_color $BLUE "📁 إنشاء مجلد البيانات النموذجية..."
mkdir -p sample_data

echo
print_color $GREEN "🚀 بدء إنشاء ملفات البيانات..."
echo

print_color $BLUE "📊 إنشاء ملفات Excel..."
node backend/utils/create-excel-files.js

echo
print_color $YELLOW "📋 الملفات المنشأة:"
echo

print_color $WHITE "📄 ملفات CSV:"
if [ -f "sample_data/موردين_جمعية_المنقف.csv" ]; then
    print_color $GREEN "   ✅ موردين_جمعية_المنقف.csv"
else
    print_color $RED "   ❌ موردين_جمعية_المنقف.csv - غير موجود"
fi

if [ -f "sample_data/عقود_جمعية_المنقف.csv" ]; then
    print_color $GREEN "   ✅ عقود_جمعية_المنقف.csv"
else
    print_color $RED "   ❌ عقود_جمعية_المنقف.csv - غير موجود"
fi

if [ -f "sample_data/ايجارات_العيون_جمعية_المنقف.csv" ]; then
    print_color $GREEN "   ✅ ايجارات_العيون_جمعية_المنقف.csv"
else
    print_color $RED "   ❌ ايجارات_العيون_جمعية_المنقف.csv - غير موجود"
fi

echo
print_color $WHITE "📊 ملفات Excel:"
if [ -f "sample_data/بيانات_جمعية_المنقف_الشاملة.xlsx" ]; then
    print_color $GREEN "   ✅ بيانات_جمعية_المنقف_الشاملة.xlsx - ملف شامل"
else
    print_color $RED "   ❌ بيانات_جمعية_المنقف_الشاملة.xlsx - غير موجود"
fi

if [ -f "sample_data/موردين_جمعية_المنقف.xlsx" ]; then
    print_color $GREEN "   ✅ موردين_جمعية_المنقف.xlsx"
else
    print_color $RED "   ❌ موردين_جمعية_المنقف.xlsx - غير موجود"
fi

if [ -f "sample_data/عقود_جمعية_المنقف.xlsx" ]; then
    print_color $GREEN "   ✅ عقود_جمعية_المنقف.xlsx"
else
    print_color $RED "   ❌ عقود_جمعية_المنقف.xlsx - غير موجود"
fi

if [ -f "sample_data/ايجارات_العيون_جمعية_المنقف.xlsx" ]; then
    print_color $GREEN "   ✅ ايجارات_العيون_جمعية_المنقف.xlsx"
else
    print_color $RED "   ❌ ايجارات_العيون_جمعية_المنقف.xlsx - غير موجود"
fi

echo
print_color $CYAN "📊 إحصائيات البيانات النموذجية:"
echo "   👥 الموردين: 25 مورد (بهارات، استهلاكي، أجبان)"
echo "   📋 العقود: 30 عقد (توريد، إيجار، خدمات)"
echo "   👁️ إيجارات العيون: 45 إيجار نشط"
echo "   🏢 الطبليات: 20 طبلية × 4 أقسام = 80 عين"
echo "   💰 الإيرادات الشهرية: 2,700 د.ك (45 عين × 60 د.ك)"
echo

print_color $YELLOW "🎯 طريقة الاستخدام:"
echo "   1. افتح نظام رفع الملفات: http://localhost:3000/upload_suppliers.html"
echo "   2. اختر أحد الملفات من مجلد sample_data"
echo "   3. ارفع الملف واختبر النظام"
echo "   4. تحقق من البيانات في واجهات الإدارة"
echo

print_color $BLUE "🔗 الواجهات المتاحة للاختبار:"
echo "   📁 رفع الملفات: http://localhost:3000/upload_suppliers.html"
echo "   👥 إدارة الموردين: http://localhost:3000/suppliers_management.html"
echo "   📋 إدارة العقود: http://localhost:3000/contracts_management.html"
echo "   👁️ إدارة العيون: http://localhost:3000/eyes_management.html"
echo "   📊 التقارير: http://localhost:3000/reports_dashboard.html"
echo

print_color $GREEN "💡 نصائح للاختبار:"
echo "   • ابدأ برفع ملف الموردين أولاً"
echo "   • ثم ارفع ملف العقود"
echo "   • أخيراً ارفع ملف إيجارات العيون"
echo "   • تحقق من التقارير والإحصائيات"
echo "   • اختبر وظائف البحث والتصفية"
echo

print_color $CYAN "🎉 ==============================================="
print_color $GREEN "✅ تم إنشاء جميع ملفات البيانات النموذجية بنجاح!"
print_color $BLUE "📁 المجلد: sample_data/"
print_color $GREEN "🚀 النظام جاهز للاختبار الشامل"
echo
print_color $PURPLE "👨‍💻 تطوير: محمد مرزوق العقاب"
print_color $CYAN "🏢 جمعية المنقف التعاونية"
print_color $CYAN "🎉 ==============================================="
echo

# عرض محتويات مجلد البيانات
if [ -d "sample_data" ]; then
    print_color $YELLOW "📁 محتويات مجلد البيانات النموذجية:"
    ls -la sample_data/ | grep -E '\.(csv|xlsx)$' | while read line; do
        filename=$(echo $line | awk '{print $NF}')
        size=$(echo $line | awk '{print $5}')
        print_color $WHITE "   📄 $filename ($size bytes)"
    done
    echo
fi

read -p "اضغط Enter للمتابعة..."
