/**
 * مسارات إدارة العقود
 * نظام إدارة عقود الموردين والإيجارات - جمعية المنقف
 */

const express = require('express');
const { body, param, query, validationResult } = require('express-validator');
const database = require('../utils/database');
const logger = require('../utils/logger');

const router = express.Router();

// =====================================================
// قواعد التحقق من صحة البيانات
// =====================================================

const contractValidationRules = [
  body('contract_number')
    .notEmpty()
    .withMessage('رقم العقد مطلوب')
    .matches(/^[0-9]{4}-[0-9]{3}$/)
    .withMessage('تنسيق رقم العقد يجب أن يكون YYYY-XXX'),
  
  body('contract_name')
    .notEmpty()
    .withMessage('اسم العقد مطلوب')
    .isLength({ min: 2, max: 200 })
    .withMessage('اسم العقد يجب أن يكون بين 2 و 200 حرف'),
  
  body('supplier_id')
    .isInt({ min: 1 })
    .withMessage('معرف المورد يجب أن يكون رقماً صحيحاً'),
  
  body('contract_type_id')
    .isInt({ min: 1 })
    .withMessage('نوع العقد مطلوب'),
  
  body('start_date')
    .isISO8601()
    .withMessage('تاريخ البداية يجب أن يكون تاريخاً صحيحاً'),
  
  body('end_date')
    .isISO8601()
    .withMessage('تاريخ النهاية يجب أن يكون تاريخاً صحيحاً')
    .custom((endDate, { req }) => {
      if (new Date(endDate) <= new Date(req.body.start_date)) {
        throw new Error('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
      }
      return true;
    }),
  
  body('total_amount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('المبلغ الإجمالي يجب أن يكون رقماً موجباً'),
  
  body('status')
    .optional()
    .isIn(['draft', 'active', 'expired', 'cancelled', 'suspended'])
    .withMessage('حالة العقد غير صحيحة'),
  
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('أولوية العقد غير صحيحة')
];

// =====================================================
// دوال مساعدة
// =====================================================

/**
 * معالجة أخطاء التحقق من صحة البيانات
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    logger.logValidationError(
      'contract_validation',
      req.body,
      errors.array(),
      req.user?.id,
      req.ip
    );
    
    return res.status(400).json({
      success: false,
      message: 'خطأ في البيانات المدخلة',
      errors: errors.array().map(error => ({
        field: error.param,
        message: error.msg,
        value: error.value
      }))
    });
  }
  next();
};

/**
 * التحقق من وجود العقد
 */
const checkContractExists = async (req, res, next) => {
  try {
    const contractId = req.params.id;
    const contract = await database.findOne('contracts', { id: contractId });
    
    if (!contract) {
      return res.status(404).json({
        success: false,
        message: 'العقد غير موجود'
      });
    }
    
    req.contract = contract;
    next();
  } catch (error) {
    logger.error('خطأ في التحقق من وجود العقد:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم'
    });
  }
};

/**
 * التحقق من تكرار رقم العقد
 */
const checkDuplicateContractNumber = async (req, res, next) => {
  try {
    const { contract_number } = req.body;
    const contractId = req.params.id;
    
    let whereClause = { contract_number };
    if (contractId) {
      // في حالة التحديث، استثناء العقد الحالي
      const existingContract = await database.query(
        'SELECT id FROM contracts WHERE contract_number = ? AND id != ?',
        [contract_number, contractId]
      );
      
      if (existingContract.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'رقم العقد موجود مسبقاً'
        });
      }
    } else {
      // في حالة الإنشاء الجديد
      const existingContract = await database.findOne('contracts', whereClause);
      
      if (existingContract) {
        return res.status(400).json({
          success: false,
          message: 'رقم العقد موجود مسبقاً'
        });
      }
    }
    
    next();
  } catch (error) {
    logger.error('خطأ في التحقق من تكرار رقم العقد:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم'
    });
  }
};

// =====================================================
// المسارات
// =====================================================

/**
 * GET /api/v1/contracts
 * الحصول على قائمة العقود مع الفلترة والترقيم
 */
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('رقم الصفحة يجب أن يكون رقماً موجباً'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('حد النتائج يجب أن يكون بين 1 و 100'),
  query('status').optional().isIn(['draft', 'active', 'expired', 'cancelled', 'suspended']),
  query('supplier_id').optional().isInt({ min: 1 }),
  query('search').optional().isLength({ max: 255 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'معاملات البحث غير صحيحة',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 10,
      status,
      supplier_id,
      search,
      start_date,
      end_date,
      sort_by = 'created_at',
      sort_order = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    
    // بناء استعلام البحث
    let whereConditions = [];
    let queryParams = [];
    
    if (status) {
      whereConditions.push('c.status = ?');
      queryParams.push(status);
    }
    
    if (supplier_id) {
      whereConditions.push('c.supplier_id = ?');
      queryParams.push(supplier_id);
    }
    
    if (search) {
      whereConditions.push('(c.contract_number LIKE ? OR c.contract_name LIKE ? OR s.name LIKE ?)');
      const searchTerm = `%${search}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm);
    }
    
    if (start_date) {
      whereConditions.push('c.start_date >= ?');
      queryParams.push(start_date);
    }
    
    if (end_date) {
      whereConditions.push('c.end_date <= ?');
      queryParams.push(end_date);
    }
    
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
    
    // استعلام العد الإجمالي
    const countQuery = `
      SELECT COUNT(*) as total
      FROM contracts c
      LEFT JOIN suppliers s ON c.supplier_id = s.id
      ${whereClause}
    `;
    
    const [countResult] = await database.query(countQuery, queryParams);
    const total = countResult.total;
    
    // استعلام البيانات
    const dataQuery = `
      SELECT 
        c.*,
        s.name as supplier_name,
        s.supplier_code,
        ct.name as contract_type_name,
        u1.full_name as created_by_name,
        u2.full_name as approved_by_name,
        DATEDIFF(c.end_date, CURDATE()) as days_to_expiry
      FROM contracts c
      LEFT JOIN suppliers s ON c.supplier_id = s.id
      LEFT JOIN contract_types ct ON c.contract_type_id = ct.id
      LEFT JOIN users u1 ON c.created_by = u1.id
      LEFT JOIN users u2 ON c.approved_by = u2.id
      ${whereClause}
      ORDER BY c.${sort_by} ${sort_order}
      LIMIT ? OFFSET ?
    `;
    
    queryParams.push(parseInt(limit), parseInt(offset));
    const contracts = await database.query(dataQuery, queryParams);
    
    // حساب معلومات الترقيم
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;
    
    logger.logApiRequest(
      req.method,
      req.originalUrl,
      req.user?.id,
      req.ip,
      Date.now() - req.startTime,
      200
    );
    
    res.json({
      success: true,
      data: contracts,
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_records: total,
        per_page: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      }
    });
    
  } catch (error) {
    logger.error('خطأ في جلب العقود:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب العقود'
    });
  }
});

/**
 * GET /api/v1/contracts/:id
 * الحصول على تفاصيل عقد محدد
 */
router.get('/:id', [
  param('id').isInt({ min: 1 }).withMessage('معرف العقد يجب أن يكون رقماً صحيحاً'),
  checkContractExists
], async (req, res) => {
  try {
    const contractId = req.params.id;
    
    // جلب تفاصيل العقد مع المعلومات المرتبطة
    const contractQuery = `
      SELECT 
        c.*,
        s.name as supplier_name,
        s.supplier_code,
        s.contact_person,
        s.phone as supplier_phone,
        s.email as supplier_email,
        ct.name as contract_type_name,
        u1.full_name as created_by_name,
        u2.full_name as approved_by_name,
        DATEDIFF(c.end_date, CURDATE()) as days_to_expiry
      FROM contracts c
      LEFT JOIN suppliers s ON c.supplier_id = s.id
      LEFT JOIN contract_types ct ON c.contract_type_id = ct.id
      LEFT JOIN users u1 ON c.created_by = u1.id
      LEFT JOIN users u2 ON c.approved_by = u2.id
      WHERE c.id = ?
    `;
    
    const [contract] = await database.query(contractQuery, [contractId]);
    
    // جلب عناصر العقد
    const itemsQuery = `
      SELECT * FROM contract_items 
      WHERE contract_id = ? 
      ORDER BY id
    `;
    const items = await database.query(itemsQuery, [contractId]);
    
    // جلب المرفقات
    const attachmentsQuery = `
      SELECT 
        id, file_name, original_name, file_size, file_type, 
        description, uploaded_at, uploaded_by
      FROM contract_attachments 
      WHERE contract_id = ? 
      ORDER BY uploaded_at DESC
    `;
    const attachments = await database.query(attachmentsQuery, [contractId]);
    
    // جلب المدفوعات
    const paymentsQuery = `
      SELECT * FROM contract_payments 
      WHERE contract_id = ? 
      ORDER BY payment_date DESC
    `;
    const payments = await database.query(paymentsQuery, [contractId]);
    
    const result = {
      ...contract,
      items,
      attachments,
      payments
    };
    
    logger.logApiRequest(
      req.method,
      req.originalUrl,
      req.user?.id,
      req.ip,
      Date.now() - req.startTime,
      200
    );
    
    res.json({
      success: true,
      data: result
    });
    
  } catch (error) {
    logger.error('خطأ في جلب تفاصيل العقد:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب تفاصيل العقد'
    });
  }
});

/**
 * POST /api/v1/contracts
 * إنشاء عقد جديد
 */
router.post('/', [
  ...contractValidationRules,
  handleValidationErrors,
  checkDuplicateContractNumber
], async (req, res) => {
  try {
    const contractData = {
      ...req.body,
      created_by: req.user.id,
      created_at: new Date(),
      updated_at: new Date()
    };
    
    // بدء معاملة قاعدة البيانات
    const result = await database.transaction(async (connection) => {
      // إدراج العقد
      const [contractResult] = await connection.execute(
        `INSERT INTO contracts (
          contract_number, contract_name, supplier_id, contract_type_id,
          start_date, end_date, total_amount, currency, payment_terms,
          status, priority, auto_renewal, renewal_period, contract_color,
          description, terms_conditions, notes, created_by, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          contractData.contract_number,
          contractData.contract_name,
          contractData.supplier_id,
          contractData.contract_type_id,
          contractData.start_date,
          contractData.end_date,
          contractData.total_amount || 0,
          contractData.currency || 'KWD',
          contractData.payment_terms,
          contractData.status || 'draft',
          contractData.priority || 'medium',
          contractData.auto_renewal || false,
          contractData.renewal_period || 12,
          contractData.contract_color || '#3b82f6',
          contractData.description,
          contractData.terms_conditions,
          contractData.notes,
          contractData.created_by,
          contractData.created_at,
          contractData.updated_at
        ]
      );
      
      const contractId = contractResult.insertId;
      
      // إدراج عناصر العقد إذا كانت موجودة
      if (req.body.items && Array.isArray(req.body.items)) {
        for (const item of req.body.items) {
          await connection.execute(
            `INSERT INTO contract_items (
              contract_id, item_name, item_code, description, quantity,
              unit, unit_price, total_price, specifications, delivery_terms,
              warranty_period, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              contractId,
              item.item_name,
              item.item_code,
              item.description,
              item.quantity || 1,
              item.unit,
              item.unit_price || 0,
              item.total_price || 0,
              item.specifications,
              item.delivery_terms,
              item.warranty_period || 0,
              item.notes
            ]
          );
        }
      }
      
      return contractId;
    });
    
    // تسجيل العملية
    logger.logDatabaseOperation(
      'CREATE',
      'contracts',
      result,
      req.user.id,
      { contract_number: contractData.contract_number }
    );
    
    res.status(201).json({
      success: true,
      message: 'تم إنشاء العقد بنجاح',
      data: { id: result }
    });
    
  } catch (error) {
    logger.error('خطأ في إنشاء العقد:', error);
    
    if (error.code === 'ER_DUP_ENTRY') {
      res.status(400).json({
        success: false,
        message: 'رقم العقد موجود مسبقاً'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'خطأ في إنشاء العقد'
      });
    }
  }
});

/**
 * PUT /api/v1/contracts/:id
 * تحديث عقد موجود
 */
router.put('/:id', [
  param('id').isInt({ min: 1 }).withMessage('معرف العقد يجب أن يكون رقماً صحيحاً'),
  ...contractValidationRules,
  handleValidationErrors,
  checkContractExists,
  checkDuplicateContractNumber
], async (req, res) => {
  try {
    const contractId = req.params.id;
    const updateData = {
      ...req.body,
      updated_at: new Date()
    };

    // حذف الحقول التي لا يجب تحديثها
    delete updateData.id;
    delete updateData.created_by;
    delete updateData.created_at;

    // بدء معاملة قاعدة البيانات
    await database.transaction(async (connection) => {
      // تحديث العقد
      const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
      const updateValues = Object.values(updateData);

      await connection.execute(
        `UPDATE contracts SET ${updateFields} WHERE id = ?`,
        [...updateValues, contractId]
      );

      // تحديث عناصر العقد إذا كانت موجودة
      if (req.body.items && Array.isArray(req.body.items)) {
        // حذف العناصر القديمة
        await connection.execute('DELETE FROM contract_items WHERE contract_id = ?', [contractId]);

        // إدراج العناصر الجديدة
        for (const item of req.body.items) {
          await connection.execute(
            `INSERT INTO contract_items (
              contract_id, item_name, item_code, description, quantity,
              unit, unit_price, total_price, specifications, delivery_terms,
              warranty_period, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              contractId,
              item.item_name,
              item.item_code,
              item.description,
              item.quantity || 1,
              item.unit,
              item.unit_price || 0,
              item.total_price || 0,
              item.specifications,
              item.delivery_terms,
              item.warranty_period || 0,
              item.notes
            ]
          );
        }
      }
    });

    // تسجيل العملية
    logger.logDatabaseOperation(
      'UPDATE',
      'contracts',
      contractId,
      req.user.id,
      updateData
    );

    res.json({
      success: true,
      message: 'تم تحديث العقد بنجاح'
    });

  } catch (error) {
    logger.error('خطأ في تحديث العقد:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث العقد'
    });
  }
});

/**
 * DELETE /api/v1/contracts/:id
 * حذف عقد
 */
router.delete('/:id', [
  param('id').isInt({ min: 1 }).withMessage('معرف العقد يجب أن يكون رقماً صحيحاً'),
  checkContractExists
], async (req, res) => {
  try {
    const contractId = req.params.id;
    const contract = req.contract;

    // التحقق من الصلاحيات (يمكن حذف العقود المسودة فقط أو بصلاحية إدارية)
    if (contract.status !== 'draft' && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'لا يمكن حذف العقود النشطة إلا للمديرين'
      });
    }

    // حذف العقد (سيتم حذف العناصر والمرفقات تلقائياً بسبب CASCADE)
    await database.delete('contracts', { id: contractId });

    // تسجيل العملية
    logger.logDatabaseOperation(
      'DELETE',
      'contracts',
      contractId,
      req.user.id,
      { contract_number: contract.contract_number }
    );

    res.json({
      success: true,
      message: 'تم حذف العقد بنجاح'
    });

  } catch (error) {
    logger.error('خطأ في حذف العقد:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف العقد'
    });
  }
});

/**
 * GET /api/v1/contracts/statistics
 * الحصول على إحصائيات العقود
 */
router.get('/statistics', async (req, res) => {
  try {
    const stats = await database.callProcedure('GetDashboardStats');

    // إحصائيات إضافية
    const monthlyStats = await database.query(`
      SELECT
        MONTH(created_at) as month,
        YEAR(created_at) as year,
        COUNT(*) as count,
        SUM(total_amount) as total_value
      FROM contracts
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
      GROUP BY YEAR(created_at), MONTH(created_at)
      ORDER BY year DESC, month DESC
    `);

    const typeStats = await database.query(`
      SELECT
        ct.name as contract_type,
        COUNT(c.id) as count,
        SUM(c.total_amount) as total_value
      FROM contract_types ct
      LEFT JOIN contracts c ON ct.id = c.contract_type_id
      GROUP BY ct.id, ct.name
      ORDER BY count DESC
    `);

    res.json({
      success: true,
      data: {
        overview: stats[0][0],
        monthly: monthlyStats,
        by_type: typeStats
      }
    });

  } catch (error) {
    logger.error('خطأ في جلب إحصائيات العقود:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب الإحصائيات'
    });
  }
});

/**
 * POST /api/v1/contracts/:id/renew
 * تجديد عقد
 */
router.post('/:id/renew', [
  param('id').isInt({ min: 1 }).withMessage('معرف العقد يجب أن يكون رقماً صحيحاً'),
  body('new_end_date').isISO8601().withMessage('تاريخ النهاية الجديد مطلوب'),
  body('renewal_amount').optional().isFloat({ min: 0 }).withMessage('مبلغ التجديد يجب أن يكون رقماً موجباً'),
  checkContractExists,
  handleValidationErrors
], async (req, res) => {
  try {
    const contractId = req.params.id;
    const { new_end_date, renewal_amount = 0, renewal_terms } = req.body;
    const contract = req.contract;

    // التحقق من أن العقد قابل للتجديد
    if (contract.status !== 'active' && contract.status !== 'expired') {
      return res.status(400).json({
        success: false,
        message: 'يمكن تجديد العقود النشطة أو المنتهية فقط'
      });
    }

    // بدء معاملة قاعدة البيانات
    await database.transaction(async (connection) => {
      // إدراج سجل التجديد
      await connection.execute(
        `INSERT INTO contract_renewals (
          contract_id, old_end_date, new_end_date, renewal_amount,
          renewal_terms, created_by, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          contractId,
          contract.end_date,
          new_end_date,
          renewal_amount,
          renewal_terms,
          req.user.id,
          new Date()
        ]
      );

      // تحديث العقد
      await connection.execute(
        `UPDATE contracts SET
          end_date = ?,
          total_amount = total_amount + ?,
          status = 'active',
          updated_at = ?
        WHERE id = ?`,
        [new_end_date, renewal_amount, new Date(), contractId]
      );
    });

    // تسجيل العملية
    logger.logDatabaseOperation(
      'RENEW',
      'contracts',
      contractId,
      req.user.id,
      { old_end_date: contract.end_date, new_end_date, renewal_amount }
    );

    res.json({
      success: true,
      message: 'تم تجديد العقد بنجاح'
    });

  } catch (error) {
    logger.error('خطأ في تجديد العقد:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تجديد العقد'
    });
  }
});

/**
 * POST /api/v1/contracts/:id/approve
 * اعتماد عقد
 */
router.post('/:id/approve', [
  param('id').isInt({ min: 1 }).withMessage('معرف العقد يجب أن يكون رقماً صحيحاً'),
  checkContractExists
], async (req, res) => {
  try {
    const contractId = req.params.id;
    const contract = req.contract;

    // التحقق من الصلاحيات
    if (req.user.role !== 'admin' && req.user.role !== 'manager') {
      return res.status(403).json({
        success: false,
        message: 'ليس لديك صلاحية لاعتماد العقود'
      });
    }

    // التحقق من حالة العقد
    if (contract.status !== 'draft') {
      return res.status(400).json({
        success: false,
        message: 'يمكن اعتماد المسودات فقط'
      });
    }

    // تحديث العقد
    await database.update('contracts', {
      status: 'active',
      approved_by: req.user.id,
      approved_at: new Date(),
      updated_at: new Date()
    }, { id: contractId });

    // تسجيل العملية
    logger.logDatabaseOperation(
      'APPROVE',
      'contracts',
      contractId,
      req.user.id,
      { status: 'active' }
    );

    res.json({
      success: true,
      message: 'تم اعتماد العقد بنجاح'
    });

  } catch (error) {
    logger.error('خطأ في اعتماد العقد:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في اعتماد العقد'
    });
  }
});

/**
 * GET /api/v1/contracts/expiring
 * الحصول على العقود المنتهية قريباً
 */
router.get('/expiring', [
  query('days').optional().isInt({ min: 1, max: 365 }).withMessage('عدد الأيام يجب أن يكون بين 1 و 365')
], async (req, res) => {
  try {
    const days = req.query.days || 30;

    const expiringContracts = await database.query(`
      SELECT
        c.*,
        s.name as supplier_name,
        ct.name as contract_type_name,
        DATEDIFF(c.end_date, CURDATE()) as days_to_expiry
      FROM contracts c
      LEFT JOIN suppliers s ON c.supplier_id = s.id
      LEFT JOIN contract_types ct ON c.contract_type_id = ct.id
      WHERE c.status = 'active'
        AND c.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)
      ORDER BY c.end_date ASC
    `, [days]);

    res.json({
      success: true,
      data: expiringContracts,
      count: expiringContracts.length
    });

  } catch (error) {
    logger.error('خطأ في جلب العقود المنتهية قريباً:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب العقود المنتهية قريباً'
    });
  }
});

module.exports = router;
