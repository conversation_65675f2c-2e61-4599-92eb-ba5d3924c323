#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج المورد
Supplier Model for Contract Management System
"""

from datetime import datetime
from typing import Optional, List, Dict

class Supplier:
    """نموذج المورد"""
    
    def __init__(self, db_manager):
        """
        تهيئة نموذج المورد
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db_manager = db_manager
    
    def create_supplier(self, supplier_data: Dict) -> Optional[int]:
        """
        إنشاء مورد جديد
        
        Args:
            supplier_data: بيانات المورد
            
        Returns:
            معرف المورد الجديد أو None في حالة الفشل
        """
        query = """
        INSERT INTO suppliers (
            supplier_name, supplier_category, contact_person, phone, email, address,
            national_id, commercial_registration, status, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        params = (
            supplier_data.get('supplier_name'),
            supplier_data.get('supplier_category'),
            supplier_data.get('contact_person'),
            supplier_data.get('phone'),
            supplier_data.get('email'),
            supplier_data.get('address'),
            supplier_data.get('national_id'),
            supplier_data.get('commercial_registration'),
            supplier_data.get('status', 'نشط'),
            supplier_data.get('notes')
        )
        
        if self.db_manager.execute_query(query, params):
            # الحصول على معرف المورد الجديد
            result = self.db_manager.fetch_one("SELECT last_insert_rowid() as id")
            return result['id'] if result else None
        return None
    
    def get_supplier_by_id(self, supplier_id: int) -> Optional[Dict]:
        """
        الحصول على مورد بالمعرف
        
        Args:
            supplier_id: معرف المورد
            
        Returns:
            بيانات المورد أو None
        """
        query = "SELECT * FROM suppliers WHERE supplier_id = ?"
        return self.db_manager.fetch_one(query, (supplier_id,))
    
    def get_all_suppliers(self, status: Optional[str] = None, category: Optional[str] = None) -> List[Dict]:
        """
        الحصول على جميع الموردين

        Args:
            status: حالة المورد (اختياري)
            category: قسم المورد (اختياري)

        Returns:
            قائمة بالموردين
        """
        query = "SELECT * FROM suppliers WHERE 1=1"
        params = []

        if status:
            query += " AND status = ?"
            params.append(status)

        if category:
            query += " AND supplier_category = ?"
            params.append(category)

        query += " ORDER BY supplier_category, supplier_name"
        return self.db_manager.fetch_query(query, tuple(params))

    def get_suppliers_by_category(self, category: str) -> List[Dict]:
        """
        الحصول على الموردين حسب القسم

        Args:
            category: قسم المورد (بهارات، استهلاكي، أجبان)

        Returns:
            قائمة بالموردين في القسم المحدد
        """
        query = "SELECT * FROM suppliers WHERE supplier_category = ? ORDER BY supplier_name"
        return self.db_manager.fetch_query(query, (category,))
    
    def update_supplier(self, supplier_id: int, supplier_data: Dict) -> bool:
        """
        تحديث بيانات المورد
        
        Args:
            supplier_id: معرف المورد
            supplier_data: البيانات الجديدة
            
        Returns:
            True إذا نجح التحديث، False إذا فشل
        """
        query = """
        UPDATE suppliers SET
            supplier_name = ?, supplier_category = ?, contact_person = ?, phone = ?, email = ?,
            address = ?, national_id = ?, commercial_registration = ?,
            status = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
        WHERE supplier_id = ?
        """

        params = (
            supplier_data.get('supplier_name'),
            supplier_data.get('supplier_category'),
            supplier_data.get('contact_person'),
            supplier_data.get('phone'),
            supplier_data.get('email'),
            supplier_data.get('address'),
            supplier_data.get('national_id'),
            supplier_data.get('commercial_registration'),
            supplier_data.get('status'),
            supplier_data.get('notes'),
            supplier_id
        )
        
        return self.db_manager.execute_query(query, params)
    
    def delete_supplier(self, supplier_id: int) -> bool:
        """
        حذف مورد
        
        Args:
            supplier_id: معرف المورد
            
        Returns:
            True إذا نجح الحذف، False إذا فشل
        """
        # التحقق من وجود عقود مرتبطة بالمورد
        contracts = self.db_manager.fetch_query(
            "SELECT COUNT(*) as count FROM contracts WHERE supplier_id = ?",
            (supplier_id,)
        )
        
        if contracts and contracts[0]['count'] > 0:
            # تغيير الحالة إلى غير نشط بدلاً من الحذف
            return self.update_supplier(supplier_id, {'status': 'غير نشط'})
        else:
            # حذف المورد إذا لم توجد عقود مرتبطة
            query = "DELETE FROM suppliers WHERE supplier_id = ?"
            return self.db_manager.execute_query(query, (supplier_id,))
    
    def search_suppliers(self, search_term: str) -> List[Dict]:
        """
        البحث في الموردين
        
        Args:
            search_term: مصطلح البحث
            
        Returns:
            قائمة بالموردين المطابقين
        """
        query = """
        SELECT * FROM suppliers 
        WHERE supplier_name LIKE ? OR contact_person LIKE ? 
           OR phone LIKE ? OR email LIKE ?
        ORDER BY supplier_name
        """
        search_pattern = f"%{search_term}%"
        params = (search_pattern, search_pattern, search_pattern, search_pattern)
        return self.db_manager.fetch_query(query, params)
    
    def get_supplier_contracts(self, supplier_id: int) -> List[Dict]:
        """
        الحصول على عقود المورد
        
        Args:
            supplier_id: معرف المورد
            
        Returns:
            قائمة بعقود المورد
        """
        query = """
        SELECT c.*, s.supplier_name 
        FROM contracts c
        JOIN suppliers s ON c.supplier_id = s.supplier_id
        WHERE c.supplier_id = ?
        ORDER BY c.start_date DESC
        """
        return self.db_manager.fetch_query(query, (supplier_id,))
    
    def validate_supplier_data(self, supplier_data: Dict) -> List[str]:
        """
        التحقق من صحة بيانات المورد
        
        Args:
            supplier_data: بيانات المورد
            
        Returns:
            قائمة بالأخطاء (فارغة إذا كانت البيانات صحيحة)
        """
        errors = []
        
        # التحقق من الحقول المطلوبة
        if not supplier_data.get('supplier_name'):
            errors.append("اسم المورد مطلوب")

        if not supplier_data.get('supplier_category'):
            errors.append("قسم المورد مطلوب")
        elif supplier_data.get('supplier_category') not in ['بهارات', 'استهلاكي', 'أجبان']:
            errors.append("قسم المورد يجب أن يكون: بهارات، استهلاكي، أو أجبان")

        if not supplier_data.get('phone'):
            errors.append("رقم الهاتف مطلوب")
        
        # التحقق من تفرد الهوية الوطنية
        national_id = supplier_data.get('national_id')
        if national_id:
            existing = self.db_manager.fetch_one(
                "SELECT supplier_id FROM suppliers WHERE national_id = ?",
                (national_id,)
            )
            if existing:
                errors.append("الهوية الوطنية مسجلة مسبقاً")
        
        return errors
