#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
Database Manager for Supplier Contract Management System
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_path: str = "supplier_contracts.db"):
        """
        تهيئة مدير قاعدة البيانات
        
        Args:
            db_path: مسار ملف قاعدة البيانات
        """
        self.db_path = db_path
        self.connection = None
        self.connect()
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
            return True
        except sqlite3.Error as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, query: str, params: tuple = ()) -> bool:
        """
        تنفيذ استعلام (INSERT, UPDATE, DELETE)
        
        Args:
            query: الاستعلام
            params: المعاملات
            
        Returns:
            True إذا نجح التنفيذ، False إذا فشل
        """
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            self.connection.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            self.connection.rollback()
            return False
    
    def fetch_query(self, query: str, params: tuple = ()) -> List[Dict]:
        """
        تنفيذ استعلام SELECT وإرجاع النتائج
        
        Args:
            query: الاستعلام
            params: المعاملات
            
        Returns:
            قائمة بالنتائج
        """
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
        except sqlite3.Error as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return []
    
    def fetch_one(self, query: str, params: tuple = ()) -> Optional[Dict]:
        """
        تنفيذ استعلام SELECT وإرجاع نتيجة واحدة

        Args:
            query: الاستعلام
            params: المعاملات

        Returns:
            النتيجة الأولى أو None
        """
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            row = cursor.fetchone()
            return dict(row) if row else None
        except sqlite3.Error as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return None

    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""

        # جدول الموردين
        suppliers_table = """
        CREATE TABLE IF NOT EXISTS suppliers (
            supplier_id INTEGER PRIMARY KEY AUTOINCREMENT,
            supplier_name TEXT NOT NULL,
            supplier_category TEXT NOT NULL CHECK (supplier_category IN ('بهارات', 'استهلاكي', 'أجبان')),
            contact_person TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            national_id TEXT UNIQUE,
            commercial_registration TEXT,
            registration_date DATE DEFAULT CURRENT_DATE,
            status TEXT DEFAULT 'نشط',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """

        # جدول أنواع الأنشطة
        activity_types_table = """
        CREATE TABLE IF NOT EXISTS activity_types (
            activity_type_id INTEGER PRIMARY KEY AUTOINCREMENT,
            activity_name TEXT NOT NULL,
            activity_type TEXT NOT NULL CHECK (activity_type IN ('استهلاكي', 'غذائي')),
            base_price_per_sqm DECIMAL(10,2),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """

        # جدول عناصر الإيجار (الطبليات، القواطع، الجندولات، الاستنادات)
        rental_items_table = """
        CREATE TABLE IF NOT EXISTS rental_items (
            item_id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_type TEXT NOT NULL CHECK (item_type IN ('طبلية', 'قاطع', 'جندولة', 'استناد')),
            item_name TEXT NOT NULL,
            location TEXT,
            area_sqm DECIMAL(10,2),
            total_eyes INTEGER DEFAULT 0,
            eye_size_cm INTEGER DEFAULT 60,
            price_per_eye DECIMAL(10,2) DEFAULT 60.0,
            status TEXT DEFAULT 'متاح' CHECK (status IN ('متاح', 'مؤجر', 'صيانة', 'غير متاح')),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """

        # جدول العيون المؤجرة
        rented_eyes_table = """
        CREATE TABLE IF NOT EXISTS rented_eyes (
            rented_eye_id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_id INTEGER NOT NULL,
            contract_id INTEGER NOT NULL,
            supplier_id INTEGER NOT NULL,
            eye_number INTEGER NOT NULL,
            rental_price DECIMAL(10,2) NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            status TEXT DEFAULT 'نشط' CHECK (status IN ('نشط', 'منتهي', 'ملغي')),
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (item_id) REFERENCES rental_items (item_id),
            FOREIGN KEY (contract_id) REFERENCES contracts (contract_id),
            FOREIGN KEY (supplier_id) REFERENCES suppliers (supplier_id),
            UNIQUE(item_id, eye_number, start_date)
        )
        """

        # جدول العقود
        contracts_table = """
        CREATE TABLE IF NOT EXISTS contracts (
            contract_id INTEGER PRIMARY KEY AUTOINCREMENT,
            supplier_id INTEGER NOT NULL,
            contract_number TEXT UNIQUE NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            total_amount DECIMAL(12,2),
            payment_terms TEXT,
            contract_status TEXT DEFAULT 'نشط' CHECK (contract_status IN ('نشط', 'منتهي', 'ملغي', 'معلق')),
            terms_conditions TEXT,
            notes TEXT,
            created_date DATE DEFAULT CURRENT_DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (supplier_id) REFERENCES suppliers (supplier_id)
        )
        """

        # جدول عناصر العقود
        contract_items_table = """
        CREATE TABLE IF NOT EXISTS contract_items (
            contract_item_id INTEGER PRIMARY KEY AUTOINCREMENT,
            contract_id INTEGER NOT NULL,
            item_id INTEGER NOT NULL,
            activity_type_id INTEGER NOT NULL,
            area_sqm DECIMAL(10,2) NOT NULL,
            price_per_sqm DECIMAL(10,2) NOT NULL,
            total_price DECIMAL(12,2) NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            status TEXT DEFAULT 'نشط' CHECK (status IN ('نشط', 'منتهي', 'ملغي')),
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (contract_id) REFERENCES contracts (contract_id),
            FOREIGN KEY (item_id) REFERENCES rental_items (item_id),
            FOREIGN KEY (activity_type_id) REFERENCES activity_types (activity_type_id)
        )
        """

        # جدول المدفوعات
        payments_table = """
        CREATE TABLE IF NOT EXISTS payments (
            payment_id INTEGER PRIMARY KEY AUTOINCREMENT,
            contract_id INTEGER NOT NULL,
            amount DECIMAL(12,2) NOT NULL,
            payment_date DATE NOT NULL,
            due_date DATE,
            payment_method TEXT CHECK (payment_method IN ('نقدي', 'شيك', 'تحويل بنكي', 'أخرى')),
            payment_status TEXT DEFAULT 'مدفوع' CHECK (payment_status IN ('مدفوع', 'معلق', 'مرفوض')),
            receipt_number TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (contract_id) REFERENCES contracts (contract_id)
        )
        """

        # تنفيذ إنشاء الجداول
        tables = [
            suppliers_table,
            activity_types_table,
            rental_items_table,
            rented_eyes_table,
            contracts_table,
            contract_items_table,
            payments_table
        ]

        try:
            for table_sql in tables:
                self.execute_query(table_sql)

            # إدراج بيانات أولية لأنواع الأنشطة
            self._insert_initial_data()

            print("تم إنشاء جداول قاعدة البيانات بنجاح")
            return True

        except Exception as e:
            print(f"خطأ في إنشاء الجداول: {e}")
            return False

    def _insert_initial_data(self):
        """إدراج البيانات الأولية"""

        # التحقق من وجود أنواع الأنشطة
        existing_activities = self.fetch_query("SELECT COUNT(*) as count FROM activity_types")
        if existing_activities and existing_activities[0]['count'] == 0:

            # إدراج أنواع الأنشطة الأولية
            initial_activities = [
                ('بقالة', 'استهلاكي', 50.0, 'بيع المواد الاستهلاكية والمنزلية'),
                ('خضار وفواكه', 'غذائي', 60.0, 'بيع الخضار والفواكه الطازجة'),
                ('لحوم ودواجن', 'غذائي', 70.0, 'بيع اللحوم والدواجن الطازجة'),
                ('أسماك', 'غذائي', 65.0, 'بيع الأسماك والمأكولات البحرية'),
                ('مخبز', 'غذائي', 55.0, 'بيع الخبز والمعجنات'),
                ('حلويات', 'غذائي', 60.0, 'بيع الحلويات والكعك'),
                ('ملابس', 'استهلاكي', 45.0, 'بيع الملابس والأزياء'),
                ('أحذية', 'استهلاكي', 40.0, 'بيع الأحذية والصنادل'),
                ('إكسسوارات', 'استهلاكي', 35.0, 'بيع الإكسسوارات والمجوهرات'),
                ('أدوات منزلية', 'استهلاكي', 45.0, 'بيع الأدوات المنزلية والكهربائية')
            ]

            for activity in initial_activities:
                self.execute_query(
                    "INSERT INTO activity_types (activity_name, activity_type, base_price_per_sqm, description) VALUES (?, ?, ?, ?)",
                    activity
                )

        # إدراج طبليات أولية مع نظام العيون
        existing_items = self.fetch_query("SELECT COUNT(*) as count FROM rental_items")
        if existing_items and existing_items[0]['count'] == 0:

            # إدراج طبليات أولية
            initial_items = [
                ('طبلية', 'طبلية البهارات 1', 'قسم البهارات', 2.4, 4, 60, 60.0, 'متاح', 'طبلية مخصصة للبهارات - 4 عيون'),
                ('طبلية', 'طبلية البهارات 2', 'قسم البهارات', 2.4, 4, 60, 60.0, 'متاح', 'طبلية مخصصة للبهارات - 4 عيون'),
                ('طبلية', 'طبلية الاستهلاكي 1', 'قسم الاستهلاكي', 2.4, 4, 60, 60.0, 'متاح', 'طبلية مخصصة للمواد الاستهلاكية - 4 عيون'),
                ('طبلية', 'طبلية الاستهلاكي 2', 'قسم الاستهلاكي', 2.4, 4, 60, 60.0, 'متاح', 'طبلية مخصصة للمواد الاستهلاكية - 4 عيون'),
                ('طبلية', 'طبلية الأجبان 1', 'قسم الأجبان', 2.4, 4, 60, 60.0, 'متاح', 'طبلية مخصصة للأجبان - 4 عيون'),
                ('طبلية', 'طبلية الأجبان 2', 'قسم الأجبان', 2.4, 4, 60, 60.0, 'متاح', 'طبلية مخصصة للأجبان - 4 عيون'),
            ]

            for item in initial_items:
                self.execute_query(
                    """INSERT INTO rental_items
                    (item_type, item_name, location, area_sqm, total_eyes, eye_size_cm, price_per_eye, status, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                    item
                )
