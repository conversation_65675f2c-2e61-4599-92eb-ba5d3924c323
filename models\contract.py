#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج العقد
Contract Model for Contract Management System
"""

from datetime import datetime, date
from typing import Optional, List, Dict

class Contract:
    """نموذج العقد"""
    
    def __init__(self, db_manager):
        """
        تهيئة نموذج العقد
        
        Args:
            db_manager: مدير قاعدة البيانات
        """
        self.db_manager = db_manager
    
    def create_contract(self, contract_data: Dict) -> Optional[int]:
        """
        إنشاء عقد جديد
        
        Args:
            contract_data: بيانات العقد
            
        Returns:
            معرف العقد الجديد أو None في حالة الفشل
        """
        query = """
        INSERT INTO contracts (
            supplier_id, contract_number, start_date, end_date,
            total_amount, payment_terms, contract_status,
            terms_conditions, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params = (
            contract_data.get('supplier_id'),
            contract_data.get('contract_number'),
            contract_data.get('start_date'),
            contract_data.get('end_date'),
            contract_data.get('total_amount'),
            contract_data.get('payment_terms'),
            contract_data.get('contract_status', 'نشط'),
            contract_data.get('terms_conditions'),
            contract_data.get('notes')
        )
        
        if self.db_manager.execute_query(query, params):
            result = self.db_manager.fetch_one("SELECT last_insert_rowid() as id")
            return result['id'] if result else None
        return None
    
    def get_contract_by_id(self, contract_id: int) -> Optional[Dict]:
        """
        الحصول على عقد بالمعرف
        
        Args:
            contract_id: معرف العقد
            
        Returns:
            بيانات العقد أو None
        """
        query = """
        SELECT c.*, s.supplier_name, s.contact_person, s.phone
        FROM contracts c
        JOIN suppliers s ON c.supplier_id = s.supplier_id
        WHERE c.contract_id = ?
        """
        return self.db_manager.fetch_one(query, (contract_id,))
    
    def get_all_contracts(self, status: Optional[str] = None) -> List[Dict]:
        """
        الحصول على جميع العقود
        
        Args:
            status: حالة العقد (اختياري)
            
        Returns:
            قائمة بالعقود
        """
        base_query = """
        SELECT c.*, s.supplier_name, s.contact_person
        FROM contracts c
        JOIN suppliers s ON c.supplier_id = s.supplier_id
        """
        
        if status:
            query = base_query + " WHERE c.contract_status = ? ORDER BY c.start_date DESC"
            return self.db_manager.fetch_query(query, (status,))
        else:
            query = base_query + " ORDER BY c.start_date DESC"
            return self.db_manager.fetch_query(query)
    
    def update_contract(self, contract_id: int, contract_data: Dict) -> bool:
        """
        تحديث بيانات العقد
        
        Args:
            contract_id: معرف العقد
            contract_data: البيانات الجديدة
            
        Returns:
            True إذا نجح التحديث، False إذا فشل
        """
        query = """
        UPDATE contracts SET
            supplier_id = ?, contract_number = ?, start_date = ?, end_date = ?,
            total_amount = ?, payment_terms = ?, contract_status = ?,
            terms_conditions = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
        WHERE contract_id = ?
        """
        
        params = (
            contract_data.get('supplier_id'),
            contract_data.get('contract_number'),
            contract_data.get('start_date'),
            contract_data.get('end_date'),
            contract_data.get('total_amount'),
            contract_data.get('payment_terms'),
            contract_data.get('contract_status'),
            contract_data.get('terms_conditions'),
            contract_data.get('notes'),
            contract_id
        )
        
        return self.db_manager.execute_query(query, params)
    
    def delete_contract(self, contract_id: int) -> bool:
        """
        حذف عقد
        
        Args:
            contract_id: معرف العقد
            
        Returns:
            True إذا نجح الحذف، False إذا فشل
        """
        # حذف عناصر العقد أولاً
        self.db_manager.execute_query(
            "DELETE FROM contract_items WHERE contract_id = ?",
            (contract_id,)
        )
        
        # حذف المدفوعات
        self.db_manager.execute_query(
            "DELETE FROM payments WHERE contract_id = ?",
            (contract_id,)
        )
        
        # حذف العقد
        query = "DELETE FROM contracts WHERE contract_id = ?"
        return self.db_manager.execute_query(query, (contract_id,))
    
    def get_contract_items(self, contract_id: int) -> List[Dict]:
        """
        الحصول على عناصر العقد
        
        Args:
            contract_id: معرف العقد
            
        Returns:
            قائمة بعناصر العقد
        """
        query = """
        SELECT ci.*, ri.item_name, ri.item_type, ri.location,
               at.activity_name, at.activity_type
        FROM contract_items ci
        JOIN rental_items ri ON ci.item_id = ri.item_id
        JOIN activity_types at ON ci.activity_type_id = at.activity_type_id
        WHERE ci.contract_id = ?
        ORDER BY ci.start_date
        """
        return self.db_manager.fetch_query(query, (contract_id,))
    
    def add_contract_item(self, contract_item_data: Dict) -> Optional[int]:
        """
        إضافة عنصر للعقد
        
        Args:
            contract_item_data: بيانات عنصر العقد
            
        Returns:
            معرف عنصر العقد الجديد أو None
        """
        query = """
        INSERT INTO contract_items (
            contract_id, item_id, activity_type_id, area_sqm,
            price_per_sqm, total_price, start_date, end_date,
            status, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params = (
            contract_item_data.get('contract_id'),
            contract_item_data.get('item_id'),
            contract_item_data.get('activity_type_id'),
            contract_item_data.get('area_sqm'),
            contract_item_data.get('price_per_sqm'),
            contract_item_data.get('total_price'),
            contract_item_data.get('start_date'),
            contract_item_data.get('end_date'),
            contract_item_data.get('status', 'نشط'),
            contract_item_data.get('notes')
        )
        
        if self.db_manager.execute_query(query, params):
            result = self.db_manager.fetch_one("SELECT last_insert_rowid() as id")
            return result['id'] if result else None
        return None
    
    def search_contracts(self, search_term: str) -> List[Dict]:
        """
        البحث في العقود
        
        Args:
            search_term: مصطلح البحث
            
        Returns:
            قائمة بالعقود المطابقة
        """
        query = """
        SELECT c.*, s.supplier_name, s.contact_person
        FROM contracts c
        JOIN suppliers s ON c.supplier_id = s.supplier_id
        WHERE c.contract_number LIKE ? OR s.supplier_name LIKE ?
        ORDER BY c.start_date DESC
        """
        search_pattern = f"%{search_term}%"
        params = (search_pattern, search_pattern)
        return self.db_manager.fetch_query(query, params)
    
    def get_expiring_contracts(self, days: int = 30) -> List[Dict]:
        """
        الحصول على العقود المنتهية الصلاحية قريباً
        
        Args:
            days: عدد الأيام للتحقق من انتهاء الصلاحية
            
        Returns:
            قائمة بالعقود المنتهية الصلاحية قريباً
        """
        query = """
        SELECT c.*, s.supplier_name, s.contact_person, s.phone
        FROM contracts c
        JOIN suppliers s ON c.supplier_id = s.supplier_id
        WHERE c.contract_status = 'نشط' 
          AND date(c.end_date) <= date('now', '+' || ? || ' days')
        ORDER BY c.end_date
        """
        return self.db_manager.fetch_query(query, (days,))
    
    def generate_contract_number(self) -> str:
        """
        توليد رقم عقد جديد
        
        Returns:
            رقم العقد الجديد
        """
        current_year = datetime.now().year
        
        # الحصول على آخر رقم عقد في السنة الحالية
        query = """
        SELECT contract_number FROM contracts 
        WHERE contract_number LIKE ?
        ORDER BY contract_id DESC LIMIT 1
        """
        pattern = f"{current_year}-%"
        result = self.db_manager.fetch_one(query, (pattern,))
        
        if result:
            # استخراج الرقم التسلسلي
            last_number = result['contract_number'].split('-')[-1]
            next_number = int(last_number) + 1
        else:
            next_number = 1
        
        return f"{current_year}-{next_number:04d}"
