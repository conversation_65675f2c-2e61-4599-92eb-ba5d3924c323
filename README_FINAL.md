# 🏢 نظام إدارة عقود الموردين والإيجارات
## جمعية المنقف التعاونية

<div align="center">

![النظام](https://img.shields.io/badge/النظام-إدارة_العقود-blue?style=for-the-badge)
![الإصدار](https://img.shields.io/badge/الإصدار-1.0.0-green?style=for-the-badge)
![Node.js](https://img.shields.io/badge/Node.js-16+-brightgreen?style=for-the-badge)
![MySQL](https://img.shields.io/badge/MySQL-8.0+-orange?style=for-the-badge)

**🎯 نظام متكامل ومتطور لإدارة عقود الموردين وإيجار العيون في السوق المركزي**

[🚀 البدء السريع](#-البدء-السريع) • 
[🎨 الواجهات](#-الواجهات-المتاحة) • 
[📖 الوثائق](#-الوثائق) • 
[🛠️ التطوير](#️-التطوير)

</div>

---

## 🚀 البدء السريع

### ⚡ تشغيل فوري (خطوة واحدة)
```bash
node start-system.js
```

### 🎯 تشغيل مباشر
```bash
npm start
# ثم افتح: http://localhost:3000/launch-interface.html
```

### 📋 تشغيل يدوي
```bash
# 1. تثبيت التبعيات
npm install

# 2. إعداد قاعدة البيانات
npm run setup:db

# 3. تشغيل النظام
npm start
```

---

## 🎨 الواجهات المتاحة

### 👑 الواجهة المتطورة (Ultimate Interface)
**الرابط**: `/ultimate_interface.html`
- ✨ تصميم Glass Morphism متطور
- 🎭 تأثيرات بصرية متقدمة
- 📱 قوائم منسدلة تفاعلية
- 🌟 تجربة مستخدم مميزة

### 💼 الواجهة الاحترافية (Professional Interface)
**الرابط**: `/professional_interface.html`
- 🏢 تصميم احترافي متوازن
- ⚡ أداء سريع ومستقر
- 🎯 سهولة في الاستخدام
- 📊 تنظيم ممتاز للمحتوى

### 📋 إدارة العقود المتقدمة
**الرابط**: `/advanced_contracts_system.html`
- 📄 إدارة شاملة للعقود
- ⏰ تتبع المواعيد والتنبيهات
- 💰 إدارة الدفعات والفواتير
- 📊 تقارير مفصلة

### 👥 إدارة الموردين المحسنة
**الرابط**: `/enhanced_web_interface.html`
- 🏪 إدارة شاملة للموردين
- 🏷️ تصنيف حسب الفئات
- 🔍 بحث وتصفية متقدمة
- 📁 إدارة الوثائق والملفات

### 🖥️ الواجهة البسيطة
**الرابط**: `/web_interface.html`
- 🎯 تصميم بسيط وواضح
- 📚 سهل التعلم والاستخدام
- ⚡ أداء سريع وخفيف
- 👶 مناسب للمبتدئين

### 📊 لوحة التحكم الرئيسية
**الرابط**: `/frontend/`
- 📈 إحصائيات شاملة
- 📊 رسوم بيانية تفاعلية
- 💹 تقارير مالية مفصلة
- 🎯 مراقبة الأداء

---

## 🎯 الميزات الرئيسية

### 🏢 إدارة الموردين المتقدمة
- ✅ تصنيف الموردين حسب الأقسام (بهارات، استهلاكي، أجبان)
- ✅ إدارة معلومات الاتصال والوثائق
- ✅ تتبع حالة الموردين وتقييمهم
- ✅ البحث والتصفية المتقدمة

### 📋 نظام العقود الشامل
- ✅ عقود التوريد والإيجار والخدمات
- ✅ تتبع حالة العقود ومواعيد الانتهاء
- ✅ إدارة شروط الدفع والأحكام
- ✅ تنبيهات انتهاء العقود التلقائية

### 👁️ نظام العيون المبتكر
- ✅ تقسيم كل طبلية إلى 4 عيون (60×60 سم)
- ✅ تأجير العيون بشكل منفرد أو مجمع
- ✅ حساب الإيجارات تلقائياً (60 د.ك/عين/شهر)
- ✅ تتبع العيون المتاحة والمؤجرة

### 📊 التقارير والإحصائيات
- ✅ تقارير شاملة للموردين والعقود
- ✅ إحصائيات مالية مفصلة
- ✅ تقارير العيون حسب الأقسام
- ✅ تصدير Excel و PDF

### 🔒 الأمان والصلاحيات
- ✅ نظام مصادقة متقدم
- ✅ إدارة صلاحيات المستخدمين
- ✅ تشفير البيانات الحساسة
- ✅ سجل العمليات والأنشطة

---

## 🏗️ التقنيات المستخدمة

### Backend
- **Node.js** - بيئة تشغيل JavaScript
- **Express.js** - إطار عمل الخادم
- **MySQL** - قاعدة البيانات
- **JWT** - المصادقة والتوكن
- **Multer** - رفع الملفات

### Frontend
- **HTML5/CSS3** - هيكل وتصميم الواجهة
- **JavaScript ES6+** - منطق الواجهة
- **Tailwind CSS** - إطار عمل التصميم
- **Chart.js** - الرسوم البيانية
- **Flatpickr** - أدوات التاريخ

### أدوات إضافية
- **ExcelJS** - تصدير Excel
- **jsPDF** - تصدير PDF
- **Sharp** - معالجة الصور
- **Winston** - نظام السجلات

---

## 📁 هيكل المشروع

```
📦 supplier-contracts-system/
├── 📁 backend/                    # الخادم الخلفي
│   ├── 📁 api/                    # خادم API
│   ├── 📁 routes/                 # مسارات API
│   ├── 📁 utils/                  # أدوات مساعدة
│   ├── 📁 database/               # قاعدة البيانات
│   └── 📁 uploads/                # الملفات المرفوعة
├── 📁 frontend/                   # الواجهة الأمامية
│   ├── 📁 assets/                 # الموارد (CSS/JS)
│   └── 📄 index.html              # لوحة التحكم
├── 📁 scripts/                    # سكريبتات الإعداد
├── 📁 gui/                        # واجهة سطح المكتب
├── 📁 models/                     # نماذج البيانات
├── 📁 reports/                    # نظام التقارير
├── 📄 ultimate_interface.html     # الواجهة المتطورة
├── 📄 professional_interface.html # الواجهة الاحترافية
├── 📄 launch-interface.html       # صفحة التشغيل
├── 📄 .env                        # متغيرات البيئة
├── 📄 package.json                # تبعيات المشروع
└── 📄 start-system.js             # سكريبت التشغيل
```

---

## 🛠️ أوامر مفيدة

### تشغيل النظام
```bash
npm start                    # تشغيل الإنتاج
npm run dev                  # تشغيل التطوير مع المراقبة
npm run launch               # تشغيل تفاعلي شامل
```

### إدارة قاعدة البيانات
```bash
npm run setup:db            # إعداد قاعدة البيانات
npm run migrate             # تشغيل التحديثات
npm run seed                # إدراج بيانات تجريبية
npm run backup              # إنشاء نسخة احتياطية
```

### الاختبار والتطوير
```bash
npm test                    # تشغيل الاختبارات
npm run test:watch          # مراقبة الاختبارات
npm run lint                # فحص الكود
npm run lint:fix            # إصلاح مشاكل الكود
```

### الواجهات
```bash
npm run interfaces          # عرض معلومات الواجهات
npm run ultimate            # معلومات الواجهة المتطورة
npm run professional        # معلومات الواجهة الاحترافية
npm run simple              # معلومات الواجهة البسيطة
```

---

## 🔧 الإعدادات

### متغيرات البيئة الأساسية
```env
# الخادم
PORT=3000
NODE_ENV=development

# قاعدة البيانات
DB_HOST=localhost
DB_PORT=3306
DB_NAME=supplier_contracts
DB_USER=contracts_app
DB_PASSWORD=SecurePassword123!

# الأمان
JWT_SECRET=your-secret-key
ENCRYPTION_KEY=your-encryption-key
```

### إعدادات النظام
```env
# الشركة
COMPANY_NAME=جمعية المنقف التعاونية
CURRENCY=KWD
TIMEZONE=Asia/Kuwait

# العيون
EYE_RENTAL_PRICE=60
EYE_SIZE=60x60
TABLE_EYES_COUNT=4
```

---

## 📊 لوحة التحكم

### الواجهات المتاحة

| الواجهة | الرابط | الوصف | مناسبة لـ |
|---------|--------|-------|-----------|
| 🚀 صفحة التشغيل | `/launch-interface.html` | اختيار الواجهة | الجميع |
| 👑 المتطورة | `/ultimate_interface.html` | تصميم متطور | العروض |
| 💼 الاحترافية | `/professional_interface.html` | متوازنة | الاستخدام اليومي |
| 📋 العقود | `/advanced_contracts_system.html` | متخصصة | إدارة العقود |
| 👥 الموردين | `/enhanced_web_interface.html` | متخصصة | إدارة الموردين |
| 🖥️ البسيطة | `/web_interface.html` | بسيطة | المبتدئين |
| 📊 لوحة التحكم | `/frontend/` | إحصائيات | الإدارة |

### API Endpoints

| المسار | الطريقة | الوصف |
|--------|---------|-------|
| `/api/v1/suppliers` | GET/POST | إدارة الموردين |
| `/api/v1/contracts` | GET/POST | إدارة العقود |
| `/api/v1/reports` | GET | التقارير |
| `/api/v1/uploads` | POST | رفع الملفات |

---

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# جميع الاختبارات
npm test

# اختبار شامل للنظام
node scripts/test-system.js

# اختبار قاعدة البيانات
node scripts/test-database.js
```

---

## 🚀 النشر

### النشر التقليدي
```bash
npm run build
npm start
```

### النشر باستخدام Docker
```bash
npm run docker:build
npm run docker:run
```

---

## 📞 الدعم

### الحصول على المساعدة
- 📧 **البريد الإلكتروني**: <EMAIL>
- 📱 **الهاتف**: +965-1234567
- 🌐 **الموقع**: https://manqaf-coop.com

### الوثائق
- 📖 **دليل الواجهات**: `INTERFACES_GUIDE.md`
- 🚀 **دليل البدء السريع**: `QUICK_START_GUIDE.md`
- 🏗️ **هيكل المشروع**: `project_structure.md`

---

## 📄 الترخيص

هذا المشروع مرخص تحت [رخصة MIT](LICENSE)

---

## 🙏 شكر وتقدير

- **جمعية المنقف التعاونية** - العميل والراعي
- **فريق التطوير** - التصميم والتطوير
- **المجتمع المفتوح** - الأدوات والمكتبات

---

<div align="center">

**🎉 مبروك! النظام جاهز للاستخدام**

**صُنع بـ ❤️ لجمعية المنقف التعاونية**

[⬆️ العودة للأعلى](#-نظام-إدارة-عقود-الموردين-والإيجارات)

</div>
