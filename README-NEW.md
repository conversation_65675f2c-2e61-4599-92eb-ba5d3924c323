# نظام إدارة عقود الموردين والإيجارات

## جمعية المنقف التعاونية

نظام متكامل وحديث لإدارة عقود الموردين والإيجارات مصمم خصيصاً لجمعية المنقف التعاونية في دولة الكويت.

![النظام](https://img.shields.io/badge/النسخة-1.0.0-blue.svg)
![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)
![MySQL](https://img.shields.io/badge/MySQL-8.0+-orange.svg)
![الترخيص](https://img.shields.io/badge/الترخيص-MIT-yellow.svg)

## 📋 المحتويات

- [الميزات الرئيسية](#الميزات-الرئيسية)
- [متطلبات النظام](#متطلبات-النظام)
- [التثبيت والإعداد](#التثبيت-والإعداد)
- [الاستخدام](#الاستخدام)
- [هيكل المشروع](#هيكل-المشروع)
- [API Documentation](#api-documentation)
- [النشر](#النشر)
- [المساهمة](#المساهمة)
- [الدعم](#الدعم)

## ✨ الميزات الرئيسية

### 📄 إدارة العقود
- إنشاء وتعديل العقود مع الموردين
- تتبع حالة العقود (مسودة، نشط، منتهي، ملغي)
- إدارة عناصر العقود والمواصفات
- نظام تنبيهات لانتهاء صلاحية العقود
- تجديد العقود التلقائي واليدوي
- نظام اعتماد العقود متعدد المستويات

### 🏢 إدارة الموردين
- قاعدة بيانات شاملة للموردين
- تصنيف الموردين حسب الفئات:
  - موردي التوابل والبهارات
  - موردي السلع الاستهلاكية
  - موردي الأجبان ومنتجات الألبان
- تقييم أداء الموردين
- تتبع تاريخ التعاملات

### 🏠 نظام إيجار الطاولات
- إدارة طاولات السوق المركزي
- تقسيم كل طاولة إلى 4 أقسام (60 سم لكل قسم)
- تسعير موحد: 60 دينار كويتي لكل قسم
- تتبع حالة الإشغال والدفعات
- إدارة عقود الإيجار

### 📊 التقارير والإحصائيات
- تقارير مفصلة عن العقود والموردين
- إحصائيات الأداء والإيرادات
- تصدير البيانات إلى Excel و PDF
- لوحة معلومات تفاعلية مع الرسوم البيانية

### 🔐 الأمان والصلاحيات
- نظام مصادقة متقدم مع JWT
- إدارة الأدوار والصلاحيات
- تشفير البيانات الحساسة
- تسجيل جميع العمليات للمراجعة

### 🌐 واجهة المستخدم
- تصميم عصري ومتجاوب
- دعم كامل للغة العربية (RTL)
- واجهة سهلة الاستخدام
- تجربة مستخدم محسنة للأجهزة المحمولة

## 🔧 متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 10, macOS 10.15, Ubuntu 18.04+
- **Node.js**: الإصدار 18.0 أو أحدث
- **MySQL**: الإصدار 8.0 أو أحدث
- **الذاكرة**: 4 جيجابايت RAM
- **التخزين**: 10 جيجابايت مساحة فارغة

### الموصى به
- **نظام التشغيل**: Ubuntu 22.04 LTS
- **Node.js**: الإصدار 20.0 أو أحدث
- **MySQL**: الإصدار 8.0.35 أو أحدث
- **Redis**: للتخزين المؤقت (اختياري)
- **الذاكرة**: 8 جيجابايت RAM أو أكثر
- **التخزين**: 50 جيجابايت SSD

## 🚀 التثبيت والإعداد

### 1. استنساخ المشروع

```bash
git clone https://github.com/manqaf-coop/supplier-contracts-system.git
cd supplier-contracts-system
```

### 2. تثبيت التبعيات

```bash
npm install
```

### 3. إعداد قاعدة البيانات

```bash
# إنشاء قاعدة البيانات
mysql -u root -p < database/schema.sql

# تشغيل الهجرات
npm run migrate

# إدراج البيانات الأولية
npm run seed
```

### 4. إعداد متغيرات البيئة

```bash
cp .env.example .env
```

قم بتحرير ملف `.env` وإدخال البيانات المطلوبة:

```env
# إعدادات قاعدة البيانات
DB_HOST=localhost
DB_PORT=3306
DB_NAME=supplier_contracts
DB_USER=contracts_app
DB_PASSWORD=SecurePassword123!

# إعدادات JWT
JWT_SECRET=your-very-secure-jwt-secret-key-here
JWT_REFRESH_SECRET=your-very-secure-refresh-secret-key-here
JWT_EXPIRES_IN=24h

# إعدادات الخادم
PORT=3000
NODE_ENV=development
LOG_LEVEL=debug

# إعدادات البريد الإلكتروني (اختياري)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### 5. تشغيل التطبيق

```bash
# للتطوير
npm run dev

# للإنتاج
npm start
```

## 🐳 التشغيل باستخدام Docker

### التشغيل السريع

```bash
# تشغيل جميع الخدمات
docker-compose up -d

# عرض السجلات
docker-compose logs -f app

# إيقاف الخدمات
docker-compose down
```

### مع المراقبة

```bash
# تشغيل مع خدمات المراقبة
docker-compose --profile monitoring up -d
```

## 📖 الاستخدام

### الوصول للنظام

1. افتح المتصفح وانتقل إلى: `http://localhost:3000`
2. استخدم بيانات المدير الافتراضية:
   - **اسم المستخدم**: `admin`
   - **كلمة المرور**: `admin123`

### الوظائف الأساسية

#### إدارة العقود
1. انتقل إلى قسم "إدارة العقود"
2. انقر على "عقد جديد" لإنشاء عقد
3. املأ البيانات المطلوبة واختر المورد
4. أضف عناصر العقد والمواصفات
5. احفظ العقد كمسودة أو أرسله للاعتماد

#### إدارة الموردين
1. انتقل إلى قسم "إدارة الموردين"
2. انقر على "مورد جديد"
3. املأ بيانات المورد واختر الفئة المناسبة
4. احفظ البيانات

#### نظام إيجار الطاولات
1. انتقل إلى قسم "إدارة الطاولات"
2. اختر الطاولة المطلوبة
3. حدد الأقسام المراد تأجيرها
4. أدخل بيانات المستأجر
5. احفظ عقد الإيجار

## 📁 هيكل المشروع

```
supplier-contracts-system/
├── backend/                 # الخادم الخلفي
│   ├── api/                # نقاط النهاية
│   │   └── server.js       # الخادم الرئيسي
│   ├── routes/             # مسارات API
│   │   ├── auth.js         # مسارات المصادقة
│   │   ├── contracts.js    # مسارات العقود
│   │   ├── suppliers.js    # مسارات الموردين
│   │   └── reports.js      # مسارات التقارير
│   ├── middleware/         # الوسطاء
│   │   └── auth.js         # وسيط المصادقة
│   ├── utils/              # الأدوات المساعدة
│   │   ├── database.js     # إدارة قاعدة البيانات
│   │   └── logger.js       # نظام التسجيل
│   ├── uploads/            # الملفات المرفوعة
│   └── logs/               # ملفات السجلات
├── frontend/               # الواجهة الأمامية
│   ├── assets/             # الموارد الثابتة
│   │   ├── css/            # ملفات التنسيق
│   │   ├── js/             # ملفات JavaScript
│   │   └── images/         # الصور
│   ├── pages/              # صفحات النظام
│   └── index.html          # الصفحة الرئيسية
├── database/               # قاعدة البيانات
│   ├── schema.sql          # هيكل قاعدة البيانات
│   ├── migrations/         # الهجرات
│   └── seeds/              # البيانات الأولية
├── docker-compose.yml      # إعداد Docker
├── Dockerfile              # ملف Docker
├── package.json            # تبعيات Node.js
└── README.md               # هذا الملف
```

## 🔌 API Documentation

### المصادقة

#### تسجيل الدخول
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123",
  "remember_me": false
}
```

#### تسجيل الخروج
```http
POST /api/v1/auth/logout
Authorization: Bearer <token>
```

### العقود

#### جلب قائمة العقود
```http
GET /api/v1/contracts?page=1&limit=10&search=&status=active
Authorization: Bearer <token>
```

#### إنشاء عقد جديد
```http
POST /api/v1/contracts
Authorization: Bearer <token>
Content-Type: application/json

{
  "contract_number": "2024-001",
  "contract_name": "عقد توريد توابل",
  "supplier_id": 1,
  "contract_type_id": 1,
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "total_amount": 10000,
  "currency": "KWD"
}
```

### الموردين

#### جلب قائمة الموردين
```http
GET /api/v1/suppliers?page=1&limit=10&category_id=1
Authorization: Bearer <token>
```

## 🚀 النشر

### النشر على خادم Ubuntu

1. **تحديث النظام**
```bash
sudo apt update && sudo apt upgrade -y
```

2. **تثبيت Docker و Docker Compose**
```bash
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

3. **استنساخ المشروع**
```bash
git clone https://github.com/manqaf-coop/supplier-contracts-system.git
cd supplier-contracts-system
```

4. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
nano .env
```

5. **تشغيل النظام**
```bash
docker-compose up -d
```

## 🤝 المساهمة

نرحب بمساهماتكم في تطوير النظام! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى الفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📞 الدعم

### التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +965 1234 5678
- **العنوان**: المنقف، دولة الكويت

### الإبلاغ عن المشاكل

يرجى استخدام [GitHub Issues](https://github.com/manqaf-coop/supplier-contracts-system/issues) للإبلاغ عن المشاكل أو طلب ميزات جديدة.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف [LICENSE](LICENSE) للمزيد من التفاصيل.

---

**جمعية المنقف التعاونية** - نحو مستقبل رقمي أفضل 🚀
