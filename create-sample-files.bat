@echo off
chcp 65001 >nul
title إنشاء ملفات البيانات النموذجية | محمد مرزوق العقاب

echo.
echo 🎉 ===============================================
echo 📊 إنشاء ملفات البيانات النموذجية
echo 🏢 جمعية المنقف التعاونية
echo.
echo 👨‍💻 تطوير وتنفيذ: محمد مرزوق العقاب
echo 🎯 مطور نظم معلومات محترف ومتخصص
echo 🎉 ===============================================
echo.

echo 📦 التحقق من التبعيات...
if not exist "node_modules" (
    echo 🔄 تثبيت التبعيات...
    npm install
    echo.
)

echo 📁 إنشاء مجلد البيانات النموذجية...
if not exist "sample_data" mkdir sample_data

echo.
echo 🚀 بدء إنشاء ملفات البيانات...
echo.

echo 📊 إنشاء ملفات Excel...
node backend/utils/create-excel-files.js

echo.
echo 📋 الملفات المنشأة:
echo.
echo 📄 ملفات CSV:
if exist "sample_data\موردين_جمعية_المنقف.csv" (
    echo    ✅ موردين_جمعية_المنقف.csv
) else (
    echo    ❌ موردين_جمعية_المنقف.csv - غير موجود
)

if exist "sample_data\عقود_جمعية_المنقف.csv" (
    echo    ✅ عقود_جمعية_المنقف.csv
) else (
    echo    ❌ عقود_جمعية_المنقف.csv - غير موجود
)

if exist "sample_data\ايجارات_العيون_جمعية_المنقف.csv" (
    echo    ✅ ايجارات_العيون_جمعية_المنقف.csv
) else (
    echo    ❌ ايجارات_العيون_جمعية_المنقف.csv - غير موجود
)

echo.
echo 📊 ملفات Excel:
if exist "sample_data\بيانات_جمعية_المنقف_الشاملة.xlsx" (
    echo    ✅ بيانات_جمعية_المنقف_الشاملة.xlsx - ملف شامل
) else (
    echo    ❌ بيانات_جمعية_المنقف_الشاملة.xlsx - غير موجود
)

if exist "sample_data\موردين_جمعية_المنقف.xlsx" (
    echo    ✅ موردين_جمعية_المنقف.xlsx
) else (
    echo    ❌ موردين_جمعية_المنقف.xlsx - غير موجود
)

if exist "sample_data\عقود_جمعية_المنقف.xlsx" (
    echo    ✅ عقود_جمعية_المنقف.xlsx
) else (
    echo    ❌ عقود_جمعية_المنقف.xlsx - غير موجود
)

if exist "sample_data\ايجارات_العيون_جمعية_المنقف.xlsx" (
    echo    ✅ ايجارات_العيون_جمعية_المنقف.xlsx
) else (
    echo    ❌ ايجارات_العيون_جمعية_المنقف.xlsx - غير موجود
)

echo.
echo 📊 إحصائيات البيانات النموذجية:
echo    👥 الموردين: 25 مورد (بهارات، استهلاكي، أجبان)
echo    📋 العقود: 30 عقد (توريد، إيجار، خدمات)
echo    👁️ إيجارات العيون: 45 إيجار نشط
echo    🏢 الطبليات: 20 طبلية × 4 أقسام = 80 عين
echo    💰 الإيرادات الشهرية: 2,700 د.ك (45 عين × 60 د.ك)
echo.

echo 🎯 طريقة الاستخدام:
echo    1. افتح نظام رفع الملفات: http://localhost:3000/upload_suppliers.html
echo    2. اختر أحد الملفات من مجلد sample_data
echo    3. ارفع الملف واختبر النظام
echo    4. تحقق من البيانات في واجهات الإدارة
echo.

echo 🔗 الواجهات المتاحة للاختبار:
echo    📁 رفع الملفات: http://localhost:3000/upload_suppliers.html
echo    👥 إدارة الموردين: http://localhost:3000/suppliers_management.html
echo    📋 إدارة العقود: http://localhost:3000/contracts_management.html
echo    👁️ إدارة العيون: http://localhost:3000/eyes_management.html
echo    📊 التقارير: http://localhost:3000/reports_dashboard.html
echo.

echo 💡 نصائح للاختبار:
echo    • ابدأ برفع ملف الموردين أولاً
echo    • ثم ارفع ملف العقود
echo    • أخيراً ارفع ملف إيجارات العيون
echo    • تحقق من التقارير والإحصائيات
echo    • اختبر وظائف البحث والتصفية
echo.

echo 🎉 ===============================================
echo ✅ تم إنشاء جميع ملفات البيانات النموذجية بنجاح!
echo 📁 المجلد: sample_data\
echo 🚀 النظام جاهز للاختبار الشامل
echo.
echo 👨‍💻 تطوير: محمد مرزوق العقاب
echo 🏢 جمعية المنقف التعاونية
echo 🎉 ===============================================
echo.

pause
