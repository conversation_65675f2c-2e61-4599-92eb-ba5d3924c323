<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>إدارة ملفات العقود - جمعية المنقف التعاونية</title>
  
  <!-- Meta Tags -->
  <meta name="description" content="نظام إدارة ملفات العقود PDF - تطوير محمد مرزوق العقاب">
  <meta name="keywords" content="عقود, ملفات PDF, إدارة, جمعية المنقف, الكويت">
  <meta name="author" content="محمد مرزوق العقاب">
  
  <!-- CSS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
  
  <!-- Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            'cairo': ['Cairo', 'sans-serif'],
          },
          colors: {
            'primary': {
              50: '#f0f9ff',
              500: '#0ea5e9',
              600: '#0284c7',
              700: '#0369a1',
              800: '#075985',
              900: '#0c4a6e',
            }
          }
        }
      }
    }
  </script>
  
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
  
  <style>
    body {
      font-family: 'Cairo', sans-serif;
      background: linear-gradient(135deg, #0a0f1c 0%, #1e293b 50%, #334155 100%);
      min-height: 100vh;
    }
    
    .glass-card {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
    }
    
    .contract-card {
      transition: all 0.3s ease;
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .contract-card:hover {
      transform: translateY(-5px);
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(59, 130, 246, 0.5);
      box-shadow: 0 20px 40px rgba(59, 130, 246, 0.1);
    }
    
    .search-input {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
    }
    
    .search-input::placeholder {
      color: rgba(255, 255, 255, 0.6);
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border: none;
      transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
    }
    
    .loading {
      opacity: 0.6;
      pointer-events: none;
    }
    
    .file-size {
      color: #10b981;
      font-weight: 600;
    }
    
    .contract-number {
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      color: white;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 600;
    }
  </style>
</head>
<body class="min-h-screen">
  <!-- Header -->
  <header class="glass-card rounded-none border-x-0 border-t-0 mb-8">
    <div class="container mx-auto px-6 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4 space-x-reverse">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
            <i class="fas fa-file-contract text-white text-xl"></i>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-white">إدارة ملفات العقود</h1>
            <p class="text-blue-200">جمعية المنقف التعاونية</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-4 space-x-reverse">
          <button onclick="importSuppliers()" class="btn-primary px-6 py-3 rounded-xl text-white font-semibold">
            <i class="fas fa-download mr-2"></i>
            استيراد الموردين
          </button>
          <button onclick="refreshFiles()" class="bg-green-600 hover:bg-green-700 px-6 py-3 rounded-xl text-white font-semibold transition-all">
            <i class="fas fa-sync-alt mr-2"></i>
            تحديث
          </button>
          <a href="professional_ultimate_interface.html" class="bg-gray-600 hover:bg-gray-700 px-6 py-3 rounded-xl text-white font-semibold transition-all">
            <i class="fas fa-home mr-2"></i>
            الرئيسية
          </a>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <div class="container mx-auto px-6">
    <!-- Search and Stats -->
    <div class="glass-card rounded-2xl p-6 mb-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Search -->
        <div class="lg:col-span-2">
          <div class="relative">
            <i class="fas fa-search absolute right-4 top-1/2 transform -translate-y-1/2 text-blue-300"></i>
            <input 
              type="text" 
              id="searchInput"
              placeholder="البحث في ملفات العقود (اسم الشركة، رقم العقد...)"
              class="search-input w-full pr-12 pl-4 py-4 rounded-xl text-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              onkeyup="searchContracts()"
            >
          </div>
        </div>
        
        <!-- Stats -->
        <div class="text-center">
          <div class="text-3xl font-bold text-white mb-2" id="totalFiles">0</div>
          <div class="text-blue-200">إجمالي ملفات العقود</div>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div id="loading" class="text-center py-12">
      <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      <p class="text-white mt-4">جاري تحميل ملفات العقود...</p>
    </div>

    <!-- Contracts Grid -->
    <div id="contractsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8" style="display: none;">
      <!-- Contract cards will be inserted here -->
    </div>

    <!-- No Results -->
    <div id="noResults" class="text-center py-12" style="display: none;">
      <i class="fas fa-search text-6xl text-gray-400 mb-4"></i>
      <h3 class="text-xl font-semibold text-white mb-2">لا توجد نتائج</h3>
      <p class="text-gray-300">لم يتم العثور على ملفات عقود تطابق البحث</p>
    </div>
  </div>

  <!-- Footer -->
  <footer class="glass-card rounded-none border-x-0 border-b-0 mt-16">
    <div class="container mx-auto px-6 py-6">
      <div class="text-center">
        <p class="text-blue-200">
          © 2024 جمعية المنقف التعاونية - جميع الحقوق محفوظة
        </p>
        <p class="text-blue-300 mt-2">
          <i class="fas fa-code mr-2"></i>
          تطوير وتنفيذ: <span class="font-bold text-white">محمد مرزوق العقاب</span>
        </p>
      </div>
    </div>
  </footer>

  <!-- Notification -->
  <div id="notification" class="fixed top-4 left-4 right-4 z-50 transform -translate-y-full transition-transform duration-300">
    <div class="glass-card rounded-xl p-4 max-w-md mx-auto">
      <div class="flex items-center">
        <i id="notificationIcon" class="fas fa-info-circle text-blue-400 text-xl mr-3"></i>
        <span id="notificationText" class="text-white font-semibold"></span>
      </div>
    </div>
  </div>

  <script>
    let contractsData = [];
    let filteredContracts = [];

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
      loadContractFiles();
    });

    // Load contract files
    async function loadContractFiles() {
      try {
        showLoading(true);
        
        const response = await fetch('/api/contracts/files');
        const result = await response.json();
        
        if (result.success) {
          contractsData = result.data;
          filteredContracts = [...contractsData];
          displayContracts(filteredContracts);
          updateStats();
          showNotification('تم تحميل ملفات العقود بنجاح', 'success');
        } else {
          throw new Error(result.error || 'خطأ في تحميل الملفات');
        }
      } catch (error) {
        console.error('Error loading contracts:', error);
        showNotification('خطأ في تحميل ملفات العقود', 'error');
      } finally {
        showLoading(false);
      }
    }

    // Display contracts
    function displayContracts(contracts) {
      const grid = document.getElementById('contractsGrid');
      const noResults = document.getElementById('noResults');
      
      if (contracts.length === 0) {
        grid.style.display = 'none';
        noResults.style.display = 'block';
        return;
      }
      
      grid.style.display = 'grid';
      noResults.style.display = 'none';
      
      grid.innerHTML = contracts.map(contract => `
        <div class="contract-card rounded-2xl p-6 animate__animated animate__fadeInUp">
          <div class="flex items-start justify-between mb-4">
            <span class="contract-number">${contract.contractNumber}</span>
            <span class="file-size">${contract.fileSize} KB</span>
          </div>
          
          <h3 class="text-lg font-bold text-white mb-3 line-clamp-2">
            ${contract.companyName}
          </h3>
          
          <div class="space-y-2 mb-4">
            <div class="flex items-center text-blue-200">
              <i class="fas fa-file-pdf text-red-400 mr-2"></i>
              <span class="text-sm">${contract.fileName}</span>
            </div>
            <div class="flex items-center text-blue-200">
              <i class="fas fa-calendar text-green-400 mr-2"></i>
              <span class="text-sm">${new Date(contract.lastModified).toLocaleDateString('en-GB')}</span>
            </div>
          </div>
          
          <div class="flex space-x-2 space-x-reverse">
            <button 
              onclick="viewContract('${contract.filePath}')" 
              class="flex-1 btn-primary py-2 px-4 rounded-lg text-white font-semibold text-sm"
            >
              <i class="fas fa-eye mr-1"></i>
              عرض
            </button>
            <button 
              onclick="downloadContract('${contract.filePath}', '${contract.fileName}')" 
              class="flex-1 bg-green-600 hover:bg-green-700 py-2 px-4 rounded-lg text-white font-semibold text-sm transition-all"
            >
              <i class="fas fa-download mr-1"></i>
              تحميل
            </button>
          </div>
        </div>
      `).join('');
    }

    // Search contracts
    function searchContracts() {
      const query = document.getElementById('searchInput').value.toLowerCase().trim();

      if (query === '') {
        filteredContracts = [...contractsData];
      } else {
        filteredContracts = contractsData.filter(contract =>
          contract.companyName.toLowerCase().includes(query) ||
          contract.contractNumber.toLowerCase().includes(query) ||
          contract.fileName.toLowerCase().includes(query)
        );
      }

      displayContracts(filteredContracts);
      updateStats();
    }

    // Update statistics
    function updateStats() {
      document.getElementById('totalFiles').textContent = filteredContracts.length;
    }

    // View contract
    function viewContract(filePath) {
      window.open(filePath, '_blank');
    }

    // Download contract
    function downloadContract(filePath, fileName) {
      const link = document.createElement('a');
      link.href = filePath;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      showNotification('تم بدء تحميل الملف', 'success');
    }

    // Import suppliers
    async function importSuppliers() {
      try {
        showNotification('جاري استيراد الموردين...', 'info');

        const response = await fetch('/api/import/suppliers', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        const result = await response.json();

        if (result.success) {
          showNotification(`تم استيراد ${result.processed} مورد بنجاح!`, 'success');
        } else {
          throw new Error(result.error || 'خطأ في استيراد الموردين');
        }
      } catch (error) {
        console.error('Error importing suppliers:', error);
        showNotification('خطأ في استيراد الموردين', 'error');
      }
    }

    // Refresh files
    function refreshFiles() {
      loadContractFiles();
    }

    // Show loading
    function showLoading(show) {
      const loading = document.getElementById('loading');
      const grid = document.getElementById('contractsGrid');

      if (show) {
        loading.style.display = 'block';
        grid.style.display = 'none';
      } else {
        loading.style.display = 'none';
        if (filteredContracts.length > 0) {
          grid.style.display = 'grid';
        }
      }
    }

    // Show notification
    function showNotification(message, type = 'info') {
      const notification = document.getElementById('notification');
      const icon = document.getElementById('notificationIcon');
      const text = document.getElementById('notificationText');

      // Set icon and color based on type
      const config = {
        success: { icon: 'fa-check-circle', color: 'text-green-400' },
        error: { icon: 'fa-times-circle', color: 'text-red-400' },
        warning: { icon: 'fa-exclamation-triangle', color: 'text-yellow-400' },
        info: { icon: 'fa-info-circle', color: 'text-blue-400' }
      };

      const typeConfig = config[type] || config.info;
      icon.className = `fas ${typeConfig.icon} ${typeConfig.color} text-xl mr-3`;
      text.textContent = message;

      // Show notification
      notification.style.transform = 'translateY(0)';

      // Hide after 3 seconds
      setTimeout(() => {
        notification.style.transform = 'translateY(-100%)';
      }, 3000);
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
      // Ctrl/Cmd + K for search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        document.getElementById('searchInput').focus();
      }

      // F5 for refresh
      if (e.key === 'F5') {
        e.preventDefault();
        refreshFiles();
      }
    });
  </script>
</body>
</html>
