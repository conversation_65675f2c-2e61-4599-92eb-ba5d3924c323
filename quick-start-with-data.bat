@echo off
chcp 65001 >nul
title تشغيل سريع مع البيانات النموذجية | محمد مرزوق العقاب

echo.
echo 🎉 ===============================================
echo 🚀 تشغيل سريع مع البيانات النموذجية
echo 🏢 جمعية المنقف التعاونية
echo.
echo 👨‍💻 تطوير وتنفيذ: محمد مرزوق العقاب
echo 🎯 مطور نظم معلومات محترف ومتخصص
echo 🎉 ===============================================
echo.

echo 📦 التحقق من التبعيات...
if not exist "node_modules" (
    echo 🔄 تثبيت التبعيات...
    npm install
    echo.
)

echo 📊 إنشاء ملفات البيانات النموذجية...
if not exist "sample_data" mkdir sample_data

echo 📄 إنشاء ملفات Excel والـ CSV...
node backend/utils/create-excel-files.js

echo.
echo ✅ تم إنشاء الملفات النموذجية:
echo    📊 بيانات_جمعية_المنقف_الشاملة.xlsx
echo    👥 موردين_جمعية_المنقف.xlsx
echo    📋 عقود_جمعية_المنقف.xlsx
echo    👁️ ايجارات_العيون_جمعية_المنقف.xlsx
echo    📄 ملفات CSV متنوعة
echo.

echo 🎯 البيانات النموذجية المتاحة:
echo    👥 25 مورد (بهارات، استهلاكي، أجبان)
echo    📋 30 عقد (توريد، إيجار، خدمات)
echo    👁️ 45 إيجار عين نشط
echo    🏢 20 طبلية × 4 أقسام = 80 عين
echo    💰 إيرادات شهرية: 2,700 د.ك
echo.

echo 🚀 بدء تشغيل النظام المحلي...
echo.
echo 🎉 ===============================================
echo 🌐 الخادم: http://localhost:3000
echo 📊 قاعدة البيانات: SQLite (محلية)
echo 👨‍💻 المطور: محمد مرزوق العقاب
echo ✅ جميع الوظائف نشطة ومفعلة
echo 🎉 ===============================================
echo.

echo 🔗 الواجهات المتاحة مع البيانات النموذجية:
echo.
echo    🔐 تسجيل الدخول:
echo       http://localhost:3000/login.html
echo       👤 المستخدم: admin  🔑 كلمة المرور: admin123
echo.
echo    👑 الواجهة الاحترافية:
echo       http://localhost:3000/professional_ultimate_interface.html
echo.
echo    📁 رفع الملفات النموذجية:
echo       http://localhost:3000/upload_suppliers.html
echo.
echo    👥 إدارة الموردين:
echo       http://localhost:3000/suppliers_management.html
echo.
echo    📋 إدارة العقود:
echo       http://localhost:3000/contracts_management.html
echo.
echo    👁️ إدارة العيون والطبليات:
echo       http://localhost:3000/eyes_management.html
echo.
echo    📊 التقارير والإحصائيات:
echo       http://localhost:3000/reports_dashboard.html
echo.

echo 💡 خطوات الاختبار الموصى بها:
echo    1. 🔐 سجل دخولك (admin / admin123)
echo    2. 📁 ارفع ملف الموردين من sample_data
echo    3. 📋 ارفع ملف العقود
echo    4. 👁️ ارفع ملف إيجارات العيون
echo    5. 📊 تحقق من التقارير والإحصائيات
echo    6. 🔍 اختبر البحث والتصفية
echo    7. ➕ جرب إضافة بيانات جديدة
echo.

echo 🎨 مميزات البيانات النموذجية:
echo    ✅ بيانات واقعية كويتية
echo    ✅ أسماء وعناوين حقيقية
echo    ✅ تنوع في الفئات والأنواع
echo    ✅ علاقات صحيحة بين الجداول
echo    ✅ حالات مختلفة للاختبار
echo.

echo 💡 اضغط Ctrl+C لإيقاف الخادم
echo.

timeout /t 3 /nobreak >nul

echo 🌐 فتح المتصفح تلقائياً...
start http://localhost:3000/launch-interface.html

echo.
echo 🚀 تشغيل الخادم المحلي...
node backend/api/local-server.js
