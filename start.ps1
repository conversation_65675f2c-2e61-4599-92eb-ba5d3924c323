# =====================================================
# سكريپت PowerShell لتشغيل نظام إدارة عقود الموردين والإيجارات
# جمعية المنقف التعاونية
# =====================================================

# تعيين ترميز UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# تعيين عنوان النافذة
$Host.UI.RawUI.WindowTitle = "نظام إدارة عقود الموردين والإيجارات - جمعية المنقف"

# الألوان والرموز
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
    Magenta = "Magenta"
    White = "White"
}

$Icons = @{
    Success = "✓"
    Error = "✗"
    Warning = "⚠"
    Info = "ℹ"
    Rocket = "🚀"
    Gear = "⚙"
}

# دالة طباعة الرسائل الملونة
function Write-ColorMessage {
    param(
        [string]$Message,
        [string]$Color = "White",
        [string]$Icon = ""
    )
    
    if ($Icon) {
        Write-Host "$Icon " -NoNewline
    }
    Write-Host $Message -ForegroundColor $Color
}

# دالة طباعة الرأس
function Show-Header {
    Write-Host ""
    Write-Host "==============================================" -ForegroundColor Cyan
    Write-Host "  نظام إدارة عقود الموردين والإيجارات" -ForegroundColor Cyan
    Write-Host "        جمعية المنقف التعاونية" -ForegroundColor Cyan
    Write-Host "==============================================" -ForegroundColor Cyan
    Write-Host ""
}

# دالة طباعة الفاصل
function Show-Separator {
    Write-Host "----------------------------------------------" -ForegroundColor Blue
}

# دالة التحقق من المتطلبات
function Test-Requirements {
    Write-ColorMessage "التحقق من المتطلبات..." -Color Blue -Icon $Icons.Gear
    
    $missingRequirements = @()
    
    # التحقق من Node.js
    try {
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Write-ColorMessage "Node.js: $nodeVersion" -Color Green -Icon $Icons.Success
        } else {
            $missingRequirements += "Node.js"
        }
    } catch {
        $missingRequirements += "Node.js"
    }
    
    # التحقق من npm
    try {
        $npmVersion = npm --version 2>$null
        if ($npmVersion) {
            Write-ColorMessage "npm: $npmVersion" -Color Green -Icon $Icons.Success
        } else {
            $missingRequirements += "npm"
        }
    } catch {
        $missingRequirements += "npm"
    }
    
    # التحقق من MySQL
    try {
        $mysqlVersion = mysql --version 2>$null
        if ($mysqlVersion) {
            Write-ColorMessage "MySQL متوفر" -Color Green -Icon $Icons.Success
        } else {
            Write-ColorMessage "MySQL غير متوفر في PATH (قد يكون مثبت)" -Color Yellow -Icon $Icons.Info
        }
    } catch {
        Write-ColorMessage "MySQL غير متوفر في PATH (قد يكون مثبت)" -Color Yellow -Icon $Icons.Info
    }
    
    # التحقق من Docker
    try {
        $dockerVersion = docker --version 2>$null
        if ($dockerVersion) {
            Write-ColorMessage "Docker: $dockerVersion" -Color Green -Icon $Icons.Success
        } else {
            Write-ColorMessage "Docker غير متوفر (اختياري)" -Color Yellow -Icon $Icons.Info
        }
    } catch {
        Write-ColorMessage "Docker غير متوفر (اختياري)" -Color Yellow -Icon $Icons.Info
    }
    
    # التحقق من Docker Compose
    try {
        $composeVersion = docker-compose --version 2>$null
        if ($composeVersion) {
            Write-ColorMessage "Docker Compose متوفر" -Color Green -Icon $Icons.Success
        } else {
            Write-ColorMessage "Docker Compose غير متوفر (اختياري)" -Color Yellow -Icon $Icons.Info
        }
    } catch {
        Write-ColorMessage "Docker Compose غير متوفر (اختياري)" -Color Yellow -Icon $Icons.Info
    }
    
    if ($missingRequirements.Count -gt 0) {
        Write-Host ""
        Write-ColorMessage "المتطلبات التالية مفقودة:" -Color Red -Icon $Icons.Error
        foreach ($req in $missingRequirements) {
            Write-Host "  - $req" -ForegroundColor Red
        }
        Write-Host ""
        Write-ColorMessage "يرجى تثبيت المتطلبات المفقودة قبل المتابعة" -Color Yellow -Icon $Icons.Info
        return $false
    }
    
    Write-ColorMessage "جميع المتطلبات متوفرة!" -Color Green -Icon $Icons.Success
    return $true
}

# دالة إعداد ملف .env
function Initialize-Environment {
    if (-not (Test-Path ".env")) {
        Write-ColorMessage "إنشاء ملف .env من القالب..." -Color Yellow -Icon $Icons.Info
        Copy-Item ".env.example" ".env"
        Write-ColorMessage "تم إنشاء ملف .env" -Color Green -Icon $Icons.Success
        Write-ColorMessage "يرجى تحديث إعدادات قاعدة البيانات في ملف .env" -Color Yellow -Icon $Icons.Warning
    } else {
        Write-ColorMessage "ملف .env موجود" -Color Green -Icon $Icons.Success
    }
}

# دالة تثبيت التبعيات
function Install-Dependencies {
    Write-ColorMessage "تثبيت التبعيات..." -Color Blue -Icon $Icons.Gear
    
    try {
        if (Test-Path "package-lock.json") {
            npm ci
        } else {
            npm install
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorMessage "تم تثبيت التبعيات بنجاح" -Color Green -Icon $Icons.Success
            return $true
        } else {
            Write-ColorMessage "فشل في تثبيت التبعيات" -Color Red -Icon $Icons.Error
            return $false
        }
    } catch {
        Write-ColorMessage "فشل في تثبيت التبعيات: $($_.Exception.Message)" -Color Red -Icon $Icons.Error
        return $false
    }
}

# دالة إنشاء المجلدات
function New-RequiredDirectories {
    Write-ColorMessage "إنشاء المجلدات المطلوبة..." -Color Blue -Icon $Icons.Gear
    
    $directories = @(
        "backend\logs",
        "backend\uploads",
        "backend\uploads\contracts",
        "backend\uploads\documents", 
        "backend\uploads\images",
        "data",
        "data\mysql",
        "data\redis",
        "data\uploads",
        "data\logs",
        "data\backups",
        "data\nginx-logs"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-ColorMessage "تم إنشاء المجلد: $dir" -Color Green -Icon $Icons.Success
        }
    }
}

# دالة عرض القائمة
function Show-Menu {
    Show-Separator
    Write-Host "اختر طريقة التشغيل:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "1) تشغيل عادي (Node.js مباشرة)" -ForegroundColor Green
    Write-Host "2) تشغيل للتطوير (مع nodemon)" -ForegroundColor Green
    Write-Host "3) تشغيل باستخدام Docker" -ForegroundColor Green
    Write-Host "4) تشغيل باستخدام Docker مع المراقبة" -ForegroundColor Green
    Write-Host "5) إعداد أولي فقط" -ForegroundColor Green
    Write-Host "6) عرض السجلات" -ForegroundColor Green
    Write-Host "7) إيقاف الخدمات" -ForegroundColor Green
    Write-Host "8) فتح المتصفح" -ForegroundColor Green
    Write-Host "9) تنظيف الملفات المؤقتة" -ForegroundColor Green
    Write-Host "10) فحص حالة النظام" -ForegroundColor Green
    Write-Host "0) خروج" -ForegroundColor Green
    Write-Host ""
    Show-Separator
}

# دالة تشغيل النظام عادي
function Start-Normal {
    Write-ColorMessage "تشغيل النظام..." -Color Blue -Icon $Icons.Rocket
    npm start
}

# دالة تشغيل النظام للتطوير
function Start-Development {
    Write-ColorMessage "تشغيل النظام في وضع التطوير..." -Color Blue -Icon $Icons.Rocket
    npm run dev
}

# دالة تشغيل Docker
function Start-Docker {
    Write-ColorMessage "تشغيل النظام باستخدام Docker..." -Color Blue -Icon $Icons.Rocket
    
    try {
        docker-compose --version | Out-Null
        docker-compose up -d
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorMessage "تم تشغيل النظام باستخدام Docker" -Color Green -Icon $Icons.Success
            Write-ColorMessage "يمكنك الوصول للنظام على: http://localhost:3000" -Color Cyan -Icon $Icons.Info
            Write-ColorMessage "لعرض السجلات: docker-compose logs -f app" -Color Cyan -Icon $Icons.Info
        } else {
            Write-ColorMessage "فشل في تشغيل Docker" -Color Red -Icon $Icons.Error
        }
    } catch {
        Write-ColorMessage "Docker Compose غير متوفر" -Color Red -Icon $Icons.Error
    }
}

# دالة تشغيل Docker مع المراقبة
function Start-DockerMonitoring {
    Write-ColorMessage "تشغيل النظام باستخدام Docker مع المراقبة..." -Color Blue -Icon $Icons.Rocket
    
    try {
        docker-compose --version | Out-Null
        docker-compose --profile monitoring up -d
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorMessage "تم تشغيل النظام مع خدمات المراقبة" -Color Green -Icon $Icons.Success
            Write-ColorMessage "النظام: http://localhost:3000" -Color Cyan -Icon $Icons.Info
            Write-ColorMessage "Prometheus: http://localhost:9090" -Color Cyan -Icon $Icons.Info
            Write-ColorMessage "Grafana: http://localhost:3001" -Color Cyan -Icon $Icons.Info
        } else {
            Write-ColorMessage "فشل في تشغيل Docker" -Color Red -Icon $Icons.Error
        }
    } catch {
        Write-ColorMessage "Docker Compose غير متوفر" -Color Red -Icon $Icons.Error
    }
}

# دالة عرض السجلات
function Show-Logs {
    Show-Separator
    Write-Host "اختر نوع السجلات:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "1) سجلات التطبيق (ملفات)" -ForegroundColor Green
    Write-Host "2) سجلات Docker" -ForegroundColor Green
    Write-Host "3) سجلات قاعدة البيانات" -ForegroundColor Green
    Write-Host ""
    
    $logChoice = Read-Host "اختر رقم الخيار"
    
    switch ($logChoice) {
        "1" {
            if (Test-Path "backend\logs\combined.log") {
                Get-Content "backend\logs\combined.log" -Tail 50
                Write-ColorMessage "اضغط Ctrl+C لإيقاف متابعة السجلات" -Color Cyan -Icon $Icons.Info
                Get-Content "backend\logs\combined.log" -Wait
            } else {
                Write-ColorMessage "ملف السجلات غير موجود" -Color Yellow -Icon $Icons.Warning
            }
        }
        "2" {
            docker-compose logs -f app
        }
        "3" {
            docker-compose logs -f database
        }
        default {
            Write-ColorMessage "خيار غير صحيح" -Color Red -Icon $Icons.Error
        }
    }
}

# دالة إيقاف الخدمات
function Stop-Services {
    Write-ColorMessage "إيقاف الخدمات..." -Color Blue -Icon $Icons.Gear
    
    try {
        docker-compose --version | Out-Null
        docker-compose down
        Write-ColorMessage "تم إيقاف خدمات Docker" -Color Green -Icon $Icons.Success
    } catch {
        Write-ColorMessage "Docker Compose غير متوفر" -Color Yellow -Icon $Icons.Info
    }
    
    # إيقاف العمليات على المنفذ 3000
    try {
        $processes = Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue
        foreach ($process in $processes) {
            Stop-Process -Id $process.OwningProcess -Force -ErrorAction SilentlyContinue
        }
        if ($processes) {
            Write-ColorMessage "تم إيقاف العمليات على المنفذ 3000" -Color Green -Icon $Icons.Success
        }
    } catch {
        # تجاهل الأخطاء
    }
}

# دالة فتح المتصفح
function Open-Browser {
    Write-ColorMessage "فتح المتصفح..." -Color Blue -Icon $Icons.Info
    Start-Process "http://localhost:3000"
}

# دالة تنظيف الملفات المؤقتة
function Clear-TempFiles {
    Write-ColorMessage "تنظيف الملفات المؤقتة..." -Color Blue -Icon $Icons.Gear
    
    if (Test-Path "node_modules") {
        Write-ColorMessage "حذف node_modules..." -Color Yellow -Icon $Icons.Info
        Remove-Item "node_modules" -Recurse -Force
    }
    
    if (Test-Path "package-lock.json") {
        Write-ColorMessage "حذف package-lock.json..." -Color Yellow -Icon $Icons.Info
        Remove-Item "package-lock.json" -Force
    }
    
    if (Test-Path "backend\logs") {
        Write-ColorMessage "تنظيف السجلات..." -Color Yellow -Icon $Icons.Info
        Remove-Item "backend\logs\*.log" -Force -ErrorAction SilentlyContinue
    }
    
    Write-ColorMessage "تم تنظيف الملفات المؤقتة" -Color Green -Icon $Icons.Success
}

# دالة فحص حالة النظام
function Test-SystemHealth {
    Write-ColorMessage "فحص حالة النظام..." -Color Blue -Icon $Icons.Gear
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000/api/health" -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-ColorMessage "النظام يعمل بشكل طبيعي" -Color Green -Icon $Icons.Success
            $healthData = $response.Content | ConvertFrom-Json
            Write-Host "تفاصيل الحالة:" -ForegroundColor Cyan
            Write-Host "  - حالة قاعدة البيانات: $($healthData.database)" -ForegroundColor White
            Write-Host "  - وقت التشغيل: $([math]::Round($healthData.uptime / 60, 2)) دقيقة" -ForegroundColor White
        }
    } catch {
        Write-ColorMessage "النظام غير متاح أو لا يعمل" -Color Red -Icon $Icons.Error
        Write-ColorMessage "تأكد من تشغيل النظام أولاً" -Color Yellow -Icon $Icons.Info
    }
}

# دالة الإعداد الأولي
function Initialize-Setup {
    Write-ColorMessage "بدء الإعداد الأولي..." -Color Blue -Icon $Icons.Gear
    
    if (-not (Test-Requirements)) {
        return $false
    }
    
    Initialize-Environment
    New-RequiredDirectories
    
    if (-not (Install-Dependencies)) {
        return $false
    }
    
    Write-ColorMessage "تم الإعداد الأولي بنجاح!" -Color Green -Icon $Icons.Success
    Write-ColorMessage "يرجى إعداد قاعدة البيانات يدوياً إذا لم تكن معدة مسبقاً" -Color Cyan -Icon $Icons.Info
    return $true
}

# الدالة الرئيسية
function Main {
    Show-Header
    
    # التحقق من وجود ملف package.json
    if (-not (Test-Path "package.json")) {
        Write-ColorMessage "ملف package.json غير موجود" -Color Red -Icon $Icons.Error
        Write-ColorMessage "تأكد من تشغيل السكريبت من المجلد الجذر للمشروع" -Color Cyan -Icon $Icons.Info
        Read-Host "اضغط Enter للخروج"
        return
    }
    
    do {
        Show-Menu
        $choice = Read-Host "اختر رقم الخيار"
        
        switch ($choice) {
            "1" {
                if (Initialize-Setup) {
                    Start-Normal
                }
            }
            "2" {
                if (Initialize-Setup) {
                    Start-Development
                }
            }
            "3" {
                New-RequiredDirectories
                Start-Docker
            }
            "4" {
                New-RequiredDirectories
                Start-DockerMonitoring
            }
            "5" {
                Initialize-Setup
            }
            "6" {
                Show-Logs
            }
            "7" {
                Stop-Services
            }
            "8" {
                Open-Browser
            }
            "9" {
                Clear-TempFiles
            }
            "10" {
                Test-SystemHealth
            }
            "0" {
                Write-ColorMessage "شكراً لاستخدام النظام!" -Color Cyan -Icon $Icons.Info
                return
            }
            default {
                Write-ColorMessage "خيار غير صحيح، يرجى المحاولة مرة أخرى" -Color Red -Icon $Icons.Error
            }
        }
        
        if ($choice -ne "0") {
            Write-Host ""
            Read-Host "اضغط Enter للمتابعة"
            Clear-Host
            Show-Header
        }
    } while ($choice -ne "0")
}

# تشغيل الدالة الرئيسية
Main
