#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق إدارة عقود الموردين - جمعية المنقف
Supplier Contract Management System - Al-Mangaf Association

الملف الرئيسي لتشغيل التطبيق
Main application entry point
"""

import sys
import os

# إضافة مجلد المشروع إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from gui.main_window import MainWindow

class SupplierContractApp:
    """الفئة الرئيسية للتطبيق"""

    def __init__(self):
        """تهيئة التطبيق"""
        self.db_manager = None
        self.main_window = None

    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            self.db_manager = DatabaseManager()
            self.db_manager.create_tables()
            print("تم إنشاء جداول قاعدة البيانات بنجاح")
            return True
        except Exception as e:
            print(f"خطأ في قاعدة البيانات: {str(e)}")
            return False

    def run(self):
        """تشغيل التطبيق"""
        # تهيئة قاعدة البيانات
        if not self.initialize_database():
            return

        # إنشاء النافذة الرئيسية
        try:
            self.main_window = MainWindow(self.db_manager)
            self.main_window.run()
        except Exception as e:
            print(f"خطأ في التطبيق: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🚀 تشغيل نظام إدارة عقود الموردين - جمعية المنقف")
    print("=" * 50)

    app = SupplierContractApp()
    app.run()

if __name__ == "__main__":
    main()
