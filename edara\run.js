const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3000;

const server = http.createServer((req, res) => {
  console.log('Request:', req.url);
  
  let filePath = req.url;
  if (filePath === '/') {
    filePath = '/edara/professional_ultimate_interface_final.html';
  }
  
  const fullPath = path.join(__dirname, '..', filePath);
  
  if (fs.existsSync(fullPath)) {
    const ext = path.extname(fullPath);
    let contentType = 'text/html';
    
    if (ext === '.css') contentType = 'text/css';
    if (ext === '.js') contentType = 'application/javascript';
    if (ext === '.pdf') contentType = 'application/pdf';
    
    res.setHeader('Content-Type', contentType);
    fs.createReadStream(fullPath).pipe(res);
  } else {
    res.writeHead(404);
    res.end('File not found');
  }
});

server.listen(PORT, () => {
  console.log('🎉 النسخة النهائية تعمل على http://localhost:' + PORT);
  console.log('👨‍💻 تطوير: محمد مرزوق العقاب');
  console.log('🏢 جمعية المنقف التعاونية');
  console.log('📅 التقويم: ميلادي');
  console.log('📞 الهاتف: +965-23710272');
  console.log('📧 البريد: <EMAIL>');
  console.log('📍 العنوان: المنقف - قطعة 4 شارع فهد الهملان');
});
