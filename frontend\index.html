<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="نظام إدارة عقود الموردين والإيجارات - جمعية المنقف التعاونية">
  <meta name="keywords" content="عقود, موردين, إيجارات, جمعية المنقف, إدارة">
  <meta name="author" content="جمعية المنقف التعاونية">
  
  <title>نظام إدارة عقود الموردين والإيجارات - جمعية المنقف</title>
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/static/assets/images/favicon.ico">
  <link rel="apple-touch-icon" href="/static/assets/images/logo.png">
  
  <!-- CSS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- Custom CSS -->
  <link rel="stylesheet" href="/static/assets/css/main.css">
  <link rel="stylesheet" href="/static/assets/css/components.css">
  <link rel="stylesheet" href="/static/assets/css/responsive.css">
  
  <!-- PWA Manifest -->
  <link rel="manifest" href="/static/manifest.json">
  <meta name="theme-color" content="#3b82f6">
  
  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="نظام إدارة عقود الموردين والإيجارات">
  <meta property="og:description" content="نظام متكامل لإدارة عقود الموردين والإيجارات في جمعية المنقف التعاونية">
  <meta property="og:image" content="/static/assets/images/logo.png">
  <meta property="og:url" content="https://contracts.manqaf-coop.com">
  <meta property="og:type" content="website">
  
  <style>
    /* تحسينات إضافية للتصميم */
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
    
    :root {
      --primary-color: #3b82f6;
      --secondary-color: #1e40af;
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --error-color: #ef4444;
      --info-color: #06b6d4;
      --dark-color: #1f2937;
      --light-color: #f8fafc;
      --border-radius: 12px;
      --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      --transition: all 0.3s ease;
    }
    
    * {
      font-family: 'Cairo', sans-serif;
    }
    
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    
    .glass-effect {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .card-hover {
      transition: var(--transition);
    }
    
    .card-hover:hover {
      transform: translateY(-5px);
      box-shadow: var(--shadow-lg);
    }
    
    .btn-primary {
      background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
      color: white;
      border: none;
      border-radius: var(--border-radius);
      padding: 12px 24px;
      font-weight: 600;
      transition: var(--transition);
      cursor: pointer;
    }
    
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
    
    .loading-spinner {
      border: 3px solid #f3f4f6;
      border-top: 3px solid var(--primary-color);
      border-radius: 50%;
      width: 24px;
      height: 24px;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .fade-in {
      animation: fadeIn 0.5s ease-in;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .slide-in-right {
      animation: slideInRight 0.5s ease-out;
    }
    
    @keyframes slideInRight {
      from { transform: translateX(100%); }
      to { transform: translateX(0); }
    }
    
    /* تحسينات للطباعة */
    @media print {
      .no-print { display: none !important; }
      body { background: white !important; }
      .glass-effect { background: white !important; }
    }
    
    /* تحسينات للشاشات الصغيرة */
    @media (max-width: 768px) {
      .container {
        padding: 1rem;
      }
      
      .grid-responsive {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>

<body class="min-h-screen">
  <!-- شاشة التحميل -->
  <div id="loadingScreen" class="fixed inset-0 bg-white z-50 flex items-center justify-center">
    <div class="text-center">
      <div class="loading-spinner mx-auto mb-4"></div>
      <h2 class="text-xl font-bold text-gray-800 mb-2">جاري التحميل...</h2>
      <p class="text-gray-600">نظام إدارة عقود الموردين والإيجارات</p>
    </div>
  </div>

  <!-- الحاوية الرئيسية -->
  <div id="app" class="hidden">
    
    <!-- الشريط العلوي -->
    <header class="glass-effect shadow-lg sticky top-0 z-40 no-print">
      <div class="container mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
          
          <!-- الشعار والعنوان -->
          <div class="flex items-center space-x-4">
            <img src="/static/assets/images/logo.png" alt="شعار جمعية المنقف" class="w-12 h-12 rounded-lg">
            <div>
              <h1 class="text-xl font-bold text-gray-800">نظام إدارة العقود</h1>
              <p class="text-sm text-gray-600">جمعية المنقف التعاونية</p>
            </div>
          </div>
          
          <!-- أزرار التحكم -->
          <div class="flex items-center space-x-4">
            
            <!-- مؤشر حالة الاتصال -->
            <div id="connectionStatus" class="flex items-center">
              <div class="w-3 h-3 bg-green-500 rounded-full ml-2 animate-pulse"></div>
              <span class="text-sm text-green-600 font-medium">متصل</span>
            </div>
            
            <!-- الوقت الحالي -->
            <div class="text-sm text-gray-600">
              <i class="fas fa-clock ml-1"></i>
              <span id="currentTime"></span>
            </div>
            
            <!-- قائمة المستخدم -->
            <div class="relative" id="userMenu">
              <button id="userMenuButton" class="flex items-center space-x-2 text-gray-700 hover:text-blue-600 transition-colors">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <i class="fas fa-user text-blue-600"></i>
                </div>
                <span id="userName">ضيف</span>
                <i class="fas fa-chevron-down"></i>
              </button>
              
              <!-- قائمة منسدلة -->
              <div id="userDropdown" class="absolute left-0 mt-2 w-48 glass-effect rounded-lg shadow-lg hidden">
                <div class="py-2">
                  <a href="#" id="profileLink" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 transition-colors">
                    <i class="fas fa-user-circle ml-2"></i>
                    الملف الشخصي
                  </a>
                  <a href="#" id="settingsLink" class="block px-4 py-2 text-gray-700 hover:bg-blue-50 transition-colors">
                    <i class="fas fa-cog ml-2"></i>
                    الإعدادات
                  </a>
                  <hr class="my-2">
                  <button id="logoutButton" class="w-full text-right px-4 py-2 text-red-600 hover:bg-red-50 transition-colors">
                    <i class="fas fa-sign-out-alt ml-2"></i>
                    تسجيل الخروج
                  </button>
                </div>
              </div>
            </div>
            
            <!-- زر تسجيل الدخول -->
            <button id="loginButton" class="btn-primary">
              <i class="fas fa-sign-in-alt ml-2"></i>
              تسجيل الدخول
            </button>
            
          </div>
        </div>
      </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="container mx-auto px-4 py-8">
      
      <!-- الصفحة الرئيسية -->
      <div id="homePage" class="page">
        
        <!-- بطاقة الترحيب -->
        <div class="glass-effect rounded-2xl p-8 mb-8 text-center card-hover fade-in">
          <div class="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-file-contract text-blue-600 text-4xl"></i>
          </div>
          <h2 class="text-3xl font-bold text-gray-800 mb-4">مرحباً بك في نظام إدارة العقود</h2>
          <p class="text-gray-600 mb-6 max-w-2xl mx-auto">
            نظام متكامل وحديث لإدارة عقود الموردين والإيجارات في جمعية المنقف التعاونية.
            يوفر النظام أدوات متقدمة لإنشاء وإدارة ومتابعة العقود بكفاءة عالية.
          </p>
          
          <!-- أزرار الإجراءات السريعة -->
          <div class="flex flex-wrap justify-center gap-4">
            <button id="quickStartButton" class="btn-primary">
              <i class="fas fa-rocket ml-2"></i>
              البدء السريع
            </button>
            <button id="viewDemoButton" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors">
              <i class="fas fa-play ml-2"></i>
              عرض توضيحي
            </button>
            <button id="downloadAppButton" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors">
              <i class="fas fa-download ml-2"></i>
              تحميل التطبيق
            </button>
          </div>
        </div>
        
        <!-- بطاقات الميزات -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          
          <!-- ميزة إدارة العقود -->
          <div class="glass-effect rounded-xl p-6 card-hover fade-in">
            <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
              <i class="fas fa-file-contract text-blue-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-3">إدارة العقود</h3>
            <p class="text-gray-600 mb-4">إنشاء وتعديل ومتابعة العقود مع الموردين بسهولة ومرونة كاملة</p>
            <button class="text-blue-600 hover:text-blue-800 font-medium transition-colors">
              اعرف المزيد <i class="fas fa-arrow-left mr-2"></i>
            </button>
          </div>
          
          <!-- ميزة إدارة الموردين -->
          <div class="glass-effect rounded-xl p-6 card-hover fade-in">
            <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-4">
              <i class="fas fa-truck text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-3">إدارة الموردين</h3>
            <p class="text-gray-600 mb-4">قاعدة بيانات شاملة للموردين مع تتبع الأداء والتقييمات</p>
            <button class="text-green-600 hover:text-green-800 font-medium transition-colors">
              اعرف المزيد <i class="fas fa-arrow-left mr-2"></i>
            </button>
          </div>
          
          <!-- ميزة التقارير -->
          <div class="glass-effect rounded-xl p-6 card-hover fade-in">
            <div class="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <i class="fas fa-chart-bar text-purple-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-800 mb-3">التقارير والإحصائيات</h3>
            <p class="text-gray-600 mb-4">تقارير مفصلة وإحصائيات متقدمة لاتخاذ قرارات مدروسة</p>
            <button class="text-purple-600 hover:text-purple-800 font-medium transition-colors">
              اعرف المزيد <i class="fas fa-arrow-left mr-2"></i>
            </button>
          </div>
          
        </div>
        
        <!-- إحصائيات سريعة -->
        <div id="quickStats" class="glass-effect rounded-xl p-6 mb-8 hidden">
          <h3 class="text-xl font-bold text-gray-800 mb-6 text-center">إحصائيات سريعة</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center">
              <div class="text-3xl font-bold text-blue-600" id="totalContracts">0</div>
              <div class="text-gray-600">إجمالي العقود</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-green-600" id="activeContracts">0</div>
              <div class="text-gray-600">العقود النشطة</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-orange-600" id="totalSuppliers">0</div>
              <div class="text-gray-600">الموردين</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-purple-600" id="totalValue">0</div>
              <div class="text-gray-600">القيمة الإجمالية</div>
            </div>
          </div>
        </div>
        
      </div>
      
    </main>

    <!-- التذييل -->
    <footer class="glass-effect mt-16 py-8 no-print">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          
          <!-- معلومات الجمعية -->
          <div>
            <h4 class="text-lg font-bold text-gray-800 mb-4">جمعية المنقف التعاونية</h4>
            <p class="text-gray-600 mb-4">
              جمعية تعاونية رائدة في دولة الكويت تهدف إلى خدمة أعضائها وتوفير أفضل المنتجات والخدمات.
            </p>
            <div class="flex space-x-4">
              <a href="#" class="text-gray-400 hover:text-blue-600 transition-colors">
                <i class="fab fa-facebook text-xl"></i>
              </a>
              <a href="#" class="text-gray-400 hover:text-blue-600 transition-colors">
                <i class="fab fa-twitter text-xl"></i>
              </a>
              <a href="#" class="text-gray-400 hover:text-blue-600 transition-colors">
                <i class="fab fa-instagram text-xl"></i>
              </a>
            </div>
          </div>
          
          <!-- روابط سريعة -->
          <div>
            <h4 class="text-lg font-bold text-gray-800 mb-4">روابط سريعة</h4>
            <ul class="space-y-2">
              <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">الصفحة الرئيسية</a></li>
              <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">إدارة العقود</a></li>
              <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">إدارة الموردين</a></li>
              <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">التقارير</a></li>
              <li><a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">المساعدة</a></li>
            </ul>
          </div>
          
          <!-- معلومات الاتصال -->
          <div>
            <h4 class="text-lg font-bold text-gray-800 mb-4">تواصل معنا</h4>
            <div class="space-y-2">
              <div class="flex items-center text-gray-600">
                <i class="fas fa-map-marker-alt ml-2"></i>
                <span>المنقف، الكويت</span>
              </div>
              <div class="flex items-center text-gray-600">
                <i class="fas fa-phone ml-2"></i>
                <span>+965 1234 5678</span>
              </div>
              <div class="flex items-center text-gray-600">
                <i class="fas fa-envelope ml-2"></i>
                <span><EMAIL></span>
              </div>
            </div>
          </div>
          
        </div>
        
        <hr class="my-8 border-gray-200">
        
        <div class="text-center text-gray-600">
          <p>&copy; 2025 جمعية المنقف التعاونية. جميع الحقوق محفوظة.</p>
          <p class="mt-2">تم تطوير النظام بواسطة فريق تقنية المعلومات</p>
        </div>
      </div>
    </footer>

  </div>

  <!-- JavaScript Libraries -->
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
  <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  
  <!-- Custom JavaScript -->
  <script src="/static/assets/js/utils.js"></script>
  <script src="/static/assets/js/api.js"></script>
  <script src="/static/assets/js/main.js"></script>
  
  <!-- Service Worker للعمل بدون اتصال -->
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/static/sw.js')
          .then(registration => {
            console.log('SW registered: ', registration);
          })
          .catch(registrationError => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  </script>

</body>
</html>
