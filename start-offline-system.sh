#!/bin/bash

# نظام إدارة عقود الموردين والإيجارات المتطور
# جمعية المنقف التعاونية
# تطوير وتنفيذ: محمد مرزوق العقاب

# ألوان للطباعة
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# دالة طباعة ملونة
print_color() {
    echo -e "${1}${2}${NC}"
}

# دالة عرض الرأس
show_header() {
    clear
    print_color $CYAN "🎉 ==============================================="
    print_color $CYAN "👑 نظام إدارة عقود الموردين والإيجارات المتطور"
    print_color $CYAN "🏢 جمعية المنقف التعاونية"
    echo
    print_color $PURPLE "👨‍💻 تطوير وتنفيذ: محمد مرزوق العقاب"
    print_color $PURPLE "🎯 مطور نظم معلومات محترف ومتخصص"
    echo
    print_color $GREEN "🔥 النظام يعمل بدون إنترنت - جميع الوظائف نشطة!"
    print_color $CYAN "🎉 ==============================================="
    echo
}

# دالة عرض القائمة
show_menu() {
    print_color $YELLOW "🚀 اختر طريقة التشغيل:"
    echo
    echo "1. 🎮 تشغيل تفاعلي (موصى به)"
    echo "2. 🔧 تشغيل الخادم المحلي مباشرة"
    echo "3. 🧪 اختبار النظام"
    echo "4. 📊 عرض معلومات النظام"
    echo "5. 🔄 تثبيت التبعيات"
    echo
    echo "0. خروج"
    echo
    read -p "اختر رقم (0-5): " choice
}

# دالة فتح المتصفح
open_browser() {
    local url=$1
    
    if command -v xdg-open > /dev/null; then
        xdg-open "$url" 2>/dev/null
    elif command -v open > /dev/null; then
        open "$url" 2>/dev/null
    else
        print_color $YELLOW "💡 افتح المتصفح يدوياً: $url"
    fi
}

# تشغيل تفاعلي
interactive_start() {
    echo
    print_color $GREEN "🎮 تشغيل تفاعلي للنظام..."
    echo
    
    if [ -f "launch-professional.sh" ]; then
        ./launch-professional.sh
    else
        print_color $RED "❌ ملف التشغيل التفاعلي غير موجود"
        direct_start
    fi
}

# تشغيل مباشر
direct_start() {
    echo
    print_color $GREEN "🔧 تشغيل الخادم المحلي مباشرة..."
    echo
    
    print_color $BLUE "📦 التحقق من التبعيات..."
    if [ ! -d "node_modules" ]; then
        print_color $YELLOW "🔄 تثبيت التبعيات..."
        npm install
    fi
    
    echo
    print_color $CYAN "🎉 ==============================================="
    print_color $GREEN "🚀 بدء تشغيل النظام المحلي..."
    print_color $BLUE "🌐 الخادم: http://localhost:3000"
    print_color $BLUE "📊 قاعدة البيانات: SQLite (محلية)"
    print_color $PURPLE "👨‍💻 المطور: محمد مرزوق العقاب"
    print_color $GREEN "✅ جميع الوظائف نشطة ومفعلة"
    print_color $CYAN "🎉 ==============================================="
    echo
    
    print_color $YELLOW "🔗 الواجهات المتاحة:"
    echo "   👑 الواجهة الاحترافية: http://localhost:3000/professional_ultimate_interface.html"
    echo "   🔧 واجهة الإدارة المتقدمة: http://localhost:3000/advanced_management_interface.html"
    echo "   👥 إدارة الموردين: http://localhost:3000/suppliers_management.html"
    echo "   📋 إدارة العقود: http://localhost:3000/contracts_management.html"
    echo "   👁️ إدارة العيون: http://localhost:3000/eyes_management.html"
    echo "   📊 التقارير: http://localhost:3000/reports_dashboard.html"
    echo "   📁 رفع الملفات: http://localhost:3000/upload_suppliers.html"
    echo "   🔐 تسجيل الدخول: http://localhost:3000/login.html"
    echo
    
    print_color $BLUE "💡 اضغط Ctrl+C لإيقاف الخادم"
    echo
    
    # فتح المتصفح تلقائياً
    sleep 3
    open_browser "http://localhost:3000/launch-interface.html" &
    
    # تشغيل الخادم
    node backend/api/local-server.js
}

# اختبار النظام
test_system() {
    echo
    print_color $GREEN "🧪 تشغيل اختبار النظام..."
    echo
    
    if [ -f "quick-test.sh" ]; then
        ./quick-test.sh
    elif [ -f "quick-test.bat" ]; then
        print_color $YELLOW "💡 قم بتشغيل: ./quick-test.bat"
    else
        print_color $RED "❌ ملف الاختبار غير موجود"
        print_color $BLUE "💡 قم بتشغيل: npm run test:interactive"
    fi
    
    read -p "اضغط Enter للمتابعة..."
}

# تثبيت التبعيات
install_dependencies() {
    echo
    print_color $GREEN "📦 تثبيت التبعيات..."
    echo
    
    npm install
    
    echo
    print_color $GREEN "✅ تم تثبيت التبعيات بنجاح"
    echo
    
    read -p "اضغط Enter للمتابعة..."
}

# عرض معلومات النظام
show_system_info() {
    echo
    print_color $CYAN "📊 معلومات النظام المتطور:"
    echo
    print_color $WHITE "🏢 الشركة: جمعية المنقف التعاونية"
    print_color $WHITE "👨‍💻 المطور: محمد مرزوق العقاب"
    print_color $WHITE "🎯 التخصص: مطور نظم معلومات محترف"
    print_color $WHITE "🌐 الخادم: http://localhost:3000"
    print_color $WHITE "📁 المجلد: $(pwd)"
    print_color $WHITE "🕒 التاريخ: $(date)"
    print_color $WHITE "📊 قاعدة البيانات: SQLite (محلية)"
    print_color $GREEN "🔥 حالة الإنترنت: غير مطلوب"
    echo
    
    print_color $YELLOW "🎨 الواجهات الاحترافية المتطورة:"
    echo "   👑 professional_ultimate_interface.html - الواجهة الاحترافية المتطورة"
    echo "   🔧 advanced_management_interface.html - واجهة الإدارة المتقدمة"
    echo "   👥 suppliers_management.html - إدارة الموردين"
    echo "   📋 contracts_management.html - إدارة العقود"
    echo "   👁️ eyes_management.html - إدارة العيون والطبليات"
    echo "   📊 reports_dashboard.html - لوحة التقارير والإحصائيات"
    echo "   📁 upload_suppliers.html - نظام رفع الملفات"
    echo "   🔐 login.html - تسجيل الدخول"
    echo
    
    print_color $GREEN "🔧 الوظائف المتاحة:"
    echo "   ✅ إدارة الموردين (إضافة، تعديل، حذف، بحث)"
    echo "   ✅ إدارة العقود (توريد، إيجار، خدمات)"
    echo "   ✅ نظام العيون (20 طبلية × 4 أقسام)"
    echo "   ✅ حساب الإيجارات (60 د.ك لكل قسم)"
    echo "   ✅ التقارير والإحصائيات التفاعلية"
    echo "   ✅ رفع ملفات Excel/CSV"
    echo "   ✅ نظام المصادقة والصلاحيات"
    echo "   ✅ قاعدة بيانات محلية SQLite"
    echo
    
    print_color $BLUE "🎯 بيانات تسجيل الدخول:"
    echo "   👤 اسم المستخدم: admin"
    echo "   🔑 كلمة المرور: admin123"
    echo
    
    read -p "اضغط Enter للمتابعة..."
}

# الحلقة الرئيسية
main() {
    while true; do
        show_header
        show_menu
        
        case $choice in
            1)
                interactive_start
                ;;
            2)
                direct_start
                ;;
            3)
                test_system
                ;;
            4)
                show_system_info
                ;;
            5)
                install_dependencies
                ;;
            0)
                echo
                print_color $CYAN "👋 شكراً لاستخدام نظام إدارة العقود المتطور"
                print_color $PURPLE "👨‍💻 تطوير: محمد مرزوق العقاب"
                print_color $CYAN "🏢 جمعية المنقف التعاونية"
                print_color $GREEN "🔥 النظام يعمل بدون إنترنت - جميع الوظائف نشطة!"
                echo
                exit 0
                ;;
            *)
                echo
                print_color $RED "❌ اختيار غير صحيح"
                print_color $BLUE "💡 يرجى اختيار رقم صحيح (0-5)"
                sleep 2
                ;;
        esac
        
        if [ "$choice" != "2" ]; then
            echo
            print_color $GREEN "🎉 تم تشغيل النظام بنجاح!"
            print_color $BLUE "💡 النظام يعمل الآن بدون إنترنت"
            print_color $YELLOW "🔗 افتح المتصفح وانتقل إلى: http://localhost:3000"
            echo
            read -p "اضغط Enter للعودة للقائمة الرئيسية..."
        fi
    done
}

# تشغيل البرنامج
main
